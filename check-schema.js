#!/usr/bin/env node

import postgres from 'postgres';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const DATABASE_URL = process.env.DATABASE_URL;

if (!DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is not set');
  process.exit(1);
}

console.log('🔍 FlowCraft Database Schema Analysis');
console.log('=' .repeat(50));

async function checkSchema() {
  let client;
  try {
    client = postgres(DATABASE_URL, {
      ssl: 'require',
      max: 1,
      idle_timeout: 20,
      connect_timeout: 10,
    });

    // Check table structures
    console.log('📋 Checking table structures...');
    
    const tables = ['profiles', 'automations', 'credentials', 'team_members', 'activity_logs', 'automation_logs'];
    
    for (const tableName of tables) {
      console.log(`\n🔍 Table: ${tableName}`);
      try {
        // Get column information
        const columns = await client`
          SELECT 
            column_name, 
            data_type, 
            is_nullable, 
            column_default,
            character_maximum_length
          FROM information_schema.columns 
          WHERE table_name = ${tableName} 
          AND table_schema = 'public'
          ORDER BY ordinal_position
        `;
        
        if (columns.length === 0) {
          console.log('   ❌ Table does not exist');
          continue;
        }
        
        console.log('   ✅ Columns:');
        columns.forEach(col => {
          const nullable = col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL';
          const defaultVal = col.column_default ? ` DEFAULT ${col.column_default}` : '';
          const length = col.character_maximum_length ? `(${col.character_maximum_length})` : '';
          console.log(`      ${col.column_name}: ${col.data_type}${length} ${nullable}${defaultVal}`);
        });
        
        // Get row count
        const count = await client`SELECT COUNT(*) as count FROM ${client(tableName)}`;
        console.log(`   📊 Records: ${count[0].count}`);
        
        // Check for any obvious data issues in automations table
        if (tableName === 'automations') {
          const issues = await client`
            SELECT 
              id,
              name,
              trigger->>'type' as trigger_type,
              CASE 
                WHEN trigger->>'type' = 'schedule' AND trigger->'config'->>'runType' IS NULL THEN 'Missing runType'
                WHEN trigger->>'type' IS NULL THEN 'Missing trigger type'
                ELSE 'OK'
              END as issue
            FROM automations 
            WHERE 
              (trigger->>'type' = 'schedule' AND trigger->'config'->>'runType' IS NULL)
              OR trigger->>'type' IS NULL
            LIMIT 10
          `;
          
          if (issues.length > 0) {
            console.log('   ⚠️  Data Issues Found:');
            issues.forEach(issue => {
              console.log(`      ID ${issue.id} (${issue.name}): ${issue.issue} - trigger_type: ${issue.trigger_type}`);
            });
          }
        }
        
      } catch (error) {
        console.log(`   ❌ Error checking ${tableName}: ${error.message}`);
      }
    }
    
    // Check for missing columns that might be expected
    console.log('\n🔍 Checking for schema inconsistencies...');
    
    // Check automations table for expected columns
    const automationsColumns = await client`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'automations' 
      AND table_schema = 'public'
    `;
    
    const expectedAutomationColumns = ['id', 'user_id', 'name', 'description', 'trigger', 'actions', 'conditions', 'status', 'last_run', 'created_at', 'updated_at'];
    const actualColumns = automationsColumns.map(c => c.column_name);
    
    console.log('   Automations table column check:');
    expectedAutomationColumns.forEach(col => {
      if (actualColumns.includes(col)) {
        console.log(`   ✅ ${col}`);
      } else {
        console.log(`   ❌ Missing: ${col}`);
      }
    });
    
    // Check for extra columns
    const extraColumns = actualColumns.filter(col => !expectedAutomationColumns.includes(col));
    if (extraColumns.length > 0) {
      console.log('   📝 Extra columns:');
      extraColumns.forEach(col => {
        console.log(`      + ${col}`);
      });
    }
    
    // Check database performance indicators
    console.log('\n📊 Database Performance Indicators:');
    
    const stats = await client`
      SELECT 
        schemaname,
        tablename,
        n_tup_ins as inserts,
        n_tup_upd as updates,
        n_tup_del as deletes,
        n_live_tup as live_tuples,
        n_dead_tup as dead_tuples
      FROM pg_stat_user_tables 
      WHERE schemaname = 'public'
      ORDER BY tablename
    `;
    
    stats.forEach(stat => {
      console.log(`   ${stat.tablename}:`);
      console.log(`      Live tuples: ${stat.live_tuples}, Dead tuples: ${stat.dead_tuples}`);
      console.log(`      Operations: ${stat.inserts} inserts, ${stat.updates} updates, ${stat.deletes} deletes`);
    });
    
    // Check for connection issues
    console.log('\n🔗 Connection Health Check:');
    const connectionInfo = await client`
      SELECT 
        count(*) as total_connections,
        count(*) FILTER (WHERE state = 'active') as active_connections,
        count(*) FILTER (WHERE state = 'idle') as idle_connections
      FROM pg_stat_activity 
      WHERE datname = current_database()
    `;
    
    console.log(`   Total connections: ${connectionInfo[0].total_connections}`);
    console.log(`   Active: ${connectionInfo[0].active_connections}, Idle: ${connectionInfo[0].idle_connections}`);
    
    console.log('\n🎉 Schema analysis completed!');
    
  } catch (error) {
    console.error('❌ Schema check failed:', error.message);
    return false;
  } finally {
    if (client) {
      await client.end();
    }
  }
  
  return true;
}

// Run the check
checkSchema()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
