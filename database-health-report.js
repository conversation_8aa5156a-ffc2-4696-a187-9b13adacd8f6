#!/usr/bin/env node

import postgres from 'postgres';
import { performance } from 'perf_hooks';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const DATABASE_URL = process.env.DATABASE_URL;

if (!DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is not set');
  process.exit(1);
}

console.log('📊 FlowCraft Database Health Report');
console.log('=' .repeat(60));
console.log(`Generated: ${new Date().toISOString()}`);
console.log('');

async function generateHealthReport() {
  let client;
  try {
    client = postgres(DATABASE_URL, {
      ssl: 'require',
      max: 3,
      idle_timeout: 30,
      connect_timeout: 15,
      max_lifetime: 60 * 30,
      prepare: false,
      transform: { undefined: null }
    });

    // 1. Connection Health
    console.log('🔗 CONNECTION HEALTH');
    console.log('-'.repeat(40));
    
    const startTime = performance.now();
    await client`SELECT 1`;
    const connectionTime = performance.now() - startTime;
    
    console.log(`✅ Connection Time: ${connectionTime.toFixed(2)}ms`);
    
    if (connectionTime < 100) {
      console.log('   🟢 Excellent connection speed');
    } else if (connectionTime < 300) {
      console.log('   🟡 Good connection speed');
    } else {
      console.log('   🔴 Slow connection - consider connection pooling optimization');
    }

    // 2. Database Information
    console.log('\n📋 DATABASE INFORMATION');
    console.log('-'.repeat(40));
    
    const dbInfo = await client`
      SELECT 
        version() as version,
        current_database() as database,
        current_user as user,
        inet_server_addr() as server_ip,
        inet_server_port() as server_port
    `;
    
    console.log(`Database: ${dbInfo[0].database}`);
    console.log(`User: ${dbInfo[0].user}`);
    console.log(`Server: ${dbInfo[0].server_ip}:${dbInfo[0].server_port}`);
    console.log(`Version: ${dbInfo[0].version.split(',')[0]}`);

    // 3. Table Health
    console.log('\n📊 TABLE HEALTH SUMMARY');
    console.log('-'.repeat(40));
    
    const tableStats = await client`
      SELECT 
        t.table_name,
        COALESCE(s.n_live_tup, 0) as live_tuples,
        COALESCE(s.n_dead_tup, 0) as dead_tuples,
        COALESCE(s.n_tup_ins, 0) as inserts,
        COALESCE(s.n_tup_upd, 0) as updates,
        COALESCE(s.n_tup_del, 0) as deletes,
        pg_size_pretty(pg_total_relation_size(c.oid)) as size
      FROM information_schema.tables t
      LEFT JOIN pg_stat_user_tables s ON t.table_name = s.relname
      LEFT JOIN pg_class c ON t.table_name = c.relname
      WHERE t.table_schema = 'public'
      AND t.table_type = 'BASE TABLE'
      ORDER BY COALESCE(s.n_live_tup, 0) DESC
    `;
    
    tableStats.forEach(table => {
      const deadRatio = table.live_tuples > 0 ? (table.dead_tuples / table.live_tuples * 100) : 0;
      const healthIcon = deadRatio > 20 ? '🔴' : deadRatio > 10 ? '🟡' : '🟢';
      
      console.log(`${healthIcon} ${table.table_name}:`);
      console.log(`   Records: ${table.live_tuples} live, ${table.dead_tuples} dead (${deadRatio.toFixed(1)}%)`);
      console.log(`   Operations: ${table.inserts}I/${table.updates}U/${table.deletes}D`);
      console.log(`   Size: ${table.size}`);
    });

    // 4. Performance Metrics
    console.log('\n⚡ PERFORMANCE METRICS');
    console.log('-'.repeat(40));
    
    const performanceTests = [];
    for (let i = 0; i < 5; i++) {
      const start = performance.now();
      await client`SELECT COUNT(*) FROM automations`;
      const end = performance.now();
      performanceTests.push(end - start);
    }
    
    const avgTime = performanceTests.reduce((a, b) => a + b, 0) / performanceTests.length;
    const minTime = Math.min(...performanceTests);
    const maxTime = Math.max(...performanceTests);
    
    console.log(`Average Query Time: ${avgTime.toFixed(2)}ms`);
    console.log(`Min/Max: ${minTime.toFixed(2)}ms / ${maxTime.toFixed(2)}ms`);
    
    if (avgTime < 50) {
      console.log('🟢 Excellent query performance');
    } else if (avgTime < 200) {
      console.log('🟡 Good query performance');
    } else {
      console.log('🔴 Poor query performance - optimization needed');
    }

    // 5. Connection Pool Status
    console.log('\n🏊 CONNECTION POOL STATUS');
    console.log('-'.repeat(40));
    
    const connections = await client`
      SELECT 
        state,
        count(*) as count,
        round(avg(extract(epoch from (now() - state_change)))) as avg_duration_sec
      FROM pg_stat_activity 
      WHERE datname = current_database()
      GROUP BY state
      ORDER BY count DESC
    `;
    
    connections.forEach(conn => {
      console.log(`${conn.state}: ${conn.count} connections (avg: ${conn.avg_duration_sec}s)`);
    });

    // 6. Data Integrity Check
    console.log('\n🔍 DATA INTEGRITY CHECK');
    console.log('-'.repeat(40));
    
    const integrityChecks = [
      {
        name: 'Automations with valid triggers',
        query: `SELECT COUNT(*) as count FROM automations WHERE trigger IS NOT NULL AND jsonb_typeof(trigger) = 'object'`
      },
      {
        name: 'Credentials with encrypted data',
        query: `SELECT COUNT(*) as count FROM credentials WHERE data IS NOT NULL`
      },
      {
        name: 'Valid user IDs in automations',
        query: `SELECT COUNT(*) as count FROM automations WHERE user_id ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'`
      }
    ];
    
    for (const check of integrityChecks) {
      try {
        const result = await client.unsafe(check.query);
        console.log(`✅ ${check.name}: ${result[0].count}`);
      } catch (error) {
        console.log(`❌ ${check.name}: Error - ${error.message}`);
      }
    }

    // 7. Recommendations
    console.log('\n💡 RECOMMENDATIONS');
    console.log('-'.repeat(40));
    
    const recommendations = [];
    
    if (avgTime > 200) {
      recommendations.push('🔧 Consider adding database indexes for frequently queried columns');
    }
    
    if (connectionTime > 300) {
      recommendations.push('🔧 Optimize connection pooling settings');
    }
    
    const totalDeadTuples = tableStats.reduce((sum, table) => sum + parseInt(table.dead_tuples), 0);
    if (totalDeadTuples > 100) {
      recommendations.push('🔧 Run VACUUM ANALYZE to clean up dead tuples');
    }
    
    const totalConnections = connections.reduce((sum, conn) => sum + parseInt(conn.count), 0);
    if (totalConnections > 50) {
      recommendations.push('🔧 Monitor connection usage - approaching connection limit');
    }
    
    if (recommendations.length === 0) {
      console.log('🎉 Database is healthy! No immediate recommendations.');
    } else {
      recommendations.forEach(rec => console.log(rec));
    }

    // 8. Summary
    console.log('\n📈 HEALTH SUMMARY');
    console.log('-'.repeat(40));
    
    const healthScore = calculateHealthScore(connectionTime, avgTime, totalDeadTuples, totalConnections);
    console.log(`Overall Health Score: ${healthScore}/100`);
    
    if (healthScore >= 90) {
      console.log('🟢 Excellent - Database is performing optimally');
    } else if (healthScore >= 70) {
      console.log('🟡 Good - Minor optimizations recommended');
    } else if (healthScore >= 50) {
      console.log('🟠 Fair - Several issues need attention');
    } else {
      console.log('🔴 Poor - Immediate optimization required');
    }

  } catch (error) {
    console.error('❌ Health report generation failed:', error.message);
    return false;
  } finally {
    if (client) {
      await client.end();
    }
  }
  
  return true;
}

function calculateHealthScore(connectionTime, avgQueryTime, deadTuples, totalConnections) {
  let score = 100;
  
  // Connection time penalty
  if (connectionTime > 500) score -= 20;
  else if (connectionTime > 300) score -= 10;
  else if (connectionTime > 100) score -= 5;
  
  // Query performance penalty
  if (avgQueryTime > 500) score -= 25;
  else if (avgQueryTime > 200) score -= 15;
  else if (avgQueryTime > 100) score -= 5;
  
  // Dead tuples penalty
  if (deadTuples > 1000) score -= 15;
  else if (deadTuples > 500) score -= 10;
  else if (deadTuples > 100) score -= 5;
  
  // Connection usage penalty
  if (totalConnections > 50) score -= 10;
  else if (totalConnections > 30) score -= 5;
  
  return Math.max(0, score);
}

// Run the health report
generateHealthReport()
  .then(success => {
    console.log(`\n${'='.repeat(60)}`);
    console.log('📊 Health report completed!');
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
