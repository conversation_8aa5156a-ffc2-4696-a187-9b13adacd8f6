# Database Configuration
DATABASE_URL=postgresql://postgres.xyguqslzenektzuimdxi:<EMAIL>:6543/postgres

# Server Configuration
PORT=5000
NODE_ENV=development

# Session Secret
SESSION_SECRET=your-secure-session-secret-here-change-this

# Google OAuth Configuration
GOOGLE_CLIENT_ID=995644543910-bb5uscdhcic342akafln1h6ktl1vs5mo.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Other OAuth and service credentials
GMAIL_CLIENT_ID=your_gmail_client_id_here
GMAIL_CLIENT_SECRET=your_gmail_client_secret_here
SLACK_CLIENT_ID=your_slack_client_id_here
SLACK_CLIENT_SECRET=your_slack_client_secret_here

# Encryption key for credential storage
ENCRYPTION_KEY=a_strong_32_character_encryption_key_here

# Gemini API Key (if using AI features)
GEMINI_API_KEY=your_gemini_api_key_here

# Supabase Configuration (if using Supabase auth)
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Stripe Configuration (if using payments)
VITE_STRIPE_PUBLIC_KEY=your_stripe_publishable_key
STRIPE_SECRET_KEY=your_stripe_secret_key