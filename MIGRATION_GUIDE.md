# Filorina Migration Guide: Replit to Local Development

This guide will help you migrate the Filorina automation platform from Replit to your local development machine.

## Prerequisites

Before starting, ensure you have the following installed on your local machine:

1. **Node.js** (version 20 or higher)
   - Download from: https://nodejs.org/
   - Verify installation: `node --version`

2. **npm** (comes with Node.js)
   - Verify installation: `npm --version`

3. **PostgreSQL** (if you want to run the database locally)
   - Download from: https://www.postgresql.org/download/
   - Or use the existing Supabase database (recommended)

4. **Git** (for version control)
   - Download from: https://git-scm.com/

## Step 1: Download the Project

1. Download the entire project folder from Replit
2. Extract it to your desired location on your local machine
3. Open a terminal/command prompt and navigate to the project directory

## Step 2: Install Dependencies

Run the following command in your project directory:

```bash
npm install
```

This will install all the necessary packages listed in package.json.

## Step 3: Configure Environment Variables

1. Copy the `.env.local` file to `.env`:
   ```bash
   cp .env.local .env
   ```

2. Update the `.env` file with your actual credentials:
   - Keep the existing DATABASE_URL if you want to use the Supabase database
   - Update GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET with your OAuth credentials
   - Generate a secure SESSION_SECRET (use a random string generator)
   - Add any other API keys you're using

## Step 4: Update Google OAuth Redirect URI

Since we've changed from `https://filorina1.replit.app` to `http://localhost:5000`, you need to update your Google OAuth configuration:

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project
3. Navigate to "APIs & Services" > "Credentials"
4. Click on your OAuth 2.0 Client ID
5. Under "Authorized redirect URIs", remove the Replit URL and add:
   ```
   http://localhost:5000/api/auth/google/callback
   ```
6. Save the changes

## Step 5: Run the Development Server

Start the application with one of these methods:

### Method 1: Using npm (recommended)
```bash
npm run dev
```

### Method 2: Using the run script
If you get a "tsx: command not found" error, use:
```bash
chmod +x run-dev.sh  # Make executable (first time only)
./run-dev.sh
```

### Method 3: Direct command
```bash
NODE_ENV=development npx tsx server/index.ts
```

Any of these commands will:
- Start the Express backend server on port 5000
- Start the Vite development server for the React frontend
- Enable hot module replacement for development

The application will be available at:
- **Frontend**: http://localhost:5000
- **API**: http://localhost:5000/api

## Step 6: Verify the Application

1. Open your browser and go to http://localhost:5000
2. Check that the login page loads correctly
3. Test the authentication flow
4. Verify that Google OAuth works with the new redirect URI

## Important Changes Made

### 1. OAuth Redirect URLs
All OAuth callback URLs have been updated from:
- `https://filorina1.replit.app/...` to `http://localhost:5000/...`

### 2. Environment Variables
- PORT is set to 5000 (can be changed if needed)
- NODE_ENV is set to development for local development

### 3. Removed Replit-Specific Code
- The vite.config.ts still contains some Replit plugins, but they won't affect local development
- The .replit file can be ignored for local development

## Troubleshooting

### DATABASE_URL Error
If you see "DATABASE_URL must be set" error:
1. Make sure you have a `.env` file in the root directory
2. Copy from template if missing: `cp .env.local .env`
3. Ensure DATABASE_URL is set in the .env file:
   ```
   DATABASE_URL=postgresql://postgres.xyguqslzenektzuimdxi:<EMAIL>:6543/postgres
   ```

### Port Already in Use
If port 5000 is already in use, you can change it:
1. Update the PORT in your .env file
2. Update the OAuth redirect URI in Google Cloud Console

### Database Connection Issues
If you have database connection issues:
1. Ensure your DATABASE_URL is correct
2. If using local PostgreSQL, make sure the service is running
3. Check that your IP is whitelisted in Supabase (if using Supabase)

### Google OAuth Not Working
1. Double-check that you've updated the redirect URI in Google Cloud Console
2. Ensure GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET are correct in .env
3. Clear your browser cookies and try again

### Missing Dependencies
If you encounter module not found errors:
```bash
rm -rf node_modules package-lock.json
npm install
```

## Production Deployment

When you're ready to deploy to production:

1. Update all OAuth redirect URIs to your production domain
2. Set NODE_ENV=production
3. Update all localhost references to your production domain
4. Use HTTPS for production (required for OAuth)
5. Set secure session cookies in server/index.ts

## Additional Notes

- The smart refresh system will work locally without issues
- All automations and features should work identically to the Replit version
- The database is still hosted on Supabase, so your data remains intact
- You can now use your preferred code editor (VS Code, etc.)

## Commands Reference

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm start` - Run production build
- `npm run db:push` - Apply database schema changes

If you encounter any issues during migration, refer to the error messages and this guide's troubleshooting section.