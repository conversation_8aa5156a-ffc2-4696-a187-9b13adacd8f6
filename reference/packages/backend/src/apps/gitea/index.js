import defineApp from '../../helpers/define-app.js';
import addAuthHeader from './common/add-auth-header.js';
import auth from './auth/index.js';
import setBaseUrl from './common/set-base-url.js';
import triggers from './triggers/index.js';
import dynamicData from './dynamic-data/index.js';
import actions from './actions/index.js';

export default defineApp({
  name: 'Gite<PERSON>',
  key: 'gitea',
  iconUrl: '{BASE_URL}/apps/gitea/assets/favicon.svg',
  authDocUrl: '{DOCS_URL}/apps/gitea/connection',
  supportsConnections: true,
  baseUrl: 'https://www.gitea.com',
  apiBaseUrl: '',
  primaryColor: '#609926',
  beforeRequest: [setBaseUrl, addAuthHeader],
  auth,
  triggers,
  dynamicData,
  actions,
});
