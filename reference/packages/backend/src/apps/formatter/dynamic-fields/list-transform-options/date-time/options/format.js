const formatOptions = [
  {
    label: 'ccc MMM dd HH:mm:ssZZZ yyyy (Wed Aug 23 12:25:36-0000 2023)',
    value: 'ccc MMM dd HH:mm:ssZZZ yyyy',
  },
  {
    label: 'MMMM dd yyyy HH:mm:ss (August 23 2023 12:25:36)',
    value: 'MMMM dd yyyy HH:mm:ss',
  },
  {
    label: 'MMMM dd yyyy (August 23 2023)',
    value: 'MMMM dd yyyy',
  },
  {
    label: 'MMM dd yyyy (Aug 23 2023)',
    value: 'MMM dd yyyy',
  },
  {
    label: 'yyyy-MM-dd HH:mm:ss ZZZ (2023-08-23 12:25:36 -0000)',
    value: 'yyyy-MM-dd HH:mm:ss ZZZ',
  },
  {
    label: 'yyyy-MM-dd (2023-08-23)',
    value: 'yyyy-MM-dd',
  },
  {
    label: 'MM-dd-yyyy (08-23-2023)',
    value: 'MM-dd-yyyy',
  },
  {
    label: 'MM/dd/yyyy (08/23/2023)',
    value: 'MM/dd/yyyy',
  },
  {
    label: 'MM/dd/yy (08/23/23)',
    value: 'MM/dd/yy',
  },
  {
    label: 'dd-MM-yyyy (23-08-2023)',
    value: 'dd-MM-yyyy',
  },
  {
    label: 'dd/MM/yyyy (23/08/2023)',
    value: 'dd/MM/yyyy',
  },
  {
    label: 'dd/MM/yy (23/08/23)',
    value: 'dd/MM/yy',
  },
  {
    label: 'MM-yyyy (08-2023)',
    value: 'MM-yyyy',
  },
  {
    label: 'Unix timestamp in seconds (1694008283)',
    value: 'X',
  },
  {
    label: 'Unix timestamp in milliseconds (1694008306315)',
    value: 'x',
  },
];

export default formatOptions;
