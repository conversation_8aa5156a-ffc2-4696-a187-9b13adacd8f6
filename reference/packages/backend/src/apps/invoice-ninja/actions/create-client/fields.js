export const fields = [
  {
    label: 'Client Name',
    key: 'clientName',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Contact First Name',
    key: 'contactFirstName',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Contact Last Name',
    key: 'contactLastName',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Contact Email',
    key: 'contactEmail',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Contact Phone',
    key: 'contactPhone',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Language Code',
    key: 'languageCode',
    type: 'dropdown',
    required: false,
    description: '',
    variables: true,
    options: [
      { value: 1, label: 'English - United States' },
      { value: 2, label: 'Italian' },
      { value: 3, label: 'German' },
      { value: 4, label: 'French' },
      { value: 5, label: 'Portuguese - Brazilian' },
      { value: 6, label: 'Dutch' },
      { value: 7, label: 'Spanish' },
      { value: 8, label: 'Norwegian' },
      { value: 9, label: 'Danish' },
      { value: 10, label: 'Japanese' },
      { value: 11, label: 'Swedish' },
      { value: 12, label: 'Spanish - Spain' },
      { value: 13, label: 'French - Canada' },
      { value: 14, label: 'Lithuanian' },
      { value: 15, label: 'Polish' },
      { value: 16, label: 'Czech' },
      { value: 17, label: 'Croatian' },
      { value: 18, label: 'Albanian' },
      { value: 19, label: 'Greek' },
      { value: 20, label: 'English - United Kingdom' },
      { value: 21, label: 'Portuguese - Portugal' },
      { value: 22, label: 'Slovenian' },
      { value: 23, label: 'Finnish' },
      { value: 24, label: 'Romanian' },
      { value: 25, label: 'Turkish - Turkey' },
      { value: 26, label: 'Thai' },
      { value: 27, label: 'Macedonian' },
      { value: 28, label: 'Chinese - Taiwan' },
      { value: 29, label: 'Russian (Russia)' },
      { value: 30, label: 'Arabic' },
      { value: 31, label: 'Persian' },
      { value: 32, label: 'Latvian' },
      { value: 33, label: 'Serbian' },
      { value: 34, label: 'Slovak' },
      { value: 35, label: 'Estonian' },
      { value: 36, label: 'Bulgarian' },
      { value: 37, label: 'Hebrew' },
      { value: 38, label: 'Khmer' },
      { value: 39, label: 'Hungarian' },
      { value: 40, label: 'French - Swiss' },
    ],
  },
  {
    label: 'Currency Code',
    key: 'currencyCode',
    type: 'dropdown',
    required: false,
    description: '',
    variables: true,
    options: [
      { value: 1, label: 'US Dollar' },
      { value: 2, label: 'British Pound' },
      { value: 3, label: 'Euro' },
      { value: 4, label: 'South African Rand' },
      { value: 5, label: 'Danish Krone' },
      { value: 6, label: 'Israeli Shekel' },
      { value: 7, label: 'Swedish Krona' },
      { value: 8, label: 'Kenyan Shilling' },
      { value: 9, label: 'Canadian Dollar' },
      { value: 10, label: 'Philippine Peso' },
      { value: 11, label: 'Indian Rupee' },
      { value: 12, label: 'Australian Dollar' },
      { value: 13, label: 'Singapore Dollar' },
      { value: 14, label: 'Norske Kroner' },
      { value: 15, label: 'New Zealand Dollar' },
      { value: 16, label: 'Vietnamese Dong' },
      { value: 17, label: 'Swiss Franc' },
      { value: 18, label: 'Guatemalan Quetzal' },
      { value: 19, label: 'Malaysian Ringgit' },
      { value: 20, label: 'Brazilian Real' },
      { value: 21, label: 'Thai Baht' },
      { value: 22, label: 'Nigerian Naira' },
      { value: 23, label: 'Argentine Peso' },
      { value: 24, label: 'Bangladeshi Taka' },
      { value: 25, label: 'United Arab Emirates Dirham' },
      { value: 26, label: 'Hong Kong Dollar' },
      { value: 27, label: 'Indonesian Rupiah' },
      { value: 28, label: 'Mexican Peso' },
      { value: 29, label: 'Egyptian Pound' },
      { value: 30, label: 'Colombian Peso' },
      { value: 31, label: 'West African Franc' },
      { value: 32, label: 'Chinese Renminbi' },
      { value: 33, label: 'Rwandan Franc' },
      { value: 34, label: 'Tanzanian Shilling' },
      { value: 35, label: 'Netherlands Antillean Guilder' },
      { value: 36, label: 'Trinidad and Tobago Dollar' },
      { value: 37, label: 'East Caribbean Dollar' },
      { value: 38, label: 'Ghanaian Cedi' },
      { value: 39, label: 'Bulgarian Lev' },
      { value: 40, label: 'Aruban Florin' },
      { value: 41, label: 'Turkish Lira' },
      { value: 42, label: 'Romanian New Leu' },
      { value: 43, label: 'Croatian Kuna' },
      { value: 44, label: 'Saudi Riyal' },
      { value: 45, label: 'Japanese Yen' },
      { value: 46, label: 'Maldivian Rufiyaa' },
      { value: 47, label: 'Costa Rican Colón' },
      { value: 48, label: 'Pakistani Rupee' },
      { value: 49, label: 'Polish Zloty' },
      { value: 50, label: 'Sri Lankan Rupee' },
      { value: 51, label: 'Czech Koruna' },
      { value: 52, label: 'Uruguayan Peso' },
      { value: 53, label: 'Namibian Dollar' },
      { value: 54, label: 'Tunisian Dinar' },
      { value: 55, label: 'Russian Ruble' },
      { value: 56, label: 'Mozambican Metical' },
      { value: 57, label: 'Omani Rial' },
      { value: 58, label: 'Ukrainian Hryvnia' },
      { value: 59, label: 'Macanese Pataca' },
      { value: 60, label: 'Taiwan New Dollar' },
      { value: 61, label: 'Dominican Peso' },
      { value: 62, label: 'Chilean Peso' },
      { value: 63, label: 'Icelandic Króna' },
      { value: 64, label: 'Papua New Guinean Kina' },
      { value: 65, label: 'Jordanian Dinar' },
      { value: 66, label: 'Myanmar Kyat' },
      { value: 67, label: 'Peruvian Sol' },
      { value: 68, label: 'Botswana Pula' },
      { value: 69, label: 'Hungarian Forint' },
      { value: 70, label: 'Ugandan Shilling' },
      { value: 71, label: 'Barbadian Dollar' },
      { value: 72, label: 'Brunei Dollar' },
      { value: 73, label: 'Georgian Lari' },
      { value: 74, label: 'Qatari Riyal' },
      { value: 75, label: 'Honduran Lempira' },
      { value: 76, label: 'Surinamese Dollar' },
      { value: 77, label: 'Bahraini Dinar' },
      { value: 78, label: 'Venezuelan Bolivars' },
      { value: 79, label: 'South Korean Won' },
      { value: 80, label: 'Moroccan Dirham' },
      { value: 81, label: 'Jamaican Dollar' },
      { value: 82, label: 'Angolan Kwanza' },
      { value: 83, label: 'Haitian Gourde' },
      { value: 84, label: 'Zambian Kwacha' },
      { value: 85, label: 'Nepalese Rupee' },
      { value: 86, label: 'CFP Franc' },
      { value: 87, label: 'Mauritian Rupee' },
      { value: 88, label: 'Cape Verdean Escudo' },
      { value: 89, label: 'Kuwaiti Dinar' },
      { value: 90, label: 'Algerian Dinar' },
      { value: 91, label: 'Macedonian Denar' },
      { value: 92, label: 'Fijian Dollar' },
      { value: 93, label: 'Bolivian Boliviano' },
      { value: 94, label: 'Albanian Lek' },
      { value: 95, label: 'Serbian Dinar' },
      { value: 96, label: 'Lebanese Pound' },
      { value: 97, label: 'Armenian Dram' },
      { value: 98, label: 'Azerbaijan Manat' },
      { value: 99, label: 'Bosnia and Herzegovina Convertible Mark' },
      { value: 100, label: 'Belarusian Ruble' },
      { value: 101, label: 'Gibraltar Pound' },
      { value: 102, label: 'Moldovan Leu' },
      { value: 103, label: 'Kazakhstani Tenge' },
      { value: 104, label: 'Ethiopian Birr' },
      { value: 105, label: 'Gambia Dalasi' },
      { value: 106, label: 'Paraguayan Guarani' },
      { value: 107, label: 'Malawi Kwacha' },
      { value: 108, label: 'Zimbabwean Dollar' },
      { value: 109, label: 'Cambodian Riel' },
      { value: 110, label: 'Vanuatu Vatu' },
      { value: 111, label: 'Cuban Peso' },
      { value: 112, label: 'Cayman Island Dollar' },
      { value: 113, label: 'Swazi lilangeni' },
      { value: 114, label: 'BZ Dollar' },
      { value: 115, label: 'Libyan Dinar' },
      { value: 116, label: 'Silver Troy Ounce' },
      { value: 117, label: 'Gold Troy Ounce' },
      { value: 118, label: 'Nicaraguan Córdoba' },
    ],
  },
  {
    label: 'Id Number',
    key: 'idNumber',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Vat Number',
    key: 'vatNumber',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Street Address',
    key: 'streetAddress',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Apt/Suite',
    key: 'aptSuite',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'City',
    key: 'city',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'State/Province',
    key: 'stateProvince',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Postal Code',
    key: 'postalCode',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Country Code',
    key: 'countryCode',
    type: 'dropdown',
    required: false,
    description: '',
    variables: true,
    options: [
      { value: 4, label: 'Afghanistan' },
      { value: 8, label: 'Albania' },
      { value: 12, label: 'Algeria' },
      { value: 16, label: 'American Samoa' },
      { value: 20, label: 'Andorra' },
      { value: 24, label: 'Angola' },
      { value: 660, label: 'Anguilla' },
      { value: 10, label: 'Antarctica' },
      { value: 28, label: 'Antigua and Barbuda' },
      { value: 32, label: 'Argentina' },
      { value: 51, label: 'Armenia' },
      { value: 533, label: 'Aruba' },
      { value: 36, label: 'Australia' },
      { value: 40, label: 'Austria' },
      { value: 31, label: 'Azerbaijan' },
      { value: 44, label: 'Bahamas' },
      { value: 48, label: 'Bahrain' },
      { value: 50, label: 'Bangladesh' },
      { value: 52, label: 'Barbados' },
      { value: 112, label: 'Belarus' },
      { value: 56, label: 'Belgium' },
      { value: 84, label: 'Belize' },
      { value: 204, label: 'Benin' },
      { value: 60, label: 'Bermuda' },
      { value: 64, label: 'Bhutan' },
      { value: 68, label: 'Bolivia, Plurinational State of' },
      { value: 535, label: 'Bonaire, Sint Eustatius and Saba' },
      { value: 70, label: 'Bosnia and Herzegovina' },
      { value: 72, label: 'Botswana' },
      { value: 74, label: 'Bouvet Island' },
      { value: 76, label: 'Brazil' },
      { value: 86, label: 'British Indian Ocean Territory' },
      { value: 96, label: 'Brunei Darussalam' },
      { value: 100, label: 'Bulgaria' },
      { value: 854, label: 'Burkina Faso' },
      { value: 108, label: 'Burundi' },
      { value: 116, label: 'Cambodia' },
      { value: 120, label: 'Cameroon' },
      { value: 124, label: 'Canada' },
      { value: 132, label: 'Cape Verde' },
      { value: 136, label: 'Cayman Islands' },
      { value: 140, label: 'Central African Republic' },
      { value: 148, label: 'Chad' },
      { value: 152, label: 'Chile' },
      { value: 156, label: 'China' },
      { value: 162, label: 'Christmas Island' },
      { value: 166, label: 'Cocos (Keeling) Islands' },
      { value: 170, label: 'Colombia' },
      { value: 174, label: 'Comoros' },
      { value: 178, label: 'Congo' },
      { value: 180, label: 'Congo, the Democratic Republic of the' },
      { value: 184, label: 'Cook Islands' },
      { value: 188, label: 'Costa Rica' },
      { value: 191, label: 'Croatia' },
      { value: 192, label: 'Cuba' },
      { value: 531, label: 'Curaçao' },
      { value: 196, label: 'Cyprus' },
      { value: 203, label: 'Czech Republic' },
      { value: 384, label: "Côte d'Ivoire" },
      { value: 208, label: 'Denmark' },
      { value: 262, label: 'Djibouti' },
      { value: 212, label: 'Dominica' },
      { value: 214, label: 'Dominican Republic' },
      { value: 218, label: 'Ecuador' },
      { value: 818, label: 'Egypt' },
      { value: 222, label: 'El Salvador' },
      { value: 226, label: 'Equatorial Guinea' },
      { value: 232, label: 'Eritrea' },
      { value: 233, label: 'Estonia' },
      { value: 231, label: 'Ethiopia' },
      { value: 238, label: 'Falkland Islands (Malvinas)' },
      { value: 234, label: 'Faroe Islands' },
      { value: 242, label: 'Fiji' },
      { value: 246, label: 'Finland' },
      { value: 250, label: 'France' },
      { value: 254, label: 'French Guiana' },
      { value: 258, label: 'French Polynesia' },
      { value: 260, label: 'French Southern Territories' },
      { value: 266, label: 'Gabon' },
      { value: 270, label: 'Gambia' },
      { value: 268, label: 'Georgia' },
      { value: 276, label: 'Germany' },
      { value: 288, label: 'Ghana' },
      { value: 292, label: 'Gibraltar' },
      { value: 300, label: 'Greece' },
      { value: 304, label: 'Greenland' },
      { value: 308, label: 'Grenada' },
      { value: 312, label: 'Guadeloupe' },
      { value: 316, label: 'Guam' },
      { value: 320, label: 'Guatemala' },
      { value: 831, label: 'Guernsey' },
      { value: 324, label: 'Guinea' },
      { value: 624, label: 'Guinea-Bissau' },
      { value: 328, label: 'Guyana' },
      { value: 332, label: 'Haiti' },
      { value: 334, label: 'Heard Island and McDonald Islands' },
      { value: 336, label: 'Holy See (Vatican City State)' },
      { value: 340, label: 'Honduras' },
      { value: 344, label: 'Hong Kong' },
      { value: 348, label: 'Hungary' },
      { value: 352, label: 'Iceland' },
      { value: 356, label: 'India' },
      { value: 360, label: 'Indonesia' },
      { value: 364, label: 'Iran, Islamic Republic of' },
      { value: 368, label: 'Iraq' },
      { value: 372, label: 'Ireland' },
      { value: 833, label: 'Isle of Man' },
      { value: 376, label: 'Israel' },
      { value: 380, label: 'Italy' },
      { value: 388, label: 'Jamaica' },
      { value: 392, label: 'Japan' },
      { value: 832, label: 'Jersey' },
      { value: 400, label: 'Jordan' },
      { value: 398, label: 'Kazakhstan' },
      { value: 404, label: 'Kenya' },
      { value: 296, label: 'Kiribati' },
      { value: 408, label: "Korea, Democratic People's Republic of" },
      { value: 410, label: 'Korea, Republic of' },
      { value: 414, label: 'Kuwait' },
      { value: 417, label: 'Kyrgyzstan' },
      { value: 418, label: "Lao People's Democratic Republic" },
      { value: 428, label: 'Latvia' },
      { value: 422, label: 'Lebanon' },
      { value: 426, label: 'Lesotho' },
      { value: 430, label: 'Liberia' },
      { value: 434, label: 'Libya' },
      { value: 438, label: 'Liechtenstein' },
      { value: 440, label: 'Lithuania' },
      { value: 442, label: 'Luxembourg' },
      { value: 446, label: 'Macao' },
      { value: 807, label: 'Macedonia, the former Yugoslav Republic of' },
      { value: 450, label: 'Madagascar' },
      { value: 454, label: 'Malawi' },
      { value: 458, label: 'Malaysia' },
      { value: 462, label: 'Maldives' },
      { value: 466, label: 'Mali' },
      { value: 470, label: 'Malta' },
      { value: 584, label: 'Marshall Islands' },
      { value: 474, label: 'Martinique' },
      { value: 478, label: 'Mauritania' },
      { value: 480, label: 'Mauritius' },
      { value: 175, label: 'Mayotte' },
      { value: 484, label: 'Mexico' },
      { value: 583, label: 'Micronesia, Federated States of' },
      { value: 498, label: 'Moldova, Republic of' },
      { value: 492, label: 'Monaco' },
      { value: 496, label: 'Mongolia' },
      { value: 499, label: 'Montenegro' },
      { value: 500, label: 'Montserrat' },
      { value: 504, label: 'Morocco' },
      { value: 508, label: 'Mozambique' },
      { value: 104, label: 'Myanmar' },
      { value: 516, label: 'Namibia' },
      { value: 520, label: 'Nauru' },
      { value: 524, label: 'Nepal' },
      { value: 528, label: 'Netherlands' },
      { value: 540, label: 'New Caledonia' },
      { value: 554, label: 'New Zealand' },
      { value: 558, label: 'Nicaragua' },
      { value: 562, label: 'Niger' },
      { value: 566, label: 'Nigeria' },
      { value: 570, label: 'Niue' },
      { value: 574, label: 'Norfolk Island' },
      { value: 580, label: 'Northern Mariana Islands' },
      { value: 578, label: 'Norway' },
      { value: 512, label: 'Oman' },
      { value: 586, label: 'Pakistan' },
      { value: 585, label: 'Palau' },
      { value: 275, label: 'Palestine' },
      { value: 591, label: 'Panama' },
      { value: 598, label: 'Papua New Guinea' },
      { value: 600, label: 'Paraguay' },
      { value: 604, label: 'Peru' },
      { value: 608, label: 'Philippines' },
      { value: 612, label: 'Pitcairn' },
      { value: 616, label: 'Poland' },
      { value: 620, label: 'Portugal' },
      { value: 630, label: 'Puerto Rico' },
      { value: 634, label: 'Qatar' },
      { value: 642, label: 'Romania' },
      { value: 643, label: 'Russian Federation' },
      { value: 646, label: 'Rwanda' },
      { value: 638, label: 'Réunion' },
      { value: 652, label: 'Saint Barthélemy' },
      { value: 654, label: 'Saint Helena, Ascension and Tristan da Cunha' },
      { value: 659, label: 'Saint Kitts and Nevis' },
      { value: 662, label: 'Saint Lucia' },
      { value: 663, label: 'Saint Martin (French part)' },
      { value: 666, label: 'Saint Pierre and Miquelon' },
      { value: 670, label: 'Saint Vincent and the Grenadines' },
      { value: 882, label: 'Samoa' },
      { value: 674, label: 'San Marino' },
      { value: 678, label: 'Sao Tome and Principe' },
      { value: 682, label: 'Saudi Arabia' },
      { value: 686, label: 'Senegal' },
      { value: 688, label: 'Serbia' },
      { value: 690, label: 'Seychelles' },
      { value: 694, label: 'Sierra Leone' },
      { value: 702, label: 'Singapore' },
      { value: 534, label: 'Sint Maarten (Dutch part)' },
      { value: 703, label: 'Slovakia' },
      { value: 705, label: 'Slovenia' },
      { value: 90, label: 'Solomon Islands' },
      { value: 706, label: 'Somalia' },
      { value: 710, label: 'South Africa' },
      { value: 239, label: 'South Georgia and the South Sandwich Islands' },
      { value: 728, label: 'South Sudan' },
      { value: 724, label: 'Spain' },
      { value: 144, label: 'Sri Lanka' },
      { value: 729, label: 'Sudan' },
      { value: 740, label: 'Suriname' },
      { value: 744, label: 'Svalbard and Jan Mayen' },
      { value: 748, label: 'Swaziland' },
      { value: 752, label: 'Sweden' },
      { value: 756, label: 'Switzerland' },
      { value: 760, label: 'Syrian Arab Republic' },
      { value: 158, label: 'Taiwan, Province of China' },
      { value: 762, label: 'Tajikistan' },
      { value: 834, label: 'Tanzania, United Republic of' },
      { value: 764, label: 'Thailand' },
      { value: 626, label: 'Timor-Leste' },
      { value: 768, label: 'Togo' },
      { value: 772, label: 'Tokelau' },
      { value: 776, label: 'Tonga' },
      { value: 780, label: 'Trinidad and Tobago' },
      { value: 788, label: 'Tunisia' },
      { value: 792, label: 'Turkey' },
      { value: 795, label: 'Turkmenistan' },
      { value: 796, label: 'Turks and Caicos Islands' },
      { value: 798, label: 'Tuvalu' },
      { value: 800, label: 'Uganda' },
      { value: 804, label: 'Ukraine' },
      { value: 784, label: 'United Arab Emirates' },
      { value: 826, label: 'United Kingdom' },
      { value: 840, label: 'United States' },
      { value: 581, label: 'United States Minor Outlying Islands' },
      { value: 858, label: 'Uruguay' },
      { value: 860, label: 'Uzbekistan' },
      { value: 548, label: 'Vanuatu' },
      { value: 862, label: 'Venezuela, Bolivarian Republic of' },
      { value: 704, label: 'Viet Nam' },
      { value: 92, label: 'Virgin Islands, British' },
      { value: 850, label: 'Virgin Islands, U.S.' },
      { value: 876, label: 'Wallis and Futuna' },
      { value: 732, label: 'Western Sahara' },
      { value: 887, label: 'Yemen' },
      { value: 894, label: 'Zambia' },
      { value: 716, label: 'Zimbabwe' },
      { value: 248, label: 'Åland Islands' },
    ],
  },
  {
    label: 'Shipping Street Address',
    key: 'shippingStreetAddress',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Shipping Apt/Suite',
    key: 'shippingAptSuite',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Shipping City',
    key: 'shippingCity',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Shipping State/Province',
    key: 'shippingStateProvince',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Shipping Postal Code',
    key: 'shippingPostalCode',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Shipping Country Code',
    key: 'shippingCountryCode',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Private Notes',
    key: 'privateNotes',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Public Notes',
    key: 'publicNotes',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Website',
    key: 'website',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Custom Value 1',
    key: 'customValue1',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Custom Value 2',
    key: 'customValue2',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Custom Value 3',
    key: 'customValue3',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Custom Value 4',
    key: 'customValue4',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
];
