export const fields = [
  {
    label: 'Client ID',
    key: 'clientId',
    type: 'dropdown',
    required: true,
    description: 'The ID of the client, not the name or email address.',
    variables: true,
    source: {
      type: 'query',
      name: 'getDynamicData',
      arguments: [
        {
          name: 'key',
          value: 'listClients',
        },
      ],
    },
  },
  {
    label: 'Payment Date',
    key: 'paymentDate',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Invoice',
    key: 'invoiceId',
    type: 'dropdown',
    required: false,
    description: '',
    variables: true,
    source: {
      type: 'query',
      name: 'getDynamicData',
      arguments: [
        {
          name: 'key',
          value: 'listInvoices',
        },
      ],
    },
  },
  {
    label: 'Invoice Amount',
    key: 'invoiceAmount',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Payment Type',
    key: 'paymentType',
    type: 'dropdown',
    required: false,
    description: '',
    variables: true,
    options: [
      { label: 'Bank Transfer', value: '1' },
      { label: 'Cash', value: '2' },
      { label: 'Debit', value: '3' },
      { label: 'ACH', value: '4' },
      { label: 'Visa Card', value: '5' },
      { label: 'MasterCard', value: '6' },
      { label: 'American Express', value: '7' },
      { label: 'Discover Card', value: '8' },
      { label: 'Diners Card', value: '9' },
      { label: 'EuroCard', value: '10' },
      { label: 'Nova', value: '11' },
      { label: 'Credit Card Other', value: '12' },
      { label: 'PayPal', value: '13' },
      { label: 'Google Wallet', value: '14' },
      { label: 'Check', value: '15' },
      { label: 'Carte Blanche', value: '16' },
      { label: 'UnionPay', value: '17' },
      { label: 'JCB', value: '18' },
      { label: 'Laser', value: '19' },
      { label: 'Maestro', value: '20' },
      { label: 'Solo', value: '21' },
      { label: 'Switch', value: '22' },
      { label: 'iZettle', value: '23' },
      { label: 'Swish', value: '24' },
      { label: 'Venmo', value: '25' },
      { label: 'Money Order', value: '26' },
      { label: 'Alipay', value: '27' },
      { label: 'Sofort', value: '28' },
      { label: 'SEPA', value: '29' },
      { label: 'GoCardless', value: '30' },
      { label: 'Bitcoin', value: '31' },
    ],
  },
  {
    label: 'Transfer Reference',
    key: 'transferReference',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Private Notes',
    key: 'privateNotes',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
];
