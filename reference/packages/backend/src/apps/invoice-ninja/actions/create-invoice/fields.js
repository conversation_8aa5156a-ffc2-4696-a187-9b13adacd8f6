export const fields = [
  {
    label: 'Client ID',
    key: 'clientId',
    type: 'dropdown',
    required: true,
    description: 'The ID of the client, not the name or email address.',
    variables: true,
    source: {
      type: 'query',
      name: 'getDynamicData',
      arguments: [
        {
          name: 'key',
          value: 'listClients',
        },
      ],
    },
  },
  {
    label: 'Send Email',
    key: 'sendEmail',
    type: 'dropdown',
    required: false,
    description: '',
    variables: true,
    options: [
      { label: 'False', value: 'false' },
      { label: 'True', value: 'true' },
    ],
  },
  {
    label: '<PERSON> Sent',
    key: 'markSent',
    type: 'dropdown',
    required: false,
    description: 'Setting this to true creates the invoice as sent.',
    variables: true,
    options: [
      { label: 'False', value: 'false' },
      { label: 'True', value: 'true' },
    ],
  },
  {
    label: 'Paid',
    key: 'paid',
    type: 'dropdown',
    required: false,
    description: 'Setting this to true creates the invoice as paid.',
    variables: true,
    options: [
      { label: 'False', value: 'false' },
      { label: 'True', value: 'true' },
    ],
  },
  {
    label: 'Amount Paid',
    key: 'amountPaid',
    type: 'string',
    required: false,
    description:
      'If this value is greater than zero a payment will be created along with the invoice.',
    variables: true,
  },
  {
    label: 'Number',
    key: 'number',
    type: 'string',
    required: false,
    description:
      'The invoice number - is a unique alpha numeric number per invoice per company',
    variables: true,
  },
  {
    label: 'Discount',
    key: 'discount',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'PO Number',
    key: 'poNumber',
    type: 'string',
    required: false,
    description: 'The purchase order associated with this invoice',
    variables: true,
  },
  {
    label: 'Date',
    key: 'date',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Due Date',
    key: 'dueDate',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Tax Rate 1',
    key: 'taxRate1',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Tax Name 1',
    key: 'taxName1',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Tax Rate 2',
    key: 'taxRate2',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Tax Name 2',
    key: 'taxName2',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Tax Rate 3',
    key: 'taxRate3',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Tax Name 3',
    key: 'taxName3',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Custom Field 1',
    key: 'customField1',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Custom Field 2',
    key: 'customField2',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Custom Field 3',
    key: 'customField3',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Custom Field 4',
    key: 'customField4',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Custom Surcharge 1',
    key: 'customSurcharge1',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Custom Surcharge 2',
    key: 'customSurcharge2',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Custom Surcharge 3',
    key: 'customSurcharge3',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Custom Surcharge 4',
    key: 'customSurcharge4',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Is Amount Discount',
    key: 'isAmountDiscount',
    type: 'dropdown',
    required: false,
    description:
      'By default the discount is applied as a percentage, enabling this applies the discount as a fixed amount.',
    variables: true,
    options: [
      { label: 'False', value: 'false' },
      { label: 'True', value: 'true' },
    ],
  },
  {
    label: 'Partial/Deposit',
    key: 'partialDeposit',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Partial Due Date',
    key: 'partialDueDate',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Line Item Cost',
    key: 'lineItemCost',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Line Item Quatity',
    key: 'lineItemQuantity',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Line Item Product',
    key: 'lineItemProduct',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Line Item Discount',
    key: 'lineItemDiscount',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Line Item Description',
    key: 'lineItemDescription',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Line Item Tax Rate 1',
    key: 'lineItemTaxRate1',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Line Item Tax Name 1',
    key: 'lineItemTaxName1',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Line Item Tax Rate 2',
    key: 'lineItemTaxRate2',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Line Item Tax Name 2',
    key: 'lineItemTaxName2',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Line Item Tax Rate 3',
    key: 'lineItemTaxRate3',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Line Item Tax Name 3',
    key: 'lineItemTaxName3',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Line Item Custom Field 1',
    key: 'lineItemCustomField1',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Line Item Custom Field 2',
    key: 'lineItemCustomField2',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Line Item Custom Field 3',
    key: 'lineItemCustomField3',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Line Item Custom Field 4',
    key: 'lineItemCustomField4',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Line Item Product Cost',
    key: 'lineItemProductCost',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Public Notes',
    key: 'publicNotes',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Private Notes',
    key: 'privateNotes',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Terms',
    key: 'terms',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
  {
    label: 'Footer',
    key: 'footer',
    type: 'string',
    required: false,
    description: '',
    variables: true,
  },
];
