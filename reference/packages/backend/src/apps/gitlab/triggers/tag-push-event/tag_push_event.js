// https://docs.gitlab.com/ee/user/project/integrations/webhook_events.html#tag-events

export default {
  object_kind: 'tag_push',
  event_name: 'tag_push',
  before: '0000000000000000000000000000000000000000',
  after: '82b3d5ae55f7080f1e6022629cdb57bfae7cccc7',
  ref: 'refs/tags/v1.0.0',
  checkout_sha: '82b3d5ae55f7080f1e6022629cdb57bfae7cccc7',
  user_id: 1,
  user_name: '<PERSON>',
  user_avatar:
    'https://s.gravatar.com/avatar/d4c74594d841139328695756648b6bd6?s=8://s.gravatar.com/avatar/d4c74594d841139328695756648b6bd6?s=80',
  project_id: 1,
  project: {
    id: 1,
    name: 'Example',
    description: '',
    web_url: 'http://example.com/jsmith/example',
    avatar_url: null,
    git_ssh_url: '***************:jsmith/example.git',
    git_http_url: 'http://example.com/jsmith/example.git',
    namespace: 'Jsmith',
    visibility_level: 0,
    path_with_namespace: 'jsmith/example',
    default_branch: 'master',
    homepage: 'http://example.com/jsmith/example',
    url: '***************:jsmith/example.git',
    ssh_url: '***************:jsmith/example.git',
    http_url: 'http://example.com/jsmith/example.git',
  },
  repository: {
    name: 'Example',
    url: 'ssh://***************/jsmith/example.git',
    description: '',
    homepage: 'http://example.com/jsmith/example',
    git_http_url: 'http://example.com/jsmith/example.git',
    git_ssh_url: '***************:jsmith/example.git',
    visibility_level: 0,
  },
  commits: [],
  total_commits_count: 0,
};
