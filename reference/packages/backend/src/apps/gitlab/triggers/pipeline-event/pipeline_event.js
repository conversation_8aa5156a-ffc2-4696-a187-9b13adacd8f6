// https://docs.gitlab.com/ee/user/project/integrations/webhook_events.html#pipeline-events

export default {
  object_kind: 'pipeline',
  object_attributes: {
    id: 31,
    iid: 3,
    ref: 'master',
    tag: false,
    sha: 'bcbb5ec396a2c0f828686f14fac9b80b780504f2',
    before_sha: 'bcbb5ec396a2c0f828686f14fac9b80b780504f2',
    source: 'merge_request_event',
    status: 'success',
    stages: ['build', 'test', 'deploy'],
    created_at: '2016-08-12 15:23:28 UTC',
    finished_at: '2016-08-12 15:26:29 UTC',
    duration: 63,
    variables: [
      {
        key: 'NESTOR_PROD_ENVIRONMENT',
        value: 'us-west-1',
      },
    ],
  },
  merge_request: {
    id: 1,
    iid: 1,
    title: 'Test',
    source_branch: 'test',
    source_project_id: 1,
    target_branch: 'master',
    target_project_id: 1,
    state: 'opened',
    merge_status: 'can_be_merged',
    detailed_merge_status: 'mergeable',
    url: 'http://************:3005/gitlab-org/gitlab-test/merge_requests/1',
  },
  user: {
    id: 1,
    name: 'Administrator',
    username: 'root',
    avatar_url:
      'http://www.gravatar.com/avatar/e32bd13e2add097461cb96824b7a829c?s=80\u0026d=identicon',
    email: '<EMAIL>',
  },
  project: {
    id: 1,
    name: 'Gitlab Test',
    description: 'Atque in sunt eos similique dolores voluptatem.',
    web_url: 'http://************:3005/gitlab-org/gitlab-test',
    avatar_url: null,
    git_ssh_url: 'git@************:gitlab-org/gitlab-test.git',
    git_http_url: 'http://************:3005/gitlab-org/gitlab-test.git',
    namespace: 'Gitlab Org',
    visibility_level: 20,
    path_with_namespace: 'gitlab-org/gitlab-test',
    default_branch: 'master',
  },
  commit: {
    id: 'bcbb5ec396a2c0f828686f14fac9b80b780504f2',
    message: 'test\n',
    timestamp: '2016-08-12T17:23:21+02:00',
    url: 'http://example.com/gitlab-org/gitlab-test/commit/bcbb5ec396a2c0f828686f14fac9b80b780504f2',
    author: {
      name: 'User',
      email: '<EMAIL>',
    },
  },
  source_pipeline: {
    project: {
      id: 41,
      web_url: 'https://gitlab.example.com/gitlab-org/upstream-project',
      path_with_namespace: 'gitlab-org/upstream-project',
    },
    pipeline_id: 30,
    job_id: 3401,
  },
  builds: [
    {
      id: 380,
      stage: 'deploy',
      name: 'production',
      status: 'skipped',
      created_at: '2016-08-12 15:23:28 UTC',
      started_at: null,
      finished_at: null,
      duration: null,
      queued_duration: null,
      failure_reason: null,
      when: 'manual',
      manual: true,
      allow_failure: false,
      user: {
        id: 1,
        name: 'Administrator',
        username: 'root',
        avatar_url:
          'http://www.gravatar.com/avatar/e32bd13e2add097461cb96824b7a829c?s=80\u0026d=identicon',
        email: '<EMAIL>',
      },
      runner: null,
      artifacts_file: {
        filename: null,
        size: null,
      },
      environment: {
        name: 'production',
        action: 'start',
        deployment_tier: 'production',
      },
    },
    {
      id: 377,
      stage: 'test',
      name: 'test-image',
      status: 'success',
      created_at: '2016-08-12 15:23:28 UTC',
      started_at: '2016-08-12 15:26:12 UTC',
      finished_at: '2016-08-12 15:26:29 UTC',
      duration: 17.0,
      queued_duration: 196.0,
      failure_reason: null,
      when: 'on_success',
      manual: false,
      allow_failure: false,
      user: {
        id: 1,
        name: 'Administrator',
        username: 'root',
        avatar_url:
          'http://www.gravatar.com/avatar/e32bd13e2add097461cb96824b7a829c?s=80\u0026d=identicon',
        email: '<EMAIL>',
      },
      runner: {
        id: 380987,
        description: 'shared-runners-manager-6.gitlab.com',
        active: true,
        runner_type: 'instance_type',
        is_shared: true,
        tags: ['linux', 'docker', 'shared-runner'],
      },
      artifacts_file: {
        filename: null,
        size: null,
      },
      environment: null,
    },
    {
      id: 378,
      stage: 'test',
      name: 'test-build',
      status: 'failed',
      created_at: '2016-08-12 15:23:28 UTC',
      started_at: '2016-08-12 15:26:12 UTC',
      finished_at: '2016-08-12 15:26:29 UTC',
      duration: 17.0,
      queued_duration: 196.0,
      failure_reason: 'script_failure',
      when: 'on_success',
      manual: false,
      allow_failure: false,
      user: {
        id: 1,
        name: 'Administrator',
        username: 'root',
        avatar_url:
          'http://www.gravatar.com/avatar/e32bd13e2add097461cb96824b7a829c?s=80\u0026d=identicon',
        email: '<EMAIL>',
      },
      runner: {
        id: 380987,
        description: 'shared-runners-manager-6.gitlab.com',
        active: true,
        runner_type: 'instance_type',
        is_shared: true,
        tags: ['linux', 'docker'],
      },
      artifacts_file: {
        filename: null,
        size: null,
      },
      environment: null,
    },
    {
      id: 376,
      stage: 'build',
      name: 'build-image',
      status: 'success',
      created_at: '2016-08-12 15:23:28 UTC',
      started_at: '2016-08-12 15:24:56 UTC',
      finished_at: '2016-08-12 15:25:26 UTC',
      duration: 17.0,
      queued_duration: 196.0,
      failure_reason: null,
      when: 'on_success',
      manual: false,
      allow_failure: false,
      user: {
        id: 1,
        name: 'Administrator',
        username: 'root',
        avatar_url:
          'http://www.gravatar.com/avatar/e32bd13e2add097461cb96824b7a829c?s=80\u0026d=identicon',
        email: '<EMAIL>',
      },
      runner: {
        id: 380987,
        description: 'shared-runners-manager-6.gitlab.com',
        active: true,
        runner_type: 'instance_type',
        is_shared: true,
        tags: ['linux', 'docker'],
      },
      artifacts_file: {
        filename: null,
        size: null,
      },
      environment: null,
    },
    {
      id: 379,
      stage: 'deploy',
      name: 'staging',
      status: 'created',
      created_at: '2016-08-12 15:23:28 UTC',
      started_at: null,
      finished_at: null,
      duration: null,
      queued_duration: null,
      failure_reason: null,
      when: 'on_success',
      manual: false,
      allow_failure: false,
      user: {
        id: 1,
        name: 'Administrator',
        username: 'root',
        avatar_url:
          'http://www.gravatar.com/avatar/e32bd13e2add097461cb96824b7a829c?s=80\u0026d=identicon',
        email: '<EMAIL>',
      },
      runner: null,
      artifacts_file: {
        filename: null,
        size: null,
      },
      environment: {
        name: 'staging',
        action: 'start',
        deployment_tier: 'staging',
      },
    },
  ],
};
