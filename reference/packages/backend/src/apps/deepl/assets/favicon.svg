<svg width="128" height="128" xmlns="http://www.w3.org/2000/svg">
 <metadata id="metadata4177">image/svg+xml</metadata>
 <defs>
  <clipPath id="clipPath4187" clipPathUnits="userSpaceOnUse">
   <path id="path4189" d="m70.850157,393.069981l708.661,0l0,-425.197l-708.661,0l0,425.197z"/>
  </clipPath>
 </defs>
 <g>
  <title>background</title>
  <rect fill="#ffffff" id="canvas_background" height="130" width="130" y="-1" x="-1"/>
 </g>
 <g>
  <title>Layer 1</title>
  <g fill="#FFF" transform="matrix(1.36265596065192,0,0,-1.3395561298863794,-403.63320244005325,311.0674198374575) " id="g4179">
   <g fill="#FFF" id="g4183">
    <g fill="#FFF" clip-path="url(#clipPath4187)" id="g4185">
     <g fill="#FFF" id="g4191">
      <path fill="#000000" fill-rule="nonzero" id="path4193" d="m411.83557,185.452083c0,-2.425 -0.463,-4.637 -1.3,-6.226c-1.009,-1.905 -2.173,-3.234 -3.366,-3.843c-1.641,-0.835 -3.707,-1.69 -6.586,-1.69l-4.044,0l0,22.753l3.221,0c4.28,0 7.213,-0.841 8.971,-2.572c2.09,-2.059 3.104,-4.814 3.104,-8.422m-1.89,15.206c-2.434,1.24 -6.053,1.24 -10.633,1.24l-7.069,0c-1.158,0 -2.097,-0.939 -2.097,-2.097l0,-29.463c0,-1.157 0.939,-2.096 2.097,-2.096l6.662,0c3.78,0 7.209,0.393 9.411,1.079c1.585,0.491 3.181,1.529 5.022,3.27c1.559,1.472 2.785,3.317 3.642,5.484c0.886,2.243 1.318,4.609 1.318,7.232c0,3.66 -0.771,6.809 -2.359,9.622c-1.441,2.558 -3.344,4.377 -5.994,5.729"/>
     </g>
     <g fill="#FFF" id="g4195">
      <path fill="#000000" fill-rule="nonzero" id="path4197" d="m533.882353,173.693278l-10.436,0l0,27.36c0,0.467 -0.38,0.845 -0.845,0.845l-4.702,0c-0.466,0 -0.845,-0.378 -0.845,-0.845l0,-31.966c0,-0.467 0.379,-0.846 0.845,-0.846l15.983,0c0.465,0 0.848,0.379 0.848,0.846l0,3.759c0,0.469 -0.383,0.847 -0.848,0.847"/>
     </g>
     <g fill="#FFF" id="g4199">
      <path fill="#000000" fill-rule="nonzero" id="path4201" d="m506.182158,181.465785c0,-2.225 -0.685,-4.08 -2.034,-5.516c-1.35,-1.437 -3.039,-2.167 -5.017,-2.167c-2.038,0 -3.751,0.718 -5.096,2.13c-1.34,1.415 -2.022,3.291 -2.022,5.576c0,2.242 0.682,4.081 2.024,5.471c1.343,1.391 3.056,2.096 5.094,2.096c2.026,0 3.724,-0.716 5.05,-2.132c1.326,-1.412 2.001,-3.249 2.001,-5.458m-5.813,13.324c-1.536,0 -3.091,-0.305 -4.385,-0.862c-1.508,-0.646 -2.894,-1.553 -3.903,-2.554l-0.601,-0.48l-2.708,2.543l-2.843,0c-0.465,0 -0.842,-0.377 -0.842,-0.842l0,-32.911c0,-0.467 0.378,-0.845 0.845,-0.845l4.701,0c0.467,0 0.847,0.379 0.847,0.847l0,12.166l0.598,-0.455c1.287,-1.204 2.95,-2.034 4.125,-2.52c1.249,-0.516 2.621,-0.78 4.076,-0.78c3.306,0 6.085,1.248 8.499,3.812c2.408,2.557 3.581,5.661 3.581,9.488c0,3.886 -1.176,7.13 -3.505,9.64c-2.302,2.491 -5.157,3.753 -8.485,3.753"/>
     </g>
     <g fill="#FFF" id="g4203">
      <path fill="#000000" fill-rule="nonzero" id="path4205" d="m460.078642,184.036586l0.149,0.387c0.847,1.866 1.627,3.045 2.381,3.604c1.364,1.009 2.965,1.522 4.765,1.522c1.543,0 3.024,-0.499 4.396,-1.485c1.209,-0.865 2.09,-2.13 2.533,-3.616l0.101,-0.412l-14.325,0zm6.978,10.753c-3.801,0 -6.871,-1.255 -9.39,-3.836c-2.52,-2.584 -3.745,-5.741 -3.745,-9.651c0,-3.815 1.228,-6.902 3.757,-9.439c2.526,-2.535 5.709,-3.766 9.73,-3.766c2.584,0 4.824,0.454 6.656,1.348c1.543,0.752 2.915,1.843 4.079,3.243c0.167,0.203 0.233,0.472 0.172,0.728c-0.056,0.257 -0.23,0.472 -0.469,0.583l-3.782,1.739c-0.3,0.138 -0.656,0.089 -0.907,-0.128c-1.691,-1.459 -3.641,-2.2 -5.797,-2.2c-2.156,0 -3.793,0.489 -5.008,1.493c-1.29,1.072 -2.003,2.191 -2.332,3.679l19.696,0c0.466,0 0.842,0.375 0.844,0.841l0.007,0.835c0,4.359 -1.369,8.256 -3.757,10.695c-2.529,2.581 -5.718,3.836 -9.754,3.836"/>
     </g>
     <g fill="#FFF" id="g4207">
      <path fill="#000000" fill-rule="nonzero" id="path4209" d="m428.942961,184.036586l0.148,0.386c0.854,1.871 1.635,3.05 2.387,3.605c1.36,1.009 2.963,1.522 4.763,1.522c1.542,0 3.022,-0.5 4.396,-1.485c1.208,-0.865 2.088,-2.13 2.533,-3.615l0.099,-0.413l-14.326,0zm6.979,10.753c-3.798,0 -6.871,-1.255 -9.39,-3.836c-2.521,-2.584 -3.745,-5.741 -3.745,-9.651c0,-3.816 1.227,-6.904 3.757,-9.439c2.526,-2.535 5.709,-3.766 9.73,-3.766c2.584,0 4.824,0.454 6.656,1.348c1.544,0.752 2.915,1.843 4.079,3.244c0.168,0.202 0.231,0.471 0.172,0.728c-0.056,0.256 -0.231,0.471 -0.469,0.582l-3.782,1.739c-0.298,0.138 -0.655,0.089 -0.907,-0.128c-1.688,-1.459 -3.639,-2.2 -5.797,-2.2c-2.156,0 -3.793,0.489 -5.005,1.494c-1.29,1.071 -2.006,2.189 -2.335,3.678l19.696,0c0.465,0 0.844,0.375 0.847,0.841l0.004,0.835c0,4.358 -1.369,8.254 -3.757,10.695c-2.526,2.581 -5.715,3.836 -9.754,3.836"/>
     </g>
     <g fill="#FFF" id="g4211">
      <path fill="#000000" fill-rule="nonzero" id="path4213" d="m355.595244,180.065883c-2.797,0 -5.063,2.266 -5.063,5.063c0,0.295 0.025,0.584 0.075,0.865l-12.108,6.972c-0.881,-0.744 -2.02,-1.193 -3.263,-1.193c-2.797,0 -5.064,2.267 -5.064,5.063c0,2.796 2.267,5.063 5.064,5.063c2.797,0 5.064,-2.267 5.064,-5.063c0,-0.322 -0.033,-0.637 -0.091,-0.943l12.064,-6.946c0.889,0.775 2.051,1.245 3.322,1.245c2.795,0 5.062,-2.267 5.062,-5.063c0,-2.797 -2.267,-5.063 -5.062,-5.063m-15.357,-6.724c0,-2.796 -2.266,-5.063 -5.063,-5.063c-2.795,0 -5.062,2.267 -5.062,5.063c0,2.797 2.267,5.064 5.062,5.064c1.257,0 2.405,-0.459 3.29,-1.217l8.827,5.073c0.377,-1.089 0.963,-2.081 1.708,-2.929l-8.843,-5.083c0.052,-0.295 0.081,-0.598 0.081,-0.908m30.444,30.899l-24.042,13.738c-1.637,0.935 -3.647,0.935 -5.284,0l-24.04,-13.738c-1.66,-0.948 -2.684,-2.714 -2.684,-4.624l0,-27.946c0,-1.902 1.015,-3.661 2.663,-4.612l41.387,-23.889l0.007,16.968l12.01,6.921c1.65,0.951 2.666,2.71 2.666,4.614l0,27.944c0,1.91 -1.024,3.676 -2.683,4.624"/>
     </g>
    </g>
   </g>
  </g>
 </g>
</svg>
