#!/usr/bin/env node

import postgres from 'postgres';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const DATABASE_URL = process.env.DATABASE_URL;

if (!DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is not set');
  process.exit(1);
}

console.log('🔧 FlowCraft Database Issue Resolution');
console.log('=' .repeat(50));

async function fixDatabaseIssues() {
  let client;
  try {
    client = postgres(DATABASE_URL, {
      ssl: 'require',
      max: 1,
      idle_timeout: 20,
      connect_timeout: 10,
    });

    console.log('🔍 Identifying and fixing database issues...\n');

    // Issue 1: Fix automations with missing runType in schedule triggers
    console.log('1️⃣  Fixing schedule automations with missing runType...');
    
    const brokenSchedules = await client`
      SELECT id, name, trigger
      FROM automations 
      WHERE trigger->>'type' = 'schedule' 
      AND trigger->'config'->>'runType' IS NULL
    `;
    
    console.log(`   Found ${brokenSchedules.length} automations with missing runType`);
    
    for (const automation of brokenSchedules) {
      try {
        // Set a default runType of 'at-regular-intervals' with 1 hour interval
        const updatedTrigger = {
          ...automation.trigger,
          config: {
            ...automation.trigger.config,
            runType: 'at-regular-intervals',
            interval: '1hour',
            startTime: '09:00'
          }
        };
        
        await client`
          UPDATE automations 
          SET trigger = ${JSON.stringify(updatedTrigger)}, updated_at = NOW()
          WHERE id = ${automation.id}
        `;
        
        console.log(`   ✅ Fixed automation ${automation.id} (${automation.name})`);
      } catch (error) {
        console.log(`   ❌ Failed to fix automation ${automation.id}: ${error.message}`);
      }
    }

    // Issue 2: Optimize database connection settings
    console.log('\n2️⃣  Checking database connection optimization...');
    
    const currentSettings = await client`
      SELECT name, setting, unit 
      FROM pg_settings 
      WHERE name IN ('max_connections', 'shared_buffers', 'work_mem', 'maintenance_work_mem', 'effective_cache_size')
    `;
    
    console.log('   Current database settings:');
    currentSettings.forEach(setting => {
      console.log(`      ${setting.name}: ${setting.setting}${setting.unit || ''}`);
    });

    // Issue 3: Check for connection pooling optimization
    console.log('\n3️⃣  Analyzing connection patterns...');
    
    const connectionStats = await client`
      SELECT 
        state,
        count(*) as count,
        avg(extract(epoch from (now() - state_change))) as avg_duration_seconds
      FROM pg_stat_activity 
      WHERE datname = current_database()
      GROUP BY state
      ORDER BY count DESC
    `;
    
    console.log('   Connection state analysis:');
    connectionStats.forEach(stat => {
      console.log(`      ${stat.state}: ${stat.count} connections (avg duration: ${Math.round(stat.avg_duration_seconds || 0)}s)`);
    });

    // Issue 4: Check for slow queries
    console.log('\n4️⃣  Checking for performance issues...');
    
    try {
      const slowQueries = await client`
        SELECT 
          query,
          calls,
          total_time,
          mean_time,
          rows
        FROM pg_stat_statements 
        WHERE mean_time > 100
        ORDER BY mean_time DESC 
        LIMIT 5
      `;
      
      if (slowQueries.length > 0) {
        console.log('   ⚠️  Slow queries detected:');
        slowQueries.forEach(query => {
          console.log(`      Query: ${query.query.substring(0, 60)}...`);
          console.log(`      Calls: ${query.calls}, Mean time: ${Math.round(query.mean_time)}ms`);
        });
      } else {
        console.log('   ✅ No slow queries detected (pg_stat_statements may not be enabled)');
      }
    } catch (error) {
      console.log('   ℹ️  pg_stat_statements extension not available (normal for managed databases)');
    }

    // Issue 5: Vacuum and analyze tables for performance
    console.log('\n5️⃣  Optimizing table performance...');
    
    const tables = ['automations', 'credentials', 'profiles', 'team_members', 'activity_logs'];
    
    for (const table of tables) {
      try {
        console.log(`   Analyzing ${table}...`);
        await client`ANALYZE ${client(table)}`;
        console.log(`   ✅ ${table} analyzed`);
      } catch (error) {
        console.log(`   ❌ Failed to analyze ${table}: ${error.message}`);
      }
    }

    // Issue 6: Check for data consistency
    console.log('\n6️⃣  Checking data consistency...');
    
    // Check for orphaned records
    const orphanedCredentials = await client`
      SELECT COUNT(*) as count
      FROM credentials c
      LEFT JOIN profiles p ON c.user_id = p.id
      WHERE p.id IS NULL
    `;
    
    console.log(`   Orphaned credentials: ${orphanedCredentials[0].count}`);
    
    const orphanedAutomations = await client`
      SELECT COUNT(*) as count
      FROM automations a
      LEFT JOIN profiles p ON a.user_id = p.id
      WHERE p.id IS NULL
    `;
    
    console.log(`   Orphaned automations: ${orphanedAutomations[0].count}`);

    // Issue 7: Create indexes for better performance if they don't exist
    console.log('\n7️⃣  Checking and creating performance indexes...');
    
    const indexes = [
      {
        name: 'idx_automations_user_id',
        table: 'automations',
        column: 'user_id',
        sql: 'CREATE INDEX IF NOT EXISTS idx_automations_user_id ON automations(user_id)'
      },
      {
        name: 'idx_credentials_user_id',
        table: 'credentials', 
        column: 'user_id',
        sql: 'CREATE INDEX IF NOT EXISTS idx_credentials_user_id ON credentials(user_id)'
      },
      {
        name: 'idx_automations_status',
        table: 'automations',
        column: 'status',
        sql: 'CREATE INDEX IF NOT EXISTS idx_automations_status ON automations(status)'
      }
    ];
    
    for (const index of indexes) {
      try {
        await client.unsafe(index.sql);
        console.log(`   ✅ Index ${index.name} created/verified`);
      } catch (error) {
        console.log(`   ❌ Failed to create index ${index.name}: ${error.message}`);
      }
    }

    console.log('\n🎉 Database issue resolution completed!');
    
    // Final verification
    console.log('\n🔍 Final verification...');
    
    const verificationQuery = await client`
      SELECT 
        COUNT(*) as total_automations,
        COUNT(*) FILTER (WHERE trigger->>'type' = 'schedule' AND trigger->'config'->>'runType' IS NOT NULL) as fixed_schedules,
        COUNT(*) FILTER (WHERE trigger->>'type' = 'schedule' AND trigger->'config'->>'runType' IS NULL) as broken_schedules
      FROM automations
    `;
    
    const result = verificationQuery[0];
    console.log(`   Total automations: ${result.total_automations}`);
    console.log(`   Fixed schedule automations: ${result.fixed_schedules}`);
    console.log(`   Remaining broken schedules: ${result.broken_schedules}`);
    
    if (result.broken_schedules === '0') {
      console.log('   ✅ All schedule automation issues resolved!');
    } else {
      console.log('   ⚠️  Some schedule issues remain');
    }

  } catch (error) {
    console.error('❌ Database fix failed:', error.message);
    return false;
  } finally {
    if (client) {
      await client.end();
    }
  }
  
  return true;
}

// Run the fix
fixDatabaseIssues()
  .then(success => {
    console.log(success ? '\n✅ Database issues resolved successfully!' : '\n❌ Some issues could not be resolved');
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
