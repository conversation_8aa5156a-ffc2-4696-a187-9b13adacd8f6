#!/usr/bin/env node

import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('🔍 FlowCraft OAuth Configuration Test');
console.log('=' .repeat(50));

function testOAuthConfig() {
  console.log('📋 Checking OAuth Environment Variables...\n');

  const requiredVars = [
    'GOOGLE_CLIENT_ID',
    'GOOGLE_CLIENT_SECRET',
    'SESSION_SECRET',
    'ENCRYPTION_KEY'
  ];

  const optionalVars = [
    'GMAIL_CLIENT_ID',
    'GMAIL_CLIENT_SECRET',
    'SLACK_CLIENT_ID',
    'SLACK_CLIENT_SECRET'
  ];

  let allGood = true;

  // Check required variables
  console.log('🔴 REQUIRED Variables:');
  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (!value || value.includes('your_') || value.includes('here')) {
      console.log(`   ❌ ${varName}: NOT SET or using placeholder`);
      allGood = false;
    } else {
      const maskedValue = value.length > 10 ? 
        value.substring(0, 10) + '...' + value.substring(value.length - 4) :
        '*'.repeat(value.length);
      console.log(`   ✅ ${varName}: ${maskedValue}`);
    }
  });

  console.log('\n🟡 OPTIONAL Variables:');
  optionalVars.forEach(varName => {
    const value = process.env[varName];
    if (!value || value.includes('your_') || value.includes('here')) {
      console.log(`   ⚠️  ${varName}: Not set (optional)`);
    } else {
      const maskedValue = value.length > 10 ? 
        value.substring(0, 10) + '...' + value.substring(value.length - 4) :
        '*'.repeat(value.length);
      console.log(`   ✅ ${varName}: ${maskedValue}`);
    }
  });

  // Check Google Client ID format
  console.log('\n🔍 Google OAuth Configuration Validation:');
  const googleClientId = process.env.GOOGLE_CLIENT_ID;
  if (googleClientId && !googleClientId.includes('your_')) {
    if (googleClientId.endsWith('.apps.googleusercontent.com')) {
      console.log('   ✅ GOOGLE_CLIENT_ID format looks correct');
    } else {
      console.log('   ⚠️  GOOGLE_CLIENT_ID format may be incorrect (should end with .apps.googleusercontent.com)');
    }
  } else {
    console.log('   ❌ GOOGLE_CLIENT_ID not properly configured');
    allGood = false;
  }

  const googleClientSecret = process.env.GOOGLE_CLIENT_SECRET;
  if (googleClientSecret && !googleClientSecret.includes('your_')) {
    if (googleClientSecret.startsWith('GOCSPX-')) {
      console.log('   ✅ GOOGLE_CLIENT_SECRET format looks correct');
    } else {
      console.log('   ⚠️  GOOGLE_CLIENT_SECRET format may be incorrect (should start with GOCSPX-)');
    }
  } else {
    console.log('   ❌ GOOGLE_CLIENT_SECRET not properly configured');
    allGood = false;
  }

  // Check encryption key
  const encryptionKey = process.env.ENCRYPTION_KEY;
  if (encryptionKey && !encryptionKey.includes('here')) {
    if (encryptionKey.length >= 32) {
      console.log('   ✅ ENCRYPTION_KEY length is adequate');
    } else {
      console.log('   ⚠️  ENCRYPTION_KEY should be at least 32 characters long');
    }
  } else {
    console.log('   ❌ ENCRYPTION_KEY not properly configured');
    allGood = false;
  }

  // Expected redirect URI
  console.log('\n🔗 OAuth Redirect URI Configuration:');
  console.log('   📍 Your app expects this redirect URI:');
  console.log('      http://localhost:5000/api/auth/google/callback');
  console.log('   📍 Make sure this EXACT URL is added to your Google Cloud Console');
  console.log('      under "Authorized redirect URIs"');

  // Summary
  console.log('\n📊 CONFIGURATION SUMMARY:');
  if (allGood) {
    console.log('   🎉 All required OAuth variables are configured!');
    console.log('   ✅ Your OAuth setup should work');
    console.log('');
    console.log('   🔄 Next steps:');
    console.log('   1. Restart your server: npm run dev');
    console.log('   2. Test OAuth flow in your application');
    console.log('   3. Check Google Cloud Console redirect URI settings');
  } else {
    console.log('   ❌ OAuth configuration is incomplete');
    console.log('   🔧 Please fix the issues above before testing OAuth');
    console.log('');
    console.log('   📖 See GOOGLE_OAUTH_SETUP.md for detailed instructions');
  }

  return allGood;
}

// Test OAuth endpoint availability
async function testOAuthEndpoint() {
  console.log('\n🌐 Testing OAuth Endpoint Availability...');
  
  try {
    const response = await fetch('http://localhost:5000/api/auth/google/start?automationId=test');
    
    if (response.ok) {
      const data = await response.json();
      if (data.authUrl) {
        console.log('   ✅ OAuth endpoint is working');
        console.log('   🔗 Auth URL generated successfully');
        return true;
      } else {
        console.log('   ❌ OAuth endpoint returned unexpected response');
        console.log('   📄 Response:', data);
        return false;
      }
    } else {
      console.log(`   ❌ OAuth endpoint returned ${response.status}: ${response.statusText}`);
      const errorText = await response.text();
      console.log('   📄 Error:', errorText);
      return false;
    }
  } catch (error) {
    console.log('   ❌ Cannot reach OAuth endpoint');
    console.log('   🔧 Make sure your server is running on http://localhost:5000');
    console.log(`   📄 Error: ${error.message}`);
    return false;
  }
}

// Run tests
async function runTests() {
  const configOk = testOAuthConfig();
  
  if (configOk) {
    await testOAuthEndpoint();
  }
  
  console.log('\n' + '='.repeat(50));
  console.log('🔍 OAuth configuration test completed!');
}

runTests().catch(error => {
  console.error('💥 Test failed:', error);
  process.exit(1);
});
