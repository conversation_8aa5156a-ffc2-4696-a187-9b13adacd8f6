#!/usr/bin/env node

import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('🔍 Google Sheets UI Fix Verification');
console.log('=' .repeat(50));

async function verifyFix() {
  console.log('📋 Checking Google OAuth Configuration...\n');

  // 1. Check environment variables
  const requiredVars = ['GOOGLE_CLIENT_ID', 'GOOGLE_CLIENT_SECRET', 'SESSION_SECRET', 'ENCRYPTION_KEY'];
  let configOk = true;

  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (!value || value.includes('your_') || value.includes('here')) {
      console.log(`   ❌ ${varName}: NOT SET or using placeholder`);
      configOk = false;
    } else {
      console.log(`   ✅ ${varName}: Configured`);
    }
  });

  if (!configOk) {
    console.log('\n❌ OAuth configuration incomplete. Please fix environment variables first.');
    return false;
  }

  // 2. Test server endpoints
  console.log('\n🌐 Testing Server Endpoints...');
  
  try {
    // Test health endpoint
    const healthResponse = await fetch('http://localhost:5000/health');
    if (healthResponse.ok) {
      console.log('   ✅ Health endpoint: Working');
    } else {
      console.log('   ❌ Health endpoint: Failed');
      return false;
    }

    // Test OAuth start endpoint
    const oauthResponse = await fetch('http://localhost:5000/api/auth/google/start?automationId=test');
    if (oauthResponse.ok) {
      const data = await oauthResponse.json();
      if (data.authUrl) {
        console.log('   ✅ OAuth start endpoint: Working');
        console.log('   🔗 Auth URL generated successfully');
      } else {
        console.log('   ❌ OAuth start endpoint: No auth URL returned');
        return false;
      }
    } else {
      console.log('   ❌ OAuth start endpoint: Failed');
      return false;
    }

    // Test main app endpoint
    const appResponse = await fetch('http://localhost:5000/');
    if (appResponse.ok) {
      console.log('   ✅ Main app endpoint: Working');
    } else {
      console.log('   ❌ Main app endpoint: Failed');
      return false;
    }

  } catch (error) {
    console.log(`   ❌ Server connection failed: ${error.message}`);
    return false;
  }

  // 3. Check file modifications
  console.log('\n📁 Verifying File Modifications...');
  
  try {
    const fs = await import('fs');
    
    // Check ExistingGoogleAccounts.tsx
    const googleAccountsFile = fs.readFileSync('client/src/components/ExistingGoogleAccounts.tsx', 'utf8');
    
    if (googleAccountsFile.includes('googleCredentials.length > 0 && !!effectiveSelectedAccount')) {
      console.log('   ✅ ExistingGoogleAccounts.tsx: Conditional logic fixed');
    } else {
      console.log('   ❌ ExistingGoogleAccounts.tsx: Conditional logic not updated');
      return false;
    }

    if (googleAccountsFile.includes('Connect Google Account') && googleAccountsFile.includes('service-specific messaging')) {
      console.log('   ✅ ExistingGoogleAccounts.tsx: Enhanced UI implemented');
    } else {
      console.log('   ⚠️  ExistingGoogleAccounts.tsx: Enhanced UI may not be fully implemented');
    }

    // Check automation form
    const automationFormFile = fs.readFileSync('client/src/pages/automation-form-multi-page.tsx', 'utf8');
    
    if (!automationFormFile.includes('console.log(\'Google Sheets - selectedGoogleAccount state changed:\'')) {
      console.log('   ✅ automation-form-multi-page.tsx: Debug logging cleaned up');
    } else {
      console.log('   ⚠️  automation-form-multi-page.tsx: Some debug logging may remain');
    }

  } catch (error) {
    console.log(`   ❌ File verification failed: ${error.message}`);
    return false;
  }

  // 4. Summary and next steps
  console.log('\n📊 VERIFICATION SUMMARY');
  console.log('-'.repeat(40));
  console.log('✅ Google OAuth configuration: Complete');
  console.log('✅ Server endpoints: Working');
  console.log('✅ UI fix implementation: Applied');
  console.log('✅ Code cleanup: Complete');

  console.log('\n🧪 MANUAL TESTING REQUIRED');
  console.log('-'.repeat(40));
  console.log('1. Open http://localhost:5000 in your browser');
  console.log('2. Create a new automation');
  console.log('3. Select "Google Sheets" trigger');
  console.log('4. Verify:');
  console.log('   - No spreadsheet dropdowns visible initially');
  console.log('   - "Connect Google Account" prompt is shown');
  console.log('   - After OAuth, spreadsheet options appear');
  console.log('   - Selections persist and remain functional');

  console.log('\n🎯 EXPECTED BEHAVIOR');
  console.log('-'.repeat(40));
  console.log('BEFORE AUTH: Show "Connect Google Account" prompt');
  console.log('AFTER AUTH:  Show spreadsheet and sheet selection');
  console.log('PERSISTENCE: Selections remain stable');

  console.log('\n🚀 Google Sheets UI fix verification completed!');
  console.log('The technical implementation is ready for testing.');

  return true;
}

// Run verification
verifyFix()
  .then(success => {
    if (success) {
      console.log('\n✅ All checks passed! Ready for manual testing.');
    } else {
      console.log('\n❌ Some issues found. Please review and fix.');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('💥 Verification failed:', error);
    process.exit(1);
  });
