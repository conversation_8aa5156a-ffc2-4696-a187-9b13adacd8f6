import { useState } from 'react'
import { motion } from 'framer-motion'
import { useLocation } from 'wouter'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  MapPin, Key, Search, Database, Download, Check, ArrowRight, ChevronLeft,
  X, Settings, Mail, Calendar, MessageSquare, Hash, Globe, Eye, EyeOff, Plus
} from 'lucide-react'
import { <PERSON><PERSON><PERSON>glemaps, SiGooglesheets } from 'react-icons/si'

type StepType = 'intro' | 'naming' | 'serpapi-config' | 'search-config' | 'data-export' | 'actions-question' | 'actions' | 'review' | 'complete'

interface ScraperConfig {
  name: string
  serpApi: {
    apiKey: string
    isConfigured: boolean
  }
  googleAuth: {
    isAuthenticated: boolean
    userEmail: string
    userName: string
    accountId: string
  }
  searchConfig: {
    query: string
    location: string
    radius: number
    resultsLimit: number
    categories: string[]
  }
  dataFields: {
    businessName: boolean
    address: boolean
    phone: boolean
    website: boolean
    rating: boolean
    reviews: boolean
    hours: boolean
    categories: boolean
  }
  exportConfig: {
    format: 'csv' | 'json' | 'googlesheets'
    destination: string
    filename: string
    filesList: string[]
  }
  selectedActionsList: Array<{
    id: string
    type: string
    name: string
    description: string
    config: Record<string, any>
  }>
  wantsActions: boolean | null
}

export default function GoogleMapsScraperTemplate() {
  const [, setLocation] = useLocation()
  const [currentStep, setCurrentStep] = useState<StepType>('intro')
  const [showApiKey, setShowApiKey] = useState(false)
  const [showActionConfig, setShowActionConfig] = useState<number | null>(null)
  const [actionSearchQuery, setActionSearchQuery] = useState('')
  const [config, setConfig] = useState<ScraperConfig>({
    name: '',
    serpApi: {
      apiKey: '',
      isConfigured: false
    },
    googleAuth: {
      isAuthenticated: false,
      userEmail: '',
      userName: '',
      accountId: ''
    },
    searchConfig: {
      query: '',
      location: '',
      radius: 5,
      resultsLimit: 20,
      categories: []
    },
    dataFields: {
      businessName: true,
      address: true,
      phone: true,
      website: true,
      rating: true,
      reviews: true,
      hours: true,
      categories: true
    },
    exportConfig: {
      format: 'csv',
      destination: '',
      filename: 'google-maps-data',
      filesList: []
    },
    selectedActionsList: [],
    wantsActions: null
  })

  const stepConfig = {
    intro: { 
      title: 'Google Maps Data Scraper', 
      subtitle: 'Extract business data for lead generation and market research',
      icon: MapPin,
      progress: 0
    },
    naming: {
      title: 'Name Your Automation',
      subtitle: 'Give your automation a memorable name',
      icon: Settings,
      progress: 12
    },
    'serpapi-config': { 
      title: 'SerpAPI Configuration', 
      subtitle: 'Configure your SerpAPI for Google Maps access',
      icon: Key,
      progress: 25
    },
    'search-config': { 
      title: 'Search Configuration', 
      subtitle: 'Define what businesses to search for',
      icon: Search,
      progress: 38
    },
    'data-export': { 
      title: 'Data Fields & Export', 
      subtitle: 'Choose data to extract and export format',
      icon: Database,
      progress: 50
    },
    'actions-question': { 
      title: 'Additional Actions', 
      subtitle: 'Add more capabilities to your automation',
      icon: Plus,
      progress: 62
    },
    'actions': { 
      title: 'Configure Actions', 
      subtitle: 'Set up your selected actions',
      icon: Settings,
      progress: 75
    },
    review: { 
      title: 'Review Configuration', 
      subtitle: 'Confirm your automation settings',
      icon: Eye,
      progress: 88
    },
    complete: { 
      title: 'Complete', 
      subtitle: 'Your automation is ready',
      icon: Check,
      progress: 100
    }
  }

  const businessCategories = [
    'Restaurants', 'Hotels', 'Shopping', 'Gas Stations', 'Hospitals',
    'Banks', 'Schools', 'Gyms', 'Pharmacies', 'Real Estate',
    'Law Firms', 'Dentists', 'Auto Repair', 'Beauty Salons', 'Cafes'
  ]

  const nextStep = () => {
    const steps: StepType[] = ['intro', 'naming', 'serpapi-config', 'search-config', 'data-export', 'actions-question', 'actions', 'review', 'complete']
    const currentIndex = steps.indexOf(currentStep)
    
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1])
    }
  }

  const prevStep = () => {
    const steps: StepType[] = ['intro', 'naming', 'serpapi-config', 'search-config', 'data-export', 'actions-question', 'actions', 'review', 'complete']
    const currentIndex = steps.indexOf(currentStep)
    
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1])
    }
  }

  const renderIntro = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-green-600 rounded-full flex items-center justify-center mx-auto">
        <MapPin className="h-10 w-10 text-white" />
      </div>
      <div>
        <h2 className="text-2xl font-bold mb-2">Google Maps Data Scraper</h2>
        <p className="text-muted-foreground max-w-md mx-auto">
          This template creates an automated workflow that extracts business data from Google Maps
          using SerpAPI, filters results based on your criteria, and exports to your preferred format
          for lead generation or market research.
        </p>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <SiGooglemaps className="h-8 w-8 mx-auto mb-2 text-blue-600" />
          <p className="text-xs font-medium">Google Maps</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Search className="h-8 w-8 mx-auto mb-2 text-purple-600" />
          <p className="text-xs font-medium">Smart Search</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Database className="h-8 w-8 mx-auto mb-2 text-green-600" />
          <p className="text-xs font-medium">Data Extract</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Download className="h-8 w-8 mx-auto mb-2 text-orange-600" />
          <p className="text-xs font-medium">Auto Export</p>
        </div>
      </div>

      <Button 
        onClick={nextStep} 
        size="lg"
        className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700"
      >
        Start Setup
        <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
    </motion.div>
  )

  const renderNaming = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Settings className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h2 className="text-xl font-semibold mb-2">Name Your Automation</h2>
        <p className="text-muted-foreground">
          Give your automation a memorable name that describes what it does
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="automation-name">Automation Name *</Label>
          <Input
            id="automation-name"
            placeholder="e.g., Restaurant Leads NYC, Dentist Contact List"
            value={config.name}
            onChange={(e) => setConfig({ ...config, name: e.target.value })}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && config.name) {
                nextStep()
              }
            }}
            className="mt-2"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Choose a descriptive name you'll easily recognize
          </p>
        </div>
      </div>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          disabled={!config.name}
          className="w-full"
        >
          Continue to SerpAPI Setup
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderSerpApiConfig = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Key className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h3 className="text-lg font-semibold mb-2">Configure SerpAPI</h3>
        <p className="text-muted-foreground text-sm">
          Connect your SerpAPI account to access Google Maps data
        </p>
      </div>

      <Card>
        <CardContent className="p-6 space-y-4">
          <div>
            <Label htmlFor="api-key">SerpAPI Key</Label>
            <div className="relative mt-2">
              <Input
                id="api-key"
                type={showApiKey ? "text" : "password"}
                placeholder="Enter your SerpAPI key"
                value={config.serpApi.apiKey}
                onChange={(e) => setConfig({
                  ...config,
                  serpApi: { ...config.serpApi, apiKey: e.target.value }
                })}
              />
              <Button
                size="sm"
                variant="ghost"
                className="absolute right-2 top-1/2 -translate-y-1/2"
                onClick={() => setShowApiKey(!showApiKey)}
              >
                {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Get your API key from <a href="https://serpapi.com" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">serpapi.com</a>
            </p>
          </div>

          {config.serpApi.apiKey && (
            <Button
              className="w-full"
              onClick={() => setConfig({
                ...config,
                serpApi: { ...config.serpApi, isConfigured: true }
              })}
            >
              Verify API Key
            </Button>
          )}

          {config.serpApi.isConfigured && (
            <div className="flex items-center gap-2 text-green-600">
              <Check className="h-4 w-4" />
              <span className="text-sm">API key verified successfully</span>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          disabled={!config.serpApi.isConfigured}
          className="w-full"
        >
          Continue to Search Setup
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderSearchConfig = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Search className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h3 className="text-lg font-semibold mb-2">Configure Your Search</h3>
        <p className="text-muted-foreground text-sm">
          Define what businesses you want to find
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="search-query">Search Query *</Label>
          <Input
            id="search-query"
            placeholder="e.g., restaurants, dentists, coffee shops"
            value={config.searchConfig.query}
            onChange={(e) => setConfig({
              ...config,
              searchConfig: { ...config.searchConfig, query: e.target.value }
            })}
            className="mt-2"
          />
        </div>

        <div>
          <Label htmlFor="location">Location *</Label>
          <Input
            id="location"
            placeholder="e.g., New York, NY or 10001"
            value={config.searchConfig.location}
            onChange={(e) => setConfig({
              ...config,
              searchConfig: { ...config.searchConfig, location: e.target.value }
            })}
            className="mt-2"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="radius">Search Radius (miles)</Label>
            <Select
              value={config.searchConfig.radius.toString()}
              onValueChange={(value) => setConfig({
                ...config,
                searchConfig: { ...config.searchConfig, radius: parseInt(value) }
              })}
            >
              <SelectTrigger id="radius" className="mt-2">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">1 mile</SelectItem>
                <SelectItem value="5">5 miles</SelectItem>
                <SelectItem value="10">10 miles</SelectItem>
                <SelectItem value="25">25 miles</SelectItem>
                <SelectItem value="50">50 miles</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="limit">Results Limit</Label>
            <Select
              value={config.searchConfig.resultsLimit.toString()}
              onValueChange={(value) => setConfig({
                ...config,
                searchConfig: { ...config.searchConfig, resultsLimit: parseInt(value) }
              })}
            >
              <SelectTrigger id="limit" className="mt-2">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="20">20 results</SelectItem>
                <SelectItem value="50">50 results</SelectItem>
                <SelectItem value="100">100 results</SelectItem>
                <SelectItem value="200">200 results</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div>
          <Label>Business Categories (Optional)</Label>
          <div className="grid grid-cols-2 gap-3 mt-2">
            {businessCategories.map((category) => (
              <div key={category} className="flex items-center space-x-2">
                <Checkbox
                  id={category}
                  checked={config.searchConfig.categories.includes(category)}
                  onCheckedChange={(checked) => {
                    const newCategories = checked
                      ? [...config.searchConfig.categories, category]
                      : config.searchConfig.categories.filter(c => c !== category)
                    setConfig({
                      ...config,
                      searchConfig: { ...config.searchConfig, categories: newCategories }
                    })
                  }}
                />
                <Label htmlFor={category} className="text-sm font-normal cursor-pointer">
                  {category}
                </Label>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          disabled={!config.searchConfig.query || !config.searchConfig.location}
          className="w-full"
        >
          Continue to Data Export
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderDataExport = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Database className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h3 className="text-lg font-semibold mb-2">Data Fields & Export</h3>
        <p className="text-muted-foreground text-sm">
          Choose which data to extract and how to export it
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Data Fields to Extract</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {Object.entries({
            businessName: 'Business Name',
            address: 'Address',
            phone: 'Phone Number',
            website: 'Website',
            rating: 'Rating',
            reviews: 'Review Count',
            hours: 'Operating Hours',
            categories: 'Categories'
          }).map(([key, label]) => (
            <div key={key} className="flex items-center space-x-2">
              <Checkbox
                id={key}
                checked={config.dataFields[key as keyof typeof config.dataFields]}
                onCheckedChange={(checked) => setConfig({
                  ...config,
                  dataFields: { ...config.dataFields, [key]: checked }
                })}
              />
              <Label htmlFor={key} className="text-sm font-normal cursor-pointer">
                {label}
              </Label>
            </div>
          ))}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Export Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label>Export Format</Label>
            <RadioGroup
              value={config.exportConfig.format}
              onValueChange={(value) => setConfig({
                ...config,
                exportConfig: { ...config.exportConfig, format: value as any }
              })}
              className="mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="csv" id="csv" />
                <Label htmlFor="csv" className="font-normal">CSV File</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="json" id="json" />
                <Label htmlFor="json" className="font-normal">JSON File</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="googlesheets" id="googlesheets" />
                <Label htmlFor="googlesheets" className="font-normal">Google Sheets</Label>
              </div>
            </RadioGroup>
          </div>

          {config.exportConfig.format === 'googlesheets' && (
            <div className="space-y-4 pt-2">
              <div>
                <Label className="text-sm font-medium">Authentication Type <span className="text-red-500">*</span></Label>
                <div className="mt-2">
                  <div className="flex items-center space-x-2">
                    <input type="radio" id="google-signin" checked readOnly className="text-blue-600" />
                    <Label htmlFor="google-signin" className="text-sm">Google Sign-In</Label>
                  </div>
                </div>
              </div>

              <div className="text-xs text-muted-foreground">
                <strong>Note:</strong> Select existing Google account from below or Signin with a different account.
              </div>

              {!config.googleAuth.isAuthenticated ? (
                <Button
                  onClick={() => setConfig({
                    ...config,
                    googleAuth: {
                      isAuthenticated: true,
                      userEmail: '<EMAIL>',
                      userName: 'John Doe',
                      accountId: 'gauth-12345'
                    }
                  })}
                  className="w-full"
                >
                  <SiGooglesheets className="mr-2 h-4 w-4" />
                  Sign in with Google
                </Button>
              ) : (
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Badge variant="default" className="bg-green-600">Connected</Badge>
                    <span className="text-sm font-medium">Google</span>
                  </div>
                  
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Google account</Label>
                    <div className="p-3 border rounded-md bg-muted/50">
                      <div className="text-sm font-medium">{config.googleAuth.userName}</div>
                      <div className="text-sm text-muted-foreground">({config.googleAuth.userEmail})</div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          <div>
            <Label htmlFor="filename">File Name</Label>
            <Input
              id="filename"
              value={config.exportConfig.filename}
              onChange={(e) => setConfig({
                ...config,
                exportConfig: { ...config.exportConfig, filename: e.target.value }
              })}
              className="mt-2"
            />
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          className="w-full"
        >
          Continue
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderActionsQuestion = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Plus className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h3 className="text-lg font-semibold mb-2">Do you want to add any actions?</h3>
        <p className="text-muted-foreground text-sm">
          Actions let you extend your automation with additional capabilities
        </p>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <Button
          variant={config.wantsActions === true ? "default" : "outline"}
          size="lg"
          onClick={() => {
            setConfig({ ...config, wantsActions: true })
            nextStep()
          }}
          className="h-auto py-6"
        >
          <div className="flex flex-col items-center gap-2">
            <Check className="h-8 w-8" />
            <span>Yes, I want to add actions</span>
          </div>
        </Button>
        <Button
          variant={config.wantsActions === false ? "default" : "outline"}
          size="lg"
          onClick={() => {
            setConfig({ ...config, wantsActions: false })
            setCurrentStep('review')  // Skip directly to review
          }}
          className="h-auto py-6"
        >
          <div className="flex flex-col items-center gap-2">
            <ArrowRight className="h-8 w-8" />
            <span>No, continue without</span>
          </div>
        </Button>
      </div>

      <div>
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
      </div>
    </motion.div>
  )

  const renderActions = () => {
    const selectedActionsList = config.selectedActionsList || []
    const [wantsMoreActions, setWantsMoreActions] = useState(false)
    const [actionSearchQuery, setActionSearchQuery] = useState('')

    const availableActionsList = [
      { id: 'discord-read', name: 'Discord Read', icon: MessageSquare, desc: 'Read messages from any Discord channel' },
      { id: 'discord-send', name: 'Discord Send', icon: MessageSquare, desc: 'Send messages to any Discord channel' },
      { id: 'gmail-read', name: 'Gmail Read', icon: Mail, desc: 'Read emails from your Gmail account' },
      { id: 'gmail-send', name: 'Gmail Send', icon: Mail, desc: 'Send emails via your Gmail account' },
      { id: 'gcalendar-read', name: 'Google Calendar Read', icon: Calendar, desc: 'Read events from any Google Calendar' },
      { id: 'gcalendar-write', name: 'Google Calendar Write', icon: Calendar, desc: 'Create events on any Google Calendar' },
      { id: 'http-request', name: 'HTTP Request', icon: Globe, desc: 'Make GET, POST, PUT, or DELETE HTTP requests' },
      { id: 'slack-read', name: 'Slack Read', icon: Hash, desc: 'Read messages from a specified Slack channel' },
      { id: 'slack-send', name: 'Slack Send', icon: Hash, desc: 'Send a message to a specified Slack channel' }
    ]

    // Filter actions based on search query
    const filteredActions = availableActionsList.filter(action => {
      const query = actionSearchQuery.toLowerCase()
      return (
        action.name.toLowerCase().includes(query) ||
        action.desc.toLowerCase().includes(query)
      )
    })

    const addAction = (action: any) => {
      const newAction = {
        id: Date.now().toString(),
        type: action.id,
        name: action.name,
        description: action.desc,
        config: {}
      }
      const updatedList = [...selectedActionsList, newAction]
      setConfig({ ...config, selectedActionsList: updatedList })
      setShowActionConfig(updatedList.length - 1)
      setWantsMoreActions(false)
    }

    const removeAction = (index: number) => {
      const updatedList = selectedActionsList.filter((_, i) => i !== index)
      setConfig({ ...config, selectedActionsList: updatedList })
    }

    // If no actions yet or wants more actions, show action selection
    if (selectedActionsList.length === 0 || wantsMoreActions) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          <div className="text-center">
            <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
            <h3 className="text-lg font-semibold mb-2">
              {selectedActionsList.length === 0 ? 'What should happen next?' : 'Add another action?'}
            </h3>
            <p className="text-muted-foreground text-sm">
              {selectedActionsList.length === 0 
                ? 'Choose an action to perform when your trigger fires'
                : 'Chain multiple actions to create powerful workflows'}
            </p>
          </div>

          {/* Search */}
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search actions..."
                value={actionSearchQuery}
                onChange={(e) => setActionSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Available Actions */}
          {filteredActions.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No actions found matching "{actionSearchQuery}"</p>
            </div>
          ) : (
            <div className="grid gap-3 max-w-2xl mx-auto">
              {filteredActions.map((action) => (
                <Card
                  key={action.id}
                  className="cursor-pointer transition-all hover:shadow-md"
                  onClick={() => addAction(action)}
                >
                  <CardHeader className="p-4">
                    <div className="flex items-center gap-3">
                      <action.icon className="h-5 w-5 text-primary" />
                      <div className="flex-1">
                        <CardTitle className="text-sm">{action.name}</CardTitle>
                        <CardDescription className="text-xs mt-1">{action.desc}</CardDescription>
                      </div>
                      <Plus className="h-5 w-5 text-muted-foreground" />
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          )}

          <div className="flex gap-3 max-w-2xl mx-auto">
            <Button onClick={prevStep} variant="outline" className="w-full">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Previous
            </Button>
            {selectedActionsList.length > 0 && (
              <Button 
                onClick={() => setWantsMoreActions(false)} 
                variant="outline"
                className="w-full"
              >
                Continue without adding
              </Button>
            )}
          </div>
        </motion.div>
      )
    }

    // Show current actions with option to add more
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <div className="text-center">
          <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
          <h3 className="text-lg font-semibold mb-2">Your Action Chain</h3>
          <p className="text-muted-foreground text-sm">
            {selectedActionsList.length} action{selectedActionsList.length !== 1 ? 's' : ''} configured
          </p>
        </div>

        {/* Action Chain Visualization */}
        <div className="max-w-2xl mx-auto space-y-3">
          {/* Trigger */}
          <div className="flex items-center gap-3 p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg border-2 border-purple-200 dark:border-purple-800">
            <MapPin className="h-5 w-5 text-purple-600" />
            <div className="flex-1">
              <p className="font-medium">Trigger: Google Maps Search</p>
              <p className="text-xs text-muted-foreground">Extract data for "{config.searchConfig.query}" in {config.searchConfig.location}</p>
            </div>
          </div>

          {/* Arrow */}
          <div className="flex justify-center">
            <ArrowRight className="h-4 w-4 text-muted-foreground" />
          </div>

          {/* Actions */}
          {selectedActionsList.map((action, index) => (
            <div key={action.id}>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center gap-3 p-4 bg-muted/30 rounded-lg border"
              >
                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-xs font-medium">
                  {index + 1}
                </div>
                <div className="flex-1">
                  <p className="font-medium">{action.name}</p>
                  <p className="text-xs text-muted-foreground">{action.description}</p>
                </div>
                <div className="flex gap-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setShowActionConfig(index)}
                    className="h-8 px-2"
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => removeAction(index)}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </motion.div>
              {index < selectedActionsList.length - 1 && (
                <div className="flex justify-center my-2">
                  <ArrowRight className="h-4 w-4 text-muted-foreground" />
                </div>
              )}
            </div>
          ))}

          {/* Add Another Action Button */}
          <Button
            variant="outline"
            onClick={() => setWantsMoreActions(true)}
            className="w-full"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Another Action
          </Button>
        </div>

        <div className="flex gap-3 max-w-2xl mx-auto">
          <Button onClick={prevStep} variant="outline" className="w-full">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button 
            onClick={nextStep} 
            className="w-full"
            disabled={selectedActionsList.length === 0}
          >
            Continue <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>

        {/* Action Configuration Modal */}
        {showActionConfig !== null && selectedActionsList[showActionConfig] && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowActionConfig(null)}
          >
            <motion.div
              initial={{ scale: 0.95 }}
              animate={{ scale: 1 }}
              className="bg-card p-6 rounded-lg shadow-lg max-w-md w-full max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">
                Configure {selectedActionsList[showActionConfig].name}
              </h3>
              
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Configuration options will appear here based on the action type.
                </p>
                <div className="flex justify-end gap-2 mt-6">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowActionConfig(null)}
                  >
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => setShowActionConfig(null)}
                  >
                    Save Configuration
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </motion.div>
    )
  }

  const renderReview = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Eye className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h3 className="text-lg font-semibold mb-2">Review Your Configuration</h3>
        <p className="text-muted-foreground text-sm">
          Make sure everything looks correct before creating your automation
        </p>
      </div>

      <div className="space-y-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Automation Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Name</span>
              <span className="text-sm font-medium">{config.name}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Search Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Query</span>
              <span className="text-sm font-medium">{config.searchConfig.query}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Location</span>
              <span className="text-sm font-medium">{config.searchConfig.location}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Radius</span>
              <span className="text-sm font-medium">{config.searchConfig.radius} miles</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Results Limit</span>
              <span className="text-sm font-medium">{config.searchConfig.resultsLimit}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Export Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Format</span>
              <span className="text-sm font-medium">{config.exportConfig.format.toUpperCase()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">File Name</span>
              <span className="text-sm font-medium">{config.exportConfig.filename}</span>
            </div>
          </CardContent>
        </Card>

        {config.selectedActionsList.length > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Actions ({config.selectedActionsList.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {config.selectedActionsList.map((action) => (
                  <div key={action.id} className="text-sm">
                    • {action.name}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          className="w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700"
        >
          Create Automation
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderComplete = () => (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto">
        <Check className="h-10 w-10 text-green-600 dark:text-green-400" />
      </div>
      <div>
        <h2 className="text-3xl font-bold mb-2">Automation Created!</h2>
        <p className="text-muted-foreground text-lg">
          Your "{config.name}" automation is now ready and will start extracting data automatically.
        </p>
      </div>
      
      <div className="flex flex-col sm:flex-row gap-3 justify-center mt-8">
        <Button 
          onClick={() => setLocation('/dashboard/automations')}
          size="lg"
          className="bg-primary hover:bg-primary/90"
        >
          View All Automations
        </Button>
        <Button 
          onClick={() => setLocation('/dashboard/browse-templates')}
          variant="outline"
          size="lg"
        >
          Browse More Templates
        </Button>
      </div>
    </motion.div>
  )

  const renderStepContent = () => {
    switch (currentStep) {
      case 'intro': return renderIntro()
      case 'naming': return renderNaming()
      case 'serpapi-config': return renderSerpApiConfig()
      case 'search-config': return renderSearchConfig()
      case 'data-export': return renderDataExport()
      case 'actions-question': return renderActionsQuestion()
      case 'actions': return renderActions()
      case 'review': return renderReview()
      case 'complete': return renderComplete()
      default: return null
    }
  }

  const CurrentIcon = stepConfig[currentStep].icon

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => setLocation('/dashboard/browse-templates')}
          className="mb-4"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back to Templates
        </Button>

        {currentStep !== 'complete' && (
          <>
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <CurrentIcon className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">{stepConfig[currentStep].title}</h1>
                <p className="text-muted-foreground text-sm">{stepConfig[currentStep].subtitle}</p>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex justify-between text-xs text-muted-foreground mb-2">
                <span>Step {Object.keys(stepConfig).indexOf(currentStep) + 1} of 8</span>
                <span>{stepConfig[currentStep].progress}% Complete</span>
              </div>
              <Progress value={stepConfig[currentStep].progress} className="h-2" />
            </div>
          </>
        )}
      </div>

      {/* Content */}
      <Card className="p-6">
        {renderStepContent()}
      </Card>
    </div>
  )
}