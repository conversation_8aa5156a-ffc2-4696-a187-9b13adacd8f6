import { useState } from 'react'
import { 
  Key,
  Plus,
  Edit,
  Trash2,
  <PERSON>,
  <PERSON>Off,
  <PERSON><PERSON>,
  Check,
  Shield,
  AlertCircle,
  ExternalLink
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { useToast } from '@/hooks/use-toast'
import { cn } from '@/lib/utils'
import { useAuth } from '@/contexts/AuthContext'

interface Credential {
  id: string
  name: string
  service: string
  type: 'oauth' | 'api_key' | 'username_password'
  lastUsed: Date | null
  created: Date
  connected: boolean
  email?: string
}

// Mock credentials data with all services
const mockCredentials: Credential[] = [
  {
    id: '1',
    name: 'Google Account',
    service: 'google',
    type: 'oauth',
    email: '<EMAIL>',
    lastUsed: new Date(Date.now() - 3600000),
    created: new Date(Date.now() - ******** * 30),
    connected: true
  },
  {
    id: '2',
    name: 'Slack Workspace',
    service: 'slack',
    type: 'oauth',
    email: '<EMAIL>',
    lastUsed: new Date(Date.now() - ******** * 2),
    created: new Date(Date.now() - ******** * 15),
    connected: true
  },
  {
    id: '3',
    name: 'Discord Bot Token',
    service: 'discord',
    type: 'api_key',
    lastUsed: new Date(Date.now() - 3600000 * 5),
    created: new Date(Date.now() - ******** * 7),
    connected: true
  },
  {
    id: '4',
    name: 'Telegram Bot',
    service: 'telegram',
    type: 'api_key',
    lastUsed: new Date(Date.now() - ******** * 5),
    created: new Date(Date.now() - ******** * 20),
    connected: true
  }
]

const serviceLogos: Record<string, string> = {
  google: '🔍',
  gmail: '📧',
  'google-calendar': '📅',
  'google-docs': '📄',
  'google-sheets': '📊',
  slack: '💬',
  discord: '🎮',
  telegram: '✈️',
  sendgrid: '📮',
  github: '🐙',
  notion: '📝',
  airtable: '📊',
  twitter: '🐦',
  facebook: '👍',
  instagram: '📸',
  linkedin: '💼',
  whatsapp: '💬',
  zoom: '📹',
  stripe: '💳',
  paypal: '💰',
  shopify: '🛍️',
  mailchimp: '🐒',
  hubspot: '🧲',
  salesforce: '☁️',
  dropbox: '📦',
  'google-drive': '💾',
  'microsoft-teams': '👥',
  todoist: '✅',
  asana: '🎯',
  trello: '📋',
  clickup: '🚀',
  evernote: '🐘',
  onenote: '📓',
  'pdf-generator': '📄',
  'http-request': '🌐'
}

export default function Credentials() {
  const [credentials, setCredentials] = useState(mockCredentials)
  const [deleteId, setDeleteId] = useState<string | null>(null)
  const [showApiKey, setShowApiKey] = useState<Record<string, boolean>>({})
  const [copiedId, setCopiedId] = useState<string | null>(null)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const { toast } = useToast()
  const { user } = useAuth()

  const handleDelete = (id: string) => {
    setCredentials(credentials.filter(c => c.id !== id))
    setDeleteId(null)
    toast({
      title: 'Credential removed',
      description: 'The credential has been disconnected and removed.',
    })
  }

  const handleCopy = async (id: string, value: string) => {
    await navigator.clipboard.writeText(value)
    setCopiedId(id)
    setTimeout(() => setCopiedId(null), 2000)
    toast({
      title: 'Copied to clipboard',
      description: 'The value has been copied to your clipboard.',
    })
  }

  const getRelativeTime = (date: Date | null) => {
    if (!date) return 'Never used'
    const now = Date.now()
    const diff = Math.abs(now - date.getTime())
    const hours = Math.floor(diff / 3600000)
    const days = Math.floor(hours / 24)
    
    if (days > 0) return `${days}d ago`
    if (hours > 0) return `${hours}h ago`
    return 'Recently'
  }

  const getCredentialIcon = (type: Credential['type']) => {
    switch (type) {
      case 'oauth':
        return <Shield className="h-4 w-4" />
      case 'api_key':
        return <Key className="h-4 w-4" />
      case 'username_password':
        return <Key className="h-4 w-4" />
    }
  }

  const getCredentialTypeBadge = (type: Credential['type']) => {
    switch (type) {
      case 'oauth':
        return <Badge variant="default">OAuth</Badge>
      case 'api_key':
        return <Badge variant="secondary">API Key</Badge>
      case 'username_password':
        return <Badge variant="outline">Username/Password</Badge>
    }
  }

  return (
    <div>
      {/* Header */}
      <div className="mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold mb-2">Credentials</h1>
          <p className="text-muted-foreground">
            Manage your connected accounts and API keys
          </p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button className="mt-4 sm:mt-0 bg-[#155DB8] hover:bg-[#155DB8]/90">
              <Plus className="h-4 w-4 mr-2" />
              Add Credential
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[80vh]">
            <DialogHeader>
              <DialogTitle>Add New Credential</DialogTitle>
              <DialogDescription>
                Connect a new service or add an API key to use in your automations.
              </DialogDescription>
            </DialogHeader>
            <ScrollArea className="h-[60vh] pr-4">
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                <Label>Google Services</Label>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  {[
                    { name: 'Gmail', service: 'gmail' },
                    { name: 'Google Calendar', service: 'google-calendar' },
                    { name: 'Google Docs', service: 'google-docs' },
                    { name: 'Google Sheets', service: 'google-sheets' },
                    { name: 'Google Drive', service: 'google-drive' }
                  ].map((item) => (
                    <Button
                      key={item.service}
                      variant="outline"
                      className="justify-start text-sm"
                      onClick={() => {
                        toast({
                          title: 'OAuth flow',
                          description: `Redirecting to ${item.name} authorization...`,
                        })
                        setIsAddDialogOpen(false)
                      }}
                    >
                      <span className="mr-2">{serviceLogos[item.service] || '🔗'}</span>
                      {item.name}
                    </Button>
                  ))}
                </div>
              </div>

              <Separator />

              <div className="grid gap-2">
                <Label>Communication Services</Label>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  {[
                    { name: 'Slack', service: 'slack' },
                    { name: 'Discord', service: 'discord' },
                    { name: 'Telegram', service: 'telegram' },
                    { name: 'WhatsApp', service: 'whatsapp' },
                    { name: 'Microsoft Teams', service: 'microsoft-teams' },
                    { name: 'Zoom', service: 'zoom' }
                  ].map((item) => (
                    <Button
                      key={item.service}
                      variant="outline"
                      className="justify-start text-sm"
                      onClick={() => {
                        toast({
                          title: 'OAuth flow',
                          description: `Redirecting to ${item.name} authorization...`,
                        })
                        setIsAddDialogOpen(false)
                      }}
                    >
                      <span className="mr-2">{serviceLogos[item.service] || '🔗'}</span>
                      {item.name}
                    </Button>
                  ))}
                </div>
              </div>

              <Separator />

              <div className="grid gap-2">
                <Label>Social Media</Label>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  {[
                    { name: 'Twitter', service: 'twitter' },
                    { name: 'Facebook', service: 'facebook' },
                    { name: 'Instagram', service: 'instagram' },
                    { name: 'LinkedIn', service: 'linkedin' }
                  ].map((item) => (
                    <Button
                      key={item.service}
                      variant="outline"
                      className="justify-start text-sm"
                      onClick={() => {
                        toast({
                          title: 'OAuth flow',
                          description: `Redirecting to ${item.name} authorization...`,
                        })
                        setIsAddDialogOpen(false)
                      }}
                    >
                      <span className="mr-2">{serviceLogos[item.service] || '🔗'}</span>
                      {item.name}
                    </Button>
                  ))}
                </div>
              </div>

              <Separator />

              <div className="grid gap-2">
                <Label>Productivity & Project Management</Label>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  {[
                    { name: 'Notion', service: 'notion' },
                    { name: 'Asana', service: 'asana' },
                    { name: 'Trello', service: 'trello' },
                    { name: 'Todoist', service: 'todoist' },
                    { name: 'ClickUp', service: 'clickup' },
                    { name: 'Airtable', service: 'airtable' },
                    { name: 'Evernote', service: 'evernote' },
                    { name: 'OneNote', service: 'onenote' }
                  ].map((item) => (
                    <Button
                      key={item.service}
                      variant="outline"
                      className="justify-start text-sm"
                      onClick={() => {
                        toast({
                          title: 'OAuth flow',
                          description: `Redirecting to ${item.name} authorization...`,
                        })
                        setIsAddDialogOpen(false)
                      }}
                    >
                      <span className="mr-2">{serviceLogos[item.service] || '🔗'}</span>
                      {item.name}
                    </Button>
                  ))}
                </div>
              </div>

              <Separator />

              <div className="grid gap-2">
                <Label>E-commerce & Payment</Label>
                <div className="grid grid-cols-2 gap-2">
                  {[
                    { name: 'Stripe', service: 'stripe' },
                    { name: 'PayPal', service: 'paypal' },
                    { name: 'Shopify', service: 'shopify' }
                  ].map((item) => (
                    <Button
                      key={item.service}
                      variant="outline"
                      className="justify-start text-sm"
                      onClick={() => {
                        toast({
                          title: 'OAuth flow',
                          description: `Redirecting to ${item.name} authorization...`,
                        })
                        setIsAddDialogOpen(false)
                      }}
                    >
                      <span className="mr-2">{serviceLogos[item.service] || '🔗'}</span>
                      {item.name}
                    </Button>
                  ))}
                </div>
              </div>

              <Separator />

              <div className="grid gap-2">
                <Label>Marketing & CRM</Label>
                <div className="grid grid-cols-2 gap-2">
                  {[
                    { name: 'Mailchimp', service: 'mailchimp' },
                    { name: 'HubSpot', service: 'hubspot' },
                    { name: 'Salesforce', service: 'salesforce' },
                    { name: 'SendGrid', service: 'sendgrid' }
                  ].map((item) => (
                    <Button
                      key={item.service}
                      variant="outline"
                      className="justify-start text-sm"
                      onClick={() => {
                        toast({
                          title: 'OAuth flow',
                          description: `Redirecting to ${item.name} authorization...`,
                        })
                        setIsAddDialogOpen(false)
                      }}
                    >
                      <span className="mr-2">{serviceLogos[item.service] || '🔗'}</span>
                      {item.name}
                    </Button>
                  ))}
                </div>
              </div>

              <Separator />

              <div className="grid gap-2">
                <Label>Development & Other</Label>
                <div className="grid grid-cols-2 gap-2">
                  {[
                    { name: 'GitHub', service: 'github' },
                    { name: 'Dropbox', service: 'dropbox' },
                    { name: 'HTTP Request', service: 'http-request' },
                    { name: 'PDF Generator', service: 'pdf-generator' }
                  ].map((item) => (
                    <Button
                      key={item.service}
                      variant="outline"
                      className="justify-start text-sm"
                      onClick={() => {
                        toast({
                          title: 'OAuth flow',
                          description: `Redirecting to ${item.name} authorization...`,
                        })
                        setIsAddDialogOpen(false)
                      }}
                    >
                      <span className="mr-2">{serviceLogos[item.service] || '🔗'}</span>
                      {item.name}
                    </Button>
                  ))}
                </div>
              </div>
              
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-background px-2 text-muted-foreground">Or</span>
                </div>
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="api-key">API Key</Label>
                <Input
                  id="api-key"
                  placeholder="Enter your API key"
                  type="password"
                />
              </div>
              </div>
            </ScrollArea>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={() => {
                toast({
                  title: 'Credential added',
                  description: 'Your new credential has been saved securely.',
                })
                setIsAddDialogOpen(false)
              }}>
                Add Credential
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Security Notice */}
      <Card className="mb-6 border-amber-200 bg-amber-50/50 dark:border-amber-900 dark:bg-amber-950/20">
        <CardHeader className="pb-3">
          <div className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-amber-600 dark:text-amber-400" />
            <CardTitle className="text-base">Security Notice</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            Your credentials are encrypted and stored securely. We never share your credentials with third parties.
            OAuth tokens are refreshed automatically when needed.
          </p>
        </CardContent>
      </Card>

      {/* Credentials Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {credentials.map((credential) => (
          <Card key={credential.id} className={cn(
            "relative overflow-hidden",
            !credential.connected && "opacity-75"
          )}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <div className="text-2xl">
                    {serviceLogos[credential.service] || '🔗'}
                  </div>
                  <div>
                    <CardTitle className="text-base">{credential.name}</CardTitle>
                    <CardDescription className="text-xs">
                      {credential.email || credential.service}
                    </CardDescription>
                  </div>
                </div>
                {getCredentialTypeBadge(credential.type)}
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Status</span>
                <Badge 
                  variant={credential.connected ? "default" : "secondary"}
                  className={cn(
                    credential.connected 
                      ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400" 
                      : ""
                  )}
                >
                  {credential.connected ? 'Connected' : 'Disconnected'}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Last used</span>
                <span>{getRelativeTime(credential.lastUsed)}</span>
              </div>

              {credential.type === 'api_key' && (
                <div className="space-y-2">
                  <Label className="text-xs">API Key</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      type={showApiKey[credential.id] ? 'text' : 'password'}
                      value="sk_test_4eC39HqLyjWDarjtT1zdp7dc"
                      readOnly
                      className="text-xs font-mono"
                    />
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setShowApiKey({
                        ...showApiKey,
                        [credential.id]: !showApiKey[credential.id]
                      })}
                    >
                      {showApiKey[credential.id] ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleCopy(credential.id, 'sk_test_4eC39HqLyjWDarjtT1zdp7dc')}
                    >
                      {copiedId === credential.id ? (
                        <Check className="h-4 w-4 text-green-500" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
              )}

              <div className="flex items-center gap-2 pt-2">
                {credential.connected ? (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => {
                        toast({
                          title: 'Refreshing...',
                          description: 'OAuth token is being refreshed.',
                        })
                      }}
                    >
                      <ExternalLink className="h-3 w-3 mr-1" />
                      Reconnect
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setDeleteId(credential.id)}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </>
                ) : (
                  <Button
                    variant="default"
                    size="sm"
                    className="w-full bg-[#155DB8] hover:bg-[#155DB8]/90"
                    onClick={() => {
                      toast({
                        title: 'Connecting...',
                        description: `Redirecting to ${credential.service} authorization.`,
                      })
                    }}
                  >
                    Connect
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Delete Confirmation */}
      <AlertDialog open={!!deleteId} onOpenChange={() => setDeleteId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove credential?</AlertDialogTitle>
            <AlertDialogDescription>
              This will disconnect the service and remove the stored credential. 
              Any automations using this credential will stop working.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deleteId && handleDelete(deleteId)}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Remove
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}