import { useState } from 'react'
import { motion } from 'framer-motion'
import { useLocation } from 'wouter'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  ChevronLeft, ArrowRight, Globe, Brain, Mail, Calendar, 
  FileText, Settings, Check, Zap, Share2, Clock, Users, Shield, Search
} from 'lucide-react'
import { SiOpenai } from 'react-icons/si'

import { Plus, X, MessageSquare, Hash } from 'lucide-react'

type StepType = 'intro' | 'naming' | 'browse-ai-setup' | 'content-config' | 'ai-settings' | 'social-platforms' | 'schedule-delivery' | 'actions-question' | 'actions' | 'review' | 'complete'

interface WorkflowConfig {
  name: string
  browseAI: {
    apiKey: string
    isAuthenticated: boolean
    robotId: string
    robotName: string
  }
  contentConfig: {
    targetUrls: string[]
    contentTypes: string[]
    summaryLength: string
    extractImages: boolean
    extractLinks: boolean
    customInstructions: string
  }
  aiSettings: {
    model: string
    tone: string
    length: string
    structure: string
    language: string
  }
  socialPlatforms: {
    twitter: {
      enabled: boolean
      maxLength: number
      hashtags: string[]
      mentions: string[]
    }
    linkedin: {
      enabled: boolean
      format: string
      hashtags: string[]
    }
    facebook: {
      enabled: boolean
      format: string
      includeImage: boolean
    }
    instagram: {
      enabled: boolean
      format: string
      aspectRatio: string
    }
  }
  scheduleDelivery: {
    schedule: string
    frequency: string
    outputLocation: string
    deliveryMethod: string
    recipients: string[]
  }
  selectedActionsList: Array<{
    id: string
    type: string
    name: string
    description: string
    config: Record<string, any>
  }>
  wantsActions: boolean | null
}

export default function WebsiteSocialSummarizerTemplate() {
  const [, setLocation] = useLocation()
  const [currentStep, setCurrentStep] = useState<StepType>('intro')
  const [showActionConfig, setShowActionConfig] = useState<number | null>(null)
  const [actionSearchQuery, setActionSearchQuery] = useState('')
  const [wantsMoreActions, setWantsMoreActions] = useState(false)
  const [workflow, setWorkflow] = useState<WorkflowConfig>({
    name: '',
    browseAI: {
      apiKey: '',
      isAuthenticated: false,
      robotId: '',
      robotName: 'Website Content Extractor'
    },
    contentConfig: {
      targetUrls: [],
      contentTypes: ['articles', 'blog-posts'],
      summaryLength: 'medium',
      extractImages: true,
      extractLinks: false,
      customInstructions: ''
    },
    aiSettings: {
      model: 'gpt-4o',
      tone: 'professional',
      length: 'medium',
      structure: 'intro-body-conclusion',
      language: 'english'
    },
    socialPlatforms: {
      twitter: {
        enabled: true,
        maxLength: 280,
        hashtags: [],
        mentions: []
      },
      linkedin: {
        enabled: true,
        format: 'post',
        hashtags: []
      },
      facebook: {
        enabled: false,
        format: 'post',
        includeImage: true
      },
      instagram: {
        enabled: false,
        format: 'post',
        aspectRatio: 'square'
      }
    },
    scheduleDelivery: {
      schedule: 'daily',
      frequency: '09:00',
      outputLocation: 'dashboard',
      deliveryMethod: 'email',
      recipients: []
    },
    selectedActionsList: [],
    wantsActions: null
  })

  const stepConfig = {
    intro: { 
      title: 'Website Social Summarizer', 
      subtitle: 'Summarize website content and create social media posts with AI',
      icon: Globe,
      progress: 0
    },
    naming: {
      title: 'Name Your Automation',
      subtitle: 'Give your automation a memorable name',
      icon: Settings,
      progress: 15
    },
    'browse-ai-setup': { 
      title: 'Browse AI Setup', 
      subtitle: 'Configure Browse AI for content extraction',
      icon: Brain,
      progress: 25
    },
    'content-config': { 
      title: 'Content Configuration', 
      subtitle: 'Configure what content to extract and how',
      icon: FileText,
      progress: 40
    },
    'ai-settings': { 
      title: 'AI Settings', 
      subtitle: 'Configure AI for content summarization',
      icon: Brain,
      progress: 55
    },
    'social-platforms': { 
      title: 'Social Platforms', 
      subtitle: 'Configure social media platforms and settings',
      icon: Share2,
      progress: 70
    },
    'schedule-delivery': { 
      title: 'Schedule & Delivery', 
      subtitle: 'Configure when and how to deliver content',
      icon: Calendar,
      progress: 85
    },
    'actions-question': { 
      title: 'Additional Actions', 
      subtitle: 'Do you want to add any tools or actions?',
      icon: Zap,
      progress: 90
    },
    'actions': { 
      title: 'Configure Actions', 
      subtitle: 'Add and configure additional actions',
      icon: Zap,
      progress: 90
    },
    'review': { 
      title: 'Review & Confirm', 
      subtitle: 'Review your automation configuration',
      icon: Check,
      progress: 95
    },
    'complete': { 
      title: 'Automation Created', 
      subtitle: 'Your automation is ready to use',
      icon: Check,
      progress: 100
    }
  }

  const getCurrentStepConfig = () => stepConfig[currentStep]

  const nextStep = () => {
    const steps: StepType[] = ['intro', 'naming', 'browse-ai-setup', 'content-config', 'ai-settings', 'social-platforms', 'schedule-delivery', 'actions-question', 'actions', 'review', 'complete']
    const currentIndex = steps.indexOf(currentStep)
    
    if (currentStep === 'actions-question' && workflow.wantsActions === false) {
      setCurrentStep('review')
    } else if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1])
    }
  }

  const previousStep = () => {
    const steps: StepType[] = ['intro', 'naming', 'browse-ai-setup', 'content-config', 'ai-settings', 'social-platforms', 'schedule-delivery', 'actions-question', 'actions', 'review', 'complete']
    const currentIndex = steps.indexOf(currentStep)
    
    if (currentStep === 'review' && workflow.wantsActions === false) {
      setCurrentStep('actions-question')
    } else if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1])
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent, action: () => void) => {
    if (e.key === 'Enter') {
      action()
    }
  }

  const addUrl = () => {
    setWorkflow(prev => ({
      ...prev,
      contentConfig: {
        ...prev.contentConfig,
        targetUrls: [...prev.contentConfig.targetUrls, '']
      }
    }))
  }

  const removeUrl = (index: number) => {
    setWorkflow(prev => ({
      ...prev,
      contentConfig: {
        ...prev.contentConfig,
        targetUrls: prev.contentConfig.targetUrls.filter((_, i) => i !== index)
      }
    }))
  }

  const updateUrl = (index: number, value: string) => {
    setWorkflow(prev => ({
      ...prev,
      contentConfig: {
        ...prev.contentConfig,
        targetUrls: prev.contentConfig.targetUrls.map((url, i) => i === index ? value : url)
      }
    }))
  }

  const addHashtag = (platform: string, hashtag: string) => {
    if (!hashtag.trim()) return
    setWorkflow(prev => ({
      ...prev,
      socialPlatforms: {
        ...prev.socialPlatforms,
        [platform]: {
          ...prev.socialPlatforms[platform as keyof typeof prev.socialPlatforms],
          hashtags: [...(prev.socialPlatforms[platform as keyof typeof prev.socialPlatforms] as any).hashtags, hashtag]
        }
      }
    }))
  }

  const removeHashtag = (platform: string, index: number) => {
    setWorkflow(prev => ({
      ...prev,
      socialPlatforms: {
        ...prev.socialPlatforms,
        [platform]: {
          ...prev.socialPlatforms[platform as keyof typeof prev.socialPlatforms],
          hashtags: (prev.socialPlatforms[platform as keyof typeof prev.socialPlatforms] as any).hashtags.filter((_: any, i: number) => i !== index)
        }
      }
    }))
  }

  const availableActions = [
    {
      id: 'webhook',
      name: 'Webhook',
      description: 'Send data to external services via HTTP webhooks',
      icon: Zap,
      color: '#10b981',
      category: 'write'
    },
    {
      id: 'email',
      name: 'Email',
      description: 'Send email notifications and alerts',
      icon: Mail,
      color: '#3b82f6',
      category: 'write'
    },
    {
      id: 'slack',
      name: 'Slack',
      description: 'Send messages to Slack channels',
      icon: MessageSquare,
      color: '#8b5cf6',
      category: 'write'
    },
    {
      id: 'google-sheets',
      name: 'Google Sheets',
      description: 'Update Google Sheets with data',
      icon: FileText,
      color: '#059669',
      category: 'write'
    }
  ]

  const filteredActions = availableActions.filter(action =>
    action.name.toLowerCase().includes(actionSearchQuery.toLowerCase()) ||
    action.description.toLowerCase().includes(actionSearchQuery.toLowerCase())
  )

  const renderIntroStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto">
        <Globe className="h-10 w-10 text-white" />
      </div>
      <div>
        <h2 className="text-2xl font-bold mb-2">Website Content & Social Media Summarizer</h2>
        <p className="text-muted-foreground max-w-md mx-auto">
          This template monitors websites, extracts content using Browse AI, generates AI-powered summaries, 
          and creates optimized social media posts for multiple platforms automatically.
        </p>
      </div>
      
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Globe className="h-8 w-8 mx-auto mb-2 text-blue-600" />
          <p className="text-xs font-medium">Browse AI</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Brain className="h-8 w-8 mx-auto mb-2 text-purple-600" />
          <p className="text-xs font-medium">AI Summary</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Share2 className="h-8 w-8 mx-auto mb-2 text-green-600" />
          <p className="text-xs font-medium">Social Posts</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Mail className="h-8 w-8 mx-auto mb-2 text-red-600" />
          <p className="text-xs font-medium">Email Delivery</p>
        </div>
      </div>

      <div className="space-y-4">
        <div className="p-4 bg-muted/20 rounded-lg text-left">
          <h3 className="font-medium mb-2 flex items-center gap-2">
            <Zap className="h-4 w-4" />
            What it does:
          </h3>
          <ul className="text-sm text-muted-foreground space-y-1 ml-6">
            <li>• Monitors multiple websites for new content using Browse AI</li>
            <li>• Extracts and summarizes content with AI</li>
            <li>• Creates platform-specific social media posts</li>
            <li>• Delivers summaries via email or webhook</li>
          </ul>
        </div>
      </div>

      <Button 
        onClick={nextStep}
        size="lg"
        className="w-full max-w-xs bg-gradient-to-r from-purple-600 to-pink-600"
      >
        Start Setup <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
    </motion.div>
  )

  const renderNamingStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="space-y-4">
        <div>
          <Label htmlFor="automation-name">Automation Name *</Label>
          <Input
            id="automation-name"
            placeholder="e.g., Tech Blog Social Summarizer"
            value={workflow.name}
            onChange={(e) => setWorkflow(prev => ({ ...prev, name: e.target.value }))}
            onKeyDown={(e) => handleKeyPress(e, () => workflow.name.trim() && nextStep())}
            className="mt-1"
          />
        </div>
      </div>

      <div className="flex gap-3">
        <Button onClick={previousStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          disabled={!workflow.name.trim()}
          className="w-full"
        >
          Continue <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderBrowseAISetup = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="space-y-4">
        <div>
          <Label htmlFor="browse-api-key">Browse AI API Key *</Label>
          <Input
            id="browse-api-key"
            type="password"
            placeholder="Enter your Browse AI API key"
            value={workflow.browseAI.apiKey}
            onChange={(e) => setWorkflow(prev => ({
              ...prev,
              browseAI: { ...prev.browseAI, apiKey: e.target.value }
            }))}
            onKeyDown={(e) => handleKeyPress(e, () => workflow.browseAI.apiKey.trim() && nextStep())}
            className="mt-1"
          />
          <p className="text-sm text-muted-foreground mt-1">
            Get your API key from <a href="https://browse.ai" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">Browse AI Dashboard</a>
          </p>
        </div>

        <div>
          <Label htmlFor="robot-name">Robot Name</Label>
          <Input
            id="robot-name"
            placeholder="Website Content Extractor"
            value={workflow.browseAI.robotName}
            onChange={(e) => setWorkflow(prev => ({
              ...prev,
              browseAI: { ...prev.browseAI, robotName: e.target.value }
            }))}
            className="mt-1"
          />
        </div>
      </div>

      <div className="flex gap-3">
        <Button onClick={previousStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          disabled={!workflow.browseAI.apiKey.trim()}
          className="w-full"
        >
          Continue <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderContentConfig = () => (
    <div className="space-y-6">
      <div className="max-w-2xl mx-auto space-y-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label>Target URLs *</Label>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={addUrl}
              className="gap-2"
            >
              <Plus className="w-4 h-4" />
              Add URL
            </Button>
          </div>
          
          {workflow.contentConfig.targetUrls.map((url, index) => (
            <div key={index} className="flex gap-2">
              <Input
                placeholder="https://example.com"
                value={url}
                onChange={(e) => updateUrl(index, e.target.value)}
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => removeUrl(index)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          ))}
          
          {workflow.contentConfig.targetUrls.length === 0 && (
            <Button 
              variant="outline" 
              onClick={addUrl}
              className="w-full gap-2"
            >
              <Plus className="w-4 h-4" />
              Add First URL
            </Button>
          )}
        </div>

        <div className="space-y-2">
          <Label>Summary Length</Label>
          <Select 
            value={workflow.contentConfig.summaryLength} 
            onValueChange={(value) => setWorkflow(prev => ({
              ...prev,
              contentConfig: { ...prev.contentConfig, summaryLength: value }
            }))}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select summary length" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="short">Short (1-2 sentences)</SelectItem>
              <SelectItem value="medium">Medium (3-4 sentences)</SelectItem>
              <SelectItem value="long">Long (5+ sentences)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="extract-images"
              checked={workflow.contentConfig.extractImages}
              onCheckedChange={(checked) => setWorkflow(prev => ({
                ...prev,
                contentConfig: { ...prev.contentConfig, extractImages: checked as boolean }
              }))}
            />
            <Label htmlFor="extract-images">Extract images from content</Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox
              id="extract-links"
              checked={workflow.contentConfig.extractLinks}
              onCheckedChange={(checked) => setWorkflow(prev => ({
                ...prev,
                contentConfig: { ...prev.contentConfig, extractLinks: checked as boolean }
              }))}
            />
            <Label htmlFor="extract-links">Extract important links</Label>
          </div>
        </div>
      </div>

      <div className="flex justify-between items-center">
        <Button variant="outline" onClick={previousStep} className="gap-2">
          <ChevronLeft className="w-4 h-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          disabled={workflow.contentConfig.targetUrls.length === 0 || workflow.contentConfig.targetUrls.some(url => !url.trim())}
          className="gap-2"
        >
          Continue
          <ArrowRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  )

  const renderAISettings = () => (
    <div className="space-y-6">
      <div className="max-w-md mx-auto space-y-4">
        <div className="space-y-2">
          <Label>AI Model</Label>
          <Select 
            value={workflow.aiSettings.model} 
            onValueChange={(value) => setWorkflow(prev => ({
              ...prev,
              aiSettings: { ...prev.aiSettings, model: value }
            }))}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select AI model" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="gpt-4o">GPT-4o (Latest)</SelectItem>
              <SelectItem value="gpt-4">GPT-4</SelectItem>
              <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
              <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
              <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
              <SelectItem value="claude-3-haiku">Claude 3 Haiku</SelectItem>
              <SelectItem value="gemini-1.5-pro">Gemini 1.5 Pro</SelectItem>
              <SelectItem value="gemini-1.0-pro">Gemini 1.0 Pro</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Tone</Label>
          <Select 
            value={workflow.aiSettings.tone} 
            onValueChange={(value) => setWorkflow(prev => ({
              ...prev,
              aiSettings: { ...prev.aiSettings, tone: value }
            }))}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select tone" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="professional">Professional</SelectItem>
              <SelectItem value="casual">Casual</SelectItem>
              <SelectItem value="friendly">Friendly</SelectItem>
              <SelectItem value="engaging">Engaging</SelectItem>
              <SelectItem value="informative">Informative</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Language</Label>
          <Select 
            value={workflow.aiSettings.language} 
            onValueChange={(value) => setWorkflow(prev => ({
              ...prev,
              aiSettings: { ...prev.aiSettings, language: value }
            }))}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select language" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="english">English</SelectItem>
              <SelectItem value="spanish">Spanish</SelectItem>
              <SelectItem value="french">French</SelectItem>
              <SelectItem value="german">German</SelectItem>
              <SelectItem value="italian">Italian</SelectItem>
              <SelectItem value="portuguese">Portuguese</SelectItem>
              <SelectItem value="chinese">Chinese</SelectItem>
              <SelectItem value="japanese">Japanese</SelectItem>
              <SelectItem value="korean">Korean</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex justify-between items-center">
        <Button variant="outline" onClick={previousStep} className="gap-2">
          <ChevronLeft className="w-4 h-4" />
          Previous
        </Button>
        <Button onClick={nextStep} className="gap-2">
          Continue
          <ArrowRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  )

  const renderSocialPlatforms = () => (
    <div className="space-y-6">
      <div className="max-w-2xl mx-auto space-y-6">
        <div className="grid gap-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Share2 className="w-5 h-5 text-blue-400" />
                  <div>
                    <CardTitle>Twitter</CardTitle>
                    <CardDescription>Create tweets with optimal formatting</CardDescription>
                  </div>
                </div>
                <Checkbox
                  checked={workflow.socialPlatforms.twitter.enabled}
                  onCheckedChange={(checked) => setWorkflow(prev => ({
                    ...prev,
                    socialPlatforms: {
                      ...prev.socialPlatforms,
                      twitter: { ...prev.socialPlatforms.twitter, enabled: checked as boolean }
                    }
                  }))}
                />
              </div>
            </CardHeader>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Users className="w-5 h-5 text-blue-600" />
                  <div>
                    <CardTitle>LinkedIn</CardTitle>
                    <CardDescription>Professional posts and articles</CardDescription>
                  </div>
                </div>
                <Checkbox
                  checked={workflow.socialPlatforms.linkedin.enabled}
                  onCheckedChange={(checked) => setWorkflow(prev => ({
                    ...prev,
                    socialPlatforms: {
                      ...prev.socialPlatforms,
                      linkedin: { ...prev.socialPlatforms.linkedin, enabled: checked as boolean }
                    }
                  }))}
                />
              </div>
            </CardHeader>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Globe className="w-5 h-5 text-blue-600" />
                  <div>
                    <CardTitle>Facebook</CardTitle>
                    <CardDescription>Posts and stories</CardDescription>
                  </div>
                </div>
                <Checkbox
                  checked={workflow.socialPlatforms.facebook.enabled}
                  onCheckedChange={(checked) => setWorkflow(prev => ({
                    ...prev,
                    socialPlatforms: {
                      ...prev.socialPlatforms,
                      facebook: { ...prev.socialPlatforms.facebook, enabled: checked as boolean }
                    }
                  }))}
                />
              </div>
            </CardHeader>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <FileText className="w-5 h-5 text-pink-500" />
                  <div>
                    <CardTitle>Instagram</CardTitle>
                    <CardDescription>Visual posts and stories</CardDescription>
                  </div>
                </div>
                <Checkbox
                  checked={workflow.socialPlatforms.instagram.enabled}
                  onCheckedChange={(checked) => setWorkflow(prev => ({
                    ...prev,
                    socialPlatforms: {
                      ...prev.socialPlatforms,
                      instagram: { ...prev.socialPlatforms.instagram, enabled: checked as boolean }
                    }
                  }))}
                />
              </div>
            </CardHeader>
          </Card>
        </div>
      </div>

      <div className="flex justify-between items-center">
        <Button variant="outline" onClick={previousStep} className="gap-2">
          <ChevronLeft className="w-4 h-4" />
          Previous
        </Button>
        <Button onClick={nextStep} className="gap-2">
          Continue
          <ArrowRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  )

  const renderScheduleDelivery = () => (
    <div className="space-y-6">
      <div className="max-w-md mx-auto space-y-4">
        <div className="space-y-2">
          <Label>Schedule</Label>
          <Select 
            value={workflow.scheduleDelivery.schedule} 
            onValueChange={(value) => setWorkflow(prev => ({
              ...prev,
              scheduleDelivery: { ...prev.scheduleDelivery, schedule: value }
            }))}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select schedule" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="manual">Manual</SelectItem>
              <SelectItem value="daily">Daily</SelectItem>
              <SelectItem value="weekly">Weekly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {workflow.scheduleDelivery.schedule !== 'manual' && (
          <div className="space-y-2">
            <Label>Time</Label>
            <Input
              type="time"
              value={workflow.scheduleDelivery.frequency}
              onChange={(e) => setWorkflow(prev => ({
                ...prev,
                scheduleDelivery: { ...prev.scheduleDelivery, frequency: e.target.value }
              }))}
            />
          </div>
        )}

        <div className="space-y-2">
          <Label>Delivery Method</Label>
          <Select 
            value={workflow.scheduleDelivery.deliveryMethod} 
            onValueChange={(value) => setWorkflow(prev => ({
              ...prev,
              scheduleDelivery: { ...prev.scheduleDelivery, deliveryMethod: value }
            }))}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select delivery method" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="email">Email</SelectItem>
              <SelectItem value="webhook">Webhook</SelectItem>
              <SelectItem value="dashboard">Dashboard</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex justify-between items-center">
        <Button variant="outline" onClick={previousStep} className="gap-2">
          <ChevronLeft className="w-4 h-4" />
          Previous
        </Button>
        <Button onClick={nextStep} className="gap-2">
          Continue
          <ArrowRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  )

  const renderActionsQuestion = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Zap className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h3 className="text-lg font-semibold mb-2">Do you want to add any tools or actions?</h3>
        <p className="text-muted-foreground text-sm">
          Actions let you send data, trigger notifications, and integrate with other services
        </p>
      </div>
      
      <div className="flex gap-3 max-w-sm mx-auto">
        <Button 
          onClick={() => {
            setWorkflow(prev => ({ ...prev, wantsActions: true }))
            nextStep()
          }}
          className="w-full bg-gradient-to-r from-purple-600 to-pink-600"
        >
          Yes, add actions
        </Button>
        <Button 
          variant="outline"
          onClick={() => {
            setWorkflow(prev => ({ ...prev, wantsActions: false }))
            nextStep()
          }}
          className="w-full"
        >
          No, continue
        </Button>
      </div>

      <div className="flex gap-3">
        <Button onClick={previousStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
      </div>
    </motion.div>
  )

  const renderActionsStep = () => {
    const selectedActionsList = workflow.selectedActionsList || []

    const availableActionsList = [
      { id: 'discord-read', name: 'Discord Read', icon: MessageSquare, desc: 'Read messages from any Discord channel' },
      { id: 'discord-send', name: 'Discord Send', icon: MessageSquare, desc: 'Send messages to any Discord channel' },
      { id: 'gmail-read', name: 'Gmail Read', icon: Mail, desc: 'Read emails from your Gmail account' },
      { id: 'gmail-send', name: 'Gmail Send', icon: Mail, desc: 'Send emails via your Gmail account' },
      { id: 'gcalendar-read', name: 'Google Calendar Read', icon: Calendar, desc: 'Read events from any Google Calendar' },
      { id: 'gcalendar-write', name: 'Google Calendar Write', icon: Calendar, desc: 'Create events on any Google Calendar' },
      { id: 'http-request', name: 'HTTP Request', icon: Globe, desc: 'Make GET, POST, PUT, or DELETE HTTP requests' },
      { id: 'slack-read', name: 'Slack Read', icon: Hash, desc: 'Read messages from a specified Slack channel' },
      { id: 'slack-send', name: 'Slack Send', icon: Hash, desc: 'Send a message to a specified Slack channel' }
    ]

    // Filter actions based on search query
    const filteredActions = availableActionsList.filter(action => {
      const query = actionSearchQuery.toLowerCase()
      return (
        action.name.toLowerCase().includes(query) ||
        action.desc.toLowerCase().includes(query)
      )
    })

    const addAction = (action: any) => {
      const newAction = {
        id: Date.now().toString(),
        type: action.id,
        name: action.name,
        description: action.desc,
        config: {}
      }
      const updatedList = [...selectedActionsList, newAction]
      setWorkflow({ ...workflow, selectedActionsList: updatedList })
      setShowActionConfig(updatedList.length - 1)
      setWantsMoreActions(false)
    }

    const removeAction = (index: number) => {
      const updatedList = selectedActionsList.filter((_, i) => i !== index)
      setWorkflow({ ...workflow, selectedActionsList: updatedList })
    }

    // If no actions yet or wants more actions, show action selection
    if (selectedActionsList.length === 0 || wantsMoreActions) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          <div className="text-center">
            <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
            <h3 className="text-lg font-semibold mb-2">
              {selectedActionsList.length === 0 ? 'What should happen next?' : 'Add another action?'}
            </h3>
            <p className="text-muted-foreground text-sm">
              {selectedActionsList.length === 0 
                ? 'Choose an action to perform when your trigger fires'
                : 'Chain multiple actions to create powerful workflows'}
            </p>
          </div>

          {/* Search */}
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search actions..."
                value={actionSearchQuery}
                onChange={(e) => setActionSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Available Actions */}
          {filteredActions.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No actions found matching "{actionSearchQuery}"</p>
            </div>
          ) : (
            <div className="grid gap-3 max-w-2xl mx-auto">
              {filteredActions.map((action) => (
                <Card
                  key={action.id}
                  className="cursor-pointer transition-all hover:shadow-md"
                  onClick={() => addAction(action)}
                >
                  <CardHeader className="p-4">
                    <div className="flex items-center gap-3">
                      <action.icon className="h-5 w-5 text-primary" />
                      <div className="flex-1">
                        <CardTitle className="text-sm">{action.name}</CardTitle>
                        <CardDescription className="text-xs mt-1">{action.desc}</CardDescription>
                      </div>
                      <Plus className="h-5 w-5 text-muted-foreground" />
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          )}

          <div className="flex gap-3 max-w-2xl mx-auto">
            <Button onClick={previousStep} variant="outline" className="w-full">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Previous
            </Button>
            {selectedActionsList.length > 0 && (
              <Button 
                onClick={() => setWantsMoreActions(false)} 
                variant="outline"
                className="w-full"
              >
                Continue without adding
              </Button>
            )}
          </div>
        </motion.div>
      )
    }

    // Show current actions with option to add more
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <div className="text-center">
          <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
          <h3 className="text-lg font-semibold mb-2">Your Action Chain</h3>
          <p className="text-muted-foreground text-sm">
            {selectedActionsList.length} action{selectedActionsList.length !== 1 ? 's' : ''} configured
          </p>
        </div>

        {/* Action Chain Visualization */}
        <div className="max-w-2xl mx-auto space-y-3">
          {/* Trigger */}
          <div className="flex items-center gap-3 p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg border-2 border-purple-200 dark:border-purple-800">
            <Globe className="h-5 w-5 text-purple-600" />
            <div className="flex-1">
              <p className="font-medium">Trigger: Website Content Update</p>
              <p className="text-xs text-muted-foreground">When new content is detected on target URLs</p>
            </div>
          </div>

          {/* Arrow */}
          <div className="flex justify-center">
            <ArrowRight className="h-4 w-4 text-muted-foreground" />
          </div>

          {/* Actions */}
          {selectedActionsList.map((action, index) => (
            <div key={action.id}>
              <Card className="relative">
                <CardHeader className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                      <span className="text-sm font-medium">{index + 1}</span>
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-sm">{action.name}</CardTitle>
                      <CardDescription className="text-xs mt-1">{action.description}</CardDescription>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeAction(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
              </Card>
              {index < selectedActionsList.length - 1 && (
                <div className="flex justify-center py-2">
                  <ArrowRight className="h-4 w-4 text-muted-foreground" />
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Add More Actions Button */}
        <div className="text-center">
          <Button
            variant="outline"
            onClick={() => setWantsMoreActions(true)}
            className="gap-2"
          >
            <Plus className="h-4 w-4" />
            Add Another Action
          </Button>
        </div>

        <div className="flex gap-3">
          <Button onClick={previousStep} variant="outline" className="w-full">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button onClick={nextStep} className="w-full bg-gradient-to-r from-purple-600 to-pink-600">
            Continue <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </motion.div>
    )
  }

  const renderReviewStep = () => (
    <div className="space-y-6">
      <div className="max-w-2xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Automation Summary</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span className="font-medium">Name:</span>
              <span>{workflow.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Browse AI Robot:</span>
              <span>{workflow.browseAI.robotName}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Target URLs:</span>
              <span>{workflow.contentConfig.targetUrls.length} URLs</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">AI Model:</span>
              <span>{workflow.aiSettings.model}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Social Platforms:</span>
              <span>
                {Object.entries(workflow.socialPlatforms)
                  .filter(([_, platform]) => (platform as any).enabled)
                  .map(([name]) => name)
                  .join(', ')}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Schedule:</span>
              <span className="capitalize">{workflow.scheduleDelivery.schedule}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Additional Actions:</span>
              <span>{workflow.selectedActionsList.length} actions</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-between items-center">
        <Button variant="outline" onClick={previousStep} className="gap-2">
          <ChevronLeft className="w-4 h-4" />
          Previous
        </Button>
        <Button onClick={nextStep} className="gap-2 bg-gradient-to-r from-purple-600 to-pink-600">
          Create Automation
          <Check className="w-4 h-4" />
        </Button>
      </div>
    </div>
  )

  const renderCompleteStep = () => (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto">
        <Check className="h-10 w-10 text-green-600 dark:text-green-400" />
      </div>
      <div>
        <h2 className="text-3xl font-bold mb-2">Automation Created!</h2>
        <p className="text-muted-foreground text-lg">
          Your "{workflow.name}" automation is now ready and will start summarizing content automatically.
        </p>
      </div>
      
      <div className="flex flex-col sm:flex-row gap-3 justify-center mt-8">
        <Button 
          onClick={() => setLocation('/dashboard/automations')}
          size="lg"
          className="bg-primary hover:bg-primary/90"
        >
          View All Automations
        </Button>
        <Button 
          onClick={() => setLocation('/dashboard/browse-templates')}
          variant="outline"
          size="lg"
        >
          Browse More Templates
        </Button>
      </div>
    </motion.div>
  )

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'intro':
        return renderIntroStep()
      case 'naming':
        return renderNamingStep()
      case 'browse-ai-setup':
        return renderBrowseAISetup()
      case 'content-config':
        return renderContentConfig()
      case 'ai-settings':
        return renderAISettings()
      case 'social-platforms':
        return renderSocialPlatforms()
      case 'schedule-delivery':
        return renderScheduleDelivery()
      case 'actions-question':
        return renderActionsQuestion()
      case 'actions':
        return renderActionsStep()
      case 'review':
        return renderReviewStep()
      case 'complete':
        return renderCompleteStep()
      default:
        return renderIntroStep()
    }
  }

  const CurrentIcon = stepConfig[currentStep].icon

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => setLocation('/dashboard/browse-templates')}
          className="mb-4"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back to Templates
        </Button>

        {currentStep !== 'complete' && (
          <>
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <CurrentIcon className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">{stepConfig[currentStep].title}</h1>
                <p className="text-muted-foreground text-sm">{stepConfig[currentStep].subtitle}</p>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex justify-between text-xs text-muted-foreground mb-2">
                <span>Step {Object.keys(stepConfig).indexOf(currentStep) + 1} of 10</span>
                <span>{stepConfig[currentStep].progress}% Complete</span>
              </div>
              <Progress value={stepConfig[currentStep].progress} className="h-2" />
            </div>
          </>
        )}
      </div>

      {/* Content */}
      <Card className="p-6">
        {renderCurrentStep()}
      </Card>
    </div>
  )
}