import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { useLocation } from 'wouter'
import { motion } from 'framer-motion'
import { 
  ArrowRight, ChevronLeft, Check, Settings, 
  Sheet, Palette, FolderOpen, Image, Zap,
  Plus, X, Search, Globe, Mail, Calendar, 
  MessageSquare, Hash, Lock, FileImage,
  Download, Upload, Database
} from 'lucide-react'
import { SiGooglesheets, SiGoogledrive, SiCanva } from 'react-icons/si'
import { ActionsSelector } from '@/components/actions-selector'
import { EmojiCelebration } from '@/components/emoji-celebration'

type StepType = 'intro' | 'naming' | 'google-auth' | 'sheets-config' | 'canva-setup' | 'design-mapping' | 'export-settings' | 'drive-config' | 'actions-question' | 'actions' | 'review' | 'complete'

interface WorkflowConfig {
  name: string
  googleAuth: {
    isAuthenticated: boolean
    userEmail: string
    userName: string
    accountId: string
  }
  sheetsConfig: {
    spreadsheetId: string
    sheetName: string
    dataRange: string
    hasHeaders: boolean
    columnMapping: {
      [key: string]: string
    }
  }
  canvaSetup: {
    apiKey: string
    isAuthenticated: boolean
    templateId: string
    templateName: string
    designFormat: string
  }
  designMapping: {
    textElements: Array<{
      elementName: string
      sheetColumn: string
      defaultValue: string
    }>
    imageElements: Array<{
      elementName: string
      sourceType: 'url' | 'drive' | 'static'
      sheetColumn?: string
      staticUrl?: string
    }>
    colorScheme: string
    font: string
  }
  exportSettings: {
    format: string
    quality: string
    dimensions: {
      width: number
      height: number
      unit: string
    }
    includeBleed: boolean
    transparentBackground: boolean
  }
  driveConfig: {
    folderId: string
    folderName: string
    namingPattern: string
    createSubfolders: boolean
    overwriteExisting: boolean
  }
  selectedActionsList: Array<{
    id: string
    type: string
    name: string
    description: string
    config: Record<string, any>
  }>
  wantsActions: boolean | null
}

export default function CanvaDesignAutomationTemplate() {
  const [location, setLocation] = useLocation()
  const [currentStep, setCurrentStep] = useState<StepType>('intro')
  const [workflow, setWorkflow] = useState<WorkflowConfig>({
    name: '',
    googleAuth: {
      isAuthenticated: false,
      userEmail: '',
      userName: '',
      accountId: ''
    },
    sheetsConfig: {
      spreadsheetId: '',
      sheetName: '',
      dataRange: 'A1:Z1000',
      hasHeaders: true,
      columnMapping: {}
    },
    canvaSetup: {
      apiKey: '',
      isAuthenticated: false,
      templateId: '',
      templateName: '',
      designFormat: 'instagram-post'
    },
    designMapping: {
      textElements: [],
      imageElements: [],
      colorScheme: 'brand',
      font: 'default'
    },
    exportSettings: {
      format: 'png',
      quality: 'high',
      dimensions: {
        width: 1080,
        height: 1080,
        unit: 'px'
      },
      includeBleed: false,
      transparentBackground: false
    },
    driveConfig: {
      folderId: '',
      folderName: '',
      namingPattern: '{row_number}_{timestamp}',
      createSubfolders: false,
      overwriteExisting: false
    },
    selectedActionsList: [],
    wantsActions: null
  })
  const [actionSearchQuery, setActionSearchQuery] = useState('')
  const [wantsMoreActions, setWantsMoreActions] = useState(false)
  const [showActionConfig, setShowActionConfig] = useState<number | null>(null)
  const [selectedActions, setSelectedActions] = useState<string[]>([])
  const [showCelebration, setShowCelebration] = useState(false)

  const steps: { id: StepType; title: string; icon: React.ComponentType<{ className?: string }> }[] = [
    { id: 'intro', title: 'Introduction', icon: Palette },
    { id: 'naming', title: 'Name Your Automation', icon: Settings },
    { id: 'google-auth', title: 'Google Authentication', icon: Lock },
    { id: 'sheets-config', title: 'Google Sheets Setup', icon: Sheet },
    { id: 'canva-setup', title: 'Canva Configuration', icon: () => <SiCanva className="w-5 h-5" /> },
    { id: 'design-mapping', title: 'Design Elements Mapping', icon: Image },
    { id: 'export-settings', title: 'Export Settings', icon: Download },
    { id: 'drive-config', title: 'Google Drive Setup', icon: FolderOpen },
    { id: 'actions-question', title: 'Additional Actions', icon: Zap },
    { id: 'actions', title: 'Configure Actions', icon: Settings },
    { id: 'review', title: 'Review & Confirm', icon: Check },
    { id: 'complete', title: 'Complete', icon: Check }
  ]

  const currentStepIndex = steps.findIndex(s => s.id === currentStep)
  const totalSteps = steps.filter(s => 
    s.id !== 'actions' || workflow.wantsActions === true
  ).length

  const nextStep = () => {
    const currentIndex = steps.findIndex(s => s.id === currentStep)
    if (currentIndex < steps.length - 1) {
      let nextStepId = steps[currentIndex + 1].id
      
      if (nextStepId === 'actions' && workflow.wantsActions === false) {
        nextStepId = 'review'
      }
      
      if (currentStep === 'review') {
        setShowCelebration(true)
      }
      
      setCurrentStep(nextStepId)
    }
  }

  const previousStep = () => {
    const currentIndex = steps.findIndex(s => s.id === currentStep)
    if (currentIndex > 0) {
      let prevStepId = steps[currentIndex - 1].id
      
      if (prevStepId === 'actions' && workflow.wantsActions === false) {
        prevStepId = 'actions-question'
      }
      
      setCurrentStep(prevStepId)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent, action: () => void) => {
    if (e.key === 'Enter') {
      action()
    }
  }

  const handleGoogleSignIn = () => {
    setWorkflow(prev => ({
      ...prev,
      googleAuth: {
        isAuthenticated: true,
        userEmail: '<EMAIL>',
        userName: 'John Doe',
        accountId: 'google-123456'
      }
    }))
  }

  const addTextElement = () => {
    setWorkflow(prev => ({
      ...prev,
      designMapping: {
        ...prev.designMapping,
        textElements: [
          ...prev.designMapping.textElements,
          { elementName: '', sheetColumn: '', defaultValue: '' }
        ]
      }
    }))
  }

  const removeTextElement = (index: number) => {
    setWorkflow(prev => ({
      ...prev,
      designMapping: {
        ...prev.designMapping,
        textElements: prev.designMapping.textElements.filter((_, i) => i !== index)
      }
    }))
  }

  const updateTextElement = (index: number, field: string, value: string) => {
    setWorkflow(prev => ({
      ...prev,
      designMapping: {
        ...prev.designMapping,
        textElements: prev.designMapping.textElements.map((elem, i) => 
          i === index ? { ...elem, [field]: value } : elem
        )
      }
    }))
  }

  const addImageElement = () => {
    setWorkflow(prev => ({
      ...prev,
      designMapping: {
        ...prev.designMapping,
        imageElements: [
          ...prev.designMapping.imageElements,
          { elementName: '', sourceType: 'url', sheetColumn: '' }
        ]
      }
    }))
  }

  const removeImageElement = (index: number) => {
    setWorkflow(prev => ({
      ...prev,
      designMapping: {
        ...prev.designMapping,
        imageElements: prev.designMapping.imageElements.filter((_, i) => i !== index)
      }
    }))
  }

  const updateImageElement = (index: number, field: string, value: string) => {
    setWorkflow(prev => ({
      ...prev,
      designMapping: {
        ...prev.designMapping,
        imageElements: prev.designMapping.imageElements.map((elem, i) => 
          i === index ? { ...elem, [field]: value } : elem
        )
      }
    }))
  }

  const handleActionsChange = (actions: string[]) => {
    setSelectedActions(actions)
  }



  const renderIntroStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center mx-auto">
        <SiCanva className="h-10 w-10 text-white" />
      </div>
      <div>
        <h2 className="text-2xl font-bold mb-2">Canva Design Automation</h2>
        <p className="text-muted-foreground max-w-md mx-auto">
          Automate the creation of personalized Canva designs using data from Google Sheets, 
          export them in your preferred format, and save all designs automatically to Google Drive.
        </p>
      </div>
      
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <SiGooglesheets className="h-8 w-8 mx-auto mb-2 text-green-600" />
          <p className="text-xs font-medium">Google Sheets</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <SiCanva className="h-8 w-8 mx-auto mb-2 text-purple-600" />
          <p className="text-xs font-medium">Canva API</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <FileImage className="h-8 w-8 mx-auto mb-2 text-blue-600" />
          <p className="text-xs font-medium">Design Export</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <SiGoogledrive className="h-8 w-8 mx-auto mb-2 text-blue-600" />
          <p className="text-xs font-medium">Google Drive</p>
        </div>
      </div>

      <div className="space-y-4">
        <div className="p-4 bg-muted/20 rounded-lg text-left">
          <h3 className="font-medium mb-2 flex items-center gap-2">
            <Zap className="h-4 w-4" />
            What it does:
          </h3>
          <ul className="text-sm text-muted-foreground space-y-1 ml-6">
            <li>• Reads data from your Google Sheets spreadsheet</li>
            <li>• Creates personalized Canva designs for each row</li>
            <li>• Automatically exports designs in your preferred format</li>
            <li>• Saves all designs to a Google Drive folder</li>
          </ul>
        </div>
      </div>

      <Button 
        onClick={nextStep}
        size="lg"
        className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 min-h-[48px]"
      >
        Start Setup
        <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
    </motion.div>
  )

  const renderNamingStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Settings className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h2 className="text-xl font-semibold mb-2">Name Your Automation</h2>
        <p className="text-muted-foreground">
          Give your automation a memorable name that describes what it does
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="automation-name">Automation Name *</Label>
          <Input
            id="automation-name"
            placeholder="e.g., Social Media Design Generator"
            value={workflow.name}
            onChange={(e) => setWorkflow(prev => ({ ...prev, name: e.target.value }))}
            onKeyDown={(e) => handleKeyPress(e, () => workflow.name.trim() && nextStep())}
            className="mt-2"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Choose a descriptive name you'll easily recognize
          </p>
        </div>
      </div>

      <div className="flex gap-3">
        <Button onClick={previousStep} variant="outline" className="w-full min-h-[44px]">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          disabled={!workflow.name.trim()}
          className="w-full min-h-[44px]"
        >
          Continue to Google Authentication
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderGoogleAuth = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="space-y-4">
            <div>
              <Label>Authentication Type *</Label>
              <div className="mt-2 space-y-2">
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="oauth"
                    name="auth-type"
                    defaultChecked
                    className="rounded-full"
                  />
                  <Label htmlFor="oauth">OAuth (Recommended)</Label>
                </div>
              </div>
            </div>

            {!workflow.googleAuth.isAuthenticated ? (
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Note: Select existing Google account from below or Signin with a different account
                </p>
                <Button 
                  onClick={handleGoogleSignIn}
                  className="w-full"
                  variant="outline"
                >
                  <SiGooglesheets className="mr-2 h-4 w-4" />
                  Sign in with Google
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                      <SiGooglesheets className="h-5 w-5" />
                    </div>
                    <div>
                      <p className="font-medium">Google account: {workflow.googleAuth.userName} ({workflow.googleAuth.userEmail})</p>
                      <p className="text-sm text-muted-foreground">Account ID: {workflow.googleAuth.accountId}</p>
                    </div>
                  </div>
                  <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Connected</span>
                </div>
              </div>
            )}

            <div className="space-y-2">
              <Label>File</Label>
              <div className="p-3 bg-muted/50 rounded-lg">
                <p className="text-sm text-muted-foreground">There are no files, please refresh</p>
              </div>
            </div>
          </div>

      <div className="flex gap-3">
        <Button onClick={previousStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          disabled={!workflow.googleAuth.isAuthenticated}
          className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
        >
          Continue <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderSheetsConfig = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="space-y-4">
          <div>
            <Label htmlFor="spreadsheet-id">Spreadsheet ID *</Label>
            <Input
              id="spreadsheet-id"
              placeholder="e.g., 1a2b3c4d5e6f7g8h9i0j"
              value={workflow.sheetsConfig.spreadsheetId}
              onChange={(e) => setWorkflow(prev => ({
                ...prev,
                sheetsConfig: { ...prev.sheetsConfig, spreadsheetId: e.target.value }
              }))}
              className="mt-1"
            />
            <p className="text-sm text-muted-foreground mt-1">
              Find this in your spreadsheet URL after /d/
            </p>
          </div>

          <div>
            <Label htmlFor="sheet-name">Sheet Name *</Label>
            <Input
              id="sheet-name"
              placeholder="e.g., Design Data"
              value={workflow.sheetsConfig.sheetName}
              onChange={(e) => setWorkflow(prev => ({
                ...prev,
                sheetsConfig: { ...prev.sheetsConfig, sheetName: e.target.value }
              }))}
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="data-range">Data Range</Label>
            <Input
              id="data-range"
              placeholder="e.g., A1:Z1000"
              value={workflow.sheetsConfig.dataRange}
              onChange={(e) => setWorkflow(prev => ({
                ...prev,
                sheetsConfig: { ...prev.sheetsConfig, dataRange: e.target.value }
              }))}
              className="mt-1"
            />
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="has-headers"
              checked={workflow.sheetsConfig.hasHeaders}
              onCheckedChange={(checked) => setWorkflow(prev => ({
                ...prev,
                sheetsConfig: { ...prev.sheetsConfig, hasHeaders: checked as boolean }
              }))}
            />
            <Label htmlFor="has-headers">First row contains headers</Label>
          </div>
        </div>

      <div className="flex gap-3">
        <Button onClick={previousStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          disabled={!workflow.sheetsConfig.spreadsheetId || !workflow.sheetsConfig.sheetName}
          className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
        >
          Continue <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderCanvaSetup = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="space-y-4">
          <div>
            <Label htmlFor="canva-api-key">Canva API Key *</Label>
            <Input
              id="canva-api-key"
              type="password"
              placeholder="Enter your Canva API key"
              value={workflow.canvaSetup.apiKey}
              onChange={(e) => setWorkflow(prev => ({
                ...prev,
                canvaSetup: { ...prev.canvaSetup, apiKey: e.target.value }
              }))}
              className="mt-1"
            />
            <p className="text-sm text-muted-foreground mt-1">
              Get your API key from <a href="https://www.canva.com/developers" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">Canva Developers</a>
            </p>
          </div>

          <div>
            <Label htmlFor="template-id">Template ID</Label>
            <Input
              id="template-id"
              placeholder="e.g., DAF1234567890"
              value={workflow.canvaSetup.templateId}
              onChange={(e) => setWorkflow(prev => ({
                ...prev,
                canvaSetup: { ...prev.canvaSetup, templateId: e.target.value }
              }))}
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="design-format">Design Format</Label>
            <Select 
              value={workflow.canvaSetup.designFormat} 
              onValueChange={(value) => setWorkflow(prev => ({
                ...prev,
                canvaSetup: { ...prev.canvaSetup, designFormat: value }
              }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select design format" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="instagram-post">Instagram Post (1080x1080)</SelectItem>
                <SelectItem value="instagram-story">Instagram Story (1080x1920)</SelectItem>
                <SelectItem value="facebook-post">Facebook Post (1200x630)</SelectItem>
                <SelectItem value="twitter-post">Twitter Post (1200x675)</SelectItem>
                <SelectItem value="linkedin-post">LinkedIn Post (1200x1200)</SelectItem>
                <SelectItem value="youtube-thumbnail">YouTube Thumbnail (1280x720)</SelectItem>
                <SelectItem value="presentation">Presentation (1920x1080)</SelectItem>
                <SelectItem value="a4-document">A4 Document (2480x3508)</SelectItem>
                <SelectItem value="custom">Custom Dimensions</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

      <div className="flex gap-3">
        <Button onClick={previousStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          disabled={!workflow.canvaSetup.apiKey}
          className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
        >
          Continue <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderDesignMapping = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="space-y-6">
          {/* Text Elements */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Text Elements</h4>
              <Button
                variant="outline"
                size="sm"
                onClick={addTextElement}
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Text Element
              </Button>
            </div>
            
            {workflow.designMapping.textElements.map((element, index) => (
              <div key={index} className="grid grid-cols-3 gap-3 items-end">
                <div>
                  <Label>Element Name</Label>
                  <Input
                    placeholder="e.g., Title"
                    value={element.elementName}
                    onChange={(e) => updateTextElement(index, 'elementName', e.target.value)}
                  />
                </div>
                <div>
                  <Label>Sheet Column</Label>
                  <Input
                    placeholder="e.g., A"
                    value={element.sheetColumn}
                    onChange={(e) => updateTextElement(index, 'sheetColumn', e.target.value)}
                  />
                </div>
                <div className="flex gap-2">
                  <Input
                    placeholder="Default value"
                    value={element.defaultValue}
                    onChange={(e) => updateTextElement(index, 'defaultValue', e.target.value)}
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeTextElement(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>

          {/* Image Elements */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Image Elements</h4>
              <Button
                variant="outline"
                size="sm"
                onClick={addImageElement}
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Image Element
              </Button>
            </div>
            
            {workflow.designMapping.imageElements.map((element, index) => (
              <div key={index} className="space-y-3 p-3 bg-muted/30 rounded-lg">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label>Element Name</Label>
                    <Input
                      placeholder="e.g., Background"
                      value={element.elementName}
                      onChange={(e) => updateImageElement(index, 'elementName', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label>Source Type</Label>
                    <Select
                      value={element.sourceType}
                      onValueChange={(value) => updateImageElement(index, 'sourceType', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="url">URL from Sheet</SelectItem>
                        <SelectItem value="drive">Google Drive</SelectItem>
                        <SelectItem value="static">Static URL</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                {element.sourceType === 'url' && (
                  <div>
                    <Label>Sheet Column</Label>
                    <Input
                      placeholder="e.g., B"
                      value={element.sheetColumn || ''}
                      onChange={(e) => updateImageElement(index, 'sheetColumn', e.target.value)}
                    />
                  </div>
                )}
                {element.sourceType === 'static' && (
                  <div>
                    <Label>Static URL</Label>
                    <Input
                      placeholder="https://example.com/image.jpg"
                      value={element.staticUrl || ''}
                      onChange={(e) => updateImageElement(index, 'staticUrl', e.target.value)}
                    />
                  </div>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeImageElement(index)}
                  className="w-full"
                >
                  <X className="h-4 w-4 mr-2" />
                  Remove
                </Button>
              </div>
            ))}
          </div>

          {/* Style Settings */}
          <div className="space-y-4">
            <h4 className="font-medium">Style Settings</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Color Scheme</Label>
                <Select
                  value={workflow.designMapping.colorScheme}
                  onValueChange={(value) => setWorkflow(prev => ({
                    ...prev,
                    designMapping: { ...prev.designMapping, colorScheme: value }
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="brand">Brand Colors</SelectItem>
                    <SelectItem value="vibrant">Vibrant</SelectItem>
                    <SelectItem value="pastel">Pastel</SelectItem>
                    <SelectItem value="monochrome">Monochrome</SelectItem>
                    <SelectItem value="custom">Custom</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>Font</Label>
                <Select
                  value={workflow.designMapping.font}
                  onValueChange={(value) => setWorkflow(prev => ({
                    ...prev,
                    designMapping: { ...prev.designMapping, font: value }
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="default">Default</SelectItem>
                    <SelectItem value="modern">Modern</SelectItem>
                    <SelectItem value="classic">Classic</SelectItem>
                    <SelectItem value="playful">Playful</SelectItem>
                    <SelectItem value="elegant">Elegant</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>

      <div className="flex gap-3">
        <Button onClick={previousStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
        >
          Continue <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderExportSettings = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>File Format</Label>
              <Select
                value={workflow.exportSettings.format}
                onValueChange={(value) => setWorkflow(prev => ({
                  ...prev,
                  exportSettings: { ...prev.exportSettings, format: value }
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="png">PNG (Recommended)</SelectItem>
                  <SelectItem value="jpg">JPG</SelectItem>
                  <SelectItem value="pdf">PDF</SelectItem>
                  <SelectItem value="svg">SVG</SelectItem>
                  <SelectItem value="gif">GIF</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Quality</Label>
              <Select
                value={workflow.exportSettings.quality}
                onValueChange={(value) => setWorkflow(prev => ({
                  ...prev,
                  exportSettings: { ...prev.exportSettings, quality: value }
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low (Faster)</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High (Recommended)</SelectItem>
                  <SelectItem value="maximum">Maximum</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label>Dimensions</Label>
            <div className="grid grid-cols-3 gap-3 mt-2">
              <div>
                <Input
                  type="number"
                  placeholder="Width"
                  value={workflow.exportSettings.dimensions.width}
                  onChange={(e) => setWorkflow(prev => ({
                    ...prev,
                    exportSettings: {
                      ...prev.exportSettings,
                      dimensions: { ...prev.exportSettings.dimensions, width: parseInt(e.target.value) || 0 }
                    }
                  }))}
                />
              </div>
              <div>
                <Input
                  type="number"
                  placeholder="Height"
                  value={workflow.exportSettings.dimensions.height}
                  onChange={(e) => setWorkflow(prev => ({
                    ...prev,
                    exportSettings: {
                      ...prev.exportSettings,
                      dimensions: { ...prev.exportSettings.dimensions, height: parseInt(e.target.value) || 0 }
                    }
                  }))}
                />
              </div>
              <div>
                <Select
                  value={workflow.exportSettings.dimensions.unit}
                  onValueChange={(value) => setWorkflow(prev => ({
                    ...prev,
                    exportSettings: {
                      ...prev.exportSettings,
                      dimensions: { ...prev.exportSettings.dimensions, unit: value }
                    }
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="px">Pixels</SelectItem>
                    <SelectItem value="in">Inches</SelectItem>
                    <SelectItem value="cm">Centimeters</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-bleed"
                checked={workflow.exportSettings.includeBleed}
                onCheckedChange={(checked) => setWorkflow(prev => ({
                  ...prev,
                  exportSettings: { ...prev.exportSettings, includeBleed: checked as boolean }
                }))}
              />
              <Label htmlFor="include-bleed">Include bleed area (for print)</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="transparent-bg"
                checked={workflow.exportSettings.transparentBackground}
                onCheckedChange={(checked) => setWorkflow(prev => ({
                  ...prev,
                  exportSettings: { ...prev.exportSettings, transparentBackground: checked as boolean }
                }))}
              />
              <Label htmlFor="transparent-bg">Transparent background (PNG only)</Label>
            </div>
          </div>
        </div>

      <div className="flex gap-3">
        <Button onClick={previousStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
        >
          Continue <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderDriveConfig = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="space-y-4">
          <div>
            <Label htmlFor="folder-id">Folder ID *</Label>
            <Input
              id="folder-id"
              placeholder="e.g., 1abc2def3ghi4jkl5mno"
              value={workflow.driveConfig.folderId}
              onChange={(e) => setWorkflow(prev => ({
                ...prev,
                driveConfig: { ...prev.driveConfig, folderId: e.target.value }
              }))}
              className="mt-1"
            />
            <p className="text-sm text-muted-foreground mt-1">
              Find this in your folder URL after /folders/
            </p>
          </div>

          <div>
            <Label htmlFor="folder-name">Folder Name</Label>
            <Input
              id="folder-name"
              placeholder="e.g., Canva Exports"
              value={workflow.driveConfig.folderName}
              onChange={(e) => setWorkflow(prev => ({
                ...prev,
                driveConfig: { ...prev.driveConfig, folderName: e.target.value }
              }))}
              className="mt-1"
            />
          </div>

          <div>
            <Label>File Naming Pattern</Label>
            <Select
              value={workflow.driveConfig.namingPattern}
              onValueChange={(value) => setWorkflow(prev => ({
                ...prev,
                driveConfig: { ...prev.driveConfig, namingPattern: value }
              }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="{row_number}_{timestamp}">Row Number + Timestamp</SelectItem>
                <SelectItem value="{column_a}_{date}">Column A Value + Date</SelectItem>
                <SelectItem value="{template_name}_{row_number}">Template Name + Row Number</SelectItem>
                <SelectItem value="{custom}">Custom Pattern</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="create-subfolders"
                checked={workflow.driveConfig.createSubfolders}
                onCheckedChange={(checked) => setWorkflow(prev => ({
                  ...prev,
                  driveConfig: { ...prev.driveConfig, createSubfolders: checked as boolean }
                }))}
              />
              <Label htmlFor="create-subfolders">Create subfolders by date</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="overwrite-existing"
                checked={workflow.driveConfig.overwriteExisting}
                onCheckedChange={(checked) => setWorkflow(prev => ({
                  ...prev,
                  driveConfig: { ...prev.driveConfig, overwriteExisting: checked as boolean }
                }))}
              />
              <Label htmlFor="overwrite-existing">Overwrite existing files</Label>
            </div>
          </div>
        </div>

      <div className="flex gap-3">
        <Button onClick={previousStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          disabled={!workflow.driveConfig.folderId}
          className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
        >
          Continue <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderActionsQuestion = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Zap className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h3 className="text-lg font-semibold mb-2">Do you want to add any tools or actions?</h3>
        <p className="text-muted-foreground text-sm">
          Actions let you send data, trigger notifications, and integrate with other services
        </p>
      </div>
      
      <div className="flex gap-3 max-w-sm mx-auto">
        <Button 
          onClick={() => {
            setWorkflow(prev => ({ ...prev, wantsActions: true }))
            nextStep()
          }}
          className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
        >
          Yes, add actions
        </Button>
        <Button 
          variant="outline"
          onClick={() => {
            setWorkflow(prev => ({ ...prev, wantsActions: false }))
            nextStep()
          }}
          className="w-full"
        >
          No, continue
        </Button>
      </div>

      <div className="flex gap-3">
        <Button onClick={previousStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
      </div>
    </motion.div>
  )

  const renderActionsStep = () => {
    const selectedActionsList = workflow.selectedActionsList || []

    const availableActionsList = [
      { id: 'discord-read', name: 'Discord Read', icon: MessageSquare, desc: 'Read messages from any Discord channel' },
      { id: 'discord-send', name: 'Discord Send', icon: MessageSquare, desc: 'Send messages to any Discord channel' },
      { id: 'gmail-read', name: 'Gmail Read', icon: Mail, desc: 'Read emails from your Gmail account' },
      { id: 'gmail-send', name: 'Gmail Send', icon: Mail, desc: 'Send emails via your Gmail account' },
      { id: 'gcalendar-read', name: 'Google Calendar Read', icon: Calendar, desc: 'Read events from any Google Calendar' },
      { id: 'gcalendar-write', name: 'Google Calendar Write', icon: Calendar, desc: 'Create events on any Google Calendar' },
      { id: 'http-request', name: 'HTTP Request', icon: Globe, desc: 'Make GET, POST, PUT, or DELETE HTTP requests' },
      { id: 'slack-read', name: 'Slack Read', icon: Hash, desc: 'Read messages from a specified Slack channel' },
      { id: 'slack-send', name: 'Slack Send', icon: Hash, desc: 'Send a message to a specified Slack channel' }
    ]

    // Filter actions based on search query
    const filteredActions = availableActionsList.filter(action => {
      const query = actionSearchQuery.toLowerCase()
      return (
        action.name.toLowerCase().includes(query) ||
        action.desc.toLowerCase().includes(query)
      )
    })

    const addAction = (action: any) => {
      const newAction = {
        id: Date.now().toString(),
        type: action.id,
        name: action.name,
        description: action.desc,
        config: {}
      }
      const updatedList = [...selectedActionsList, newAction]
      setWorkflow({ ...workflow, selectedActionsList: updatedList })
      setShowActionConfig(updatedList.length - 1)
      setWantsMoreActions(false)
    }

    const removeAction = (index: number) => {
      const updatedList = selectedActionsList.filter((_, i) => i !== index)
      setWorkflow({ ...workflow, selectedActionsList: updatedList })
    }

    // If no actions yet or wants more actions, show action selection
    if (selectedActionsList.length === 0 || wantsMoreActions) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          <div className="text-center">
            <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
            <h3 className="text-lg font-semibold mb-2">
              {selectedActionsList.length === 0 ? 'What should happen next?' : 'Add another action?'}
            </h3>
            <p className="text-muted-foreground text-sm">
              {selectedActionsList.length === 0 
                ? 'Choose an action to perform when your designs are created'
                : 'Chain multiple actions to create powerful workflows'}
            </p>
          </div>

          {/* Search */}
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search actions..."
                value={actionSearchQuery}
                onChange={(e) => setActionSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Available Actions */}
          {filteredActions.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No actions found matching "{actionSearchQuery}"</p>
            </div>
          ) : (
            <div className="grid gap-3 max-w-2xl mx-auto">
              {filteredActions.map((action) => (
                <Card
                  key={action.id}
                  className="cursor-pointer transition-all hover:shadow-md"
                  onClick={() => addAction(action)}
                >
                  <CardHeader className="p-4">
                    <div className="flex items-center gap-3">
                      <action.icon className="h-5 w-5 text-primary" />
                      <div className="flex-1">
                        <CardTitle className="text-sm">{action.name}</CardTitle>
                        <CardDescription className="text-xs mt-1">{action.desc}</CardDescription>
                      </div>
                      <Plus className="h-5 w-5 text-muted-foreground" />
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          )}

          <div className="flex gap-3 max-w-2xl mx-auto">
            <Button onClick={previousStep} variant="outline" className="w-full">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Previous
            </Button>
            {selectedActionsList.length > 0 && (
              <Button 
                onClick={() => setWantsMoreActions(false)} 
                variant="outline"
                className="w-full"
              >
                Continue without adding
              </Button>
            )}
          </div>
        </motion.div>
      )
    }

    // Show current actions with option to add more
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <div className="text-center">
          <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
          <h3 className="text-lg font-semibold mb-2">Your Action Chain</h3>
          <p className="text-muted-foreground text-sm">
            {selectedActionsList.length} action{selectedActionsList.length !== 1 ? 's' : ''} configured
          </p>
        </div>

        {/* Action Chain Visualization */}
        <div className="max-w-2xl mx-auto space-y-3">
          {/* Trigger */}
          <div className="flex items-center gap-3 p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg border-2 border-purple-200 dark:border-purple-800">
            <SiCanva className="h-5 w-5 text-purple-600" />
            <div className="flex-1">
              <p className="font-medium">Trigger: Design Creation</p>
              <p className="text-xs text-muted-foreground">When Canva designs are exported</p>
            </div>
          </div>

          {/* Arrow */}
          <div className="flex justify-center">
            <ArrowRight className="h-4 w-4 text-muted-foreground" />
          </div>

          {/* Actions */}
          {selectedActionsList.map((action, index) => (
            <div key={action.id}>
              <Card className="relative">
                <CardHeader className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                      <span className="text-sm font-medium">{index + 1}</span>
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-sm">{action.name}</CardTitle>
                      <CardDescription className="text-xs mt-1">{action.description}</CardDescription>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeAction(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
              </Card>
              {index < selectedActionsList.length - 1 && (
                <div className="flex justify-center py-2">
                  <ArrowRight className="h-4 w-4 text-muted-foreground" />
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Add More Actions Button */}
        <div className="text-center">
          <Button
            variant="outline"
            onClick={() => setWantsMoreActions(true)}
            className="gap-2"
          >
            <Plus className="h-4 w-4" />
            Add Another Action
          </Button>
        </div>

        <div className="flex gap-3">
          <Button onClick={previousStep} variant="outline" className="w-full">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button onClick={nextStep} className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700">
            Continue <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </motion.div>
    )
  }

  const renderReviewStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="space-y-6">
        <h3 className="text-lg font-semibold">Automation Summary</h3>
        <div className="space-y-4">
            <div className="flex justify-between">
              <span className="font-medium">Name:</span>
              <span>{workflow.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Google Sheets:</span>
              <span>{workflow.sheetsConfig.sheetName}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Design Format:</span>
              <span className="capitalize">{workflow.canvaSetup.designFormat.replace('-', ' ')}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Export Format:</span>
              <span className="uppercase">{workflow.exportSettings.format}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Google Drive Folder:</span>
              <span>{workflow.driveConfig.folderName || 'Configured'}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Text Elements:</span>
              <span>{workflow.designMapping.textElements.length} configured</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Image Elements:</span>
              <span>{workflow.designMapping.imageElements.length} configured</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Additional Actions:</span>
              <span>{workflow.selectedActionsList.length} actions</span>
            </div>
          </div>
      </div>

      <div className="flex justify-between items-center">
        <Button variant="outline" onClick={previousStep} className="gap-2">
          <ChevronLeft className="w-4 h-4" />
          Previous
        </Button>
        <Button onClick={nextStep} className="gap-2 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700">
          Create Automation
          <Check className="w-4 h-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderCompleteStep = () => (
    <>
      {showCelebration && (
        <EmojiCelebration
          onComplete={() => setShowCelebration(false)}
          duration={3000}
        />
      )}
      
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="text-center space-y-6"
      >
        <div className="w-20 h-20 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto">
          <Check className="h-10 w-10 text-green-600 dark:text-green-400" />
        </div>
        <div>
          <h2 className="text-3xl font-bold mb-2">Automation Created!</h2>
          <p className="text-muted-foreground text-lg">
            Your "{workflow.name}" automation is now ready and will start generating designs automatically.
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3 justify-center mt-8">
          <Button 
            onClick={() => setLocation('/dashboard/automations')}
            size="lg"
            className="bg-primary hover:bg-primary/90"
          >
            View All Automations
          </Button>
          <Button 
            onClick={() => setLocation('/dashboard/browse-templates')}
            variant="outline"
            size="lg"
          >
            Browse More Templates
          </Button>
        </div>
      </motion.div>
    </>
  )

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'intro':
        return renderIntroStep()
      case 'naming':
        return renderNamingStep()
      case 'google-auth':
        return renderGoogleAuth()
      case 'sheets-config':
        return renderSheetsConfig()
      case 'canva-setup':
        return renderCanvaSetup()
      case 'design-mapping':
        return renderDesignMapping()
      case 'export-settings':
        return renderExportSettings()
      case 'drive-config':
        return renderDriveConfig()
      case 'actions-question':
        return renderActionsQuestion()
      case 'actions':
        return renderActionsStep()
      case 'review':
        return renderReviewStep()
      case 'complete':
        return renderCompleteStep()
      default:
        return null
    }
  }

  const CurrentIcon = steps.find(s => s.id === currentStep)?.icon || Settings
  
  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => setLocation('/dashboard/browse-templates')}
          className="mb-4"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back to Templates
        </Button>

        {currentStep !== 'complete' && currentStep !== 'intro' && (
          <>
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <CurrentIcon className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">{steps.find(s => s.id === currentStep)?.title}</h1>
                <p className="text-muted-foreground text-sm">
                  {currentStep === 'naming' && 'Give your automation a memorable name'}
                  {currentStep === 'google-auth' && 'Connect your Google account for Sheets and Drive access'}
                  {currentStep === 'sheets-config' && 'Select the spreadsheet containing your design data'}
                  {currentStep === 'canva-setup' && 'Configure your Canva API connection'}
                  {currentStep === 'design-mapping' && 'Map your spreadsheet data to design elements'}
                  {currentStep === 'export-settings' && 'Choose how your designs will be exported'}
                  {currentStep === 'drive-config' && 'Set up where to save your designs'}
                  {currentStep === 'actions-question' && 'Extend your automation with additional actions'}
                  {currentStep === 'actions' && 'Configure additional actions for your workflow'}
                  {currentStep === 'review' && 'Review your automation configuration'}
                </p>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mb-6">
              {(() => {
                const workflowSteps = steps.filter(s => s.id !== 'intro' && s.id !== 'complete')
                const currentIndex = workflowSteps.findIndex(s => s.id === currentStep)
                const progress = Math.round(((currentIndex + 1) / workflowSteps.length) * 100)
                return (
                  <>
                    <div className="flex justify-between text-xs text-muted-foreground mb-2">
                      <span>Step {currentIndex + 1} of {workflowSteps.length}</span>
                      <span>{progress}% Complete</span>
                    </div>
                    <Progress value={progress} className="h-2" />
                  </>
                )
              })()}
            </div>
          </>
        )}
      </div>

      {/* Content */}
      {currentStep !== 'intro' && currentStep !== 'complete' ? (
        <Card className="p-6">
          {renderCurrentStep()}
        </Card>
      ) : (
        renderCurrentStep()
      )}
    </div>
  )
}