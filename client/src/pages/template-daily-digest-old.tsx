import { useState } from 'react'
import { useLocation } from 'wouter'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  ArrowLeft, 
  ArrowRight, 
  Check, 
  Calendar,
  Mail,
  Clock,
  Brain,
  Send,
  User,
  Settings,
  FileText,
  Globe,
  Key,
  Shield,
  ChevronDown,
  ChevronRight,
  Eye,
  Users,
  Zap,
  Bell
} from 'lucide-react'
import { SiGmail, SiGooglecalendar, SiOpenai, SiAnthropic, SiGoogle } from 'react-icons/si'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Checkbox } from '@/components/ui/checkbox'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { <PERSON>lide<PERSON> } from '@/components/ui/slider'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import { ActionsSelector } from '@/components/actions-selector'

type StepType = 'intro' | 'naming' | 'google-auth' | 'calendar-config' | 'email-config' | 'ai-settings' | 'delivery-settings' | 'actions-question' | 'actions' | 'review' | 'complete'

interface DailyDigestConfig {
  name: string
  googleAuth: {
    isAuthenticated: boolean
    userEmail: string
    userName: string
    accountId: string
  }
  calendarConfig: {
    calendars: string[]
    includeAllDay: boolean
    includeDeclined: boolean
    timeRange: {
      start: string
      end: string
    }
    eventDetails: {
      title: boolean
      time: boolean
      location: boolean
      attendees: boolean
      description: boolean
    }
  }
  emailConfig: {
    filters: {
      unreadOnly: boolean
      labels: string[]
      senders: string[]
      keywords: string[]
      timeRange: number // hours to look back
    }
    summaryDepth: 'brief' | 'detailed' | 'comprehensive'
    includeAttachments: boolean
  }
  aiSettings: {
    provider: 'openai' | 'anthropic' | 'google'
    model: string
    isConfigured: boolean
    summaryStyle: 'professional' | 'casual' | 'executive'
    language: string
    tone: 'informative' | 'friendly' | 'formal'
  }
  deliverySettings: {
    schedule: {
      time: string
      timezone: string
      weekdays: string[]
    }
    email: {
      recipient: string
      subject: string
      format: 'html' | 'text'
      includeActions: boolean
    }
    backup: {
      enabled: boolean
      slackChannel: string
      webhookUrl: string
    }
  }
}

const defaultConfig: DailyDigestConfig = {
  name: '',
  googleAuth: {
    isAuthenticated: false,
    userEmail: '',
    userName: '',
    accountId: ''
  },
  calendarConfig: {
    calendars: [],
    includeAllDay: true,
    includeDeclined: false,
    timeRange: {
      start: '00:00',
      end: '23:59'
    },
    eventDetails: {
      title: true,
      time: true,
      location: true,
      attendees: false,
      description: false
    }
  },
  emailConfig: {
    filters: {
      unreadOnly: true,
      labels: [],
      senders: [],
      keywords: [],
      timeRange: 24
    },
    summaryDepth: 'brief',
    includeAttachments: false
  },
  aiSettings: {
    provider: 'openai',
    model: 'gpt-4o',
    isConfigured: false,
    summaryStyle: 'professional',
    language: 'English',
    tone: 'informative'
  },
  deliverySettings: {
    schedule: {
      time: '07:00',
      timezone: 'America/New_York',
      weekdays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
    },
    email: {
      recipient: '',
      subject: 'Daily Summary - {date}',
      format: 'html',
      includeActions: true
    },
    backup: {
      enabled: false,
      slackChannel: '',
      webhookUrl: ''
    }
  }
}

const steps: { id: StepType; title: string; icon: React.ComponentType<{ className?: string }> }[] = [
  { id: 'intro', title: 'Introduction', icon: FileText },
  { id: 'naming', title: 'Automation Name', icon: User },
  { id: 'google-auth', title: 'Google Authentication', icon: Shield },
  { id: 'calendar-config', title: 'Calendar Configuration', icon: Calendar },
  { id: 'email-config', title: 'Email Configuration', icon: Mail },
  { id: 'ai-settings', title: 'AI & Summary Settings', icon: Brain },
  { id: 'delivery-settings', title: 'Delivery & Schedule', icon: Send },
  { id: 'review', title: 'Review & Create', icon: Check }
]

export default function DailyDigestTemplate() {
  const [, setLocation] = useLocation()
  const [currentStep, setCurrentStep] = useState<StepType>('intro')
  const [config, setConfig] = useState<DailyDigestConfig>(defaultConfig)
  const [isGoogleAuthOpen, setIsGoogleAuthOpen] = useState(false)
  const [isCalendarOpen, setIsCalendarOpen] = useState(false)
  const [isEmailOpen, setIsEmailOpen] = useState(false)
  const [isAIOpen, setIsAIOpen] = useState(false)

  const updateConfig = (updates: Partial<DailyDigestConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }))
  }

  const nextStep = () => {
    const stepOrder: StepType[] = ['intro', 'naming', 'google-auth', 'calendar-config', 'email-config', 'ai-settings', 'delivery-settings', 'review', 'complete']
    const currentIndex = stepOrder.indexOf(currentStep)
    if (currentIndex < stepOrder.length - 1) {
      setCurrentStep(stepOrder[currentIndex + 1])
    }
  }

  const prevStep = () => {
    const stepOrder: StepType[] = ['intro', 'naming', 'google-auth', 'calendar-config', 'email-config', 'ai-settings', 'delivery-settings', 'review', 'complete']
    const currentIndex = stepOrder.indexOf(currentStep)
    if (currentIndex > 0) {
      setCurrentStep(stepOrder[currentIndex - 1])
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      if (currentStep !== 'complete') {
        nextStep()
      }
    }
  }

  const getCurrentStepIndex = () => {
    return steps.findIndex(step => step.id === currentStep)
  }

  const mockGoogleAuth = () => {
    updateConfig({
      googleAuth: {
        isAuthenticated: true,
        userEmail: '<EMAIL>',
        userName: 'John Doe',
        accountId: 'google_123456789'
      }
    })
  }

  const mockCalendars = ['Primary', 'Work Calendar', 'Personal', 'Team Meetings']
  const mockLabels = ['Important', 'Work', 'Projects', 'Clients', 'Notifications']
  const weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
  const timezones = [
    'America/New_York',
    'America/Los_Angeles', 
    'America/Chicago',
    'Europe/London',
    'Europe/Paris',
    'Asia/Tokyo',
    'Asia/Shanghai',
    'Australia/Sydney'
  ]

  if (currentStep === 'complete') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl p-8 max-w-2xl w-full text-center"
        >
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="w-20 h-20 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-6"
          >
            <Check className="w-10 h-10 text-green-600 dark:text-green-400" />
          </motion.div>
          
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="text-3xl font-bold text-gray-900 dark:text-white mb-4"
          >
            Automation Created!
          </motion.h1>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="text-gray-600 dark:text-gray-300 text-lg mb-8"
          >
            Your Daily Calendar + Email Summary Digest automation "{config.name}" has been successfully created and will start delivering your personalized daily summaries.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Button
              onClick={() => setLocation('/dashboard')}
              variant="outline"
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Dashboard
            </Button>
            <Button
              onClick={() => setLocation('/dashboard/browse-templates')}
              className="bg-[#155DB8] hover:bg-[#155DB8]/90 text-white flex items-center gap-2"
            >
              <Zap className="w-4 h-4" />
              Create Another Automation
            </Button>
          </motion.div>
        </motion.div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setLocation('/dashboard/browse-templates')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Templates
            </Button>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-[#155DB8]/10 rounded-lg">
                <Bell className="h-6 w-6 text-[#155DB8]" />
              </div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900 dark:text-white">Daily Calendar + Email Summary Digest</h1>
                <p className="text-sm text-gray-600 dark:text-gray-400">Automated morning briefings for busy professionals</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-3">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Step {getCurrentStepIndex() + 1} of {steps.length}
            </span>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {Math.round(((getCurrentStepIndex() + 1) / steps.length) * 100)}% Complete
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <motion.div
              className="bg-[#155DB8] h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${((getCurrentStepIndex() + 1) / steps.length) * 100}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto p-6">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            onKeyDown={handleKeyPress}
            tabIndex={0}
            className="focus:outline-none"
          >
            {currentStep === 'intro' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bell className="h-5 w-5 text-[#155DB8]" />
                    Daily Calendar + Email Summary Digest
                  </CardTitle>
                  <CardDescription>
                    Perfect for busy professionals and startup founders who need a comprehensive morning briefing
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h3 className="font-semibold text-gray-900 dark:text-white">What this automation does:</h3>
                      <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                        <li className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-[#155DB8]" />
                          Fetches today's Google Calendar events with details
                        </li>
                        <li className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-[#155DB8]" />
                          Summarizes unread Gmail highlights and important messages
                        </li>
                        <li className="flex items-center gap-2">
                          <Brain className="h-4 w-4 text-[#155DB8]" />
                          Creates AI-powered intelligent summaries
                        </li>
                        <li className="flex items-center gap-2">
                          <Send className="h-4 w-4 text-[#155DB8]" />
                          Delivers personalized digest via email at scheduled time
                        </li>
                      </ul>
                    </div>
                    <div className="space-y-4">
                      <h3 className="font-semibold text-gray-900 dark:text-white">Perfect for:</h3>
                      <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                        <li className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-green-600" />
                          Busy professionals managing multiple meetings
                        </li>
                        <li className="flex items-center gap-2">
                          <Zap className="h-4 w-4 text-green-600" />
                          Startup founders with packed schedules
                        </li>
                        <li className="flex items-center gap-2">
                          <Globe className="h-4 w-4 text-green-600" />
                          Remote workers coordinating across time zones
                        </li>
                        <li className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-green-600" />
                          Anyone wanting efficient morning preparation
                        </li>
                      </ul>
                    </div>
                  </div>
                  
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                    <h4 className="font-medium text-blue-900 dark:text-blue-300 mb-2">What you'll get:</h4>
                    <p className="text-sm text-blue-700 dark:text-blue-400">
                      A personalized email every morning with your day's calendar events, prioritized email highlights, 
                      and AI-generated insights to help you start your day prepared and focused.
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}

            {currentStep === 'naming' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5 text-[#155DB8]" />
                    Name Your Automation
                  </CardTitle>
                  <CardDescription>
                    Give your Daily Summary Digest a descriptive name
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="automation-name">Automation Name *</Label>
                    <Input
                      id="automation-name"
                      placeholder="e.g., Daily Executive Briefing, Morning Productivity Summary"
                      value={config.name}
                      onChange={(e) => updateConfig({ name: e.target.value })}
                      className="text-lg"
                      autoFocus
                    />
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      This name will help you identify this automation in your dashboard
                    </p>
                  </div>
                  
                  <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">Suggested names:</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {[
                        'Daily Executive Briefing',
                        'Morning Productivity Summary',
                        'Founder\'s Daily Digest',
                        'Professional Morning Brief',
                        'Daily Schedule & Email Overview',
                        'Smart Morning Assistant'
                      ].map((suggestion) => (
                        <Button
                          key={suggestion}
                          variant="ghost"
                          size="sm"
                          className="justify-start text-left h-auto p-2"
                          onClick={() => updateConfig({ name: suggestion })}
                        >
                          {suggestion}
                        </Button>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {currentStep === 'google-auth' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5 text-[#155DB8]" />
                    Google Authentication
                  </CardTitle>
                  <CardDescription>
                    Connect your Google account to access Calendar and Gmail data
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <Label className="text-base font-medium">Authentication Type *</Label>
                    <RadioGroup value="google" className="space-y-3">
                      <div className="flex items-center space-x-2 p-3 border rounded-lg bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
                        <RadioGroupItem value="google" id="google" checked />
                        <div className="flex items-center gap-2 flex-1">
                          <div className="p-1 bg-white rounded">
                            <SiGoogle className="h-4 w-4" />
                          </div>
                          <Label htmlFor="google" className="flex-1 cursor-pointer">
                            Google Account (Gmail + Calendar)
                          </Label>
                        </div>
                      </div>
                    </RadioGroup>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Note: Select existing Google account from below or Sign in with a different account
                    </p>
                    
                    {!config.googleAuth.isAuthenticated ? (
                      <div className="space-y-4">
                        <Button
                          onClick={mockGoogleAuth}
                          className="w-full bg-[#4285f4] hover:bg-[#3367d6] text-white flex items-center gap-2"
                        >
                          <SiGoogle className="h-4 w-4" />
                          Sign in with Google
                        </Button>
                        
                        <div className="text-center">
                          <Button variant="link" className="text-sm text-gray-500">
                            Sign in with a different account
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="p-2 bg-white rounded-full">
                                <SiGoogle className="h-4 w-4" />
                              </div>
                              <div>
                                <p className="font-medium text-gray-900 dark:text-white">
                                  Google account: {config.googleAuth.userName} ({config.googleAuth.userEmail})
                                </p>
                                <div className="flex items-center gap-2 mt-1">
                                  <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    Connected
                                  </Badge>
                                  <span className="text-sm text-gray-500">Gmail + Calendar access granted</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <Button
                          variant="outline"
                          onClick={() => updateConfig({ googleAuth: { ...config.googleAuth, isAuthenticated: false } })}
                          className="w-full"
                        >
                          Sign in with a different account
                        </Button>
                      </div>
                    )}
                  </div>

                  <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                    <h4 className="font-medium text-yellow-900 dark:text-yellow-300 mb-2">Required Permissions:</h4>
                    <ul className="text-sm text-yellow-700 dark:text-yellow-400 space-y-1">
                      <li>• Read Google Calendar events</li>
                      <li>• Read Gmail messages and labels</li>
                      <li>• Basic profile information</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            )}

            {currentStep === 'calendar-config' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5 text-[#155DB8]" />
                    Calendar Configuration
                  </CardTitle>
                  <CardDescription>
                    Configure which calendar events to include in your daily summary
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <Label className="text-base font-medium">Select Calendars</Label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {mockCalendars.map((calendar) => (
                        <div key={calendar} className="flex items-center space-x-2 p-3 border rounded-lg">
                          <Checkbox
                            id={calendar}
                            checked={config.calendarConfig.calendars.includes(calendar)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                updateConfig({
                                  calendarConfig: {
                                    ...config.calendarConfig,
                                    calendars: [...config.calendarConfig.calendars, calendar]
                                  }
                                })
                              } else {
                                updateConfig({
                                  calendarConfig: {
                                    ...config.calendarConfig,
                                    calendars: config.calendarConfig.calendars.filter(c => c !== calendar)
                                  }
                                })
                              }
                            }}
                          />
                          <Label htmlFor={calendar} className="flex-1 cursor-pointer">
                            {calendar}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <Separator />

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <Label className="text-base font-medium">Time Range</Label>
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <Label htmlFor="start-time" className="w-16">From:</Label>
                          <Input
                            id="start-time"
                            type="time"
                            value={config.calendarConfig.timeRange.start}
                            onChange={(e) => updateConfig({
                              calendarConfig: {
                                ...config.calendarConfig,
                                timeRange: { ...config.calendarConfig.timeRange, start: e.target.value }
                              }
                            })}
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <Label htmlFor="end-time" className="w-16">To:</Label>
                          <Input
                            id="end-time"
                            type="time"
                            value={config.calendarConfig.timeRange.end}
                            onChange={(e) => updateConfig({
                              calendarConfig: {
                                ...config.calendarConfig,
                                timeRange: { ...config.calendarConfig.timeRange, end: e.target.value }
                              }
                            })}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <Label className="text-base font-medium">Event Options</Label>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <Label htmlFor="all-day">Include all-day events</Label>
                          <Switch
                            id="all-day"
                            checked={config.calendarConfig.includeAllDay}
                            onCheckedChange={(checked) => updateConfig({
                              calendarConfig: { ...config.calendarConfig, includeAllDay: checked }
                            })}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <Label htmlFor="declined">Include declined events</Label>
                          <Switch
                            id="declined"
                            checked={config.calendarConfig.includeDeclined}
                            onCheckedChange={(checked) => updateConfig({
                              calendarConfig: { ...config.calendarConfig, includeDeclined: checked }
                            })}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <Label className="text-base font-medium">Event Details to Include</Label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {Object.entries(config.calendarConfig.eventDetails).map(([key, value]) => (
                        <div key={key} className="flex items-center space-x-2">
                          <Checkbox
                            id={key}
                            checked={value}
                            onCheckedChange={(checked) => updateConfig({
                              calendarConfig: {
                                ...config.calendarConfig,
                                eventDetails: { ...config.calendarConfig.eventDetails, [key]: checked }
                              }
                            })}
                          />
                          <Label htmlFor={key} className="capitalize cursor-pointer">
                            {key.replace(/([A-Z])/g, ' $1').toLowerCase()}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {currentStep === 'email-config' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Mail className="h-5 w-5 text-[#155DB8]" />
                    Email Configuration
                  </CardTitle>
                  <CardDescription>
                    Configure which emails to include in your daily summary
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <Label className="text-base font-medium">Email Filters</Label>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <Label htmlFor="unread-only">Unread emails only</Label>
                          <Switch
                            id="unread-only"
                            checked={config.emailConfig.filters.unreadOnly}
                            onCheckedChange={(checked) => updateConfig({
                              emailConfig: {
                                ...config.emailConfig,
                                filters: { ...config.emailConfig.filters, unreadOnly: checked }
                              }
                            })}
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="time-range">Look back (hours)</Label>
                          <Select
                            value={config.emailConfig.filters.timeRange.toString()}
                            onValueChange={(value) => updateConfig({
                              emailConfig: {
                                ...config.emailConfig,
                                filters: { ...config.emailConfig.filters, timeRange: parseInt(value) }
                              }
                            })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="12">12 hours</SelectItem>
                              <SelectItem value="24">24 hours</SelectItem>
                              <SelectItem value="48">48 hours</SelectItem>
                              <SelectItem value="72">72 hours</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <Label className="text-base font-medium">Summary Options</Label>
                      <div className="space-y-3">
                        <div className="space-y-2">
                          <Label htmlFor="summary-depth">Summary depth</Label>
                          <Select
                            value={config.emailConfig.summaryDepth}
                            onValueChange={(value: 'brief' | 'detailed' | 'comprehensive') => updateConfig({
                              emailConfig: { ...config.emailConfig, summaryDepth: value }
                            })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="brief">Brief (key points only)</SelectItem>
                              <SelectItem value="detailed">Detailed (with context)</SelectItem>
                              <SelectItem value="comprehensive">Comprehensive (full analysis)</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="flex items-center justify-between">
                          <Label htmlFor="attachments">Include attachment info</Label>
                          <Switch
                            id="attachments"
                            checked={config.emailConfig.includeAttachments}
                            onCheckedChange={(checked) => updateConfig({
                              emailConfig: { ...config.emailConfig, includeAttachments: checked }
                            })}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <Label className="text-base font-medium">Gmail Labels (Optional)</Label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {mockLabels.map((label) => (
                        <div key={label} className="flex items-center space-x-2">
                          <Checkbox
                            id={`label-${label}`}
                            checked={config.emailConfig.filters.labels.includes(label)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                updateConfig({
                                  emailConfig: {
                                    ...config.emailConfig,
                                    filters: {
                                      ...config.emailConfig.filters,
                                      labels: [...config.emailConfig.filters.labels, label]
                                    }
                                  }
                                })
                              } else {
                                updateConfig({
                                  emailConfig: {
                                    ...config.emailConfig,
                                    filters: {
                                      ...config.emailConfig.filters,
                                      labels: config.emailConfig.filters.labels.filter(l => l !== label)
                                    }
                                  }
                                })
                              }
                            }}
                          />
                          <Label htmlFor={`label-${label}`} className="cursor-pointer">
                            {label}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-4">
                    <Label htmlFor="keywords" className="text-base font-medium">Priority Keywords (Optional)</Label>
                    <Input
                      id="keywords"
                      placeholder="e.g., urgent, deadline, meeting, client (comma separated)"
                      value={config.emailConfig.filters.keywords.join(', ')}
                      onChange={(e) => updateConfig({
                        emailConfig: {
                          ...config.emailConfig,
                          filters: {
                            ...config.emailConfig.filters,
                            keywords: e.target.value.split(',').map(k => k.trim()).filter(k => k)
                          }
                        }
                      })}
                    />
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Emails containing these keywords will be prioritized in the summary
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}

            {currentStep === 'ai-settings' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Brain className="h-5 w-5 text-[#155DB8]" />
                    AI & Summary Settings
                  </CardTitle>
                  <CardDescription>
                    Configure how AI will analyze and summarize your data
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <Label className="text-base font-medium">AI Provider</Label>
                      <RadioGroup
                        value={config.aiSettings.provider}
                        onValueChange={(value: 'openai' | 'anthropic' | 'google') => updateConfig({
                          aiSettings: { ...config.aiSettings, provider: value }
                        })}
                        className="space-y-3"
                      >
                        <div className="flex items-center space-x-2 p-3 border rounded-lg">
                          <RadioGroupItem value="openai" id="openai" />
                          <div className="flex items-center gap-2 flex-1">
                            <SiOpenai className="h-4 w-4" />
                            <Label htmlFor="openai" className="flex-1 cursor-pointer">OpenAI (GPT-4o)</Label>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 p-3 border rounded-lg">
                          <RadioGroupItem value="anthropic" id="anthropic" />
                          <div className="flex items-center gap-2 flex-1">
                            <SiAnthropic className="h-4 w-4" />
                            <Label htmlFor="anthropic" className="flex-1 cursor-pointer">Anthropic (Claude)</Label>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 p-3 border rounded-lg">
                          <RadioGroupItem value="google" id="google" />
                          <div className="flex items-center gap-2 flex-1">
                            <SiGoogle className="h-4 w-4" />
                            <Label htmlFor="google" className="flex-1 cursor-pointer">Google (Gemini)</Label>
                          </div>
                        </div>
                      </RadioGroup>
                    </div>

                    <div className="space-y-4">
                      <Label className="text-base font-medium">Summary Style</Label>
                      <Select
                        value={config.aiSettings.summaryStyle}
                        onValueChange={(value: 'professional' | 'casual' | 'executive') => updateConfig({
                          aiSettings: { ...config.aiSettings, summaryStyle: value }
                        })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="professional">Professional (business tone)</SelectItem>
                          <SelectItem value="casual">Casual (friendly tone)</SelectItem>
                          <SelectItem value="executive">Executive (concise, action-oriented)</SelectItem>
                        </SelectContent>
                      </Select>

                      <div className="space-y-2">
                        <Label htmlFor="tone">Communication Tone</Label>
                        <Select
                          value={config.aiSettings.tone}
                          onValueChange={(value: 'informative' | 'friendly' | 'formal') => updateConfig({
                            aiSettings: { ...config.aiSettings, tone: value }
                          })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="informative">Informative</SelectItem>
                            <SelectItem value="friendly">Friendly</SelectItem>
                            <SelectItem value="formal">Formal</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="language">Language</Label>
                        <Select
                          value={config.aiSettings.language}
                          onValueChange={(value) => updateConfig({
                            aiSettings: { ...config.aiSettings, language: value }
                          })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="English">English</SelectItem>
                            <SelectItem value="Spanish">Spanish</SelectItem>
                            <SelectItem value="French">French</SelectItem>
                            <SelectItem value="German">German</SelectItem>
                            <SelectItem value="Italian">Italian</SelectItem>
                            <SelectItem value="Portuguese">Portuguese</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                    <h4 className="font-medium text-blue-900 dark:text-blue-300 mb-2">Summary Preview</h4>
                    <p className="text-sm text-blue-700 dark:text-blue-400">
                      Based on your {config.aiSettings.summaryStyle} style and {config.aiSettings.tone} tone, 
                      your daily digest will be generated in {config.aiSettings.language} with a focus on 
                      actionable insights and clear priorities for your day ahead.
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}

            {currentStep === 'delivery-settings' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Send className="h-5 w-5 text-[#155DB8]" />
                    Delivery & Schedule
                  </CardTitle>
                  <CardDescription>
                    Configure when and how you'll receive your daily summary
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <Label className="text-base font-medium">Schedule</Label>
                      <div className="space-y-3">
                        <div className="space-y-2">
                          <Label htmlFor="delivery-time">Delivery time</Label>
                          <Input
                            id="delivery-time"
                            type="time"
                            value={config.deliverySettings.schedule.time}
                            onChange={(e) => updateConfig({
                              deliverySettings: {
                                ...config.deliverySettings,
                                schedule: { ...config.deliverySettings.schedule, time: e.target.value }
                              }
                            })}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="timezone">Timezone</Label>
                          <Select
                            value={config.deliverySettings.schedule.timezone}
                            onValueChange={(value) => updateConfig({
                              deliverySettings: {
                                ...config.deliverySettings,
                                schedule: { ...config.deliverySettings.schedule, timezone: value }
                              }
                            })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {timezones.map((tz) => (
                                <SelectItem key={tz} value={tz}>{tz}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <Label className="text-base font-medium">Days of Week</Label>
                      <div className="grid grid-cols-2 gap-2">
                        {weekdays.map((day) => (
                          <div key={day} className="flex items-center space-x-2">
                            <Checkbox
                              id={day}
                              checked={config.deliverySettings.schedule.weekdays.includes(day)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  updateConfig({
                                    deliverySettings: {
                                      ...config.deliverySettings,
                                      schedule: {
                                        ...config.deliverySettings.schedule,
                                        weekdays: [...config.deliverySettings.schedule.weekdays, day]
                                      }
                                    }
                                  })
                                } else {
                                  updateConfig({
                                    deliverySettings: {
                                      ...config.deliverySettings,
                                      schedule: {
                                        ...config.deliverySettings.schedule,
                                        weekdays: config.deliverySettings.schedule.weekdays.filter(d => d !== day)
                                      }
                                    }
                                  })
                                }
                              }}
                            />
                            <Label htmlFor={day} className="cursor-pointer text-sm">
                              {day}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <Label className="text-base font-medium">Email Settings</Label>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="recipient">Recipient email</Label>
                        <Input
                          id="recipient"
                          type="email"
                          placeholder="<EMAIL>"
                          value={config.deliverySettings.email.recipient}
                          onChange={(e) => updateConfig({
                            deliverySettings: {
                              ...config.deliverySettings,
                              email: { ...config.deliverySettings.email, recipient: e.target.value }
                            }
                          })}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="subject">Subject line</Label>
                        <Input
                          id="subject"
                          placeholder="Daily Summary - {date}"
                          value={config.deliverySettings.email.subject}
                          onChange={(e) => updateConfig({
                            deliverySettings: {
                              ...config.deliverySettings,
                              email: { ...config.deliverySettings.email, subject: e.target.value }
                            }
                          })}
                        />
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="format">Email format</Label>
                        <Select
                          value={config.deliverySettings.email.format}
                          onValueChange={(value: 'html' | 'text') => updateConfig({
                            deliverySettings: {
                              ...config.deliverySettings,
                              email: { ...config.deliverySettings.email, format: value }
                            }
                          })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="html">HTML (rich formatting)</SelectItem>
                            <SelectItem value="text">Plain text</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="flex items-center justify-between mt-6">
                        <Label htmlFor="actions">Include action buttons</Label>
                        <Switch
                          id="actions"
                          checked={config.deliverySettings.email.includeActions}
                          onCheckedChange={(checked) => updateConfig({
                            deliverySettings: {
                              ...config.deliverySettings,
                              email: { ...config.deliverySettings.email, includeActions: checked }
                            }
                          })}
                        />
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label className="text-base font-medium">Backup Delivery (Optional)</Label>
                      <Switch
                        checked={config.deliverySettings.backup.enabled}
                        onCheckedChange={(checked) => updateConfig({
                          deliverySettings: {
                            ...config.deliverySettings,
                            backup: { ...config.deliverySettings.backup, enabled: checked }
                          }
                        })}
                      />
                    </div>

                    {config.deliverySettings.backup.enabled && (
                      <div className="grid md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="slack">Slack channel (optional)</Label>
                          <Input
                            id="slack"
                            placeholder="#daily-summary"
                            value={config.deliverySettings.backup.slackChannel}
                            onChange={(e) => updateConfig({
                              deliverySettings: {
                                ...config.deliverySettings,
                                backup: { ...config.deliverySettings.backup, slackChannel: e.target.value }
                              }
                            })}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="webhook">Webhook URL (optional)</Label>
                          <Input
                            id="webhook"
                            placeholder="https://hooks.slack.com/..."
                            value={config.deliverySettings.backup.webhookUrl}
                            onChange={(e) => updateConfig({
                              deliverySettings: {
                                ...config.deliverySettings,
                                backup: { ...config.deliverySettings.backup, webhookUrl: e.target.value }
                              }
                            })}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {currentStep === 'review' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Check className="h-5 w-5 text-[#155DB8]" />
                    Review & Create
                  </CardTitle>
                  <CardDescription>
                    Review your configuration and create the automation
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Automation Details</h3>
                        <div className="space-y-2 text-sm">
                          <div><span className="font-medium">Name:</span> {config.name}</div>
                          <div><span className="font-medium">Google Account:</span> {config.googleAuth.userEmail}</div>
                          <div><span className="font-medium">AI Provider:</span> {config.aiSettings.provider}</div>
                        </div>
                      </div>

                      <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Calendar Settings</h3>
                        <div className="space-y-2 text-sm">
                          <div><span className="font-medium">Calendars:</span> {config.calendarConfig.calendars.length} selected</div>
                          <div><span className="font-medium">Time range:</span> {config.calendarConfig.timeRange.start} - {config.calendarConfig.timeRange.end}</div>
                          <div><span className="font-medium">All-day events:</span> {config.calendarConfig.includeAllDay ? 'Yes' : 'No'}</div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Email Settings</h3>
                        <div className="space-y-2 text-sm">
                          <div><span className="font-medium">Look back:</span> {config.emailConfig.filters.timeRange} hours</div>
                          <div><span className="font-medium">Summary depth:</span> {config.emailConfig.summaryDepth}</div>
                          <div><span className="font-medium">Labels:</span> {config.emailConfig.filters.labels.length} selected</div>
                        </div>
                      </div>

                      <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Delivery Schedule</h3>
                        <div className="space-y-2 text-sm">
                          <div><span className="font-medium">Time:</span> {config.deliverySettings.schedule.time}</div>
                          <div><span className="font-medium">Days:</span> {config.deliverySettings.schedule.weekdays.join(', ')}</div>
                          <div><span className="font-medium">Recipient:</span> {config.deliverySettings.email.recipient}</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                    <h4 className="font-medium text-blue-900 dark:text-blue-300 mb-2">What happens next?</h4>
                    <ul className="text-sm text-blue-700 dark:text-blue-400 space-y-1">
                      <li>• Your automation will be created and scheduled to run daily</li>
                      <li>• The first digest will be sent tomorrow at {config.deliverySettings.schedule.time}</li>
                      <li>• You can modify settings or pause the automation anytime from your dashboard</li>
                      <li>• Check your email for delivery confirmations and summaries</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            )}
          </motion.div>
        </AnimatePresence>

        {/* Navigation */}
        <div className="flex justify-between items-center mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
          <Button
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 'intro'}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Previous
          </Button>

          <div className="text-sm text-gray-500 dark:text-gray-400">
            Press Enter to continue
          </div>

          <Button
            onClick={nextStep}
            disabled={
              (currentStep === 'naming' && !config.name.trim()) ||
              (currentStep === 'google-auth' && !config.googleAuth.isAuthenticated) ||
              (currentStep === 'calendar-config' && config.calendarConfig.calendars.length === 0) ||
              (currentStep === 'delivery-settings' && (!config.deliverySettings.email.recipient || config.deliverySettings.schedule.weekdays.length === 0))
            }
            className="bg-[#155DB8] hover:bg-[#155DB8]/90 text-white flex items-center gap-2"
          >
            {currentStep === 'review' ? 'Create Automation' : 'Next'}
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}