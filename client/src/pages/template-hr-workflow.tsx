import { useState } from 'react'
import { motion } from 'framer-motion'
import { useLocation } from 'wouter'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Checkbox } from "@/components/ui/checkbox"
import { Slider } from "@/components/ui/slider"
import { 
  Users, FolderOpen, Brain, FileText, TableProperties, Settings, Check, 
  ArrowRight, ChevronLeft, X, Eye, Plus, Calendar, MessageSquare, Hash, Globe, Mail, Search
} from 'lucide-react'
import { SiGoogledrive, SiGooglesheets, Si<PERSON><PERSON>ai, SiAnthropic, SiGoogle } from 'react-icons/si'

type StepType = 'intro' | 'naming' | 'drive-setup' | 'ai-parsing' | 'evaluation-criteria' | 'google-sheets' | 'actions-question' | 'actions' | 'review' | 'complete'

interface HRWorkflowConfig {
  name: string
  googleAuth: {
    isAuthenticated: boolean
    userEmail: string
    userName: string
    accountId: string
  }
  driveSetup: {
    folderId: string
    fileTypes: string[]
    monitoringFrequency: number
    timeUnit: 'minutes' | 'hours'
  }
  aiParsing: {
    provider: 'openai' | 'anthropic' | 'google'
    model: string
    extractFields: {
      name: boolean
      email: boolean
      phone: boolean
      experience: boolean
      education: boolean
      skills: boolean
      summary: boolean
    }
  }
  evaluationCriteria: {
    criteria: Array<{
      id: string
      name: string
      weight: number
      description: string
    }>
    passingScore: number
    autoReject: boolean
    rejectThreshold: number
  }
  googleSheets: {
    spreadsheetId: string
    sheetName: string
    columns: {
      name: string
      email: string
      phone: string
      score: string
      status: string
      summary: string
      timestamp: string
    }
    filesList: string[]
  }
  selectedActionsList: Array<{
    id: string
    type: string
    name: string
    description: string
    config: Record<string, any>
  }>
  wantsActions: boolean | null
}

export default function HRWorkflowTemplate() {
  const [, setLocation] = useLocation()
  const [currentStep, setCurrentStep] = useState<StepType>('intro')
  const [showActionConfig, setShowActionConfig] = useState<number | null>(null)
  const [actionSearchQuery, setActionSearchQuery] = useState('')
  const [config, setConfig] = useState<HRWorkflowConfig>({
    name: '',
    googleAuth: {
      isAuthenticated: false,
      userEmail: '',
      userName: '',
      accountId: ''
    },
    driveSetup: {
      folderId: '',
      fileTypes: ['pdf', 'docx', 'doc'],
      monitoringFrequency: 10,
      timeUnit: 'minutes'
    },
    aiParsing: {
      provider: 'openai',
      model: 'gpt-4o',
      extractFields: {
        name: true,
        email: true,
        phone: true,
        experience: true,
        education: true,
        skills: true,
        summary: true
      }
    },
    evaluationCriteria: {
      criteria: [
        { id: '1', name: 'Relevant Experience', weight: 30, description: 'Years and relevance of work experience' },
        { id: '2', name: 'Education', weight: 20, description: 'Educational qualifications and certifications' },
        { id: '3', name: 'Technical Skills', weight: 25, description: 'Required technical competencies' },
        { id: '4', name: 'Communication', weight: 15, description: 'Written and verbal communication skills' },
        { id: '5', name: 'Culture Fit', weight: 10, description: 'Alignment with company values' }
      ],
      passingScore: 70,
      autoReject: true,
      rejectThreshold: 40
    },
    googleSheets: {
      spreadsheetId: '',
      sheetName: 'Candidates',
      columns: {
        name: 'A',
        email: 'B',
        phone: 'C',
        score: 'D',
        status: 'E',
        summary: 'F',
        timestamp: 'G'
      },
      filesList: []
    },
    selectedActionsList: [],
    wantsActions: null
  })

  const stepConfig = {
    intro: { 
      title: 'AI Automated HR Workflow', 
      subtitle: 'Streamline your recruitment process with AI',
      icon: Users,
      progress: 0
    },
    naming: {
      title: 'Name Your Automation',
      subtitle: 'Give your automation a memorable name',
      icon: Settings,
      progress: 12
    },
    'drive-setup': { 
      title: 'Google Drive Setup', 
      subtitle: 'Configure resume monitoring',
      icon: SiGoogledrive,
      progress: 24
    },
    'ai-parsing': { 
      title: 'AI Parsing Configuration', 
      subtitle: 'Set up resume extraction',
      icon: Brain,
      progress: 36
    },
    'evaluation-criteria': { 
      title: 'Evaluation Criteria', 
      subtitle: 'Define scoring parameters',
      icon: FileText,
      progress: 48
    },
    'google-sheets': { 
      title: 'Google Sheets Integration', 
      subtitle: 'Configure data logging',
      icon: SiGooglesheets,
      progress: 60
    },
    'actions-question': { 
      title: 'Additional Actions', 
      subtitle: 'Add more capabilities to your automation',
      icon: Plus,
      progress: 72
    },
    'actions': { 
      title: 'Configure Actions', 
      subtitle: 'Set up your selected actions',
      icon: Settings,
      progress: 84
    },
    review: { 
      title: 'Review Configuration', 
      subtitle: 'Confirm your automation settings',
      icon: Eye,
      progress: 96
    },
    complete: { 
      title: 'Complete', 
      subtitle: 'Your automation is ready',
      icon: Check,
      progress: 100
    }
  }

  const nextStep = () => {
    const steps: StepType[] = ['intro', 'naming', 'drive-setup', 'ai-parsing', 'evaluation-criteria', 'google-sheets', 'actions-question', 'actions', 'review', 'complete']
    const currentIndex = steps.indexOf(currentStep)
    
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1])
    }
  }

  const prevStep = () => {
    const steps: StepType[] = ['intro', 'naming', 'drive-setup', 'ai-parsing', 'evaluation-criteria', 'google-sheets', 'actions-question', 'actions', 'review', 'complete']
    const currentIndex = steps.indexOf(currentStep)
    
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1])
    }
  }

  const renderIntro = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto">
        <Users className="h-10 w-10 text-white" />
      </div>
      <div>
        <h2 className="text-2xl font-bold mb-2">AI Automated HR Workflow</h2>
        <p className="text-muted-foreground max-w-md mx-auto">
          This template monitors Google Drive for resumes, automatically extracts candidate
          information using AI, scores applicants based on your criteria, and logs results
          to Google Sheets for easy tracking and analysis.
        </p>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <SiGoogledrive className="h-8 w-8 mx-auto mb-2 text-yellow-600" />
          <p className="text-xs font-medium">Drive Monitor</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Brain className="h-8 w-8 mx-auto mb-2 text-purple-600" />
          <p className="text-xs font-medium">CV Parsing</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <FileText className="h-8 w-8 mx-auto mb-2 text-blue-600" />
          <p className="text-xs font-medium">AI Scoring</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <SiGooglesheets className="h-8 w-8 mx-auto mb-2 text-green-600" />
          <p className="text-xs font-medium">Data Logging</p>
        </div>
      </div>

      <Button 
        onClick={nextStep} 
        size="lg"
        className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
      >
        Start Setup
        <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
    </motion.div>
  )

  const renderNaming = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Settings className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h2 className="text-xl font-semibold mb-2">Name Your Automation</h2>
        <p className="text-muted-foreground">
          Give your automation a memorable name that describes what it does
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="automation-name">Automation Name *</Label>
          <Input
            id="automation-name"
            placeholder="e.g., Engineering Resume Screener, Sales Candidate Tracker"
            value={config.name}
            onChange={(e) => setConfig({ ...config, name: e.target.value })}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && config.name) {
                nextStep()
              }
            }}
            className="mt-2"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Choose a descriptive name you'll easily recognize
          </p>
        </div>
      </div>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          disabled={!config.name}
          className="w-full"
        >
          Continue to Drive Setup
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderDriveSetup = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <SiGoogledrive className="h-16 w-16 mx-auto mb-4 text-yellow-600" />
        <h3 className="text-lg font-semibold mb-2">Google Drive Configuration</h3>
        <p className="text-muted-foreground text-sm">
          Set up folder monitoring for new resumes
        </p>
      </div>

      <Card>
        <CardContent className="p-6 space-y-4">
          <div>
            <Label className="text-sm font-medium">Authentication Type <span className="text-red-500">*</span></Label>
            <div className="mt-2">
              <div className="flex items-center space-x-2">
                <input type="radio" id="google-signin" checked readOnly className="text-blue-600" />
                <Label htmlFor="google-signin" className="text-sm">Google Sign-In</Label>
              </div>
            </div>
          </div>

          <div className="text-xs text-muted-foreground">
            <strong>Note:</strong> Select existing Google account from below or Signin with a different account.
          </div>

          {!config.googleAuth.isAuthenticated ? (
            <Button
              onClick={() => setConfig({
                ...config,
                googleAuth: {
                  isAuthenticated: true,
                  userEmail: '<EMAIL>',
                  userName: 'John Doe',
                  accountId: 'gauth-12345'
                }
              })}
              className="w-full"
            >
              <SiGoogledrive className="mr-2 h-4 w-4" />
              Sign in with Google
            </Button>
          ) : (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Badge variant="default" className="bg-green-600">Connected</Badge>
                <span className="text-sm font-medium">Google</span>
              </div>
              
              <div className="space-y-2">
                <Label className="text-sm font-medium">Google account</Label>
                <div className="p-3 border rounded-md bg-muted/50">
                  <div className="text-sm font-medium">{config.googleAuth.userName}</div>
                  <div className="text-sm text-muted-foreground">({config.googleAuth.userEmail})</div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Folder Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="folder-id">Google Drive Folder ID</Label>
            <Input
              id="folder-id"
              placeholder="e.g., 1abc-DefGHijKLMnoPQRsTuvWXyZ"
              value={config.driveSetup.folderId}
              onChange={(e) => setConfig({
                ...config,
                driveSetup: { ...config.driveSetup, folderId: e.target.value }
              })}
              className="mt-2"
            />
            <p className="text-xs text-muted-foreground mt-1">
              Find the ID in the folder URL after /folders/
            </p>
          </div>

          <div>
            <Label>File Types to Monitor</Label>
            <div className="mt-2 space-y-2">
              {['pdf', 'docx', 'doc', 'txt'].map((type) => (
                <div key={type} className="flex items-center space-x-2">
                  <Checkbox
                    id={type}
                    checked={config.driveSetup.fileTypes.includes(type)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setConfig({
                          ...config,
                          driveSetup: {
                            ...config.driveSetup,
                            fileTypes: [...config.driveSetup.fileTypes, type]
                          }
                        })
                      } else {
                        setConfig({
                          ...config,
                          driveSetup: {
                            ...config.driveSetup,
                            fileTypes: config.driveSetup.fileTypes.filter(t => t !== type)
                          }
                        })
                      }
                    }}
                  />
                  <Label htmlFor={type} className="text-sm font-normal cursor-pointer">
                    .{type.toUpperCase()}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="frequency">Check Frequency</Label>
              <Input
                id="frequency"
                type="number"
                min="1"
                value={config.driveSetup.monitoringFrequency}
                onChange={(e) => setConfig({
                  ...config,
                  driveSetup: { 
                    ...config.driveSetup, 
                    monitoringFrequency: parseInt(e.target.value) 
                  }
                })}
                className="mt-2"
              />
            </div>
            <div>
              <Label htmlFor="timeUnit">Time Unit</Label>
              <Select
                value={config.driveSetup.timeUnit}
                onValueChange={(value) => setConfig({
                  ...config,
                  driveSetup: { 
                    ...config.driveSetup, 
                    timeUnit: value as any 
                  }
                })}
              >
                <SelectTrigger id="timeUnit" className="mt-2">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="minutes">Minutes</SelectItem>
                  <SelectItem value="hours">Hours</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          disabled={!config.googleAuth.isAuthenticated || !config.driveSetup.folderId}
          className="w-full"
        >
          Continue to AI Parsing
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderAIParsing = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Brain className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h3 className="text-lg font-semibold mb-2">AI Parsing Configuration</h3>
        <p className="text-muted-foreground text-sm">
          Configure AI to extract information from resumes
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">AI Provider</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <RadioGroup
            value={config.aiParsing.provider}
            onValueChange={(value) => setConfig({
              ...config,
              aiParsing: { ...config.aiParsing, provider: value as any }
            })}
          >
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <RadioGroupItem value="openai" id="openai" />
              <Label htmlFor="openai" className="flex items-center gap-2 cursor-pointer">
                <SiOpenai className="h-4 w-4" />
                OpenAI
              </Label>
            </div>
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <RadioGroupItem value="anthropic" id="anthropic" />
              <Label htmlFor="anthropic" className="flex items-center gap-2 cursor-pointer">
                <SiAnthropic className="h-4 w-4" />
                Anthropic
              </Label>
            </div>
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <RadioGroupItem value="google" id="google" />
              <Label htmlFor="google" className="flex items-center gap-2 cursor-pointer">
                <SiGoogle className="h-4 w-4" />
                Google AI
              </Label>
            </div>
          </RadioGroup>

          <div>
            <Label>Model Selection</Label>
            <Select
              value={config.aiParsing.model}
              onValueChange={(value) => setConfig({
                ...config,
                aiParsing: { ...config.aiParsing, model: value }
              })}
            >
              <SelectTrigger className="mt-2">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {config.aiParsing.provider === 'openai' && (
                  <>
                    <SelectItem value="gpt-4o">GPT-4o (Recommended)</SelectItem>
                    <SelectItem value="gpt-4">GPT-4</SelectItem>
                    <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                  </>
                )}
                {config.aiParsing.provider === 'anthropic' && (
                  <>
                    <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
                    <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                    <SelectItem value="claude-3-haiku">Claude 3 Haiku</SelectItem>
                  </>
                )}
                {config.aiParsing.provider === 'google' && (
                  <>
                    <SelectItem value="gemini-1.5-pro">Gemini 1.5 Pro</SelectItem>
                    <SelectItem value="gemini-1.0-pro">Gemini 1.0 Pro</SelectItem>
                  </>
                )}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Information to Extract</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {Object.entries({
              name: 'Candidate Name',
              email: 'Email Address',
              phone: 'Phone Number',
              experience: 'Work Experience',
              education: 'Education Details',
              skills: 'Skills & Technologies',
              summary: 'Professional Summary'
            }).map(([key, label]) => (
              <div key={key} className="flex items-center space-x-2">
                <Checkbox
                  id={key}
                  checked={config.aiParsing.extractFields[key as keyof typeof config.aiParsing.extractFields]}
                  onCheckedChange={(checked) => setConfig({
                    ...config,
                    aiParsing: {
                      ...config.aiParsing,
                      extractFields: {
                        ...config.aiParsing.extractFields,
                        [key]: checked as boolean
                      }
                    }
                  })}
                />
                <Label htmlFor={key} className="text-sm font-normal cursor-pointer">
                  {label}
                </Label>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          className="w-full"
        >
          Continue to Evaluation Criteria
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderEvaluationCriteria = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <FileText className="h-16 w-16 mx-auto mb-4 text-blue-600" />
        <h3 className="text-lg font-semibold mb-2">Evaluation Criteria</h3>
        <p className="text-muted-foreground text-sm">
          Define how candidates should be scored
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Scoring Criteria</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {config.evaluationCriteria.criteria.map((criterion) => (
            <div key={criterion.id} className="space-y-2">
              <div className="flex justify-between items-center">
                <Label className="text-sm font-medium">{criterion.name}</Label>
                <span className="text-sm text-muted-foreground">{criterion.weight}%</span>
              </div>
              <Slider
                value={[criterion.weight]}
                onValueChange={([value]) => {
                  const updatedCriteria = config.evaluationCriteria.criteria.map(c =>
                    c.id === criterion.id ? { ...c, weight: value } : c
                  )
                  setConfig({
                    ...config,
                    evaluationCriteria: {
                      ...config.evaluationCriteria,
                      criteria: updatedCriteria
                    }
                  })
                }}
                max={100}
                step={5}
                className="w-full"
              />
              <p className="text-xs text-muted-foreground">{criterion.description}</p>
            </div>
          ))}

          <div className="pt-2 text-sm text-muted-foreground">
            Total weight: {config.evaluationCriteria.criteria.reduce((sum, c) => sum + c.weight, 0)}%
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Scoring Thresholds</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="passing-score">Passing Score (%)</Label>
            <Input
              id="passing-score"
              type="number"
              min="0"
              max="100"
              value={config.evaluationCriteria.passingScore}
              onChange={(e) => setConfig({
                ...config,
                evaluationCriteria: {
                  ...config.evaluationCriteria,
                  passingScore: parseInt(e.target.value)
                }
              })}
              className="mt-2"
            />
            <p className="text-xs text-muted-foreground mt-1">
              Candidates scoring above this will be marked as qualified
            </p>
          </div>

          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="auto-reject"
                checked={config.evaluationCriteria.autoReject}
                onCheckedChange={(checked) => setConfig({
                  ...config,
                  evaluationCriteria: {
                    ...config.evaluationCriteria,
                    autoReject: checked as boolean
                  }
                })}
              />
              <Label htmlFor="auto-reject" className="text-sm font-normal cursor-pointer">
                Automatically reject low-scoring candidates
              </Label>
            </div>

            {config.evaluationCriteria.autoReject && (
              <div>
                <Label htmlFor="reject-threshold">Rejection Threshold (%)</Label>
                <Input
                  id="reject-threshold"
                  type="number"
                  min="0"
                  max="100"
                  value={config.evaluationCriteria.rejectThreshold}
                  onChange={(e) => setConfig({
                    ...config,
                    evaluationCriteria: {
                      ...config.evaluationCriteria,
                      rejectThreshold: parseInt(e.target.value)
                    }
                  })}
                  className="mt-2"
                />
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          className="w-full"
        >
          Continue to Google Sheets
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderGoogleSheets = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <SiGooglesheets className="h-16 w-16 mx-auto mb-4 text-green-600" />
        <h3 className="text-lg font-semibold mb-2">Google Sheets Integration</h3>
        <p className="text-muted-foreground text-sm">
          Configure where to log candidate data
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Spreadsheet Configuration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="spreadsheet-id">Spreadsheet ID</Label>
            <Input
              id="spreadsheet-id"
              placeholder="e.g., 1abc-DefGHijKLMnoPQRsTuvWXyZ"
              value={config.googleSheets.spreadsheetId}
              onChange={(e) => setConfig({
                ...config,
                googleSheets: { ...config.googleSheets, spreadsheetId: e.target.value }
              })}
              className="mt-2"
            />
            <p className="text-xs text-muted-foreground mt-1">
              Find the ID in the spreadsheet URL between /d/ and /edit
            </p>
          </div>

          <div>
            <Label htmlFor="sheet-name">Sheet Name</Label>
            <Input
              id="sheet-name"
              placeholder="e.g., Candidates, Applications"
              value={config.googleSheets.sheetName}
              onChange={(e) => setConfig({
                ...config,
                googleSheets: { ...config.googleSheets, sheetName: e.target.value }
              })}
              className="mt-2"
            />
          </div>

          <div className="space-y-3">
            <Label className="text-sm font-medium">File</Label>
            {config.googleAuth.isAuthenticated && (
              <>
                {config.googleSheets.filesList.length === 0 ? (
                  <div className="p-3 border rounded-md bg-muted/50 text-sm text-muted-foreground">
                    There are no files, please refresh
                  </div>
                ) : (
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a spreadsheet" />
                    </SelectTrigger>
                    <SelectContent>
                      {config.googleSheets.filesList.map((file, index) => (
                        <SelectItem key={index} value={file}>{file}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setConfig({
                    ...config,
                    googleSheets: {
                      ...config.googleSheets,
                      filesList: ['HR Candidates Tracker', 'Recruitment Pipeline 2024', 'Interview Schedule']
                    }
                  })}
                >
                  Refresh Files
                </Button>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Column Mapping</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            Map data fields to spreadsheet columns (e.g., A, B, C)
          </p>
          <div className="grid grid-cols-2 gap-4">
            {Object.entries({
              name: 'Candidate Name',
              email: 'Email',
              phone: 'Phone',
              score: 'Score',
              status: 'Status',
              summary: 'Summary',
              timestamp: 'Timestamp'
            }).map(([key, label]) => (
              <div key={key}>
                <Label htmlFor={`col-${key}`} className="text-sm">{label}</Label>
                <Input
                  id={`col-${key}`}
                  placeholder="Column letter"
                  value={config.googleSheets.columns[key as keyof typeof config.googleSheets.columns]}
                  onChange={(e) => setConfig({
                    ...config,
                    googleSheets: {
                      ...config.googleSheets,
                      columns: {
                        ...config.googleSheets.columns,
                        [key]: e.target.value.toUpperCase()
                      }
                    }
                  })}
                  className="mt-1"
                  maxLength={2}
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          disabled={!config.googleSheets.spreadsheetId || !config.googleSheets.sheetName}
          className="w-full"
        >
          Continue
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderActionsQuestion = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Plus className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h3 className="text-lg font-semibold mb-2">Do you want to add any actions?</h3>
        <p className="text-muted-foreground text-sm">
          Actions let you extend your automation with additional capabilities
        </p>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <Button
          variant={config.wantsActions === true ? "default" : "outline"}
          size="lg"
          onClick={() => {
            setConfig({ ...config, wantsActions: true })
            nextStep()
          }}
          className="h-auto py-6"
        >
          <div className="flex flex-col items-center gap-2">
            <Check className="h-8 w-8" />
            <span>Yes, I want to add actions</span>
          </div>
        </Button>
        <Button
          variant={config.wantsActions === false ? "default" : "outline"}
          size="lg"
          onClick={() => {
            setConfig({ ...config, wantsActions: false })
            setCurrentStep('review')  // Skip directly to review
          }}
          className="h-auto py-6"
        >
          <div className="flex flex-col items-center gap-2">
            <ArrowRight className="h-8 w-8" />
            <span>No, continue without</span>
          </div>
        </Button>
      </div>

      <div>
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
      </div>
    </motion.div>
  )

  const renderActions = () => {
    const selectedActionsList = config.selectedActionsList || []

    const availableActionsList = [
      { id: 'discord-read', name: 'Discord Read', icon: MessageSquare, desc: 'Read messages from any Discord channel' },
      { id: 'discord-send', name: 'Discord Send', icon: MessageSquare, desc: 'Send messages to any Discord channel' },
      { id: 'gmail-read', name: 'Gmail Read', icon: Mail, desc: 'Read emails from your Gmail account' },
      { id: 'gmail-send', name: 'Gmail Send', icon: Mail, desc: 'Send emails via your Gmail account' },
      { id: 'gcalendar-read', name: 'Google Calendar Read', icon: Calendar, desc: 'Read events from any Google Calendar' },
      { id: 'gcalendar-write', name: 'Google Calendar Write', icon: Calendar, desc: 'Create events on any Google Calendar' },
      { id: 'http-request', name: 'HTTP Request', icon: Globe, desc: 'Make GET, POST, PUT, or DELETE HTTP requests' },
      { id: 'slack-read', name: 'Slack Read', icon: Hash, desc: 'Read messages from a specified Slack channel' },
      { id: 'slack-send', name: 'Slack Send', icon: Hash, desc: 'Send a message to a specified Slack channel' }
    ]

    const addAction = (action: any) => {
      const newAction = {
        id: Date.now().toString(),
        type: action.id,
        name: action.name,
        description: action.desc,
        config: {}
      }
      const updatedList = [...selectedActionsList, newAction]
      setConfig({ ...config, selectedActionsList: updatedList })
      setShowActionConfig(updatedList.length - 1)
    }

    const removeAction = (index: number) => {
      const updatedList = selectedActionsList.filter((_, i) => i !== index)
      setConfig({ ...config, selectedActionsList: updatedList })
    }

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <div className="text-center">
          <Plus className="h-16 w-16 mx-auto mb-4 text-purple-600" />
          <h3 className="text-lg font-semibold mb-2">What actions should happen?</h3>
          <p className="text-muted-foreground text-sm">
            Define the steps your automation will take
          </p>
        </div>

        {/* Selected Actions */}
        {selectedActionsList.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base">⚙️ Actions ({selectedActionsList.length})</Label>
            </div>
            <div className="space-y-2">
              {selectedActionsList.map((action, index) => {
                const actionInfo = availableActionsList.find(a => a.id === action.type)
                const Icon = actionInfo?.icon || Settings
                return (
                  <motion.div
                    key={action.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="flex items-center gap-3 p-4 bg-muted/30 rounded-lg border"
                  >
                    <Icon className="h-5 w-5 text-primary" />
                    <div className="flex-1">
                      <p className="font-medium">{action.name}</p>
                      <p className="text-xs text-muted-foreground">{action.description}</p>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setShowActionConfig(index)}
                        className="h-8 px-2"
                      >
                        <Settings className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => removeAction(index)}
                        className="h-8 w-8 p-0"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </motion.div>
                )
              })}
            </div>
          </div>
        )}

        {/* Available Actions */}
        <div>
          <Label className="text-base mb-3 block">Available Actions</Label>
          <div className="grid gap-3">
            {availableActionsList.map((action) => (
              <Card
                key={action.id}
                className="cursor-pointer transition-all hover:shadow-md"
                onClick={() => addAction(action)}
              >
                <CardHeader className="p-4">
                  <div className="flex items-center gap-3">
                    <action.icon className="h-5 w-5 text-primary" />
                    <div className="flex-1">
                      <CardTitle className="text-sm">{action.name}</CardTitle>
                      <CardDescription className="text-xs mt-1">{action.desc}</CardDescription>
                    </div>
                    <Plus className="h-4 w-4 text-muted-foreground" />
                  </div>
                </CardHeader>
              </Card>
            ))}
          </div>
        </div>

        <div className="flex gap-3">
          <Button 
            onClick={prevStep}
            variant="outline"
            className="w-full"
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button 
            onClick={nextStep}
            className="w-full"
          >
            Continue to Review
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </motion.div>
    )
  }

  const renderReview = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Eye className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h3 className="text-lg font-semibold mb-2">Review Your Configuration</h3>
        <p className="text-muted-foreground text-sm">
          Make sure everything looks correct before creating your automation
        </p>
      </div>

      <div className="space-y-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Automation Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Name</span>
              <span className="text-sm font-medium">{config.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Google Account</span>
              <span className="text-sm font-medium">{config.googleAuth.userEmail}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Monitoring Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Check Frequency</span>
              <span className="text-sm font-medium">Every {config.driveSetup.monitoringFrequency} {config.driveSetup.timeUnit}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">File Types</span>
              <span className="text-sm font-medium">{config.driveSetup.fileTypes.map(t => `.${t.toUpperCase()}`).join(', ')}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">AI Processing</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Provider</span>
              <span className="text-sm font-medium capitalize">{config.aiParsing.provider}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Model</span>
              <span className="text-sm font-medium">{config.aiParsing.model}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Passing Score</span>
              <span className="text-sm font-medium">{config.evaluationCriteria.passingScore}%</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Output Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Spreadsheet</span>
              <span className="text-sm font-medium truncate max-w-[200px]">{config.googleSheets.sheetName}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Data Columns</span>
              <span className="text-sm font-medium">7 fields mapped</span>
            </div>
          </CardContent>
        </Card>

        {config.selectedActionsList.length > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Additional Actions ({config.selectedActionsList.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {config.selectedActionsList.map((action) => (
                  <div key={action.id} className="text-sm">
                    • {action.name}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
        >
          Create Automation
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderComplete = () => (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto">
        <Check className="h-10 w-10 text-green-600 dark:text-green-400" />
      </div>
      <div>
        <h2 className="text-3xl font-bold mb-2">Automation Created!</h2>
        <p className="text-muted-foreground text-lg">
          Your "{config.name}" automation is now ready and will start monitoring resumes automatically.
        </p>
      </div>
      
      <div className="flex flex-col sm:flex-row gap-3 justify-center mt-8">
        <Button 
          onClick={() => setLocation('/dashboard/automations')}
          size="lg"
          className="bg-primary hover:bg-primary/90"
        >
          View All Automations
        </Button>
        <Button 
          onClick={() => setLocation('/dashboard/browse-templates')}
          variant="outline"
          size="lg"
        >
          Browse More Templates
        </Button>
      </div>
    </motion.div>
  )

  const renderStepContent = () => {
    switch (currentStep) {
      case 'intro': return renderIntro()
      case 'naming': return renderNaming()
      case 'drive-setup': return renderDriveSetup()
      case 'ai-parsing': return renderAIParsing()
      case 'evaluation-criteria': return renderEvaluationCriteria()
      case 'google-sheets': return renderGoogleSheets()
      case 'actions-question': return renderActionsQuestion()
      case 'actions': return renderActions()
      case 'review': return renderReview()
      case 'complete': return renderComplete()
      default: return null
    }
  }

  const CurrentIcon = stepConfig[currentStep].icon

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => setLocation('/dashboard/browse-templates')}
          className="mb-4"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back to Templates
        </Button>

        {currentStep !== 'complete' && (
          <>
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <CurrentIcon className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">{stepConfig[currentStep].title}</h1>
                <p className="text-muted-foreground text-sm">{stepConfig[currentStep].subtitle}</p>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex justify-between text-xs text-muted-foreground mb-2">
                <span>Step {Object.keys(stepConfig).indexOf(currentStep) + 1} of 9</span>
                <span>{stepConfig[currentStep].progress}% Complete</span>
              </div>
              <Progress value={stepConfig[currentStep].progress} className="h-2" />
            </div>
          </>
        )}
      </div>

      {/* Content */}
      <Card className="p-6">
        {renderStepContent()}
      </Card>
    </div>
  )
}