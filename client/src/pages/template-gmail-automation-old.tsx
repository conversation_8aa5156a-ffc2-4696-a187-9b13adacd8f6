import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { useLocation } from 'wouter'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Mail, 
  Shield, 
  Brain, 
  Clock, 
  Settings,
  Bell,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  ChevronLeft,
  AlertCircle,
  Eye,
  EyeOff,
  Zap,
  Filter,
  Flag,
  Users,
  Archive,
  X
} from 'lucide-react'
import { SiG<PERSON>, SiOpenai, SiAnthropic, SiGoogle } from 'react-icons/si'
import { Checkbox } from "@/components/ui/checkbox"
import { Switch } from "@/components/ui/switch"
import { ActionsSelector } from '@/components/actions-selector'

type StepType = 'intro' | 'naming' | 'gmail-connection' | 'api-integration' | 'polling-settings' | 'analysis-settings' | 'approval-settings' | 'notification-settings' | 'actions-question' | 'actions' | 'review' | 'complete'

interface GmailAutomationConfig {
  name: string
  googleAuth: {
    isAuthenticated: boolean
    userEmail: string
    userName: string
    accountId: string
  }
  gmailConnection: {
    email: string
    scopes: string[]
  }
  apiIntegration: {
    provider: 'openai' | 'anthropic' | 'google'
    model: string
    isConfigured: boolean
  }
  pollingSettings: {
    frequency: number
    timeUnit: 'minutes' | 'hours'
    filters: {
      labels: string[]
      senders: string[]
      keywords: string[]
      unreadOnly: boolean
    }
  }
  analysisSettings: {
    actions: {
      autoSort: boolean
      flagImportant: boolean
      sendAlerts: boolean
      routeToHuman: boolean
      autoArchive: boolean
      autoReply: boolean
    }
    criteria: {
      urgency: boolean
      sentiment: boolean
      category: boolean
      customerType: boolean
    }
    customPrompt: string
  }
  approvalSettings: {
    enabled: boolean
    actions: string[]
    reviewerEmail: string
    timeout: number
  }
  notificationSettings: {
    channels: {
      email: boolean
      slack: boolean
      webhook: boolean
    }
    triggers: string[]
    destinations: {
      email: string
      slackChannel: string
      webhookUrl: string
    }
  }
  selectedActionsList: Array<{
    id: string
    type: string
    name: string
    description: string
    config: Record<string, any>
  }>
  wantsActions: boolean | null
}

const defaultConfig: GmailAutomationConfig = {
  name: '',
  googleAuth: {
    isAuthenticated: false,
    userEmail: '',
    userName: '',
    accountId: ''
  },
  gmailConnection: {
    email: '',
    scopes: ['read', 'modify', 'send']
  },
  apiIntegration: {
    provider: 'openai',
    model: 'gpt-4o',
    isConfigured: false
  },
  pollingSettings: {
    frequency: 5,
    timeUnit: 'minutes',
    filters: {
      labels: [],
      senders: [],
      keywords: [],
      unreadOnly: true
    }
  },
  analysisSettings: {
    actions: {
      autoSort: true,
      flagImportant: true,
      sendAlerts: true,
      routeToHuman: false,
      autoArchive: false,
      autoReply: false
    },
    criteria: {
      urgency: true,
      sentiment: true,
      category: true,
      customerType: false
    },
    customPrompt: ''
  },
  approvalSettings: {
    enabled: false,
    actions: [],
    reviewerEmail: '',
    timeout: 24
  },
  notificationSettings: {
    channels: {
      email: true,
      slack: false,
      webhook: false
    },
    triggers: ['important_flagged', 'urgent_detected'],
    destinations: {
      email: '',
      slackChannel: '',
      webhookUrl: ''
    }
  },
  selectedActionsList: [],
  wantsActions: null
}

const steps: { id: StepType; title: string; icon: React.ComponentType<{ className?: string }> }[] = [
  { id: 'intro', title: 'Introduction', icon: Mail },
  { id: 'naming', title: 'Name Automation', icon: Settings },
  { id: 'gmail-connection', title: 'Gmail Connection', icon: Shield },
  { id: 'api-integration', title: 'API Integration', icon: Brain },
  { id: 'polling-settings', title: 'Email Polling', icon: Clock },
  { id: 'analysis-settings', title: 'Analysis Settings', icon: Settings },
  { id: 'approval-settings', title: 'Human Approval', icon: CheckCircle },
  { id: 'notification-settings', title: 'Notifications', icon: Bell },
  { id: 'actions-question', title: 'Actions Question', icon: Zap },
  { id: 'actions', title: 'Actions', icon: Zap },
  { id: 'review', title: 'Review', icon: Eye },
  { id: 'complete', title: 'Complete', icon: CheckCircle }
]

const availableLabels = [
  'INBOX', 'IMPORTANT', 'STARRED', 'SENT', 'DRAFTS', 'SPAM', 'TRASH',
  'CATEGORY_PERSONAL', 'CATEGORY_SOCIAL', 'CATEGORY_PROMOTIONS', 'CATEGORY_UPDATES', 'CATEGORY_FORUMS'
]

const analysisActions = [
  { id: 'autoSort', label: 'Auto-sort emails', description: 'Automatically categorize and label emails' },
  { id: 'flagImportant', label: 'Flag important emails', description: 'Mark high-priority emails as important' },
  { id: 'sendAlerts', label: 'Send alerts', description: 'Notify team of urgent emails' },
  { id: 'routeToHuman', label: 'Route to human review', description: 'Forward complex emails for manual review' },
  { id: 'autoArchive', label: 'Auto-archive', description: 'Archive low-priority emails automatically' },
  { id: 'autoReply', label: 'Auto-reply', description: 'Send automated responses to common inquiries' }
]

const analysisCriteria = [
  { id: 'urgency', label: 'Urgency detection', description: 'Identify time-sensitive emails' },
  { id: 'sentiment', label: 'Sentiment analysis', description: 'Detect positive, negative, or neutral tone' },
  { id: 'category', label: 'Content categorization', description: 'Classify email type (support, sales, etc.)' },
  { id: 'customerType', label: 'Customer type detection', description: 'Identify VIP customers or prospects' }
]

export default function GmailAutomationTemplate() {
  const [currentStep, setCurrentStep] = useState<StepType>('intro')
  const [config, setConfig] = useState<GmailAutomationConfig>(defaultConfig)
  const [, setLocation] = useLocation()

  const currentStepIndex = steps.findIndex(step => step.id === currentStep)
  const progress = ((currentStepIndex + 1) / steps.length) * 100

  const handleNext = () => {
    const nextIndex = currentStepIndex + 1
    if (nextIndex < steps.length) {
      setCurrentStep(steps[nextIndex].id)
    }
  }

  const handlePrevious = () => {
    const prevIndex = currentStepIndex - 1
    if (prevIndex >= 0) {
      setCurrentStep(steps[prevIndex].id)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && currentStep !== 'complete') {
      handleNext()
    }
  }

  const updateConfig = (section: keyof GmailAutomationConfig | '', updates: any) => {
    if (section === '') {
      setConfig(prev => ({ ...prev, ...updates }))
    } else {
      setConfig(prev => ({
        ...prev,
        [section]: { ...prev[section], ...updates }
      }))
    }
  }

  const renderIntroStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="flex justify-center">
        <div className="p-4 bg-red-100 dark:bg-red-900 rounded-full">
          <SiGmail className="h-12 w-12 text-red-600" />
        </div>
      </div>
      
      <div>
        <h2 className="text-2xl font-bold mb-2">Gmail Email Automation</h2>
        <p className="text-muted-foreground mb-6">
          Intelligently monitor, analyze, and automate incoming Gmail messages using AI
        </p>
      </div>

      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="text-left">Name Your Automation</CardTitle>
          <CardDescription className="text-left">
            Give your email automation a descriptive name
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="automationName">Automation Name</Label>
            <Input
              id="automationName"
              placeholder="e.g., Customer Support Email Handler, Sales Lead Processor"
              value={config.name}
              onChange={(e) => updateConfig('', { name: e.target.value })}
              onKeyDown={handleKeyPress}
              autoFocus
            />
          </div>
        </CardContent>
      </Card>

      <div className="grid md:grid-cols-3 gap-4 text-left">
        <Card>
          <CardHeader className="pb-3">
            <Shield className="h-6 w-6 text-red-500 mb-2" />
            <CardTitle className="text-sm">Secure Gmail Access</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Connect securely to your Gmail account with OAuth authentication
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <Brain className="h-6 w-6 text-purple-500 mb-2" />
            <CardTitle className="text-sm">AI-Powered Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Use advanced AI to understand email content, urgency, and sentiment
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <Bell className="h-6 w-6 text-blue-500 mb-2" />
            <CardTitle className="text-sm">Smart Notifications</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Get notified about important emails via email, Slack, or webhooks
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="flex gap-3">
        <Button 
          onClick={handlePrevious}
          variant="outline"
          className="w-full sm:w-auto"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={handleNext} 
          disabled={!config.name.trim()}
          className="w-full sm:w-auto" 
          onKeyDown={handleKeyPress}
        >
          Get Started
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderGmailConnectionStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Shield className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h2 className="text-2xl font-bold mb-2">Gmail Connection</h2>
        <p className="text-muted-foreground">
          Connect your Gmail account to enable email monitoring
        </p>
      </div>

      <Card>
        <CardContent className="p-6 space-y-4">
          <div>
            <Label className="text-sm font-medium">Authentication Type <span className="text-red-500">*</span></Label>
            <div className="mt-2">
              <div className="flex items-center space-x-2">
                <input type="radio" id="google-signin" checked readOnly className="text-blue-600" />
                <Label htmlFor="google-signin" className="text-sm">Google Sign-In</Label>
              </div>
            </div>
          </div>

          <div className="text-xs text-muted-foreground">
            <strong>Note:</strong> Select existing Google account from below or Signin with a different account.
          </div>

          {!config.googleAuth.isAuthenticated ? (
            <Button
              onClick={() => {
                updateConfig('googleAuth', {
                  isAuthenticated: true,
                  userEmail: '<EMAIL>',
                  userName: 'Kaizar Bharmal',
                  accountId: 'gauth-12345'
                })
                updateConfig('gmailConnection', { email: '<EMAIL>' })
              }}
              className="w-full"
            >
              <SiGoogle className="mr-2 h-4 w-4" />
              Sign in with Google
            </Button>
          ) : (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Badge variant="default" className="bg-green-600">Connected</Badge>
                <span className="text-sm font-medium">Google</span>
              </div>
              
              <div className="space-y-2">
                <Label className="text-sm font-medium">Google account</Label>
                <div className="p-3 border rounded-md bg-muted/50">
                  <div className="text-sm font-medium">{config.googleAuth.userName}</div>
                  <div className="text-sm text-muted-foreground">({config.googleAuth.userEmail})</div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button variant="outline" onClick={handlePrevious}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={handleNext} 
          disabled={!config.googleAuth.isAuthenticated}
          className="flex-1"
          onKeyDown={handleKeyPress}
        >
          Next: API Integration
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderApiIntegrationStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Brain className="h-12 w-12 text-purple-500 mx-auto mb-4" />
        <h2 className="text-2xl font-bold mb-2">AI Integration</h2>
        <p className="text-muted-foreground">
          Choose your AI provider for email analysis
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>AI Provider Selection</CardTitle>
          <CardDescription>
            Select the AI service for analyzing your emails
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>AI Provider</Label>
            <RadioGroup
              value={config.apiIntegration.provider}
              onValueChange={(value) => updateConfig('apiIntegration', { provider: value })}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="openai" id="openai" />
                <Label htmlFor="openai" className="flex items-center gap-2">
                  <SiOpenai className="h-4 w-4" />
                  OpenAI (GPT-4o, GPT-4, GPT-3.5)
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="anthropic" id="anthropic" />
                <Label htmlFor="anthropic" className="flex items-center gap-2">
                  <SiAnthropic className="h-4 w-4" />
                  Anthropic (Claude 4.0, Claude 3.5)
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="google" id="google" />
                <Label htmlFor="google" className="flex items-center gap-2">
                  <SiGoogle className="h-4 w-4" />
                  Google (Gemini 2.0, Gemini 1.5)
                </Label>
              </div>
            </RadioGroup>
          </div>

          <div className="space-y-2">
            <Label htmlFor="model">Model Selection</Label>
            <Select 
              value={config.apiIntegration.model} 
              onValueChange={(value) => updateConfig('apiIntegration', { model: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {config.apiIntegration.provider === 'openai' && (
                  <>
                    <SelectItem value="gpt-4o">GPT-4o (Latest)</SelectItem>
                    <SelectItem value="gpt-4">GPT-4</SelectItem>
                    <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                  </>
                )}
                {config.apiIntegration.provider === 'anthropic' && (
                  <>
                    <SelectItem value="claude-4-sonnet">Claude 4.0 Sonnet (Latest)</SelectItem>
                    <SelectItem value="claude-3.5-sonnet">Claude 3.5 Sonnet</SelectItem>
                    <SelectItem value="claude-3-haiku">Claude 3 Haiku</SelectItem>
                  </>
                )}
                {config.apiIntegration.provider === 'google' && (
                  <>
                    <SelectItem value="gemini-2.0-flash">Gemini 2.0 Flash (Latest)</SelectItem>
                    <SelectItem value="gemini-1.5-pro">Gemini 1.5 Pro</SelectItem>
                    <SelectItem value="gemini-1.0-pro">Gemini 1.0 Pro</SelectItem>
                  </>
                )}
              </SelectContent>
            </Select>
          </div>

          <Button 
            className="w-full"
            onClick={() => updateConfig('apiIntegration', { isConfigured: true })}
          >
            Configure API Key
          </Button>

          {config.apiIntegration.isConfigured && (
            <div className="flex items-center gap-2 text-sm text-green-600 bg-green-50 dark:bg-green-950 p-3 rounded-md">
              <CheckCircle className="h-4 w-4" />
              {config.apiIntegration.provider} API configured successfully
            </div>
          )}
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button variant="outline" onClick={handlePrevious}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={handleNext} 
          disabled={!config.apiIntegration.isConfigured}
          className="flex-1"
          onKeyDown={handleKeyPress}
        >
          Next: Polling Settings
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderPollingSettingsStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Clock className="h-12 w-12 text-blue-500 mx-auto mb-4" />
        <h2 className="text-2xl font-bold mb-2">Email Polling Settings</h2>
        <p className="text-muted-foreground">
          Configure how often to check for new emails and what to monitor
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Monitoring Frequency</CardTitle>
            <CardDescription>
              How often should we check for new emails?
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-2">
              <div className="flex-1">
                <Label htmlFor="frequency">Every</Label>
                <Input
                  id="frequency"
                  type="number"
                  min="1"
                  max="60"
                  value={config.pollingSettings.frequency}
                  onChange={(e) => updateConfig('pollingSettings', { frequency: parseInt(e.target.value) || 1 })}
                  onKeyDown={handleKeyPress}
                />
              </div>
              <div className="flex-1">
                <Label htmlFor="timeUnit">Time Unit</Label>
                <Select 
                  value={config.pollingSettings.timeUnit} 
                  onValueChange={(value) => updateConfig('pollingSettings', { timeUnit: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="minutes">Minutes</SelectItem>
                    <SelectItem value="hours">Hours</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="unreadOnly"
                checked={config.pollingSettings.filters.unreadOnly}
                onCheckedChange={(checked) => updateConfig('pollingSettings', { 
                  filters: { ...config.pollingSettings.filters, unreadOnly: checked }
                })}
              />
              <Label htmlFor="unreadOnly">Only check unread emails</Label>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Email Filters</CardTitle>
            <CardDescription>
              Filter which emails to analyze (optional)
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Gmail Labels</Label>
              <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                {availableLabels.map((label) => (
                  <div key={label} className="flex items-center space-x-2">
                    <Checkbox
                      id={label}
                      checked={config.pollingSettings.filters.labels.includes(label)}
                      onCheckedChange={(checked) => {
                        const labels = checked 
                          ? [...config.pollingSettings.filters.labels, label]
                          : config.pollingSettings.filters.labels.filter(l => l !== label)
                        updateConfig('pollingSettings', { 
                          filters: { ...config.pollingSettings.filters, labels }
                        })
                      }}
                    />
                    <Label htmlFor={label} className="text-xs">{label}</Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="keywords">Keywords (comma-separated)</Label>
              <Input
                id="keywords"
                placeholder="urgent, important, customer"
                value={config.pollingSettings.filters.keywords.join(', ')}
                onChange={(e) => updateConfig('pollingSettings', { 
                  filters: { 
                    ...config.pollingSettings.filters, 
                    keywords: e.target.value.split(',').map(k => k.trim()).filter(k => k)
                  }
                })}
                onKeyDown={handleKeyPress}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex gap-3">
        <Button variant="outline" onClick={handlePrevious}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={handleNext} className="flex-1" onKeyDown={handleKeyPress}>
          Next: Analysis Settings
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderAnalysisSettingsStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Settings className="h-12 w-12 text-green-500 mx-auto mb-4" />
        <h2 className="text-2xl font-bold mb-2">Contextual Analysis Settings</h2>
        <p className="text-muted-foreground">
          Configure how AI should analyze and act on your emails
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Analysis Criteria</CardTitle>
            <CardDescription>
              What should the AI analyze in each email?
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {analysisCriteria.map(({ id, label, description }) => (
              <div key={id} className="flex items-center space-x-2">
                <Checkbox
                  id={id}
                  checked={config.analysisSettings.criteria[id as keyof typeof config.analysisSettings.criteria]}
                  onCheckedChange={(checked) => updateConfig('analysisSettings', {
                    criteria: { ...config.analysisSettings.criteria, [id]: checked }
                  })}
                />
                <div>
                  <Label htmlFor={id} className="font-medium">{label}</Label>
                  <p className="text-sm text-muted-foreground">{description}</p>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Automated Actions</CardTitle>
            <CardDescription>
              What actions should the AI take automatically?
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {analysisActions.map(({ id, label, description }) => (
              <div key={id} className="flex items-center space-x-2">
                <Checkbox
                  id={id}
                  checked={config.analysisSettings.actions[id as keyof typeof config.analysisSettings.actions]}
                  onCheckedChange={(checked) => updateConfig('analysisSettings', {
                    actions: { ...config.analysisSettings.actions, [id]: checked }
                  })}
                />
                <div>
                  <Label htmlFor={id} className="font-medium">{label}</Label>
                  <p className="text-sm text-muted-foreground">{description}</p>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Custom Analysis Prompt</CardTitle>
          <CardDescription>
            Add specific instructions for the AI (optional)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder="e.g., Pay special attention to emails from VIP customers, prioritize support requests over marketing inquiries..."
            value={config.analysisSettings.customPrompt}
            onChange={(e) => updateConfig('analysisSettings', { customPrompt: e.target.value })}
            rows={3}
          />
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button variant="outline" onClick={handlePrevious}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={handleNext} className="flex-1" onKeyDown={handleKeyPress}>
          Next: Human Approval
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderApprovalSettingsStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <CheckCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
        <h2 className="text-2xl font-bold mb-2">Human Approval Toggle</h2>
        <p className="text-muted-foreground">
          Configure when human review is required before taking actions
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Human Approval Settings
            <Switch
              checked={config.approvalSettings.enabled}
              onCheckedChange={(checked) => updateConfig('approvalSettings', { enabled: checked })}
            />
          </CardTitle>
          <CardDescription>
            {config.approvalSettings.enabled 
              ? 'Human approval is required for selected actions'
              : 'AI will act automatically without human approval'
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {config.approvalSettings.enabled && (
            <>
              <div className="space-y-2">
                <Label htmlFor="reviewerEmail">Reviewer Email</Label>
                <Input
                  id="reviewerEmail"
                  type="email"
                  placeholder="<EMAIL>"
                  value={config.approvalSettings.reviewerEmail}
                  onChange={(e) => updateConfig('approvalSettings', { reviewerEmail: e.target.value })}
                  onKeyDown={handleKeyPress}
                />
              </div>

              <div className="space-y-2">
                <Label>Actions Requiring Approval</Label>
                <div className="space-y-2">
                  {analysisActions.map(({ id, label }) => (
                    <div key={id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`approval-${id}`}
                        checked={config.approvalSettings.actions.includes(id)}
                        onCheckedChange={(checked) => {
                          const actions = checked 
                            ? [...config.approvalSettings.actions, id]
                            : config.approvalSettings.actions.filter(a => a !== id)
                          updateConfig('approvalSettings', { actions })
                        }}
                      />
                      <Label htmlFor={`approval-${id}`}>{label}</Label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="timeout">Approval Timeout (hours)</Label>
                <Select 
                  value={config.approvalSettings.timeout.toString()} 
                  onValueChange={(value) => updateConfig('approvalSettings', { timeout: parseInt(value) })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 hour</SelectItem>
                    <SelectItem value="4">4 hours</SelectItem>
                    <SelectItem value="12">12 hours</SelectItem>
                    <SelectItem value="24">24 hours</SelectItem>
                    <SelectItem value="48">48 hours</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button variant="outline" onClick={handlePrevious}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={handleNext} 
          disabled={config.approvalSettings.enabled && !config.approvalSettings.reviewerEmail}
          className="flex-1" 
          onKeyDown={handleKeyPress}
        >
          Next: Notifications
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderNotificationSettingsStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Bell className="h-12 w-12 text-blue-500 mx-auto mb-4" />
        <h2 className="text-2xl font-bold mb-2">Notification Settings</h2>
        <p className="text-muted-foreground">
          Configure how and when you want to be notified
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Notification Channels</CardTitle>
            <CardDescription>
              Choose how you want to receive notifications
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="email-notifications">Email Notifications</Label>
                <p className="text-sm text-muted-foreground">Receive alerts via email</p>
              </div>
              <Switch
                id="email-notifications"
                checked={config.notificationSettings.channels.email}
                onCheckedChange={(checked) => updateConfig('notificationSettings', {
                  channels: { ...config.notificationSettings.channels, email: checked }
                })}
              />
            </div>

            {config.notificationSettings.channels.email && (
              <div className="space-y-2">
                <Label htmlFor="notificationEmail">Notification Email</Label>
                <Input
                  id="notificationEmail"
                  type="email"
                  placeholder="<EMAIL>"
                  value={config.notificationSettings.destinations.email}
                  onChange={(e) => updateConfig('notificationSettings', {
                    destinations: { ...config.notificationSettings.destinations, email: e.target.value }
                  })}
                  onKeyDown={handleKeyPress}
                />
              </div>
            )}

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="slack-notifications">Slack Notifications</Label>
                <p className="text-sm text-muted-foreground">Send alerts to Slack channel</p>
              </div>
              <Switch
                id="slack-notifications"
                checked={config.notificationSettings.channels.slack}
                onCheckedChange={(checked) => updateConfig('notificationSettings', {
                  channels: { ...config.notificationSettings.channels, slack: checked }
                })}
              />
            </div>

            {config.notificationSettings.channels.slack && (
              <div className="space-y-2">
                <Label htmlFor="slackChannel">Slack Channel</Label>
                <Input
                  id="slackChannel"
                  placeholder="#email-alerts"
                  value={config.notificationSettings.destinations.slackChannel}
                  onChange={(e) => updateConfig('notificationSettings', {
                    destinations: { ...config.notificationSettings.destinations, slackChannel: e.target.value }
                  })}
                  onKeyDown={handleKeyPress}
                />
              </div>
            )}

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="webhook-notifications">Webhook Notifications</Label>
                <p className="text-sm text-muted-foreground">Send to custom webhook URL</p>
              </div>
              <Switch
                id="webhook-notifications"
                checked={config.notificationSettings.channels.webhook}
                onCheckedChange={(checked) => updateConfig('notificationSettings', {
                  channels: { ...config.notificationSettings.channels, webhook: checked }
                })}
              />
            </div>

            {config.notificationSettings.channels.webhook && (
              <div className="space-y-2">
                <Label htmlFor="webhookUrl">Webhook URL</Label>
                <Input
                  id="webhookUrl"
                  placeholder="https://your-webhook-url.com"
                  value={config.notificationSettings.destinations.webhookUrl}
                  onChange={(e) => updateConfig('notificationSettings', {
                    destinations: { ...config.notificationSettings.destinations, webhookUrl: e.target.value }
                  })}
                  onKeyDown={handleKeyPress}
                />
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Notification Triggers</CardTitle>
            <CardDescription>
              When should notifications be sent?
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              { id: 'important_flagged', label: 'Important email flagged', description: 'When AI flags an email as important' },
              { id: 'urgent_detected', label: 'Urgent email detected', description: 'When AI detects urgency in content' },
              { id: 'human_review_needed', label: 'Human review required', description: 'When email needs manual review' },
              { id: 'auto_action_taken', label: 'Automated action taken', description: 'When AI performs an action' },
              { id: 'processing_error', label: 'Processing error', description: 'When email analysis fails' }
            ].map(({ id, label, description }) => (
              <div key={id} className="flex items-center space-x-2">
                <Checkbox
                  id={id}
                  checked={config.notificationSettings.triggers.includes(id)}
                  onCheckedChange={(checked) => {
                    const triggers = checked 
                      ? [...config.notificationSettings.triggers, id]
                      : config.notificationSettings.triggers.filter(t => t !== id)
                    updateConfig('notificationSettings', { triggers })
                  }}
                />
                <div>
                  <Label htmlFor={id} className="font-medium">{label}</Label>
                  <p className="text-sm text-muted-foreground">{description}</p>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      <div className="flex gap-3">
        <Button variant="outline" onClick={handlePrevious}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={handleNext} className="flex-1" onKeyDown={handleKeyPress}>
          Continue to Custom Actions
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderCustomActionsStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Zap className="h-12 w-12 text-purple-600 mx-auto mb-4" />
        <h2 className="text-2xl font-bold mb-2">Custom Actions (Optional)</h2>
        <p className="text-muted-foreground">
          Add custom actions to extend your email automation capabilities
        </p>
      </div>

      <CustomActionsEditor
        actions={config.customActions}
        onActionsChange={(actions) => setConfig(prev => ({ ...prev, customActions: actions }))}
        title="Additional Actions"
        description="These actions will run after email analysis"
      />

      <div className="flex gap-3">
        <Button variant="outline" onClick={handlePrevious}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={handleNext} className="flex-1" onKeyDown={handleKeyPress}>
          Review Configuration
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderReviewStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Eye className="h-12 w-12 text-green-500 mx-auto mb-4" />
        <h2 className="text-2xl font-bold mb-2">Review Configuration</h2>
        <p className="text-muted-foreground">
          Review your Gmail automation settings before creating
        </p>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Automation Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div><strong>Name:</strong> {config.name}</div>
            <div><strong>Gmail Account:</strong> {config.googleAuth.userName} ({config.googleAuth.userEmail})</div>
            <div><strong>AI Provider:</strong> {config.apiIntegration.provider} ({config.apiIntegration.model})</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Monitoring Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div><strong>Frequency:</strong> Every {config.pollingSettings.frequency} {config.pollingSettings.timeUnit}</div>
            <div><strong>Unread Only:</strong> {config.pollingSettings.filters.unreadOnly ? 'Yes' : 'No'}</div>
            {config.pollingSettings.filters.labels.length > 0 && (
              <div><strong>Labels:</strong> {config.pollingSettings.filters.labels.join(', ')}</div>
            )}
            {config.pollingSettings.filters.keywords.length > 0 && (
              <div><strong>Keywords:</strong> {config.pollingSettings.filters.keywords.join(', ')}</div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Analysis & Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div>
              <strong>Analysis Criteria:</strong>{' '}
              {Object.entries(config.analysisSettings.criteria)
                .filter(([, enabled]) => enabled)
                .map(([criteria]) => criteria)
                .join(', ')}
            </div>
            <div>
              <strong>Automated Actions:</strong>{' '}
              {Object.entries(config.analysisSettings.actions)
                .filter(([, enabled]) => enabled)
                .map(([action]) => action.replace(/([A-Z])/g, ' $1').toLowerCase())
                .join(', ')}
            </div>
            <div><strong>Human Approval:</strong> {config.approvalSettings.enabled ? 'Enabled' : 'Disabled'}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Notifications</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div>
              <strong>Channels:</strong>{' '}
              {Object.entries(config.notificationSettings.channels)
                .filter(([, enabled]) => enabled)
                .map(([channel]) => channel)
                .join(', ')}
            </div>
            <div>
              <strong>Triggers:</strong> {config.notificationSettings.triggers.length} selected
            </div>
          </CardContent>
        </Card>

        {config.customActions.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Custom Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div>
                <strong>{config.customActions.length} custom action{config.customActions.length !== 1 ? 's' : ''} configured</strong>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <div className="flex gap-3">
        <Button variant="outline" onClick={handlePrevious}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={handleNext} className="flex-1" onKeyDown={handleKeyPress}>
          Create Automation
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderCompleteStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="flex justify-center">
        <div className="p-4 bg-green-100 dark:bg-green-900 rounded-full">
          <CheckCircle className="h-12 w-12 text-green-600" />
        </div>
      </div>
      
      <div>
        <h2 className="text-2xl font-bold mb-2">Automation Created!</h2>
        <p className="text-muted-foreground mb-6">
          Your Gmail automation is ready to intelligently process emails
        </p>
      </div>

      <Card>
        <CardContent className="pt-6">
          <div className="grid md:grid-cols-3 gap-4 text-left">
            <div className="text-center">
              <SiGmail className="h-8 w-8 text-red-500 mx-auto mb-2" />
              <p className="font-medium">Gmail Connected</p>
              <p className="text-sm text-muted-foreground">
                Monitoring {config.gmailConnection.email}
              </p>
            </div>
            <div className="text-center">
              <Brain className="h-8 w-8 text-purple-500 mx-auto mb-2" />
              <p className="font-medium">AI Analysis Active</p>
              <p className="text-sm text-muted-foreground">
                Using {config.apiIntegration.provider} {config.apiIntegration.model}
              </p>
            </div>
            <div className="text-center">
              <Bell className="h-8 w-8 text-blue-500 mx-auto mb-2" />
              <p className="font-medium">Notifications Ready</p>
              <p className="text-sm text-muted-foreground">
                {Object.values(config.notificationSettings.channels).filter(Boolean).length} channels configured
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3 justify-center">
        <Button variant="outline" onClick={() => setLocation('/dashboard/automations')}>
          View All Automations
        </Button>
        <Button onClick={() => setLocation('/dashboard/templates')}>
          Browse More Templates
        </Button>
      </div>
    </motion.div>
  )

  const renderNamingStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Settings className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h2 className="text-xl font-semibold mb-2">Name Your Automation</h2>
        <p className="text-muted-foreground">
          Give your Gmail automation a memorable name that describes what it does
        </p>
      </div>

      <div className="space-y-4 max-w-md mx-auto">
        <div>
          <Label htmlFor="automation-name">Automation Name *</Label>
          <Input
            id="automation-name"
            placeholder="e.g., Customer Support Email Assistant, Sales Lead Classifier"
            value={config.name}
            onChange={(e) => setConfig({ ...config, name: e.target.value })}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && config.name) {
                handleNext()
              }
            }}
            className="mt-2"
            autoFocus
          />
          <p className="text-xs text-muted-foreground mt-1">
            Choose a descriptive name you'll easily recognize
          </p>
        </div>
      </div>

      <div className="flex gap-3 justify-center">
        <Button variant="outline" onClick={handlePrevious}>
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={handleNext} disabled={!config.name}>
          Continue to Gmail Setup
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'intro':
        return renderIntroStep()
      case 'naming':
        return renderNamingStep()
      case 'gmail-connection':
        return renderGmailConnectionStep()
      case 'api-integration':
        return renderApiIntegrationStep()
      case 'polling-settings':
        return renderPollingSettingsStep()
      case 'analysis-settings':
        return renderAnalysisSettingsStep()
      case 'approval-settings':
        return renderApprovalSettingsStep()
      case 'notification-settings':
        return renderNotificationSettingsStep()
      case 'custom-actions':
        return renderCustomActionsStep()
      case 'review':
        return renderReviewStep()
      case 'complete':
        return renderCompleteStep()
      default:
        return renderIntroStep()
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-2 mb-4">
            <SiGmail className="h-8 w-8 text-red-600" />
            <h1 className="text-3xl font-bold">Gmail Email Automation</h1>
          </div>
          <p className="text-muted-foreground">
            Intelligently monitor, analyze, and automate incoming Gmail messages using AI
          </p>
        </div>

        {/* Progress */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <span className="text-sm font-medium">
              Step {currentStepIndex + 1} of {steps.length}
            </span>
            <span className="text-sm text-muted-foreground">
              {Math.round(progress)}% complete
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Steps Navigation */}
        <div className="mb-8">
          <div className="flex justify-between items-center overflow-x-auto pb-2">
            {steps.map((step, index) => {
              const Icon = step.icon
              const isActive = index === currentStepIndex
              const isCompleted = index < currentStepIndex
              
              return (
                <div key={step.id} className="flex flex-col items-center min-w-0 flex-1">
                  <div className={`
                    w-10 h-10 rounded-full flex items-center justify-center mb-2
                    ${isActive ? 'bg-red-600 text-white' : 
                      isCompleted ? 'bg-green-600 text-white' : 
                      'bg-gray-200 dark:bg-gray-700 text-gray-400'}
                  `}>
                    {isCompleted ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <Icon className="h-5 w-5" />
                    )}
                  </div>
                  <span className={`text-xs text-center max-w-20 ${
                    isActive ? 'font-medium' : 'text-muted-foreground'
                  }`}>
                    {step.title}
                  </span>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto">
          {renderCurrentStep()}
        </div>
      </div>
    </div>
  )
}