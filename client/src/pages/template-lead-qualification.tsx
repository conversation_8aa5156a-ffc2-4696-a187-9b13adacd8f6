import { useState } from 'react'
import { motion } from 'framer-motion'
import { useLocation } from 'wouter'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Slider } from "@/components/ui/slider"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  ClipboardList, Brain, FileSpreadsheet, Send, Settings, Check, 
  ArrowRight, ChevronLeft, X, Plus, Calendar, MessageSquare, Hash, Globe, Mail, Search
} from 'lucide-react'
import { SiTypeform, SiGoogleforms, SiGooglesheets, SiOpenai, SiAnthropic, SiGoogle } from 'react-icons/si'

type StepType = 'intro' | 'naming' | 'form-source' | 'ai-scoring' | 'qualification-rules' | 'destination' | 'actions-question' | 'actions' | 'review' | 'complete'

interface LeadQualificationConfig {
  name: string
  formSource: {
    type: 'typeform' | 'googleforms' | 'webhook' | 'zapier'
    formId: string
    apiKey: string
    webhookUrl: string
    googleAuth: {
      isAuthenticated: boolean
      userEmail: string
      userName: string
      accountId: string
    }
    mappedFields: {
      name: string
      email: string
      phone: string
      company: string
      customFields: Array<{
        formField: string
        mappedName: string
      }>
    }
  }
  aiScoring: {
    provider: 'openai' | 'anthropic' | 'google'
    model: string
    scoringCriteria: Array<{
      id: string
      criterion: string
      weight: number
      description: string
    }>
    promptTemplate: string
    includeExplanation: boolean
  }
  qualificationRules: {
    scoreThreshold: number
    autoQualifyScore: number
    autoRejectScore: number
    customRules: Array<{
      id: string
      field: string
      operator: 'equals' | 'contains' | 'greater' | 'less'
      value: string
      action: 'qualify' | 'reject'
    }>
  }
  destination: {
    type: 'googlesheets' | 'crm' | 'webhook' | 'email'
    googleSheets: {
      spreadsheetId: string
      sheetName: string
      filesList: string[]
    }
    crmConfig: {
      platform: string
      apiKey: string
      endpoint: string
    }
    webhookConfig: {
      url: string
      headers: Record<string, string>
    }
    emailConfig: {
      recipients: string[]
      subject: string
      includeScore: boolean
    }
  }
  selectedActionsList: Array<{
    id: string
    type: string
    name: string
    description: string
    config: Record<string, any>
  }>
  wantsActions: boolean | null
}

export default function LeadQualificationTemplate() {
  const [, setLocation] = useLocation()
  const [currentStep, setCurrentStep] = useState<StepType>('intro')
  const [showActionConfig, setShowActionConfig] = useState<number | null>(null)
  const [actionSearchQuery, setActionSearchQuery] = useState('')
  const [wantsMoreActions, setWantsMoreActions] = useState(false)
  const [config, setConfig] = useState<LeadQualificationConfig>({
    name: '',
    formSource: {
      type: 'typeform',
      formId: '',
      apiKey: '',
      webhookUrl: '',
      googleAuth: {
        isAuthenticated: false,
        userEmail: '',
        userName: '',
        accountId: ''
      },
      mappedFields: {
        name: '',
        email: '',
        phone: '',
        company: '',
        customFields: []
      }
    },
    aiScoring: {
      provider: 'openai',
      model: 'gpt-4o',
      scoringCriteria: [
        { id: '1', criterion: 'Budget Fit', weight: 30, description: 'Evaluates if lead has adequate budget' },
        { id: '2', criterion: 'Use Case Match', weight: 25, description: 'How well their needs match our solution' },
        { id: '3', criterion: 'Decision Authority', weight: 20, description: 'Can they make purchasing decisions' },
        { id: '4', criterion: 'Timeline Urgency', weight: 15, description: 'How soon they need a solution' },
        { id: '5', criterion: 'Company Size', weight: 10, description: 'Is company size within target range' }
      ],
      promptTemplate: '',
      includeExplanation: true
    },
    qualificationRules: {
      scoreThreshold: 70,
      autoQualifyScore: 85,
      autoRejectScore: 30,
      customRules: []
    },
    destination: {
      type: 'googlesheets',
      googleSheets: {
        spreadsheetId: '',
        sheetName: 'Qualified Leads',
        filesList: []
      },
      crmConfig: {
        platform: '',
        apiKey: '',
        endpoint: ''
      },
      webhookConfig: {
        url: '',
        headers: {}
      },
      emailConfig: {
        recipients: [],
        subject: 'New Qualified Lead',
        includeScore: true
      }
    },
    selectedActionsList: [],
    wantsActions: null
  })

  const stepConfig: Record<StepType, { title: string; subtitle: string; icon: any; progress: number }> = {
    intro: { 
      title: 'AI Lead Qualification', 
      subtitle: 'Automatically score and qualify leads',
      icon: ClipboardList,
      progress: 0
    },
    naming: { 
      title: 'Name Your Automation', 
      subtitle: 'Choose a descriptive name for your workflow',
      icon: Settings,
      progress: 10
    },
    'form-source': { 
      title: 'Connect Form Source', 
      subtitle: 'Choose where your leads come from',
      icon: FileSpreadsheet,
      progress: 25
    },
    'ai-scoring': { 
      title: 'AI Scoring Configuration', 
      subtitle: 'Set up intelligent lead scoring',
      icon: Brain,
      progress: 40
    },
    'qualification-rules': { 
      title: 'Qualification Rules', 
      subtitle: 'Define what makes a qualified lead',
      icon: Settings,
      progress: 55
    },
    'destination': { 
      title: 'Lead Distribution', 
      subtitle: 'Configure where qualified leads go',
      icon: Send,
      progress: 70
    },
    'actions-question': {
      title: 'Additional Actions',
      subtitle: 'Extend your automation capabilities',
      icon: Settings,
      progress: 80
    },
    'actions': { 
      title: 'Select Actions', 
      subtitle: 'Define what actions to take',
      icon: Settings, 
      progress: 85 
    },
    'review': { 
      title: 'Review Configuration', 
      subtitle: 'Confirm your workflow settings',
      icon: Check,
      progress: 95
    },
    'complete': { 
      title: 'Automation Created!', 
      subtitle: 'Your automation is ready to use',
      icon: Check,
      progress: 100
    }
  }

  const nextStep = () => {
    const steps: StepType[] = ['intro', 'naming', 'form-source', 'ai-scoring', 'qualification-rules', 'destination', 'actions-question', 'actions', 'review', 'complete']
    const currentIndex = steps.indexOf(currentStep)
    
    if (currentStep === 'actions-question' && config.wantsActions === false) {
      setCurrentStep('review')
    } else if (currentStep === 'actions-question' && config.wantsActions === true) {
      setWantsMoreActions(true)
      setCurrentStep('actions')
    } else if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1])
    }
  }

  const prevStep = () => {
    const steps: StepType[] = ['intro', 'naming', 'form-source', 'ai-scoring', 'qualification-rules', 'destination', 'actions-question', 'actions', 'review', 'complete']
    const currentIndex = steps.indexOf(currentStep)
    
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1])
    }
  }

  const renderIntro = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center mx-auto">
        <ClipboardList className="h-10 w-10 text-white" />
      </div>
      
      <div>
        <h2 className="text-2xl font-bold mb-2">AI Lead Qualification</h2>
        <p className="text-muted-foreground max-w-md mx-auto">
          This template automatically scores and qualifies leads from form submissions using AI analysis,
          then routes qualified leads to your preferred destination - CRM, Google Sheets, or email.
        </p>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <SiTypeform className="h-8 w-8 mx-auto mb-2 text-purple-600" />
          <p className="text-xs font-medium">Form Capture</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Brain className="h-8 w-8 mx-auto mb-2 text-blue-600" />
          <p className="text-xs font-medium">AI Scoring</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <FileSpreadsheet className="h-8 w-8 mx-auto mb-2 text-green-600" />
          <p className="text-xs font-medium">Auto Qualify</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Send className="h-8 w-8 mx-auto mb-2 text-orange-600" />
          <p className="text-xs font-medium">Smart Routing</p>
        </div>
      </div>

      <Button 
        onClick={nextStep} 
        size="lg"
        className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
      >
        Start Setup
        <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
    </motion.div>
  )

  const renderNaming = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <ClipboardList className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h3 className="text-lg font-semibold mb-2">Let's name your lead qualification workflow</h3>
        <p className="text-muted-foreground text-sm">
          Choose a descriptive name to easily identify this automation
        </p>
      </div>

      <div className="max-w-md mx-auto">
        <Label htmlFor="workflow-name">Workflow Name *</Label>
        <Input
          id="workflow-name"
          placeholder="e.g., Website Lead Scorer, Demo Request Qualifier"
          value={config.name}
          onChange={(e) => setConfig({ ...config, name: e.target.value })}
          className="mt-2"
        />
      </div>

      <div className="flex gap-3 max-w-md mx-auto">
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          className="w-full"
          disabled={!config.name.trim()}
        >
          Continue <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderFormSource = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <ClipboardList className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h3 className="text-lg font-semibold mb-2">Connect Your Form Source</h3>
        <p className="text-muted-foreground text-sm">
          Choose how you'll receive form responses for qualification
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label>Form Platform *</Label>
          <RadioGroup
            value={config.formSource.type}
            onValueChange={(value) => setConfig({
              ...config,
              formSource: { ...config.formSource, type: value as any }
            })}
            className="mt-2"
          >
            <div className="flex items-center space-x-3 p-4 rounded-lg border hover:bg-muted/30 cursor-pointer">
              <RadioGroupItem value="typeform" id="typeform" />
              <Label htmlFor="typeform" className="cursor-pointer flex items-center gap-3 flex-1">
                <SiTypeform className="h-5 w-5 text-black" />
                <div>
                  <p className="font-medium">Typeform</p>
                  <p className="text-sm text-muted-foreground">Connect via Typeform API</p>
                </div>
              </Label>
            </div>

            <div className="flex items-center space-x-3 p-4 rounded-lg border hover:bg-muted/30 cursor-pointer">
              <RadioGroupItem value="googleforms" id="googleforms" />
              <Label htmlFor="googleforms" className="cursor-pointer flex items-center gap-3 flex-1">
                <SiGoogleforms className="h-5 w-5 text-purple-600" />
                <div>
                  <p className="font-medium">Google Forms</p>
                  <p className="text-sm text-muted-foreground">Connect with Google account</p>
                </div>
              </Label>
            </div>

            <div className="flex items-center space-x-3 p-4 rounded-lg border hover:bg-muted/30 cursor-pointer">
              <RadioGroupItem value="webhook" id="webhook" />
              <Label htmlFor="webhook" className="cursor-pointer flex items-center gap-3 flex-1">
                <Globe className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium">Webhook</p>
                  <p className="text-sm text-muted-foreground">Any form that supports webhooks</p>
                </div>
              </Label>
            </div>
          </RadioGroup>
        </div>

        {config.formSource.type === 'typeform' && (
          <div className="space-y-4">
            <div>
              <Label htmlFor="form-id">Typeform Form ID *</Label>
              <Input
                id="form-id"
                placeholder="e.g., AbCdEf123"
                value={config.formSource.formId}
                onChange={(e) => setConfig({
                  ...config,
                  formSource: { ...config.formSource, formId: e.target.value }
                })}
                className="mt-2"
              />
            </div>
            <div>
              <Label htmlFor="api-key">Typeform Personal Access Token *</Label>
              <Input
                id="api-key"
                type="password"
                placeholder="Your Typeform API token"
                value={config.formSource.apiKey}
                onChange={(e) => setConfig({
                  ...config,
                  formSource: { ...config.formSource, apiKey: e.target.value }
                })}
                className="mt-2"
              />
            </div>
          </div>
        )}

        {config.formSource.type === 'googleforms' && (
          <div className="space-y-4">
            <div>
              <Label>Authentication Type *</Label>
              <RadioGroup value="google" className="mt-2">
                <div className="flex items-center space-x-3 p-4 rounded-lg border bg-muted/30">
                  <RadioGroupItem value="google" id="google-auth" />
                  <Label htmlFor="google-auth" className="cursor-pointer flex-1">
                    <div className="flex items-center gap-3">
                      <SiGoogleforms className="h-5 w-5" />
                      <div>
                        <p className="font-medium">Google Account</p>
                        <p className="text-sm text-muted-foreground">
                          Note: Select existing Google account from below or Signin with a different account
                        </p>
                      </div>
                    </div>
                  </Label>
                </div>
              </RadioGroup>
            </div>

            {!config.formSource.googleAuth.isAuthenticated ? (
              <Button 
                onClick={() => setConfig({
                  ...config,
                  formSource: {
                    ...config.formSource,
                    googleAuth: {
                      isAuthenticated: true,
                      userEmail: '<EMAIL>',
                      userName: 'John Doe',
                      accountId: '12345'
                    }
                  }
                })}
                className="w-full"
                variant="outline"
              >
                <SiGoogleforms className="mr-2 h-4 w-4" />
                Sign in with Google
              </Button>
            ) : (
              <div className="p-4 bg-muted/30 rounded-lg border">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <SiGoogleforms className="h-5 w-5" />
                    <div>
                      <p className="font-medium">Google account: {config.formSource.googleAuth.userName} ({config.formSource.googleAuth.userEmail})</p>
                      <p className="text-sm text-muted-foreground">Account ID: {config.formSource.googleAuth.accountId}</p>
                    </div>
                  </div>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">Connected</Badge>
                </div>
              </div>
            )}

            <div>
              <Label htmlFor="form-id">Google Form ID *</Label>
              <Input
                id="form-id"
                placeholder="e.g., 1FAIpQLSd..."
                value={config.formSource.formId}
                onChange={(e) => setConfig({
                  ...config,
                  formSource: { ...config.formSource, formId: e.target.value }
                })}
                className="mt-2"
              />
            </div>
          </div>
        )}

        {config.formSource.type === 'webhook' && (
          <div>
            <Label htmlFor="webhook-url">Webhook Endpoint *</Label>
            <Input
              id="webhook-url"
              placeholder="https://your-app.com/webhook/leads"
              value={config.formSource.webhookUrl}
              onChange={(e) => setConfig({
                ...config,
                formSource: { ...config.formSource, webhookUrl: e.target.value }
              })}
              className="mt-2"
            />
            <p className="text-xs text-muted-foreground mt-2">
              We'll provide you with a unique webhook URL to add to your form
            </p>
          </div>
        )}

        {/* Field Mapping */}
        <div className="space-y-4 pt-4 border-t">
          <h4 className="font-medium">Map Form Fields</h4>
          <div className="grid gap-3">
            <div>
              <Label htmlFor="name-field">Name Field</Label>
              <Input
                id="name-field"
                placeholder="e.g., full_name, name"
                value={config.formSource.mappedFields.name}
                onChange={(e) => setConfig({
                  ...config,
                  formSource: {
                    ...config.formSource,
                    mappedFields: {
                      ...config.formSource.mappedFields,
                      name: e.target.value
                    }
                  }
                })}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="email-field">Email Field *</Label>
              <Input
                id="email-field"
                placeholder="e.g., email, email_address"
                value={config.formSource.mappedFields.email}
                onChange={(e) => setConfig({
                  ...config,
                  formSource: {
                    ...config.formSource,
                    mappedFields: {
                      ...config.formSource.mappedFields,
                      email: e.target.value
                    }
                  }
                })}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="company-field">Company Field</Label>
              <Input
                id="company-field"
                placeholder="e.g., company, organization"
                value={config.formSource.mappedFields.company}
                onChange={(e) => setConfig({
                  ...config,
                  formSource: {
                    ...config.formSource,
                    mappedFields: {
                      ...config.formSource.mappedFields,
                      company: e.target.value
                    }
                  }
                })}
                className="mt-1"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="flex gap-3">
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          className="w-full"
          disabled={
            (config.formSource.type === 'typeform' && (!config.formSource.formId || !config.formSource.apiKey)) ||
            (config.formSource.type === 'googleforms' && (!config.formSource.googleAuth.isAuthenticated || !config.formSource.formId)) ||
            (config.formSource.type === 'webhook' && !config.formSource.webhookUrl) ||
            !config.formSource.mappedFields.email
          }
        >
          Continue <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderAIScoring = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Brain className="h-16 w-16 mx-auto mb-4 text-blue-600" />
        <h3 className="text-lg font-semibold mb-2">Configure AI Scoring</h3>
        <p className="text-muted-foreground text-sm">
          Set up how AI will evaluate and score your leads
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label>AI Provider *</Label>
          <Select
            value={config.aiScoring.provider}
            onValueChange={(value) => setConfig({
              ...config,
              aiScoring: { ...config.aiScoring, provider: value as any }
            })}
          >
            <SelectTrigger className="mt-2">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="openai">
                <div className="flex items-center gap-2">
                  <SiOpenai className="h-4 w-4" />
                  OpenAI
                </div>
              </SelectItem>
              <SelectItem value="anthropic">
                <div className="flex items-center gap-2">
                  <SiAnthropic className="h-4 w-4" />
                  Anthropic
                </div>
              </SelectItem>
              <SelectItem value="google">
                <div className="flex items-center gap-2">
                  <SiGoogle className="h-4 w-4" />
                  Google AI
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label>Model *</Label>
          <Select
            value={config.aiScoring.model}
            onValueChange={(value) => setConfig({
              ...config,
              aiScoring: { ...config.aiScoring, model: value }
            })}
          >
            <SelectTrigger className="mt-2">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {config.aiScoring.provider === 'openai' && (
                <>
                  <SelectItem value="gpt-4o">GPT-4o (Latest)</SelectItem>
                  <SelectItem value="gpt-4">GPT-4</SelectItem>
                  <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                </>
              )}
              {config.aiScoring.provider === 'anthropic' && (
                <>
                  <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
                  <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                  <SelectItem value="claude-3-haiku">Claude 3 Haiku</SelectItem>
                </>
              )}
              {config.aiScoring.provider === 'google' && (
                <>
                  <SelectItem value="gemini-1.5-pro">Gemini 1.5 Pro</SelectItem>
                  <SelectItem value="gemini-1.0-pro">Gemini 1.0 Pro</SelectItem>
                </>
              )}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label>Scoring Criteria</Label>
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                const newCriterion = {
                  id: Date.now().toString(),
                  criterion: '',
                  weight: 20,
                  description: ''
                }
                setConfig({
                  ...config,
                  aiScoring: {
                    ...config.aiScoring,
                    scoringCriteria: [...config.aiScoring.scoringCriteria, newCriterion]
                  }
                })
              }}
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Criterion
            </Button>
          </div>

          <div className="space-y-3">
            {config.aiScoring.scoringCriteria.map((criterion, index) => (
              <Card key={criterion.id} className="p-4">
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <div className="flex-1 space-y-3">
                      <Input
                        placeholder="Criterion name (e.g., Budget Fit)"
                        value={criterion.criterion}
                        onChange={(e) => {
                          const updated = [...config.aiScoring.scoringCriteria]
                          updated[index].criterion = e.target.value
                          setConfig({
                            ...config,
                            aiScoring: { ...config.aiScoring, scoringCriteria: updated }
                          })
                        }}
                      />
                      <Textarea
                        placeholder="Description of what to evaluate"
                        value={criterion.description}
                        onChange={(e) => {
                          const updated = [...config.aiScoring.scoringCriteria]
                          updated[index].description = e.target.value
                          setConfig({
                            ...config,
                            aiScoring: { ...config.aiScoring, scoringCriteria: updated }
                          })
                        }}
                        rows={2}
                      />
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-center">
                        <Label className="text-xs">Weight</Label>
                        <div className="flex items-center gap-1 mt-1">
                          <Input
                            type="number"
                            min="0"
                            max="100"
                            value={criterion.weight}
                            onChange={(e) => {
                              const updated = [...config.aiScoring.scoringCriteria]
                              updated[index].weight = parseInt(e.target.value) || 0
                              setConfig({
                                ...config,
                                aiScoring: { ...config.aiScoring, scoringCriteria: updated }
                              })
                            }}
                            className="w-16 text-center"
                          />
                          <span className="text-sm">%</span>
                        </div>
                      </div>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => {
                          const updated = config.aiScoring.scoringCriteria.filter((_, i) => i !== index)
                          setConfig({
                            ...config,
                            aiScoring: { ...config.aiScoring, scoringCriteria: updated }
                          })
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
            <span className="text-sm font-medium">Total Weight:</span>
            <span className={`text-sm font-medium ${
              config.aiScoring.scoringCriteria.reduce((sum, c) => sum + c.weight, 0) === 100
                ? 'text-green-600'
                : 'text-orange-600'
            }`}>
              {config.aiScoring.scoringCriteria.reduce((sum, c) => sum + c.weight, 0)}%
            </span>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="include-explanation"
            checked={config.aiScoring.includeExplanation}
            onCheckedChange={(checked) => setConfig({
              ...config,
              aiScoring: { ...config.aiScoring, includeExplanation: checked as boolean }
            })}
          />
          <Label htmlFor="include-explanation" className="text-sm cursor-pointer">
            Include AI explanation for each score
          </Label>
        </div>
      </div>

      <div className="flex gap-3">
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          className="w-full"
          disabled={
            config.aiScoring.scoringCriteria.length === 0 ||
            config.aiScoring.scoringCriteria.some(c => !c.criterion || !c.description)
          }
        >
          Continue <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderQualificationRules = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Settings className="h-16 w-16 mx-auto mb-4 text-green-600" />
        <h3 className="text-lg font-semibold mb-2">Set Qualification Rules</h3>
        <p className="text-muted-foreground text-sm">
          Define when leads should be qualified or rejected
        </p>
      </div>

      <div className="space-y-6">
        <div className="space-y-4">
          <h4 className="font-medium">Score Thresholds</h4>
          
          <div>
            <div className="flex items-center justify-between mb-2">
              <Label htmlFor="qualify-threshold">Qualification Threshold</Label>
              <span className="text-sm font-medium">{config.qualificationRules.scoreThreshold}%</span>
            </div>
            <Slider
              id="qualify-threshold"
              min={0}
              max={100}
              step={5}
              value={[config.qualificationRules.scoreThreshold]}
              onValueChange={(value) => setConfig({
                ...config,
                qualificationRules: {
                  ...config.qualificationRules,
                  scoreThreshold: value[0]
                }
              })}
              className="mt-2"
            />
            <p className="text-xs text-muted-foreground mt-2">
              Leads scoring above this will be marked as qualified
            </p>
          </div>

          <div>
            <div className="flex items-center justify-between mb-2">
              <Label htmlFor="auto-qualify">Auto-Qualify Score</Label>
              <span className="text-sm font-medium">{config.qualificationRules.autoQualifyScore}%</span>
            </div>
            <Slider
              id="auto-qualify"
              min={0}
              max={100}
              step={5}
              value={[config.qualificationRules.autoQualifyScore]}
              onValueChange={(value) => setConfig({
                ...config,
                qualificationRules: {
                  ...config.qualificationRules,
                  autoQualifyScore: value[0]
                }
              })}
              className="mt-2"
            />
            <p className="text-xs text-muted-foreground mt-2">
              High-scoring leads will be fast-tracked to sales
            </p>
          </div>

          <div>
            <div className="flex items-center justify-between mb-2">
              <Label htmlFor="auto-reject">Auto-Reject Score</Label>
              <span className="text-sm font-medium">{config.qualificationRules.autoRejectScore}%</span>
            </div>
            <Slider
              id="auto-reject"
              min={0}
              max={100}
              step={5}
              value={[config.qualificationRules.autoRejectScore]}
              onValueChange={(value) => setConfig({
                ...config,
                qualificationRules: {
                  ...config.qualificationRules,
                  autoRejectScore: value[0]
                }
              })}
              className="mt-2"
            />
            <p className="text-xs text-muted-foreground mt-2">
              Low-scoring leads will be automatically rejected
            </p>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium">Custom Rules (Optional)</h4>
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                const newRule = {
                  id: Date.now().toString(),
                  field: '',
                  operator: 'contains' as const,
                  value: '',
                  action: 'qualify' as const
                }
                setConfig({
                  ...config,
                  qualificationRules: {
                    ...config.qualificationRules,
                    customRules: [...config.qualificationRules.customRules, newRule]
                  }
                })
              }}
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Rule
            </Button>
          </div>

          {config.qualificationRules.customRules.length > 0 && (
            <div className="space-y-3">
              {config.qualificationRules.customRules.map((rule, index) => (
                <Card key={rule.id} className="p-4">
                  <div className="flex items-center gap-3">
                    <Input
                      placeholder="Field name"
                      value={rule.field}
                      onChange={(e) => {
                        const updated = [...config.qualificationRules.customRules]
                        updated[index].field = e.target.value
                        setConfig({
                          ...config,
                          qualificationRules: {
                            ...config.qualificationRules,
                            customRules: updated
                          }
                        })
                      }}
                      className="flex-1"
                    />
                    <Select
                      value={rule.operator}
                      onValueChange={(value) => {
                        const updated = [...config.qualificationRules.customRules]
                        updated[index].operator = value as any
                        setConfig({
                          ...config,
                          qualificationRules: {
                            ...config.qualificationRules,
                            customRules: updated
                          }
                        })
                      }}
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="equals">equals</SelectItem>
                        <SelectItem value="contains">contains</SelectItem>
                        <SelectItem value="greater">greater than</SelectItem>
                        <SelectItem value="less">less than</SelectItem>
                      </SelectContent>
                    </Select>
                    <Input
                      placeholder="Value"
                      value={rule.value}
                      onChange={(e) => {
                        const updated = [...config.qualificationRules.customRules]
                        updated[index].value = e.target.value
                        setConfig({
                          ...config,
                          qualificationRules: {
                            ...config.qualificationRules,
                            customRules: updated
                          }
                        })
                      }}
                      className="flex-1"
                    />
                    <Select
                      value={rule.action}
                      onValueChange={(value) => {
                        const updated = [...config.qualificationRules.customRules]
                        updated[index].action = value as any
                        setConfig({
                          ...config,
                          qualificationRules: {
                            ...config.qualificationRules,
                            customRules: updated
                          }
                        })
                      }}
                    >
                      <SelectTrigger className="w-28">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="qualify">Qualify</SelectItem>
                        <SelectItem value="reject">Reject</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => {
                        const updated = config.qualificationRules.customRules.filter((_, i) => i !== index)
                        setConfig({
                          ...config,
                          qualificationRules: {
                            ...config.qualificationRules,
                            customRules: updated
                          }
                        })
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>

      <div className="flex gap-3">
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={nextStep} className="w-full">
          Continue <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderDestination = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Send className="h-16 w-16 mx-auto mb-4 text-green-600" />
        <h3 className="text-lg font-semibold mb-2">Where Should Qualified Leads Go?</h3>
        <p className="text-muted-foreground text-sm">
          Choose how to deliver your qualified leads
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label>Destination Type *</Label>
          <RadioGroup
            value={config.destination.type}
            onValueChange={(value) => setConfig({
              ...config,
              destination: { ...config.destination, type: value as any }
            })}
            className="mt-2"
          >
            <div className="flex items-center space-x-3 p-4 rounded-lg border hover:bg-muted/30 cursor-pointer">
              <RadioGroupItem value="googlesheets" id="googlesheets" />
              <Label htmlFor="googlesheets" className="cursor-pointer flex items-center gap-3 flex-1">
                <SiGooglesheets className="h-5 w-5 text-green-600" />
                <div>
                  <p className="font-medium">Google Sheets</p>
                  <p className="text-sm text-muted-foreground">Add leads to a spreadsheet</p>
                </div>
              </Label>
            </div>

            <div className="flex items-center space-x-3 p-4 rounded-lg border hover:bg-muted/30 cursor-pointer">
              <RadioGroupItem value="crm" id="crm" />
              <Label htmlFor="crm" className="cursor-pointer flex items-center gap-3 flex-1">
                <FileSpreadsheet className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium">CRM System</p>
                  <p className="text-sm text-muted-foreground">Send to Salesforce, HubSpot, etc.</p>
                </div>
              </Label>
            </div>

            <div className="flex items-center space-x-3 p-4 rounded-lg border hover:bg-muted/30 cursor-pointer">
              <RadioGroupItem value="webhook" id="webhook-dest" />
              <Label htmlFor="webhook-dest" className="cursor-pointer flex items-center gap-3 flex-1">
                <Globe className="h-5 w-5 text-purple-600" />
                <div>
                  <p className="font-medium">Webhook</p>
                  <p className="text-sm text-muted-foreground">Send to any API endpoint</p>
                </div>
              </Label>
            </div>

            <div className="flex items-center space-x-3 p-4 rounded-lg border hover:bg-muted/30 cursor-pointer">
              <RadioGroupItem value="email" id="email" />
              <Label htmlFor="email" className="cursor-pointer flex items-center gap-3 flex-1">
                <Mail className="h-5 w-5 text-orange-600" />
                <div>
                  <p className="font-medium">Email Notification</p>
                  <p className="text-sm text-muted-foreground">Alert your sales team</p>
                </div>
              </Label>
            </div>
          </RadioGroup>
        </div>

        {config.destination.type === 'googlesheets' && (
          <div className="space-y-4">
            {!config.formSource.googleAuth.isAuthenticated ? (
              <Button 
                onClick={() => setConfig({
                  ...config,
                  formSource: {
                    ...config.formSource,
                    googleAuth: {
                      isAuthenticated: true,
                      userEmail: '<EMAIL>',
                      userName: 'John Doe',
                      accountId: '12345'
                    }
                  }
                })}
                className="w-full"
                variant="outline"
              >
                <SiGooglesheets className="mr-2 h-4 w-4" />
                Sign in with Google
              </Button>
            ) : (
              <div className="p-4 bg-muted/30 rounded-lg border">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <SiGooglesheets className="h-5 w-5" />
                    <div>
                      <p className="font-medium">Google account: {config.formSource.googleAuth.userName} ({config.formSource.googleAuth.userEmail})</p>
                      <p className="text-sm text-muted-foreground">Account ID: {config.formSource.googleAuth.accountId}</p>
                    </div>
                  </div>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">Connected</Badge>
                </div>
              </div>
            )}

            <div>
              <Label htmlFor="spreadsheet-id">Spreadsheet ID *</Label>
              <Input
                id="spreadsheet-id"
                placeholder="e.g., 1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
                value={config.destination.googleSheets.spreadsheetId}
                onChange={(e) => setConfig({
                  ...config,
                  destination: {
                    ...config.destination,
                    googleSheets: {
                      ...config.destination.googleSheets,
                      spreadsheetId: e.target.value
                    }
                  }
                })}
                className="mt-2"
              />
            </div>

            <div>
              <Label htmlFor="sheet-name">Sheet Name *</Label>
              <Input
                id="sheet-name"
                placeholder="e.g., Qualified Leads"
                value={config.destination.googleSheets.sheetName}
                onChange={(e) => setConfig({
                  ...config,
                  destination: {
                    ...config.destination,
                    googleSheets: {
                      ...config.destination.googleSheets,
                      sheetName: e.target.value
                    }
                  }
                })}
                className="mt-2"
              />
            </div>
          </div>
        )}

        {config.destination.type === 'email' && (
          <div className="space-y-4">
            <div>
              <Label htmlFor="recipients">Recipient Emails *</Label>
              <Textarea
                id="recipients"
                placeholder="<EMAIL>, <EMAIL>"
                value={config.destination.emailConfig.recipients.join(', ')}
                onChange={(e) => setConfig({
                  ...config,
                  destination: {
                    ...config.destination,
                    emailConfig: {
                      ...config.destination.emailConfig,
                      recipients: e.target.value.split(',').map(email => email.trim()).filter(Boolean)
                    }
                  }
                })}
                className="mt-2"
                rows={2}
              />
              <p className="text-xs text-muted-foreground mt-1">
                Separate multiple emails with commas
              </p>
            </div>

            <div>
              <Label htmlFor="email-subject">Email Subject *</Label>
              <Input
                id="email-subject"
                placeholder="e.g., New Qualified Lead: {name}"
                value={config.destination.emailConfig.subject}
                onChange={(e) => setConfig({
                  ...config,
                  destination: {
                    ...config.destination,
                    emailConfig: {
                      ...config.destination.emailConfig,
                      subject: e.target.value
                    }
                  }
                })}
                className="mt-2"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-score"
                checked={config.destination.emailConfig.includeScore}
                onCheckedChange={(checked) => setConfig({
                  ...config,
                  destination: {
                    ...config.destination,
                    emailConfig: {
                      ...config.destination.emailConfig,
                      includeScore: checked as boolean
                    }
                  }
                })}
              />
              <Label htmlFor="include-score" className="text-sm cursor-pointer">
                Include AI score and explanation in email
              </Label>
            </div>
          </div>
        )}
      </div>

      <div className="flex gap-3">
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          className="w-full"
          disabled={
            (config.destination.type === 'googlesheets' && (!config.formSource.googleAuth.isAuthenticated || !config.destination.googleSheets.spreadsheetId || !config.destination.googleSheets.sheetName)) ||
            (config.destination.type === 'email' && (config.destination.emailConfig.recipients.length === 0 || !config.destination.emailConfig.subject))
          }
        >
          Continue <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderActionsQuestion = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
        <h3 className="text-lg font-semibold mb-2">Do you want to add any tools or actions?</h3>
        <p className="text-muted-foreground text-sm">
          Extend your workflow with additional integrations and actions
        </p>
      </div>

      <div className="flex gap-4 max-w-md mx-auto">
        <Button
          variant="outline"
          size="lg"
          onClick={() => {
            setConfig({ ...config, wantsActions: true })
            setCurrentStep('actions')
          }}
          className="h-auto py-6"
        >
          <div className="flex flex-col items-center gap-2">
            <Plus className="h-8 w-8" />
            <span>Yes, add actions</span>
          </div>
        </Button>

        <Button
          variant="outline"
          size="lg"
          onClick={() => {
            setConfig({ ...config, wantsActions: false })
            setCurrentStep('review')
          }}
          className="h-auto py-6"
        >
          <div className="flex flex-col items-center gap-2">
            <ArrowRight className="h-8 w-8" />
            <span>No, continue without</span>
          </div>
        </Button>
      </div>

      <div>
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
      </div>
    </motion.div>
  )

  const renderActions = () => {
    const selectedActionsList = config.selectedActionsList || []

    const availableActionsList = [
      { id: 'discord-read', name: 'Discord Read', icon: MessageSquare, desc: 'Read messages from any Discord channel' },
      { id: 'discord-send', name: 'Discord Send', icon: MessageSquare, desc: 'Send messages to any Discord channel' },
      { id: 'gmail-read', name: 'Gmail Read', icon: Mail, desc: 'Read emails from your Gmail account' },
      { id: 'gmail-send', name: 'Gmail Send', icon: Mail, desc: 'Send emails via your Gmail account' },
      { id: 'gcalendar-read', name: 'Google Calendar Read', icon: Calendar, desc: 'Read events from any Google Calendar' },
      { id: 'gcalendar-write', name: 'Google Calendar Write', icon: Calendar, desc: 'Create events on any Google Calendar' },
      { id: 'http-request', name: 'HTTP Request', icon: Globe, desc: 'Make GET, POST, PUT, or DELETE HTTP requests' },
      { id: 'slack-read', name: 'Slack Read', icon: Hash, desc: 'Read messages from a specified Slack channel' },
      { id: 'slack-send', name: 'Slack Send', icon: Hash, desc: 'Send a message to a specified Slack channel' }
    ]

    // Filter actions based on search query
    const filteredActions = availableActionsList.filter(action => {
      const query = actionSearchQuery.toLowerCase()
      return (
        action.name.toLowerCase().includes(query) ||
        action.desc.toLowerCase().includes(query)
      )
    })

    const addAction = (action: any) => {
      const newAction = {
        id: Date.now().toString(),
        type: action.id,
        name: action.name,
        description: action.desc,
        config: {}
      }
      const updatedList = [...selectedActionsList, newAction]
      setConfig({ ...config, selectedActionsList: updatedList })
      setShowActionConfig(updatedList.length - 1)
      setWantsMoreActions(false)
    }

    const removeAction = (index: number) => {
      const updatedList = selectedActionsList.filter((_, i) => i !== index)
      setConfig({ ...config, selectedActionsList: updatedList })
    }

    // If no actions yet or wants more actions, show action selection
    if (selectedActionsList.length === 0 || wantsMoreActions) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          <div className="text-center">
            <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
            <h3 className="text-lg font-semibold mb-2">
              {selectedActionsList.length === 0 ? 'What should happen next?' : 'Add another action?'}
            </h3>
            <p className="text-muted-foreground text-sm">
              {selectedActionsList.length === 0 
                ? 'Choose an action to perform when leads are qualified'
                : 'Chain multiple actions to create powerful workflows'}
            </p>
          </div>

          {/* Search */}
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search actions..."
                value={actionSearchQuery}
                onChange={(e) => setActionSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Available Actions */}
          {filteredActions.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No actions found matching "{actionSearchQuery}"</p>
            </div>
          ) : (
            <div className="grid gap-3 max-w-2xl mx-auto">
              {filteredActions.map((action) => (
                <Card
                  key={action.id}
                  className="cursor-pointer transition-all hover:shadow-md"
                  onClick={() => addAction(action)}
                >
                  <CardHeader className="p-4">
                    <div className="flex items-center gap-3">
                      <action.icon className="h-5 w-5 text-primary" />
                      <div className="flex-1">
                        <CardTitle className="text-sm">{action.name}</CardTitle>
                        <CardDescription className="text-xs mt-1">{action.desc}</CardDescription>
                      </div>
                      <Plus className="h-5 w-5 text-muted-foreground" />
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          )}

          <div className="flex gap-3 max-w-2xl mx-auto">
            <Button onClick={prevStep} variant="outline" className="w-full">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Previous
            </Button>
            {selectedActionsList.length > 0 && (
              <Button 
                onClick={() => setWantsMoreActions(false)} 
                variant="outline"
                className="w-full"
              >
                Continue without adding
              </Button>
            )}
          </div>
        </motion.div>
      )
    }

    // Show current actions with option to add more
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <div className="text-center">
          <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
          <h3 className="text-lg font-semibold mb-2">Your Action Chain</h3>
          <p className="text-muted-foreground text-sm">
            {selectedActionsList.length} action{selectedActionsList.length !== 1 ? 's' : ''} configured
          </p>
        </div>

        {/* Action Chain Visualization */}
        <div className="max-w-2xl mx-auto space-y-3">
          {/* Trigger */}
          <div className="flex items-center gap-3 p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg border-2 border-purple-200 dark:border-purple-800">
            <ClipboardList className="h-5 w-5 text-purple-600" />
            <div className="flex-1">
              <p className="font-medium">Trigger: Lead Qualified</p>
              <p className="text-xs text-muted-foreground">When a lead meets your qualification criteria</p>
            </div>
          </div>

          {/* Arrow */}
          <div className="flex justify-center">
            <ArrowRight className="h-4 w-4 text-muted-foreground" />
          </div>

          {/* Actions */}
          {selectedActionsList.map((action, index) => (
            <div key={action.id}>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center gap-3 p-4 bg-muted/30 rounded-lg border"
              >
                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-xs font-medium">
                  {index + 1}
                </div>
                <div className="flex-1">
                  <p className="font-medium">{action.name}</p>
                  <p className="text-xs text-muted-foreground">{action.description}</p>
                </div>
                <div className="flex gap-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setShowActionConfig(index)}
                    className="h-8 px-2"
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => removeAction(index)}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </motion.div>
              {index < selectedActionsList.length - 1 && (
                <div className="flex justify-center my-2">
                  <ArrowRight className="h-4 w-4 text-muted-foreground" />
                </div>
              )}
            </div>
          ))}

          {/* Add Another Action Button */}
          <Button
            variant="outline"
            onClick={() => setWantsMoreActions(true)}
            className="w-full"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Another Action
          </Button>
        </div>

        <div className="flex gap-3 max-w-2xl mx-auto">
          <Button onClick={prevStep} variant="outline" className="w-full">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button 
            onClick={nextStep} 
            className="w-full"
            disabled={selectedActionsList.length === 0}
          >
            Continue <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>

        {/* Action Configuration Modal */}
        {showActionConfig !== null && selectedActionsList[showActionConfig] && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowActionConfig(null)}
          >
            <motion.div
              initial={{ scale: 0.95 }}
              animate={{ scale: 1 }}
              className="bg-card p-6 rounded-lg shadow-lg max-w-md w-full max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">
                Configure {selectedActionsList[showActionConfig].name}
              </h3>
              
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Configuration options will appear here based on the action type.
                </p>
                <div className="flex justify-end gap-2 mt-6">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowActionConfig(null)}
                  >
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => setShowActionConfig(null)}
                  >
                    Save Configuration
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </motion.div>
    )
  }

  const renderReview = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Check className="h-16 w-16 mx-auto mb-4 text-green-600" />
        <h3 className="text-lg font-semibold mb-2">Review Your Configuration</h3>
        <p className="text-muted-foreground text-sm">
          Make sure everything looks good before creating your automation
        </p>
      </div>

      <div className="space-y-4 max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Workflow Name</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm">{config.name}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Form Source</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-center gap-2">
              {config.formSource.type === 'typeform' && <SiTypeform className="h-4 w-4" />}
              {config.formSource.type === 'googleforms' && <SiGoogleforms className="h-4 w-4" />}
              {config.formSource.type === 'webhook' && <Globe className="h-4 w-4" />}
              <span className="text-sm capitalize">{config.formSource.type}</span>
            </div>
            <p className="text-sm text-muted-foreground">
              Email field: {config.formSource.mappedFields.email}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">AI Scoring</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <p className="text-sm">
              Provider: {config.aiScoring.provider} ({config.aiScoring.model})
            </p>
            <p className="text-sm text-muted-foreground">
              {config.aiScoring.scoringCriteria.length} scoring criteria configured
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Qualification Rules</CardTitle>
          </CardHeader>
          <CardContent className="space-y-1">
            <p className="text-sm">Qualification threshold: {config.qualificationRules.scoreThreshold}%</p>
            <p className="text-sm">Auto-qualify: {config.qualificationRules.autoQualifyScore}%</p>
            <p className="text-sm">Auto-reject: {config.qualificationRules.autoRejectScore}%</p>
            {config.qualificationRules.customRules.length > 0 && (
              <p className="text-sm text-muted-foreground">
                {config.qualificationRules.customRules.length} custom rules
              </p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Destination</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              {config.destination.type === 'googlesheets' && <SiGooglesheets className="h-4 w-4 text-green-600" />}
              {config.destination.type === 'email' && <Mail className="h-4 w-4 text-orange-600" />}
              <span className="text-sm capitalize">{config.destination.type}</span>
            </div>
          </CardContent>
        </Card>

        {config.selectedActionsList.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Additional Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm">{config.selectedActionsList.length} actions configured</p>
            </CardContent>
          </Card>
        )}
      </div>

      <div className="flex gap-3 max-w-2xl mx-auto">
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={nextStep} className="w-full">
          Create Automation <Check className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderComplete = () => (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className="text-center space-y-6"
    >
      <div className="h-20 w-20 bg-green-100 rounded-full flex items-center justify-center mx-auto">
        <Check className="h-10 w-10 text-green-600" />
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-2">Automation Created!</h2>
        <p className="text-muted-foreground">
          Your lead qualification workflow "{config.name}" is ready to use
        </p>
      </div>

      <div className="flex gap-3 max-w-md mx-auto">
        <Button
          onClick={() => setLocation('/dashboard/automations')}
          variant="outline"
          className="w-full"
        >
          View Automations
        </Button>
        <Button
          onClick={() => setLocation('/dashboard')}
          className="w-full"
        >
          Go to Dashboard
        </Button>
      </div>
    </motion.div>
  )

  const renderStepContent = () => {
    switch (currentStep) {
      case 'intro': return renderIntro()
      case 'naming': return renderNaming()
      case 'form-source': return renderFormSource()
      case 'ai-scoring': return renderAIScoring()
      case 'qualification-rules': return renderQualificationRules()
      case 'destination': return renderDestination()
      case 'actions-question': return renderActionsQuestion()
      case 'actions': return renderActions()
      case 'review': return renderReview()
      case 'complete': return renderComplete()
      default: return null
    }
  }

  const CurrentIcon = stepConfig[currentStep].icon

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => setLocation('/dashboard/browse-templates')}
          className="mb-4"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back to Templates
        </Button>

        {currentStep !== 'complete' && (
          <>
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <CurrentIcon className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">{stepConfig[currentStep].title}</h1>
                <p className="text-muted-foreground text-sm">{stepConfig[currentStep].subtitle}</p>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex justify-between text-xs text-muted-foreground mb-2">
                <span>Step {Object.keys(stepConfig).indexOf(currentStep) + 1} of 10</span>
                <span>{stepConfig[currentStep].progress}% Complete</span>
              </div>
              <Progress value={stepConfig[currentStep].progress} className="h-2" />
            </div>
          </>
        )}
      </div>

      {/* Content */}
      <Card className="p-6">
        {renderStepContent()}
      </Card>
    </div>
  )
}