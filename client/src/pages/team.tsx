import { useState } from 'react'
import { 
  Users,
  UserPlus,
  Mail,
  Shield,
  MoreVertical,
  Search,
  Crown,
  User,
  X,
  Eye,
  Lock,
  Edit
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { useToast } from '@/hooks/use-toast'
import { useAuth } from '@/contexts/AuthContext'

interface TeamMember {
  id: string
  name: string
  email: string
  role: 'owner' | 'admin' | 'editor' | 'member' | 'viewer'
  avatar?: string
  joinedAt: Date
  lastActive: Date
  department?: string
  permissions?: string[]
}

const mockMembers: TeamMember[] = [
  {
    id: '1',
    name: 'Kaizar Bharmal',
    email: '<EMAIL>',
    role: 'owner',
    joinedAt: new Date(Date.now() - 86400000 * 60),
    lastActive: new Date(),
    department: 'Engineering',
    permissions: ['all'],
  },
  {
    id: '2',
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    role: 'admin',
    joinedAt: new Date(Date.now() - 86400000 * 30),
    lastActive: new Date(Date.now() - 3600000),
    department: 'Operations',
    permissions: ['manage_team', 'manage_automations', 'view_logs', 'manage_credentials'],
  },
  {
    id: '3',
    name: 'Mike Chen',
    email: '<EMAIL>',
    role: 'editor',
    joinedAt: new Date(Date.now() - 86400000 * 15),
    lastActive: new Date(Date.now() - 86400000),
    department: 'Marketing',
    permissions: ['create_automations', 'edit_automations', 'view_logs'],
  },
  {
    id: '4',
    name: 'Emily Rodriguez',
    email: '<EMAIL>',
    role: 'member',
    joinedAt: new Date(Date.now() - 86400000 * 7),
    lastActive: new Date(Date.now() - 3600000 * 4),
    department: 'Sales',
    permissions: ['create_automations', 'view_automations', 'view_logs'],
  },
  {
    id: '5',
    name: 'David Park',
    email: '<EMAIL>',
    role: 'viewer',
    joinedAt: new Date(Date.now() - 86400000 * 3),
    lastActive: new Date(Date.now() - 86400000 * 2),
    department: 'Finance',
    permissions: ['view_automations', 'view_logs'],
  },
]

export default function Team() {
  const [members, setMembers] = useState(mockMembers)
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false)
  const [removeMemberId, setRemoveMemberId] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const { user } = useAuth()
  const { toast } = useToast()
  
  const [inviteForm, setInviteForm] = useState({
    email: '',
    role: 'member' as 'admin' | 'editor' | 'member' | 'viewer',
    department: '',
    permissions: [] as string[],
  })

  const currentUserRole = members.find(m => m.email === user?.email)?.role || 'member'
  const canManageTeam = currentUserRole === 'owner' || currentUserRole === 'admin'

  const filteredMembers = members.filter(member =>
    member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    member.email.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleInvite = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (members.find(m => m.email === inviteForm.email)) {
      toast({
        title: 'User already exists',
        description: 'This email is already part of the team.',
        variant: 'destructive',
      })
      return
    }

    // Mock adding a new member
    const newMember: TeamMember = {
      id: String(members.length + 1),
      name: inviteForm.email.split('@')[0],
      email: inviteForm.email,
      role: inviteForm.role,
      joinedAt: new Date(),
      lastActive: new Date(),
      department: inviteForm.department || undefined,
      permissions: getPermissionsForRole(inviteForm.role),
    }

    setMembers([...members, newMember])
    setInviteDialogOpen(false)
    setInviteForm({ email: '', role: 'member', department: '', permissions: [] })
    
    toast({
      title: 'Invitation sent',
      description: `An invitation has been sent to ${inviteForm.email} as ${inviteForm.role}`,
    })
  }

  const getPermissionsForRole = (role: 'admin' | 'editor' | 'member' | 'viewer'): string[] => {
    switch (role) {
      case 'admin':
        return ['manage_team', 'manage_automations', 'view_logs', 'manage_credentials', 'manage_billing', 'configure_security']
      case 'editor':
        return ['create_automations', 'edit_automations', 'delete_automations', 'view_logs', 'export_data']
      case 'member':
        return ['create_automations', 'view_automations', 'view_logs', 'export_data']
      case 'viewer':
        return ['view_automations', 'view_logs']
      default:
        return []
    }
  }

  const handleRemoveMember = (id: string) => {
    setMembers(members.filter(m => m.id !== id))
    setRemoveMemberId(null)
    toast({
      title: 'Member removed',
      description: 'The team member has been removed.',
    })
  }

  const handleRoleChange = (memberId: string, newRole: 'admin' | 'editor' | 'member' | 'viewer') => {
    setMembers(members.map(m => 
      m.id === memberId ? { ...m, role: newRole, permissions: getPermissionsForRole(newRole) } : m
    ))
    toast({
      title: 'Role updated',
      description: `Team member role has been changed to ${newRole}.`,
    })
  }

  const getRoleIcon = (role: TeamMember['role']) => {
    switch (role) {
      case 'owner':
        return <Crown className="h-4 w-4 text-yellow-500" />
      case 'admin':
        return <Shield className="h-4 w-4 text-blue-500" />
      case 'editor':
        return <Mail className="h-4 w-4 text-purple-500" />
      case 'member':
        return <User className="h-4 w-4 text-green-500" />
      case 'viewer':
        return <Eye className="h-4 w-4 text-gray-500" />
    }
  }

  const getRoleBadge = (role: TeamMember['role']) => {
    switch (role) {
      case 'owner':
        return <Badge className="bg-yellow-100 text-yellow-800">Owner</Badge>
      case 'admin':
        return <Badge className="bg-blue-100 text-blue-800">Admin</Badge>
      case 'editor':
        return <Badge className="bg-purple-100 text-purple-800">Editor</Badge>
      case 'member':
        return <Badge className="bg-green-100 text-green-800">Member</Badge>
      case 'viewer':
        return <Badge variant="secondary">Viewer</Badge>
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
  }

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Team</h1>
        <p className="text-muted-foreground">
          Manage your team members and their permissions
        </p>
      </div>

      {/* Stats */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-3 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Members
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">{members.length}</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Admins
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">
              {members.filter(m => m.role === 'admin' || m.role === 'owner').length}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Active Today
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">
              {members.filter(m => 
                new Date().getTime() - m.lastActive.getTime() < 86400000
              ).length}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Actions */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search team members..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        {canManageTeam && (
          <Button
            onClick={() => setInviteDialogOpen(true)}
            className="bg-[#155DB8] hover:bg-[#155DB8]/90 w-full sm:w-auto"
          >
            <UserPlus className="h-4 w-4 mr-2" />
            Invite Member
          </Button>
        )}
      </div>

      {/* Members Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Member</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Joined</TableHead>
                  <TableHead>Last Active</TableHead>
                  {canManageTeam && <TableHead className="text-right">Actions</TableHead>}
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredMembers.map((member) => (
                  <TableRow key={member.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarImage src={member.avatar} />
                          <AvatarFallback>{getInitials(member.name)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{member.name}</p>
                          <p className="text-sm text-muted-foreground">{member.email}</p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getRoleIcon(member.role)}
                        {getRoleBadge(member.role)}
                      </div>
                    </TableCell>
                    <TableCell className="text-muted-foreground">
                      {member.department || '-'}
                    </TableCell>
                    <TableCell className="text-muted-foreground">
                      {member.joinedAt.toLocaleDateString()}
                    </TableCell>
                    <TableCell className="text-muted-foreground">
                      {new Date().getTime() - member.lastActive.getTime() < 3600000
                        ? 'Active now'
                        : member.lastActive.toLocaleDateString()}
                    </TableCell>
                    {canManageTeam && (
                      <TableCell className="text-right">
                        {member.role !== 'owner' && (
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <DropdownMenuLabel className="text-xs text-muted-foreground">Change Role</DropdownMenuLabel>
                              {member.role !== 'admin' && (
                                <DropdownMenuItem onClick={() => handleRoleChange(member.id, 'admin')}>
                                  <Shield className="h-4 w-4 mr-2 text-blue-500" />
                                  Promote to Admin
                                </DropdownMenuItem>
                              )}
                              {member.role !== 'editor' && (
                                <DropdownMenuItem onClick={() => handleRoleChange(member.id, 'editor')}>
                                  <Edit className="h-4 w-4 mr-2 text-purple-500" />
                                  Change to Editor
                                </DropdownMenuItem>
                              )}
                              {member.role !== 'member' && (
                                <DropdownMenuItem onClick={() => handleRoleChange(member.id, 'member')}>
                                  <User className="h-4 w-4 mr-2 text-green-500" />
                                  Change to Member
                                </DropdownMenuItem>
                              )}
                              {member.role !== 'viewer' && (
                                <DropdownMenuItem onClick={() => handleRoleChange(member.id, 'viewer')}>
                                  <Eye className="h-4 w-4 mr-2 text-gray-500" />
                                  Change to Viewer
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                className="text-destructive"
                                onClick={() => setRemoveMemberId(member.id)}
                              >
                                Remove from team
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        )}
                      </TableCell>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Permissions Matrix */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Role Permissions</CardTitle>
          <CardDescription>
            Overview of permissions for each role in your team
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[200px]">Permission</TableHead>
                  <TableHead className="text-center">
                    <div className="flex items-center justify-center gap-1">
                      <Shield className="h-4 w-4 text-blue-500" />
                      <span>Admin</span>
                    </div>
                  </TableHead>
                  <TableHead className="text-center">
                    <div className="flex items-center justify-center gap-1">
                      <Edit className="h-4 w-4 text-purple-500" />
                      <span>Editor</span>
                    </div>
                  </TableHead>
                  <TableHead className="text-center">
                    <div className="flex items-center justify-center gap-1">
                      <User className="h-4 w-4 text-green-500" />
                      <span>Member</span>
                    </div>
                  </TableHead>
                  <TableHead className="text-center">
                    <div className="flex items-center justify-center gap-1">
                      <Eye className="h-4 w-4 text-gray-500" />
                      <span>Viewer</span>
                    </div>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell className="font-medium">View Automations</TableCell>
                  <TableCell className="text-center">✓</TableCell>
                  <TableCell className="text-center">✓</TableCell>
                  <TableCell className="text-center">✓</TableCell>
                  <TableCell className="text-center">✓</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Create Automations</TableCell>
                  <TableCell className="text-center">✓</TableCell>
                  <TableCell className="text-center">✓</TableCell>
                  <TableCell className="text-center">✓</TableCell>
                  <TableCell className="text-center">-</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Edit Automations</TableCell>
                  <TableCell className="text-center">✓</TableCell>
                  <TableCell className="text-center">✓</TableCell>
                  <TableCell className="text-center">-</TableCell>
                  <TableCell className="text-center">-</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Delete Automations</TableCell>
                  <TableCell className="text-center">✓</TableCell>
                  <TableCell className="text-center">✓</TableCell>
                  <TableCell className="text-center">-</TableCell>
                  <TableCell className="text-center">-</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">View Activity Logs</TableCell>
                  <TableCell className="text-center">✓</TableCell>
                  <TableCell className="text-center">✓</TableCell>
                  <TableCell className="text-center">✓</TableCell>
                  <TableCell className="text-center">✓</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Export Data</TableCell>
                  <TableCell className="text-center">✓</TableCell>
                  <TableCell className="text-center">✓</TableCell>
                  <TableCell className="text-center">✓</TableCell>
                  <TableCell className="text-center">-</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Manage Credentials</TableCell>
                  <TableCell className="text-center">✓</TableCell>
                  <TableCell className="text-center">-</TableCell>
                  <TableCell className="text-center">-</TableCell>
                  <TableCell className="text-center">-</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Manage Team Members</TableCell>
                  <TableCell className="text-center">✓</TableCell>
                  <TableCell className="text-center">-</TableCell>
                  <TableCell className="text-center">-</TableCell>
                  <TableCell className="text-center">-</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Manage Billing</TableCell>
                  <TableCell className="text-center">✓</TableCell>
                  <TableCell className="text-center">-</TableCell>
                  <TableCell className="text-center">-</TableCell>
                  <TableCell className="text-center">-</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Configure Security</TableCell>
                  <TableCell className="text-center">✓</TableCell>
                  <TableCell className="text-center">-</TableCell>
                  <TableCell className="text-center">-</TableCell>
                  <TableCell className="text-center">-</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Team Activity */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Recent Team Activity</CardTitle>
          <CardDescription>
            Track what your team has been working on
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <Avatar className="h-8 w-8">
                <AvatarFallback>SJ</AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <p className="text-sm">
                  <span className="font-medium">Sarah Johnson</span> created a new automation
                  <span className="text-muted-foreground"> "Daily Email Summary"</span>
                </p>
                <p className="text-xs text-muted-foreground">2 hours ago</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Avatar className="h-8 w-8">
                <AvatarFallback>MC</AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <p className="text-sm">
                  <span className="font-medium">Mike Chen</span> updated
                  <span className="text-muted-foreground"> "Lead Qualification" workflow</span>
                </p>
                <p className="text-xs text-muted-foreground">5 hours ago</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Avatar className="h-8 w-8">
                <AvatarFallback>ER</AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <p className="text-sm">
                  <span className="font-medium">Emily Rodriguez</span> connected
                  <span className="text-muted-foreground"> Google Sheets credentials</span>
                </p>
                <p className="text-xs text-muted-foreground">Yesterday at 3:45 PM</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Avatar className="h-8 w-8">
                <AvatarFallback>DP</AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <p className="text-sm">
                  <span className="font-medium">David Park</span> viewed
                  <span className="text-muted-foreground"> "Activity Logs"</span>
                </p>
                <p className="text-xs text-muted-foreground">2 days ago</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Invite Dialog */}
      <Dialog open={inviteDialogOpen} onOpenChange={setInviteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Invite Team Member</DialogTitle>
            <DialogDescription>
              Send an invitation to join your team
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleInvite}>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="invite-email">Email address</Label>
                <Input
                  id="invite-email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={inviteForm.email}
                  onChange={(e) => setInviteForm({ ...inviteForm, email: e.target.value })}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="invite-role">Role</Label>
                <Select
                  value={inviteForm.role}
                  onValueChange={(value: 'admin' | 'editor' | 'member' | 'viewer') => 
                    setInviteForm({ ...inviteForm, role: value })
                  }
                >
                  <SelectTrigger id="invite-role">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="admin">
                      <div className="flex items-center gap-2">
                        <Shield className="h-4 w-4 text-blue-500" />
                        <span>Admin</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="editor">
                      <div className="flex items-center gap-2">
                        <Edit className="h-4 w-4 text-purple-500" />
                        <span>Editor</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="member">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-green-500" />
                        <span>Member</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="viewer">
                      <div className="flex items-center gap-2">
                        <Eye className="h-4 w-4 text-gray-500" />
                        <span>Viewer</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  {inviteForm.role === 'admin' && 'Full access to manage team, automations, and settings'}
                  {inviteForm.role === 'editor' && 'Can create and edit automations, view logs'}
                  {inviteForm.role === 'member' && 'Can create automations and view logs'}
                  {inviteForm.role === 'viewer' && 'Read-only access to view automations and logs'}
                </p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="invite-department">Department (Optional)</Label>
                <Input
                  id="invite-department"
                  type="text"
                  placeholder="e.g., Marketing, Sales, Engineering"
                  value={inviteForm.department}
                  onChange={(e) => setInviteForm({ ...inviteForm, department: e.target.value })}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" type="button" onClick={() => setInviteDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" className="bg-[#155DB8] hover:bg-[#155DB8]/90">
                Send Invitation
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Remove Member Dialog */}
      <AlertDialog open={!!removeMemberId} onOpenChange={() => setRemoveMemberId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove team member?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. The member will lose access to all team
              automations and data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              onClick={() => removeMemberId && handleRemoveMember(removeMemberId)}
            >
              Remove Member
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}