import { useState, useMemo, useCallback } from 'react'
import { useLocation } from 'wouter'
import { format } from 'date-fns'
import { useQuery } from '@tanstack/react-query'
import { 
  Edit, 
  Trash2, 
  MessageSquare, 
  Play,
  Pause,
  Clock,
  CheckCircle,
  XCircle,
  MoreVertical,
  Search,
  Filter
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { TableSkeleton } from '@/components/ui/skeleton'
import { ComponentLoader } from '@/components/ui/loading-spinner'
import { useToast } from '@/hooks/use-toast'
import { ErrorHandler } from '@/components/error-handler'
import { HelpTooltip } from '@/components/ui/help-tooltip'
import { AIHelpSuggestions } from '@/components/ai-help-suggestions'
import { AutomationChat } from '@/components/automation-chat'
import { queryClient, apiRequest } from '@/lib/queryClient'

interface Automation {
  id: number
  name: string
  description: string
  trigger: any
  actions: any[]
  status: 'active' | 'paused' | 'error'
  lastRun: Date | null
  createdAt: Date
}

export default function MyAutomations() {
  // Fetch automations from database
  const { data: automations = [], isLoading, error } = useQuery({
    queryKey: ['/api/automations'],
    queryFn: () => fetch('/api/automations').then(res => res.json())
  })
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'paused' | 'error'>('all')
  const [deleteId, setDeleteId] = useState<number | null>(null)
  const [activeChatId, setActiveChatId] = useState<number | null>(null)
  const [chatTriggerData, setChatTriggerData] = useState<any>(null)
  const [location, setLocation] = useLocation()
  const { toast } = useToast()

  // Memoize filtered results with stable sorting for better performance
  const filteredAutomations = useMemo(() => {
    if (!Array.isArray(automations)) return []
    
    return automations
      .filter((automation: Automation) => {
        const matchesSearch = automation.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                             automation.description.toLowerCase().includes(searchQuery.toLowerCase())
        const matchesStatus = statusFilter === 'all' || automation.status === statusFilter
        return matchesSearch && matchesStatus
      })
      .sort((a, b) => a.id - b.id) // Stable sort by ID to maintain consistent order
  }, [automations, searchQuery, statusFilter])

  const handleToggleStatus = useCallback(async (id: number) => {
    try {
      const automation = automations?.find((a: Automation) => a.id === id)
      if (!automation) return
      
      const newStatus = automation.status === 'active' ? 'paused' : 'active'
      
      // Optimistic update: Update the cache immediately for smooth UX
      queryClient.setQueryData(['/api/automations'], (oldData: Automation[] | undefined) => {
        if (!oldData) return oldData
        return oldData.map(a => 
          a.id === id ? { ...a, status: newStatus } : a
        )
      })
      
      // Make API call
      await apiRequest('PATCH', `/api/automations/${id}`, { status: newStatus })
      
      // Invalidate cache to sync with server (but UI already updated)
      queryClient.invalidateQueries({ queryKey: ['/api/automations'] })
      
      toast({
        title: `Automation ${newStatus === 'active' ? 'activated' : 'paused'}`,
        description: `${automation.name} is now ${newStatus}.`,
      })
    } catch (error: any) {
      // Revert optimistic update on error
      queryClient.invalidateQueries({ queryKey: ['/api/automations'] })
      
      toast({
        title: 'Failed to update automation',
        description: error.message || 'Something went wrong. Please try again.',
        variant: 'destructive'
      })
    }
  }, [automations, queryClient, toast])

  const handleDelete = async (id: number) => {
    try {
      await apiRequest('DELETE', `/api/automations/${id}`)
      
      // Invalidate cache to refresh the list
      queryClient.invalidateQueries({ queryKey: ['/api/automations'] })
      
      setDeleteId(null)
      toast({
        title: 'Automation deleted',
        description: 'The automation has been permanently deleted.',
      })
    } catch (error: any) {
      toast({
        title: 'Failed to delete automation', 
        description: error.message || 'Something went wrong. Please try again.',
        variant: 'destructive'
      })
    }
  }

  const handleStartChat = (automation: Automation) => {
    // Simulate trigger data based on automation type
    const mockTriggerData = {
      type: automation.trigger?.type || 'manual',
      timestamp: new Date(),
      source: 'manual_trigger',
      ...(automation.trigger?.type === 'schedule' && {
        scheduledTime: new Date(),
        frequency: automation.trigger.frequency || 'daily'
      }),
      ...(automation.trigger?.type === 'webhook' && {
        source: 'webhook_endpoint',
        method: 'POST'
      }),
      ...(automation.trigger?.type === 'gmail' && {
        sender: '<EMAIL>',
        subject: 'New email received',
        message: 'Sample email content...'
      }),
      ...(automation.trigger?.type === 'slack' && {
        channel: automation.trigger.config?.channel || 'general',
        message: 'Sample slack message',
        user: 'user123'
      }),
      ...(automation.trigger?.type === 'google-sheets' && {
        spreadsheet: automation.trigger.config?.spreadsheetId || 'Sample Spreadsheet',
        sheet: automation.trigger.config?.sheetName || 'Sheet1',
        action: 'row_added'
      }),
      ...(automation.trigger?.type === 'google-drive' && {
        filename: 'document.pdf',
        action: 'file_added',
        folder: automation.trigger.config?.folderId || 'Documents'
      }),
      ...(automation.trigger?.type === 'google-calendar' && {
        eventTitle: 'Meeting with team',
        action: 'event_created',
        calendar: automation.trigger.config?.calendarId || 'primary'
      })
    }

    setActiveChatId(automation.id)
    setChatTriggerData(mockTriggerData)
    
    toast({
      title: 'Chat started',
      description: `Monitoring ${automation.name} execution`,
    })
  }

  const handleCloseChat = () => {
    setActiveChatId(null)
    setChatTriggerData(null)
  }

  const getStatusIcon = (status: Automation['status']) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-500 transition-colors duration-300" />
      case 'paused':
        return <Pause className="h-4 w-4 text-gray-500 transition-colors duration-300" />
      case 'inactive':
        return <Pause className="h-4 w-4 text-gray-500 transition-colors duration-300" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500 transition-colors duration-300" />
      default:
        return <Pause className="h-4 w-4 text-gray-500 transition-colors duration-300" />
    }
  }

  const getRelativeTime = (date: Date | string | null) => {
    if (!date) return 'Never'
    const dateObj = typeof date === 'string' ? new Date(date) : date
    const now = Date.now()
    const diff = Math.abs(now - dateObj.getTime())
    const hours = Math.floor(diff / 3600000)
    const days = Math.floor(hours / 24)
    
    if (days > 0) return `${days}d ago`
    if (hours > 0) return `${hours}h ago`
    return 'Just now'
  }

  return (
    <div>
      {/* Header */}
      <div className="mb-8 animate-fade-in">
        <h1 className="text-3xl font-bold mb-2 animate-slide-in-left">My Automations</h1>
        <p className="text-muted-foreground animate-slide-in-left stagger-2">
          Manage and monitor your automation workflows
        </p>
      </div>

      {/* Error Handler */}
      <ErrorHandler />

      {/* Stats */}
      <div className="grid gap-4 grid-cols-2 lg:grid-cols-4 mb-6">
        <Card className="hover-lift animate-scale-in stagger-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground flex items-center gap-2 animate-fade-in">
              Total Automations
              <HelpTooltip content="Total number of automations in your workspace" size="sm" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold animate-fade-in stagger-2">{automations.length}</p>
          </CardContent>
        </Card>
        <Card className="hover-lift animate-scale-in stagger-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground animate-fade-in">
              Active
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold text-green-600 animate-fade-in stagger-3">
              {automations.filter(a => a.status === 'active').length}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Executions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">
              {automations.reduce((sum, a) => sum + (a.executions || 0), 0)}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              With Errors
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold text-red-600">
              {automations.filter(a => a.status === 'error').length}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search automations..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="w-full sm:w-auto">
              <Filter className="h-4 w-4 mr-2" />
              Status: {statusFilter}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuLabel>Filter by status</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {['all', 'active', 'paused', 'error'].map((status) => (
              <DropdownMenuItem
                key={status}
                onClick={() => setStatusFilter(status as any)}
                className={statusFilter === status ? 'bg-accent' : ''}
              >
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Desktop Table View */}
      <Card className="hidden md:block">
        <CardContent className="p-0">
          {isLoading ? (
            <div className="p-6">
              <TableSkeleton rows={4} />
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Trigger</TableHead>
                    <TableHead>Last Run</TableHead>
                    <TableHead>Executions</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAutomations.map((automation) => (
                  <TableRow key={automation.id} className="transition-all duration-300 ease-in-out">
                    <TableCell>
                      <div>
                        <p className="font-medium">{automation.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {automation.description || 'No description'}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={automation.status === 'active'}
                          onCheckedChange={() => handleToggleStatus(automation.id)}
                          disabled={automation.status === 'error'}
                          className="transition-all duration-200"
                        />
                        <div className="transition-all duration-300 ease-in-out">
                          {getStatusIcon(automation.status)}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {automation.trigger?.type || 'Unknown'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        {getRelativeTime(automation.lastRun)}
                      </div>
                    </TableCell>
                    <TableCell>0</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleStartChat(automation)}>
                            <MessageSquare className="mr-2 h-4 w-4" />
                            Start Chat
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => setLocation(`/dashboard/automations/edit/${automation.id}`)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-destructive"
                            onClick={() => setDeleteId(automation.id)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Mobile Cards View */}
      <div className="md:hidden space-y-4">
        {isLoading ? (
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <Card key={i}>
                <CardHeader className="pb-3">
                  <div className="h-4 bg-muted rounded w-3/4 animate-pulse"></div>
                  <div className="h-3 bg-muted rounded w-1/2 animate-pulse"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="h-3 bg-muted rounded animate-pulse"></div>
                    <div className="h-3 bg-muted rounded animate-pulse"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          filteredAutomations.map((automation) => (
            <Card key={automation.id} className="transition-all duration-300 ease-in-out">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg">{automation.name}</CardTitle>
                    <CardDescription>{automation.description || 'No description'}</CardDescription>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleStartChat(automation)}>
                        <MessageSquare className="mr-2 h-4 w-4" />
                        Start Chat
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setLocation(`/dashboard/automations/edit/${automation.id}`)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        className="text-destructive"
                        onClick={() => setDeleteId(automation.id)}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Status</span>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={automation.status === 'active'}
                      onCheckedChange={() => handleToggleStatus(automation.id)}
                      disabled={automation.status === 'error'}
                      className="transition-all duration-200"
                    />
                    <div className="transition-all duration-300 ease-in-out">
                      {getStatusIcon(automation.status)}
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Trigger</span>
                  <Badge variant="outline">
                    {automation.trigger?.type || 'Unknown'}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Last Run</span>
                  <span className="text-sm">{getRelativeTime(automation.lastRun)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Executions</span>
                  <span className="text-sm font-medium">0</span>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Delete Confirmation */}
      <AlertDialog open={!!deleteId} onOpenChange={() => setDeleteId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              automation and all its execution history.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              onClick={() => deleteId && handleDelete(deleteId)}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Automation Chat */}
      {activeChatId && chatTriggerData && (
        <AutomationChat
          automationId={activeChatId.toString()}
          automationName={
            automations.find((a: Automation) => a.id === activeChatId)?.name || 'Unknown Automation'
          }
          isActive={true}
          onClose={handleCloseChat}
          triggerData={chatTriggerData}
        />
      )}
    </div>
  )
}