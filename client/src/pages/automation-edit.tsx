import { useState, useEffect } from 'react'
import { useLocation, useParams } from 'wouter'
import { 
  ChevronLeft,
  Save,
  Play,
  Pause,
  Trash2,
  Clock,
  Globe,
  Mail,
  MessageSquare,
  Hash,
  Plus,
  X,
  Settings,
  AlertCircle,
  CheckCircle,
  Copy,
  ExternalLink
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/hooks/use-toast'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { ScrollArea } from '@/components/ui/scroll-area'
import { HelpTooltip } from '@/components/ui/help-tooltip'
import { AIHelpSuggestions } from '@/components/ai-help-suggestions'
import { motion } from 'framer-motion'

interface AutomationData {
  id: string
  name: string
  description: string
  status: 'active' | 'inactive' | 'error'
  trigger: {
    type: string
    config: Record<string, any>
  }
  actions: Array<{
    id: string
    type: string
    name: string
    config: Record<string, any>
  }>
  conditions?: Array<{
    id: string
    name: string
    rules: Array<{
      field: string
      operator: string
      value: any
    }>
    actions: string[]
  }>
  settings: {
    retryPolicy: {
      enabled: boolean
      maxRetries: number
      retryDelay: number
    }
    notifications: {
      onSuccess: boolean
      onFailure: boolean
      email: string
    }
    scheduling: {
      timezone: string
      activeHours: {
        enabled: boolean
        start: string
        end: string
      }
    }
  }
  stats: {
    lastRun: Date | null
    nextRun: Date | null
    totalRuns: number
    successRuns: number
    failedRuns: number
    averageRunTime: number
  }
  webhookUrl?: string
  created: Date
  updated: Date
}

// Mock data - replace with actual API call
const mockAutomation: AutomationData = {
  id: '1',
  name: 'Daily Sales Report',
  description: 'Generates and sends daily sales reports to the team',
  status: 'active',
  trigger: {
    type: 'schedule',
    config: {
      interval: 'daily',
      time: '09:00',
      timezone: 'America/New_York'
    }
  },
  actions: [
    {
      id: '1',
      type: 'database',
      name: 'Fetch Sales Data',
      config: {
        query: 'SELECT * FROM sales WHERE date = CURRENT_DATE',
        database: 'analytics'
      }
    },
    {
      id: '2',
      type: 'ai',
      name: 'Generate Report',
      config: {
        model: 'gpt-4',
        prompt: 'Create a summary of daily sales performance'
      }
    },
    {
      id: '3',
      type: 'email',
      name: 'Send Report',
      config: {
        to: '<EMAIL>',
        subject: 'Daily Sales Report',
        template: 'sales-report'
      }
    }
  ],
  settings: {
    retryPolicy: {
      enabled: true,
      maxRetries: 3,
      retryDelay: 300
    },
    notifications: {
      onSuccess: false,
      onFailure: true,
      email: '<EMAIL>'
    },
    scheduling: {
      timezone: 'America/New_York',
      activeHours: {
        enabled: true,
        start: '08:00',
        end: '18:00'
      }
    }
  },
  stats: {
    lastRun: new Date(Date.now() - 86400000),
    nextRun: new Date(Date.now() + 3600000),
    totalRuns: 365,
    successRuns: 358,
    failedRuns: 7,
    averageRunTime: 4.5
  },
  webhookUrl: 'https://api.filorina.com/webhook/abc123',
  created: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000),
  updated: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
}

export default function AutomationEdit() {
  const { id } = useParams()
  const [, setLocation] = useLocation()
  const { toast } = useToast()
  const [automation, setAutomation] = useState<AutomationData>(mockAutomation)
  const [isSaving, setIsSaving] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')

  // Load automation data
  useEffect(() => {
    // TODO: Replace with actual API call
    // const loadAutomation = async () => {
    //   const response = await fetch(`/api/automations/${id}`)
    //   const data = await response.json()
    //   setAutomation(data)
    // }
    // loadAutomation()
  }, [id])

  const handleSave = async () => {
    setIsSaving(true)
    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: 'Automation saved',
        description: 'Your changes have been saved successfully.',
      })
      setHasChanges(false)
    } catch (error) {
      toast({
        title: 'Error saving automation',
        description: 'Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsSaving(false)
    }
  }

  const handleDelete = async () => {
    setIsDeleting(true)
    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: 'Automation deleted',
        description: 'The automation has been deleted successfully.',
      })
      setLocation('/dashboard/automations')
    } catch (error) {
      toast({
        title: 'Error deleting automation',
        description: 'Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsDeleting(false)
      setShowDeleteDialog(false)
    }
  }

  const toggleStatus = () => {
    const newStatus = automation.status === 'active' ? 'inactive' : 'active'
    setAutomation({ ...automation, status: newStatus })
    setHasChanges(true)
    
    toast({
      title: `Automation ${newStatus === 'active' ? 'activated' : 'paused'}`,
      description: `The automation is now ${newStatus}.`,
    })
  }

  const copyWebhookUrl = () => {
    if (automation.webhookUrl) {
      navigator.clipboard.writeText(automation.webhookUrl)
      toast({
        title: 'Webhook URL copied',
        description: 'The webhook URL has been copied to your clipboard.',
      })
    }
  }

  const addAction = () => {
    const newAction = {
      id: Date.now().toString(),
      type: 'email',
      name: 'New Action',
      config: {}
    }
    setAutomation({
      ...automation,
      actions: [...automation.actions, newAction]
    })
    setHasChanges(true)
  }

  const removeAction = (actionId: string) => {
    setAutomation({
      ...automation,
      actions: automation.actions.filter(a => a.id !== actionId)
    })
    setHasChanges(true)
  }

  const updateAction = (actionId: string, updates: Partial<typeof automation.actions[0]>) => {
    setAutomation({
      ...automation,
      actions: automation.actions.map(a => 
        a.id === actionId ? { ...a, ...updates } : a
      )
    })
    setHasChanges(true)
  }

  const getTriggerIcon = (type: string) => {
    switch (type) {
      case 'schedule':
        return Clock
      case 'webhook':
        return Globe
      case 'email':
        return Mail
      case 'chat':
        return MessageSquare
      case 'slack':
        return Hash
      default:
        return Clock
    }
  }

  const TriggerIcon = getTriggerIcon(automation.trigger.type)

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              onClick={() => setLocation('/dashboard/automations')}
            >
              <ChevronLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Edit Automation</h1>
              <p className="text-muted-foreground">
                Modify your automation settings and configuration
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={toggleStatus}
            >
              {automation.status === 'active' ? (
                <>
                  <Pause className="mr-2 h-4 w-4" />
                  Pause
                </>
              ) : (
                <>
                  <Play className="mr-2 h-4 w-4" />
                  Activate
                </>
              )}
            </Button>
            <Button
              onClick={handleSave}
              disabled={!hasChanges || isSaving}
            >
              <Save className="mr-2 h-4 w-4" />
              {isSaving ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </div>

        {/* Status Alert */}
        {automation.status === 'error' && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Automation Error</AlertTitle>
            <AlertDescription>
              This automation encountered an error during the last run. Check the logs for details.
            </AlertDescription>
          </Alert>
        )}

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-4 w-full max-w-2xl">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="workflow">Workflow</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
                <CardDescription>
                  Update the name and description of your automation
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="name">Automation Name</Label>
                    <HelpTooltip content="Give your automation a descriptive name" />
                  </div>
                  <Input
                    id="name"
                    value={automation.name}
                    onChange={(e) => {
                      setAutomation({ ...automation, name: e.target.value })
                      setHasChanges(true)
                    }}
                    placeholder="Enter automation name"
                  />
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="description">Description</Label>
                    <HelpTooltip content="Describe what this automation does" />
                  </div>
                  <Textarea
                    id="description"
                    value={automation.description}
                    onChange={(e) => {
                      setAutomation({ ...automation, description: e.target.value })
                      setHasChanges(true)
                    }}
                    placeholder="Enter description"
                    rows={3}
                  />
                </div>
                <AIHelpSuggestions
                  context="automation-edit-basic"
                  fieldName="name"
                  currentValue={automation.name}
                  compact
                />
              </CardContent>
            </Card>

            {/* Status & Info */}
            <Card>
              <CardHeader>
                <CardTitle>Status & Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label>Status</Label>
                    <div className="flex items-center gap-2">
                      {automation.status === 'active' ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : automation.status === 'error' ? (
                        <AlertCircle className="h-4 w-4 text-red-500" />
                      ) : (
                        <Pause className="h-4 w-4 text-gray-500" />
                      )}
                      <Badge variant={
                        automation.status === 'active' ? 'success' :
                        automation.status === 'error' ? 'destructive' :
                        'secondary'
                      }>
                        {automation.status}
                      </Badge>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Created</Label>
                    <p className="text-sm text-muted-foreground">
                      {automation.created.toLocaleDateString()}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label>Last Updated</Label>
                    <p className="text-sm text-muted-foreground">
                      {automation.updated.toLocaleDateString()}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label>Total Runs</Label>
                    <p className="text-sm text-muted-foreground">
                      {automation.stats.totalRuns}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Webhook URL (if applicable) */}
            {automation.trigger.type === 'webhook' && automation.webhookUrl && (
              <Card>
                <CardHeader>
                  <CardTitle>Webhook URL</CardTitle>
                  <CardDescription>
                    Use this URL to trigger your automation
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-2">
                    <Input
                      value={automation.webhookUrl}
                      readOnly
                      className="font-mono text-sm"
                    />
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={copyWebhookUrl}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => window.open(automation.webhookUrl, '_blank')}
                    >
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">
                    Send a POST request to this URL to trigger the automation
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="workflow" className="space-y-6">
            {/* Trigger Configuration */}
            <Card>
              <CardHeader>
                <CardTitle>Trigger</CardTitle>
                <CardDescription>
                  Configure when this automation runs
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3 p-4 border rounded-lg">
                  <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                    <TriggerIcon className="h-5 w-5 text-primary" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium capitalize">{automation.trigger.type}</p>
                    {automation.trigger.type === 'schedule' && (
                      <p className="text-sm text-muted-foreground">
                        Runs {automation.trigger.config.interval} at {automation.trigger.config.time}
                      </p>
                    )}
                  </div>
                  <Button variant="outline" size="sm">
                    <Settings className="h-4 w-4" />
                  </Button>
                </div>
                
                {automation.trigger.type === 'schedule' && (
                  <div className="space-y-4 p-4 bg-muted/30 rounded-lg">
                    <div className="space-y-2">
                      <Label>Run scenario</Label>
                      <Select
                        value={automation.trigger.config.runType || 'at-regular-intervals'}
                        onValueChange={(value) => {
                          setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, runType: value }
                            }
                          })
                          setHasChanges(true)
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="at-regular-intervals">At regular intervals</SelectItem>
                          <SelectItem value="once">Once</SelectItem>
                          <SelectItem value="every-day">Every day</SelectItem>
                          <SelectItem value="days-of-week">Days of the week</SelectItem>
                          <SelectItem value="days-of-month">Days of the month</SelectItem>
                          <SelectItem value="specified-dates">Specified dates</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    {automation.trigger.config.runType === 'at-regular-intervals' && (
                      <div className="space-y-2">
                        <Label>Interval</Label>
                        <Select
                          value={automation.trigger.config.interval || 'hourly'}
                          onValueChange={(value) => {
                            setAutomation({
                              ...automation,
                              trigger: {
                                ...automation.trigger,
                                config: { ...automation.trigger.config, interval: value }
                              }
                            })
                            setHasChanges(true)
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="5min">Every 5 minutes</SelectItem>
                            <SelectItem value="15min">Every 15 minutes</SelectItem>
                            <SelectItem value="30min">Every 30 minutes</SelectItem>
                            <SelectItem value="hourly">Every hour</SelectItem>
                            <SelectItem value="daily">Every day</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                    
                    {automation.trigger.config.runType === 'once' && (
                      <div className="space-y-2">
                        <Label>Date & Time</Label>
                        <div className="grid grid-cols-2 gap-2">
                          <Input
                            type="date"
                            value={automation.trigger.config.runDate || ''}
                            onChange={(e) => {
                              setAutomation({
                                ...automation,
                                trigger: {
                                  ...automation.trigger,
                                  config: { ...automation.trigger.config, runDate: e.target.value }
                                }
                              })
                              setHasChanges(true)
                            }}
                          />
                          <Input
                            type="time"
                            value={automation.trigger.config.runTime || '09:00'}
                            onChange={(e) => {
                              setAutomation({
                                ...automation,
                                trigger: {
                                  ...automation.trigger,
                                  config: { ...automation.trigger.config, runTime: e.target.value }
                                }
                              })
                              setHasChanges(true)
                            }}
                          />
                        </div>
                      </div>
                    )}
                    
                    {automation.trigger.config.runType === 'every-day' && (
                      <div className="space-y-2">
                        <Label>Time (24-hour format)</Label>
                        <Input
                          type="time"
                          value={automation.trigger.config.dailyTime || '09:00'}
                          onChange={(e) => {
                            setAutomation({
                              ...automation,
                              trigger: {
                                ...automation.trigger,
                                config: { ...automation.trigger.config, dailyTime: e.target.value }
                              }
                            })
                            setHasChanges(true)
                          }}
                        />
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Actions */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Actions</CardTitle>
                    <CardDescription>
                      Define what happens when the automation runs
                    </CardDescription>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={addAction}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Add Action
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {automation.actions.map((action, index) => (
                    <motion.div
                      key={action.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="relative"
                    >
                      <div className="flex items-start gap-4 p-4 border rounded-lg">
                        <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-sm font-medium">
                          {index + 1}
                        </div>
                        <div className="flex-1 space-y-3">
                          <div className="flex items-center gap-2">
                            <Input
                              value={action.name}
                              onChange={(e) => updateAction(action.id, { name: e.target.value })}
                              className="max-w-xs"
                            />
                            <Badge variant="outline">{action.type}</Badge>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {action.type === 'email' && (
                              <p>Send email to: {action.config.to || 'Not configured'}</p>
                            )}
                            {action.type === 'database' && (
                              <p>Query: {action.config.query?.slice(0, 50) || 'Not configured'}...</p>
                            )}
                            {action.type === 'ai' && (
                              <p>Model: {action.config.model || 'Not configured'}</p>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button variant="ghost" size="icon">
                            <Settings className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => removeAction(action.id)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      {index < automation.actions.length - 1 && (
                        <div className="flex justify-center py-2">
                          <div className="w-0.5 h-8 bg-border" />
                        </div>
                      )}
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            {/* Retry Policy */}
            <Card>
              <CardHeader>
                <CardTitle>Retry Policy</CardTitle>
                <CardDescription>
                  Configure how the automation handles failures
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Enable Retry</Label>
                    <p className="text-sm text-muted-foreground">
                      Automatically retry failed executions
                    </p>
                  </div>
                  <Switch
                    checked={automation.settings.retryPolicy.enabled}
                    onCheckedChange={(checked) => {
                      setAutomation({
                        ...automation,
                        settings: {
                          ...automation.settings,
                          retryPolicy: {
                            ...automation.settings.retryPolicy,
                            enabled: checked
                          }
                        }
                      })
                      setHasChanges(true)
                    }}
                  />
                </div>
                {automation.settings.retryPolicy.enabled && (
                  <div className="grid gap-4 md:grid-cols-2 pt-4">
                    <div className="space-y-2">
                      <Label>Max Retries</Label>
                      <Input
                        type="number"
                        min="1"
                        max="10"
                        value={automation.settings.retryPolicy.maxRetries}
                        onChange={(e) => {
                          setAutomation({
                            ...automation,
                            settings: {
                              ...automation.settings,
                              retryPolicy: {
                                ...automation.settings.retryPolicy,
                                maxRetries: parseInt(e.target.value)
                              }
                            }
                          })
                          setHasChanges(true)
                        }}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Retry Delay (seconds)</Label>
                      <Input
                        type="number"
                        min="60"
                        max="3600"
                        value={automation.settings.retryPolicy.retryDelay}
                        onChange={(e) => {
                          setAutomation({
                            ...automation,
                            settings: {
                              ...automation.settings,
                              retryPolicy: {
                                ...automation.settings.retryPolicy,
                                retryDelay: parseInt(e.target.value)
                              }
                            }
                          })
                          setHasChanges(true)
                        }}
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Notifications */}
            <Card>
              <CardHeader>
                <CardTitle>Notifications</CardTitle>
                <CardDescription>
                  Configure when to receive notifications
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>On Success</Label>
                      <p className="text-sm text-muted-foreground">
                        Notify when automation runs successfully
                      </p>
                    </div>
                    <Switch
                      checked={automation.settings.notifications.onSuccess}
                      onCheckedChange={(checked) => {
                        setAutomation({
                          ...automation,
                          settings: {
                            ...automation.settings,
                            notifications: {
                              ...automation.settings.notifications,
                              onSuccess: checked
                            }
                          }
                        })
                        setHasChanges(true)
                      }}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>On Failure</Label>
                      <p className="text-sm text-muted-foreground">
                        Notify when automation fails
                      </p>
                    </div>
                    <Switch
                      checked={automation.settings.notifications.onFailure}
                      onCheckedChange={(checked) => {
                        setAutomation({
                          ...automation,
                          settings: {
                            ...automation.settings,
                            notifications: {
                              ...automation.settings.notifications,
                              onFailure: checked
                            }
                          }
                        })
                        setHasChanges(true)
                      }}
                    />
                  </div>
                </div>
                <Separator />
                <div className="space-y-2">
                  <Label>Notification Email</Label>
                  <Input
                    type="email"
                    value={automation.settings.notifications.email}
                    onChange={(e) => {
                      setAutomation({
                        ...automation,
                        settings: {
                          ...automation.settings,
                          notifications: {
                            ...automation.settings.notifications,
                            email: e.target.value
                          }
                        }
                      })
                      setHasChanges(true)
                    }}
                    placeholder="<EMAIL>"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Active Hours */}
            <Card>
              <CardHeader>
                <CardTitle>Active Hours</CardTitle>
                <CardDescription>
                  Limit when the automation can run
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Enable Active Hours</Label>
                    <p className="text-sm text-muted-foreground">
                      Only run during specific hours
                    </p>
                  </div>
                  <Switch
                    checked={automation.settings.scheduling.activeHours.enabled}
                    onCheckedChange={(checked) => {
                      setAutomation({
                        ...automation,
                        settings: {
                          ...automation.settings,
                          scheduling: {
                            ...automation.settings.scheduling,
                            activeHours: {
                              ...automation.settings.scheduling.activeHours,
                              enabled: checked
                            }
                          }
                        }
                      })
                      setHasChanges(true)
                    }}
                  />
                </div>
                {automation.settings.scheduling.activeHours.enabled && (
                  <div className="grid gap-4 md:grid-cols-2 pt-4">
                    <div className="space-y-2">
                      <Label>Start Time</Label>
                      <Input
                        type="time"
                        value={automation.settings.scheduling.activeHours.start}
                        onChange={(e) => {
                          setAutomation({
                            ...automation,
                            settings: {
                              ...automation.settings,
                              scheduling: {
                                ...automation.settings.scheduling,
                                activeHours: {
                                  ...automation.settings.scheduling.activeHours,
                                  start: e.target.value
                                }
                              }
                            }
                          })
                          setHasChanges(true)
                        }}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>End Time</Label>
                      <Input
                        type="time"
                        value={automation.settings.scheduling.activeHours.end}
                        onChange={(e) => {
                          setAutomation({
                            ...automation,
                            settings: {
                              ...automation.settings,
                              scheduling: {
                                ...automation.settings.scheduling,
                                activeHours: {
                                  ...automation.settings.scheduling.activeHours,
                                  end: e.target.value
                                }
                              }
                            }
                          })
                          setHasChanges(true)
                        }}
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Danger Zone */}
            <Card className="border-destructive">
              <CardHeader>
                <CardTitle className="text-destructive">Danger Zone</CardTitle>
                <CardDescription>
                  Irreversible actions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                  <DialogTrigger asChild>
                    <Button variant="destructive" className="w-full sm:w-auto">
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete Automation
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Delete Automation</DialogTitle>
                      <DialogDescription>
                        Are you sure you want to delete this automation? This action cannot be undone.
                      </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => setShowDeleteDialog(false)}
                      >
                        Cancel
                      </Button>
                      <Button
                        variant="destructive"
                        onClick={handleDelete}
                        disabled={isDeleting}
                      >
                        {isDeleting ? 'Deleting...' : 'Delete'}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            {/* Performance Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Performance Statistics</CardTitle>
                <CardDescription>
                  Track your automation's performance over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                  <div className="space-y-2">
                    <Label>Success Rate</Label>
                    <div className="flex items-baseline gap-2">
                      <span className="text-2xl font-bold">
                        {((automation.stats.successRuns / automation.stats.totalRuns) * 100).toFixed(1)}%
                      </span>
                      <Badge variant="success">+2.1%</Badge>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Average Run Time</Label>
                    <div className="flex items-baseline gap-2">
                      <span className="text-2xl font-bold">
                        {automation.stats.averageRunTime}s
                      </span>
                      <Badge variant="secondary">-0.5s</Badge>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Last Run</Label>
                    <p className="text-sm text-muted-foreground">
                      {automation.stats.lastRun?.toLocaleString() || 'Never'}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label>Next Run</Label>
                    <p className="text-sm text-muted-foreground">
                      {automation.stats.nextRun?.toLocaleString() || 'Not scheduled'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Runs */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Runs</CardTitle>
                <CardDescription>
                  View the latest execution history
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[300px]">
                  <div className="space-y-4">
                    {[...Array(10)].map((_, i) => (
                      <div key={i} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          {i % 3 === 0 ? (
                            <AlertCircle className="h-4 w-4 text-red-500" />
                          ) : (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          )}
                          <div>
                            <p className="text-sm font-medium">
                              {i % 3 === 0 ? 'Failed' : 'Success'}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {new Date(Date.now() - i * 3600000).toLocaleString()}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm">{4.5 + Math.random() * 2}s</p>
                          <Button variant="ghost" size="sm">
                            View Logs
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
  )
}