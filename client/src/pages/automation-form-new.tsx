import { useState, useEffect } from 'react'
import { useLocation } from 'wouter'
import { 
  ChevronLeft,
  ChevronRight,
  Clock,
  MessageSquare,
  Mail,
  Calendar,
  Bot,
  Wrench,
  Zap,
  Brain,
  Send,
  Check,
  Sparkles,
  RefreshCw,
  FileText,
  Table,
  Globe,
  Hash,
  ArrowRight,
  ThumbsUp,
  ThumbsDown,
  Wand2,
  Plus,
  X,
  Search
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { cn } from '@/lib/utils'
import { motion, AnimatePresence } from 'framer-motion'
import { EmojiCelebration } from '@/components/emoji-celebration'
import { HelpTooltip } from '@/components/ui/help-tooltip'
import { AIHelpSuggestions } from '@/components/ai-help-suggestions'
import { apiRequest, queryClient } from '@/lib/queryClient'

type StepType = 'intro' | 'trigger' | 'action' | 'responder' | 'review' | 'complete'

interface AutomationConfig {
  name: string
  trigger: {
    type: string
    config: Record<string, any>
    description?: string
  }
  agent: {
    type: string
    name: string
    personality: string
    avatar?: string
  }

  actions: {
    type: string
    description: string
    config: Record<string, any>
  }[]
  llm: {
    model: string
    temperature: number
    maxTokens: number
    creativity: 'conservative' | 'balanced' | 'creative'
  }
  responder: {
    type: string
    format: string
    destination: string
    tone: string
  }
}

const stepConfig = {
  intro: {
    title: "👋 Hi! Let's create something amazing together",
    subtitle: "I'll guide you through building your perfect automation",
    icon: Sparkles
  },
  trigger: {
    title: "First, when should this automation run?",
    subtitle: "Let's decide what will trigger your automation",
    icon: Zap
  },


  action: {
    title: "What actions should happen?",
    subtitle: "Define the steps and AI model your automation will use",
    icon: RefreshCw
  },
  responder: {
    title: "How should results be delivered?",
    subtitle: "Decide where and how to send the output",
    icon: Send
  },
  review: {
    title: "Let's review everything",
    subtitle: "Make sure your automation is perfect",
    icon: Check
  },
  complete: {
    title: "🎉 Awesome! Your automation is ready!",
    subtitle: "You can start using it right away",
    icon: Check
  }
}

export default function AutomationForm() {
  const [location, setLocation] = useLocation()
  const { toast } = useToast()
  
  const [currentStep, setCurrentStep] = useState<StepType>('intro')
  const [automation, setAutomation] = useState<AutomationConfig>({
    name: '',
    trigger: { type: '', config: {} },
    agent: { type: 'assistant', name: '', personality: '' },

    actions: [],
    llm: { model: 'gpt-4', temperature: 0.7, maxTokens: 2048, creativity: 'balanced' },
    responder: { type: 'email', format: 'text', destination: '', tone: 'professional' }
  })
  
  const [showConfirmation, setShowConfirmation] = useState(false)
  const [userPreference, setUserPreference] = useState('')
  const [showTriggerConfig, setShowTriggerConfig] = useState(false)
  const [actionSearchQuery, setActionSearchQuery] = useState('')
  const [showActionConfig, setShowActionConfig] = useState<number | null>(null)
  const [showAllActions, setShowAllActions] = useState(false)
  const [showCelebration, setShowCelebration] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  const steps: StepType[] = ['intro', 'trigger', 'action', 'responder', 'review']
  const currentStepIndex = steps.indexOf(currentStep)
  const progress = ((currentStepIndex + 1) / steps.length) * 100

  const nextStep = () => {
    if (currentStepIndex < steps.length - 1) {
      setCurrentStep(steps[currentStepIndex + 1])
      setShowConfirmation(false)
      setShowTriggerConfig(false)
    }
  }
  
  const prevStep = () => {
    if (currentStepIndex > 0) {
      setCurrentStep(steps[currentStepIndex - 1])
      setShowConfirmation(false)
      setShowTriggerConfig(false)
    }
  }

  const handleComplete = async () => {
    try {
      // Save automation to database
      const automationPayload = {
        name: automation.name,
        description: `Automation with ${automation.trigger.type} trigger and ${automation.actions.length} actions`,
        trigger: automation.trigger,
        actions: automation.actions,
        conditions: null, // Add conditions support later if needed
        responder: automation.responder,
        llm: automation.llm,
        agent: automation.agent
      }

      await apiRequest('POST', '/api/automations', automationPayload)

      // Invalidate the automations cache so the list updates immediately
      queryClient.invalidateQueries({ queryKey: ['/api/automations'] })

      toast({
        title: 'Automation created successfully!',
        description: `${automation.name} is now active and ready to use.`,
      })
      
      setHasUnsavedChanges(false) // Clear unsaved changes flag
      setShowCelebration(true)
      setCurrentStep('complete')
      setTimeout(() => {
        setLocation('/dashboard/automations')
      }, 3000)
    } catch (error: any) {
      console.error('Failed to create automation:', error)
      toast({
        title: 'Failed to create automation',
        description: error.message || 'Something went wrong. Please try again.',
        variant: 'destructive'
      })
    }
  }

  // Page leave warning system
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges && currentStep !== 'complete') {
        e.preventDefault()
        e.returnValue = 'You have unsaved changes. Are you sure you want to leave?'
        return 'You have unsaved changes. Are you sure you want to leave?'
      }
    }

    const handlePopState = (e: PopStateEvent) => {
      if (hasUnsavedChanges && currentStep !== 'complete') {
        const confirmLeave = window.confirm('You have unsaved changes. Are you sure you want to leave this page?')
        if (!confirmLeave) {
          e.preventDefault()
          window.history.pushState(null, '', window.location.pathname)
        }
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    window.addEventListener('popstate', handlePopState)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      window.removeEventListener('popstate', handlePopState)
    }
  }, [hasUnsavedChanges, currentStep])

  // Track changes to automation config
  useEffect(() => {
    if (currentStep !== 'intro' && currentStep !== 'complete') {
      setHasUnsavedChanges(true)
    }
  }, [automation, currentStep])

  const renderIntro = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto">
        <Wand2 className="h-10 w-10 text-white" />
      </div>
      <div>
        <h2 className="text-2xl font-bold mb-2">Welcome to Automation Creator!</h2>
        <p className="text-muted-foreground">I'll help you build powerful automations without any coding.</p>
      </div>
      <div className="space-y-2">
        <div className="flex items-center justify-center gap-2">
          <Label htmlFor="automation-name">What would you like to name your automation?</Label>
          <HelpTooltip content="Give your automation a descriptive name that explains what it does" />
        </div>
        <Input
          id="automation-name"
          placeholder="e.g., Daily Report Generator"
          value={automation.name}
          onChange={(e) => setAutomation({ ...automation, name: e.target.value })}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && automation.name) {
              nextStep()
            }
          }}
          className="max-w-sm mx-auto"
        />
        <AIHelpSuggestions
          context="automation-name"
          fieldName="name"
          currentValue={automation.name}
          compact
          className="max-w-sm mx-auto"
        />
      </div>
      <Button 
        onClick={nextStep} 
        disabled={!automation.name}
        size="lg"
        className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
      >
        Let's Get Started
        <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
    </motion.div>
  )

  const renderTrigger = () => {
    const triggers = [
      { type: 'schedule', icon: Clock, name: 'Schedule', desc: 'Run at specific times' },
      { type: 'webhook', icon: Globe, name: 'Webhook', desc: 'Trigger via API call' },
      { type: 'email', icon: Mail, name: 'Email', desc: 'When email is received' },
      { type: 'chat', icon: MessageSquare, name: 'Chat Command', desc: 'Manual trigger via chat' },
      { type: 'discord', icon: MessageSquare, name: 'Discord', desc: 'Discord messages' },
      { type: 'slack', icon: Hash, name: 'Slack', desc: 'Slack messages' }
    ]

    const renderTriggerConfig = () => {
      switch (automation.trigger.type) {
        case 'schedule':
          return (
            <div className="space-y-4 p-4 bg-muted/30 rounded-lg">
              <div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="runType">Run scenario</Label>
                  <HelpTooltip content="Choose when and how often this automation should run" size="sm" />
                </div>
                <Select
                  value={automation.trigger.config.runType || 'at-regular-intervals'}
                  onValueChange={(value) => setAutomation({
                    ...automation,
                    trigger: { ...automation.trigger, config: { ...automation.trigger.config, runType: value } }
                  })}
                >
                  <SelectTrigger id="runType">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="at-regular-intervals">At regular intervals</SelectItem>
                    <SelectItem value="once">Once</SelectItem>
                    <SelectItem value="every-day">Every day</SelectItem>
                    <SelectItem value="days-of-week">Days of the week</SelectItem>
                    <SelectItem value="days-of-month">Days of the month</SelectItem>
                    <SelectItem value="specified-dates">Specified dates</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {automation.trigger.config.runType === 'at-regular-intervals' && (
                <div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="interval">Interval</Label>
                    <HelpTooltip content="How often should the automation run?" size="sm" />
                  </div>
                  <Select
                    value={automation.trigger.config.interval || 'hourly'}
                    onValueChange={(value) => setAutomation({
                      ...automation,
                      trigger: { ...automation.trigger, config: { ...automation.trigger.config, interval: value } }
                    })}
                  >
                    <SelectTrigger id="interval">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="5min">Every 5 minutes</SelectItem>
                      <SelectItem value="15min">Every 15 minutes</SelectItem>
                      <SelectItem value="30min">Every 30 minutes</SelectItem>
                      <SelectItem value="hourly">Every hour</SelectItem>
                      <SelectItem value="daily">Every day</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
              
              {automation.trigger.config.runType === 'once' && (
                <div>
                  <div className="flex items-center justify-between">
                    <Label>Date & Time</Label>
                    <HelpTooltip content="Select when this automation should run once" size="sm" />
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <Input
                      type="date"
                      value={automation.trigger.config.runDate || ''}
                      onChange={(e) => setAutomation({
                        ...automation,
                        trigger: { ...automation.trigger, config: { ...automation.trigger.config, runDate: e.target.value } }
                      })}
                    />
                    <Input
                      type="time"
                      value={automation.trigger.config.runTime || '09:00'}
                      onChange={(e) => setAutomation({
                        ...automation,
                        trigger: { ...automation.trigger, config: { ...automation.trigger.config, runTime: e.target.value } }
                      })}
                    />
                  </div>
                </div>
              )}
              
              {automation.trigger.config.runType === 'every-day' && (
                <div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="dailyTime">Time (24-hour format)</Label>
                    <HelpTooltip content="What time should this run every day?" size="sm" />
                  </div>
                  <Input
                    id="dailyTime"
                    type="time"
                    value={automation.trigger.config.dailyTime || '09:00'}
                    onChange={(e) => setAutomation({
                      ...automation,
                      trigger: { ...automation.trigger, config: { ...automation.trigger.config, dailyTime: e.target.value } }
                    })}
                  />
                </div>
              )}
              
              {/* Time-based Conditions */}
              <div className="mt-4 pt-4 border-t">
                <div className="flex items-center justify-between mb-3">
                  <Label className="text-sm font-semibold">Time-based Conditions (Optional)</Label>
                  <HelpTooltip content="Add additional conditions to control when your automation runs" size="sm" />
                </div>
                
                <div className="space-y-3">
                  {/* Business Hours Only */}
                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id="business-hours-only-simple"
                      checked={automation.trigger.config.businessHoursOnly || false}
                      onChange={(e) => setAutomation({
                        ...automation,
                        trigger: { ...automation.trigger, config: { ...automation.trigger.config, businessHoursOnly: e.target.checked } }
                      })}
                      className="rounded"
                    />
                    <Label htmlFor="business-hours-only-simple" className="text-xs cursor-pointer">
                      Business hours only
                    </Label>
                  </div>
                  
                  {automation.trigger.config.businessHoursOnly && (
                    <div className="ml-5 space-y-2 p-2 bg-muted/50 rounded text-xs">
                      <div className="grid grid-cols-2 gap-2">
                        <Input
                          type="time"
                          value={automation.trigger.config.businessStartTime || '09:00'}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: { ...automation.trigger, config: { ...automation.trigger.config, businessStartTime: e.target.value } }
                          })}
                          className="text-xs h-8"
                        />
                        <Input
                          type="time"
                          value={automation.trigger.config.businessEndTime || '17:00'}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: { ...automation.trigger, config: { ...automation.trigger.config, businessEndTime: e.target.value } }
                          })}
                          className="text-xs h-8"
                        />
                      </div>
                    </div>
                  )}
                  
                  {/* Weekdays Only */}
                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id="weekdays-only-simple"
                      checked={automation.trigger.config.weekdaysOnly || false}
                      onChange={(e) => setAutomation({
                        ...automation,
                        trigger: { ...automation.trigger, config: { ...automation.trigger.config, weekdaysOnly: e.target.checked } }
                      })}
                      className="rounded"
                    />
                    <Label htmlFor="weekdays-only-simple" className="text-xs cursor-pointer">
                      Weekdays only (Mon-Fri)
                    </Label>
                  </div>
                  
                  {/* Exclude Holidays */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        id="exclude-holidays-simple"
                        checked={automation.trigger.config.excludeHolidays || false}
                        onChange={(e) => setAutomation({
                          ...automation,
                          trigger: { ...automation.trigger, config: { ...automation.trigger.config, excludeHolidays: e.target.checked } }
                        })}
                        className="rounded"
                      />
                      <Label htmlFor="exclude-holidays-simple" className="text-xs cursor-pointer">
                        Skip holidays
                      </Label>
                    </div>
                    
                    {automation.trigger.config.excludeHolidays && (
                      <div className="ml-5 p-2 bg-muted/50 rounded">
                        <Select
                          value={automation.trigger.config.holidayCalendar || 'US'}
                          onValueChange={(value) => setAutomation({
                            ...automation,
                            trigger: { ...automation.trigger, config: { ...automation.trigger.config, holidayCalendar: value } }
                          })}
                        >
                          <SelectTrigger className="text-xs h-8">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="US">🇺🇸 United States</SelectItem>
                            <SelectItem value="UK">🇬🇧 United Kingdom</SelectItem>
                            <SelectItem value="CA">🇨🇦 Canada</SelectItem>
                            <SelectItem value="AU">🇦🇺 Australia</SelectItem>
                            <SelectItem value="JP">🇯🇵 Japan</SelectItem>
                            <SelectItem value="DE">🇩🇪 Germany</SelectItem>
                            <SelectItem value="FR">🇫🇷 France</SelectItem>
                            <SelectItem value="IT">🇮🇹 Italy</SelectItem>
                            <SelectItem value="ES">🇪🇸 Spain</SelectItem>
                            <SelectItem value="NL">🇳🇱 Netherlands</SelectItem>
                            <SelectItem value="SE">🇸🇪 Sweden</SelectItem>
                            <SelectItem value="NO">🇳🇴 Norway</SelectItem>
                            <SelectItem value="DK">🇩🇰 Denmark</SelectItem>
                            <SelectItem value="FI">🇫🇮 Finland</SelectItem>
                            <SelectItem value="CH">🇨🇭 Switzerland</SelectItem>
                            <SelectItem value="AT">🇦🇹 Austria</SelectItem>
                            <SelectItem value="BE">🇧🇪 Belgium</SelectItem>
                            <SelectItem value="BR">🇧🇷 Brazil</SelectItem>
                            <SelectItem value="MX">🇲🇽 Mexico</SelectItem>
                            <SelectItem value="AR">🇦🇷 Argentina</SelectItem>
                            <SelectItem value="IN">🇮🇳 India</SelectItem>
                            <SelectItem value="CN">🇨🇳 China</SelectItem>
                            <SelectItem value="KR">🇰🇷 South Korea</SelectItem>
                            <SelectItem value="SG">🇸🇬 Singapore</SelectItem>
                            <SelectItem value="MY">🇲🇾 Malaysia</SelectItem>
                            <SelectItem value="TH">🇹🇭 Thailand</SelectItem>
                            <SelectItem value="ID">🇮🇩 Indonesia</SelectItem>
                            <SelectItem value="PH">🇵🇭 Philippines</SelectItem>
                            <SelectItem value="VN">🇻🇳 Vietnam</SelectItem>
                            <SelectItem value="NZ">🇳🇿 New Zealand</SelectItem>
                            <SelectItem value="ZA">🇿🇦 South Africa</SelectItem>
                            <SelectItem value="NG">🇳🇬 Nigeria</SelectItem>
                            <SelectItem value="EG">🇪🇬 Egypt</SelectItem>
                            <SelectItem value="IL">🇮🇱 Israel</SelectItem>
                            <SelectItem value="AE">🇦🇪 UAE</SelectItem>
                            <SelectItem value="SA">🇸🇦 Saudi Arabia</SelectItem>
                            <SelectItem value="TR">🇹🇷 Turkey</SelectItem>
                            <SelectItem value="RU">🇷🇺 Russia</SelectItem>
                            <SelectItem value="PL">🇵🇱 Poland</SelectItem>
                            <SelectItem value="CZ">🇨🇿 Czech Republic</SelectItem>
                            <SelectItem value="HU">🇭🇺 Hungary</SelectItem>
                            <SelectItem value="RO">🇷🇴 Romania</SelectItem>
                            <SelectItem value="GR">🇬🇷 Greece</SelectItem>
                            <SelectItem value="PT">🇵🇹 Portugal</SelectItem>
                            <SelectItem value="IE">🇮🇪 Ireland</SelectItem>
                            <SelectItem value="LU">🇱🇺 Luxembourg</SelectItem>
                            <SelectItem value="SK">🇸🇰 Slovakia</SelectItem>
                            <SelectItem value="SI">🇸🇮 Slovenia</SelectItem>
                            <SelectItem value="LV">🇱🇻 Latvia</SelectItem>
                            <SelectItem value="LT">🇱🇹 Lithuania</SelectItem>
                            <SelectItem value="EE">🇪🇪 Estonia</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              
              <AIHelpSuggestions
                context="trigger-schedule-interval"
                fieldName="runType"
                currentValue={automation.trigger.config.runType}
                compact
              />
            </div>
          )
        case 'webhook':
          return (
            <div className="space-y-4 p-4 bg-muted/30 rounded-lg">
              <div>
                <Label>Webhook URL will be generated after creation</Label>
                <p className="text-sm text-muted-foreground mt-1">
                  You'll receive a unique URL to trigger this automation
                </p>
              </div>
            </div>
          )
        case 'email':
          return (
            <div className="space-y-4 p-4 bg-muted/30 rounded-lg">
              <div>
                <Label htmlFor="email-filter">Email Filter (optional)</Label>
                <Input
                  id="email-filter"
                  placeholder="e.g., from:<EMAIL>"
                  value={automation.trigger.config.filter || ''}
                  onChange={(e) => setAutomation({
                    ...automation,
                    trigger: { ...automation.trigger, config: { ...automation.trigger.config, filter: e.target.value } }
                  })}
                />
              </div>
            </div>
          )
        default:
          return (
            <div className="p-4 bg-muted/30 rounded-lg">
              <p className="text-sm text-muted-foreground">
                Additional configuration will be available after creation
              </p>
            </div>
          )
      }
    }

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <div className="grid grid-cols-2 gap-4">
          {triggers.map((trigger) => (
            <Card
              key={trigger.type}
              className={cn(
                "cursor-pointer transition-all hover:shadow-lg",
                automation.trigger.type === trigger.type && "ring-2 ring-primary"
              )}
              onClick={() => {
                setAutomation({
                  ...automation,
                  trigger: { type: trigger.type, config: {}, description: trigger.desc }
                })
                setShowTriggerConfig(true)
              }}
            >
              <CardHeader className="text-center">
                <trigger.icon className="h-8 w-8 mx-auto mb-2 text-primary" />
                <CardTitle className="text-base">{trigger.name}</CardTitle>
                <CardDescription className="text-xs">{trigger.desc}</CardDescription>
              </CardHeader>
            </Card>
          ))}
        </div>

        {showTriggerConfig && automation.trigger.type && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-4"
          >
            <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
              <p className="text-sm">
                Great choice! {automation.trigger.description}. Let's configure the details.
              </p>
            </div>
            {renderTriggerConfig()}
            <div className="flex gap-2">
              <Button 
                size="sm" 
                onClick={nextStep}
                disabled={
                  automation.trigger.type === 'schedule' && (
                    !automation.trigger.config.runType ||
                    (automation.trigger.config.runType === 'once' && !automation.trigger.config.runDate) ||
                    (automation.trigger.config.runType === 'at-regular-intervals' && !automation.trigger.config.interval)
                  )
                }
              >
                Continue <ArrowRight className="ml-1 h-3 w-3" />
              </Button>
            </div>
          </motion.div>
        )}
      </motion.div>
    )
  }



  const renderTools = () => {
    const availableTools = [
      { id: 'airtable', name: 'Airtable', icon: Table, desc: 'Enable your agents to manage Airtable data' },
      { id: 'discord', name: 'Discord', icon: MessageSquare, desc: 'Enable your agents to interact with Discord channels' },
      { id: 'firecrawl', name: 'Firecrawl Web Scraper', icon: Globe, desc: 'Enable your agents to crawl and collect website data' },
      { id: 'google-sheets', name: 'Google Sheets', icon: Table, desc: 'Enable your agents to read from and write to Google Sheets' },
      { id: 'notion', name: 'Notion', icon: FileText, desc: 'Enable your agents to manage Notion pages and databases' },
      { id: 'slack', name: 'Slack', icon: Hash, desc: 'Enable your agents to interact with Slack channels' },
      { id: 'supabase', name: 'Supabase', icon: Table, desc: 'Enable your agents to interact with Supabase data' },
      { id: 'serpapi', name: 'SerpAPI', icon: Globe, desc: 'Enable your agents to perform web searches' }
    ]

    const addTool = (tool: any) => {
      const newToolIndex = automation.tools.length
      setAutomation({
        ...automation,
        tools: [...automation.tools, { 
          name: tool.name, 
          enabled: true, 
          config: { id: tool.id, description: tool.desc } 
        }]
      })
      setShowToolConfig(newToolIndex)
    }

    const removeTool = (index: number) => {
      setAutomation({
        ...automation,
        tools: automation.tools.filter((_, i) => i !== index)
      })
    }

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        {/* Selected Tools */}
        {automation.tools.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base">🛠️ Tools ({automation.tools.length})</Label>
            </div>
            <div className="space-y-2">
              {automation.tools.map((tool, index) => {
                const toolInfo = availableTools.find(t => t.id === tool.config?.id)
                const Icon = toolInfo?.icon || Wrench
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="flex items-center gap-3 p-4 bg-muted/30 rounded-lg border"
                  >
                    <Icon className="h-5 w-5 text-primary" />
                    <div className="flex-1">
                      <p className="font-medium">{tool.name}</p>
                      <p className="text-xs text-muted-foreground">{tool.config?.description}</p>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setShowToolConfig(index)}
                        className="h-8 px-2"
                      >
                        <Wrench className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => removeTool(index)}
                        className="h-8 w-8 p-0"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </motion.div>
                )
              })}
            </div>
          </div>
        )}

        {/* Available Tools */}
        <div>
          <Label className="text-base mb-3 block">Available Tools</Label>
          <div className="grid gap-3">
            {availableTools.map((tool) => {
              const isAdded = automation.tools.find(t => t.config?.id === tool.id)
              return (
                <Card
                  key={tool.id}
                  className={cn(
                    "cursor-pointer transition-all hover:shadow-md",
                    isAdded && "opacity-50 cursor-not-allowed"
                  )}
                  onClick={() => !isAdded && addTool(tool)}
                >
                  <CardHeader className="p-4">
                    <div className="flex items-center gap-3">
                      <tool.icon className="h-5 w-5 text-primary" />
                      <div className="flex-1">
                        <CardTitle className="text-sm">{tool.name}</CardTitle>
                        <CardDescription className="text-xs mt-1">{tool.desc}</CardDescription>
                      </div>
                      {isAdded ? (
                        <Badge variant="secondary">Added</Badge>
                      ) : (
                        <Plus className="h-5 w-5 text-muted-foreground" />
                      )}
                    </div>
                  </CardHeader>
                </Card>
              )
            })}
          </div>
        </div>

        {automation.tools.length > 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg"
          >
            <p className="text-sm mb-3">
              Great! You've selected {automation.tools.length} tool{automation.tools.length > 1 ? 's' : ''}. 
              Ready to choose actions?
            </p>
            <Button size="sm" onClick={nextStep}>
              Choose Actions <ArrowRight className="ml-1 h-3 w-3" />
            </Button>
          </motion.div>
        )}
        
        {/* Tool Configuration Dialog */}
        {showToolConfig !== null && automation.tools[showToolConfig] && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowToolConfig(null)}
          >
            <motion.div
              initial={{ scale: 0.95 }}
              animate={{ scale: 1 }}
              className="bg-card p-6 rounded-lg shadow-lg max-w-md w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">Configure {automation.tools[showToolConfig].name}</h3>
              <div className="space-y-4">
                {automation.tools[showToolConfig].config?.id === 'airtable' && (
                  <>
                    <div>
                      <Label htmlFor="airtable-base">Airtable Base ID</Label>
                      <Input
                        id="airtable-base"
                        placeholder="e.g., appXXXXXXXXXXXXXX"
                        value={automation.tools[showToolConfig].config?.baseId || ''}
                        onChange={(e) => {
                          const updatedTools = [...automation.tools]
                          updatedTools[showToolConfig].config = {
                            ...updatedTools[showToolConfig].config,
                            baseId: e.target.value
                          }
                          setAutomation({ ...automation, tools: updatedTools })
                        }}
                      />
                    </div>
                    <div>
                      <Label htmlFor="airtable-table">Table Name</Label>
                      <Input
                        id="airtable-table"
                        placeholder="e.g., Tasks"
                        value={automation.tools[showToolConfig].config?.tableName || ''}
                        onChange={(e) => {
                          const updatedTools = [...automation.tools]
                          updatedTools[showToolConfig].config = {
                            ...updatedTools[showToolConfig].config,
                            tableName: e.target.value
                          }
                          setAutomation({ ...automation, tools: updatedTools })
                        }}
                      />
                    </div>
                  </>
                )}
                {automation.tools[showToolConfig].config?.id === 'google-sheets' && (
                  <>
                    {!automation.tools[showToolConfig].config?.isAuthenticated ? (
                      <div className="space-y-4">
                        <div className="text-center p-6 bg-muted rounded-lg">
                          <Globe className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                          <h4 className="font-medium mb-2">Connect Google Account</h4>
                          <p className="text-sm text-muted-foreground mb-4">
                            Sign in with Google to access your spreadsheets
                          </p>
                          <Button
                            onClick={() => {
                              // Simulate Google OAuth login
                              const updatedTools = [...automation.tools]
                              updatedTools[showToolConfig].config = {
                                ...updatedTools[showToolConfig].config,
                                isAuthenticated: true,
                                userEmail: '<EMAIL>'
                              }
                              setAutomation({ ...automation, tools: updatedTools })
                              toast({
                                title: 'Google Account Connected',
                                description: 'You can now access your Google Sheets'
                              })
                            }}
                            className="w-full"
                          >
                            <Globe className="mr-2 h-4 w-4" />
                            Sign in with Google
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <>
                        <div className="p-3 bg-green-50 dark:bg-green-950 rounded-md mb-4">
                          <p className="text-sm text-green-700 dark:text-green-400">
                            ✓ Connected as {automation.tools[showToolConfig].config?.userEmail}
                          </p>
                        </div>
                        <div>
                          <Label htmlFor="sheets-id">Spreadsheet ID</Label>
                          <Input
                            id="sheets-id"
                            placeholder="Found in the spreadsheet URL"
                            value={automation.tools[showToolConfig].config?.spreadsheetId || ''}
                            onChange={(e) => {
                              const updatedTools = [...automation.tools]
                              updatedTools[showToolConfig].config = {
                                ...updatedTools[showToolConfig].config,
                                spreadsheetId: e.target.value
                              }
                              setAutomation({ ...automation, tools: updatedTools })
                            }}
                          />
                        </div>
                        <div>
                          <Label htmlFor="sheet-name">Sheet Name</Label>
                          <Input
                            id="sheet-name"
                            placeholder="e.g., Sheet1"
                            value={automation.tools[showToolConfig].config?.sheetName || ''}
                            onChange={(e) => {
                              const updatedTools = [...automation.tools]
                              updatedTools[showToolConfig].config = {
                                ...updatedTools[showToolConfig].config,
                                sheetName: e.target.value
                              }
                              setAutomation({ ...automation, tools: updatedTools })
                            }}
                          />
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const updatedTools = [...automation.tools]
                            updatedTools[showToolConfig].config = {
                              ...updatedTools[showToolConfig].config,
                              isAuthenticated: false,
                              userEmail: ''
                            }
                            setAutomation({ ...automation, tools: updatedTools })
                          }}
                          className="w-full"
                        >
                          Disconnect Google Account
                        </Button>
                      </>
                    )}
                  </>
                )}
                {automation.tools[showToolConfig].config?.id === 'slack' && (
                  <div>
                    <Label htmlFor="slack-channel">Default Channel</Label>
                    <Input
                      id="slack-channel"
                      placeholder="e.g., #general"
                      value={automation.tools[showToolConfig].config?.channel || ''}
                      onChange={(e) => {
                        const updatedTools = [...automation.tools]
                        updatedTools[showToolConfig].config = {
                          ...updatedTools[showToolConfig].config,
                          channel: e.target.value
                        }
                        setAutomation({ ...automation, tools: updatedTools })
                      }}
                    />
                  </div>
                )}
                {automation.tools[showToolConfig].config?.id === 'discord' && (
                  <div>
                    <Label htmlFor="discord-channel">Default Channel ID</Label>
                    <Input
                      id="discord-channel"
                      placeholder="e.g., 123456789012345678"
                      value={automation.tools[showToolConfig].config?.channelId || ''}
                      onChange={(e) => {
                        const updatedTools = [...automation.tools]
                        updatedTools[showToolConfig].config = {
                          ...updatedTools[showToolConfig].config,
                          channelId: e.target.value
                        }
                        setAutomation({ ...automation, tools: updatedTools })
                      }}
                    />
                  </div>
                )}
                {automation.tools[showToolConfig].config?.id === 'serpapi' && (
                  <div>
                    <Label htmlFor="serpapi-engine">Search Engine</Label>
                    <Select
                      value={automation.tools[showToolConfig].config?.engine || 'google'}
                      onValueChange={(value) => {
                        const updatedTools = [...automation.tools]
                        updatedTools[showToolConfig].config = {
                          ...updatedTools[showToolConfig].config,
                          engine: value
                        }
                        setAutomation({ ...automation, tools: updatedTools })
                      }}
                    >
                      <SelectTrigger id="serpapi-engine">
                        <SelectValue placeholder="Select search engine" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="google">Google</SelectItem>
                        <SelectItem value="bing">Bing</SelectItem>
                        <SelectItem value="duckduckgo">DuckDuckGo</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
                <div className="flex justify-end gap-2 mt-6">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowToolConfig(null)}
                  >
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => setShowToolConfig(null)}
                  >
                    Save Configuration
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </motion.div>
    )
  }

  const renderActions = () => {
    const availableActions = [
      { id: 'ai-model', name: 'AI Model', icon: Brain, desc: 'Configure AI model and settings for intelligent processing' },
      { id: 'discord-read', name: 'Discord Read', icon: MessageSquare, desc: 'Read messages from any Discord channel' },
      { id: 'discord-send', name: 'Discord Send', icon: MessageSquare, desc: 'Send messages to any Discord channel' },
      { id: 'gmail-read', name: 'Gmail Read', icon: Mail, desc: 'Read emails from your Gmail account' },
      { id: 'gmail-send', name: 'Gmail Send', icon: Mail, desc: 'Send emails via your Gmail account' },
      { id: 'gcalendar-read', name: 'Google Calendar Read', icon: Calendar, desc: 'Read events from any Google Calendar' },
      { id: 'gcalendar-write', name: 'Google Calendar Write', icon: Calendar, desc: 'Create events on any Google Calendar' },
      { id: 'gdocs-read', name: 'Google Docs Read', icon: FileText, desc: 'Read content from any Google Doc' },
      { id: 'gdocs-write', name: 'Google Docs Write', icon: FileText, desc: 'Write content to any Google Doc' },
      { id: 'gsheets-read', name: 'Google Sheets Read', icon: Table, desc: 'Read data from any Google Sheet' },
      { id: 'gsheets-write', name: 'Google Sheets Write', icon: Table, desc: 'Write data to any Google Sheet' },
      { id: 'http-request', name: 'HTTP Request', icon: Globe, desc: 'Make GET, POST, PUT, or DELETE HTTP requests' },
      { id: 'pdf-generator', name: 'Resume PDF Generator', icon: FileText, desc: 'Generate a professional PDF resume from user-provided details' },
      { id: 'slack-read', name: 'Slack Read', icon: Hash, desc: 'Read messages from a specified Slack channel' },
      { id: 'slack-send', name: 'Slack Send', icon: Hash, desc: 'Send a message to a specified Slack channel' }
    ]

    // Filter actions based on search query
    const filteredActions = availableActions.filter(action => {
      const query = actionSearchQuery.toLowerCase()
      return (
        action.name.toLowerCase().includes(query) ||
        action.desc.toLowerCase().includes(query)
      )
    })

    // Limit to 10 actions initially, unless showAllActions is true or there's a search query
    const displayedActions = actionSearchQuery || showAllActions ? filteredActions : filteredActions.slice(0, 10)

    const addAction = (action: any) => {
      const newActionIndex = automation.actions.length
      const newAction = {
        type: action.id,
        description: action.desc,
        config: { id: Date.now().toString(), name: action.name }
      }
      setAutomation({
        ...automation,
        actions: [...automation.actions, newAction]
      })
      setShowActionConfig(newActionIndex)
    }

    const removeAction = (index: number) => {
      setAutomation({
        ...automation,
        actions: automation.actions.filter((_, i) => i !== index)
      })
    }

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        {/* Selected Actions */}
        {automation.actions.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base">⚙️ Actions ({automation.actions.length})</Label>
            </div>
            <div className="space-y-2">
              {automation.actions.map((action, index) => {
                const actionInfo = availableActions.find(a => a.id === action.type)
                const Icon = actionInfo?.icon || Wrench
                return (
                  <motion.div
                    key={action.config.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="flex items-center gap-3 p-4 bg-muted/30 rounded-lg border"
                  >
                    <Icon className="h-5 w-5 text-primary" />
                    <div className="flex-1">
                      <p className="font-medium">{action.config.name}</p>
                      <p className="text-xs text-muted-foreground">{action.description}</p>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setShowActionConfig(index)}
                        className="h-8 px-2"
                      >
                        <Wrench className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => removeAction(index)}
                        className="h-8 w-8 p-0"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </motion.div>
                )
              })}
            </div>
          </div>
        )}

        {/* Available Actions */}
        <div>
          <Label className="text-base mb-3 block">Available Actions</Label>
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search actions by name or description..."
              value={actionSearchQuery}
              onChange={(e) => setActionSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          {filteredActions.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No actions found matching "{actionSearchQuery}"</p>
            </div>
          ) : (
            <>
              <div className="grid gap-3">
                {displayedActions.map((action) => (
                  <Card
                    key={action.id}
                    className="cursor-pointer transition-all hover:shadow-md"
                    onClick={() => addAction(action)}
                  >
                    <CardHeader className="p-4">
                      <div className="flex items-center gap-3">
                        <action.icon className="h-5 w-5 text-primary" />
                        <div className="flex-1">
                          <CardTitle className="text-sm">{action.name}</CardTitle>
                          <CardDescription className="text-xs mt-1">{action.desc}</CardDescription>
                        </div>
                        <Plus className="h-5 w-5 text-muted-foreground" />
                      </div>
                    </CardHeader>
                  </Card>
                ))}
              </div>
              
              {!actionSearchQuery && !showAllActions && filteredActions.length > 10 && (
                <div className="flex justify-center pt-4">
                  <Button 
                    variant="outline" 
                    onClick={() => setShowAllActions(true)}
                    className="w-full max-w-xs"
                  >
                    Load More Actions ({filteredActions.length - 10} more)
                  </Button>
                </div>
              )}
            </>
          )}
        </div>

        {automation.actions.length > 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="bg-green-50 dark:bg-green-950 p-4 rounded-lg"
          >
            <p className="text-sm mb-3">
              Perfect! You've selected {automation.actions.length} action{automation.actions.length > 1 ? 's' : ''}. 
              Ready to set up how results are delivered?
            </p>
            <Button size="sm" onClick={nextStep}>
              Configure Response <ArrowRight className="ml-1 h-3 w-3" />
            </Button>
          </motion.div>
        )}
        
        {/* Action Configuration Dialog */}
        {showActionConfig !== null && automation.actions[showActionConfig] && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowActionConfig(null)}
          >
            <motion.div
              initial={{ scale: 0.95 }}
              animate={{ scale: 1 }}
              className="bg-card p-6 rounded-lg shadow-lg max-w-md w-full max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">Configure {automation.actions[showActionConfig].config.name}</h3>
              <div className="space-y-4">
                {/* AI Model Configuration */}
                {automation.actions[showActionConfig].type === 'ai-model' && (
                  <>
                    <div>
                      <Label htmlFor="ai-model">AI Model</Label>
                      <Select
                        value={automation.llm.model}
                        onValueChange={(value) => setAutomation({
                          ...automation,
                          llm: { ...automation.llm, model: value }
                        })}
                      >
                        <SelectTrigger id="ai-model">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="gpt-4o">GPT-4o (Recommended)</SelectItem>
                          <SelectItem value="gpt-4">GPT-4</SelectItem>
                          <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                          <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
                          <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                          <SelectItem value="claude-3-haiku">Claude 3 Haiku</SelectItem>
                          <SelectItem value="gemini-pro">Gemini Pro</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="creativity">Creativity Level</Label>
                      <Select
                        value={automation.llm.creativity}
                        onValueChange={(value: 'conservative' | 'balanced' | 'creative') => setAutomation({
                          ...automation,
                          llm: { ...automation.llm, creativity: value }
                        })}
                      >
                        <SelectTrigger id="creativity">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="conservative">Conservative (Precise)</SelectItem>
                          <SelectItem value="balanced">Balanced (Recommended)</SelectItem>
                          <SelectItem value="creative">Creative (Innovative)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="max-tokens">Max Response Length</Label>
                      <Select
                        value={automation.llm.maxTokens.toString()}
                        onValueChange={(value) => setAutomation({
                          ...automation,
                          llm: { ...automation.llm, maxTokens: parseInt(value) }
                        })}
                      >
                        <SelectTrigger id="max-tokens">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="500">Short (500 tokens)</SelectItem>
                          <SelectItem value="1000">Medium (1,000 tokens)</SelectItem>
                          <SelectItem value="2000">Long (2,000 tokens)</SelectItem>
                          <SelectItem value="4000">Very Long (4,000 tokens)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}
                
                {/* Gmail Send Configuration */}
                {automation.actions[showActionConfig].type === 'gmail-send' && (
                  <>
                    <div>
                      <Label htmlFor="gmail-to">To Email</Label>
                      <Input
                        id="gmail-to"
                        placeholder="e.g., <EMAIL>"
                        value={automation.actions[showActionConfig].config?.to || ''}
                        onChange={(e) => {
                          const updatedActions = [...automation.actions]
                          updatedActions[showActionConfig].config = {
                            ...updatedActions[showActionConfig].config,
                            to: e.target.value
                          }
                          setAutomation({ ...automation, actions: updatedActions })
                        }}
                      />
                    </div>
                    <div>
                      <Label htmlFor="gmail-subject">Subject</Label>
                      <Input
                        id="gmail-subject"
                        placeholder="e.g., Daily Report"
                        value={automation.actions[showActionConfig].config?.subject || ''}
                        onChange={(e) => {
                          const updatedActions = [...automation.actions]
                          updatedActions[showActionConfig].config = {
                            ...updatedActions[showActionConfig].config,
                            subject: e.target.value
                          }
                          setAutomation({ ...automation, actions: updatedActions })
                        }}
                      />
                    </div>
                  </>
                )}
                
                {/* Discord/Slack Send Configuration */}
                {(automation.actions[showActionConfig].type === 'discord-send' || 
                  automation.actions[showActionConfig].type === 'slack-send') && (
                  <div>
                    <Label htmlFor="channel">Channel</Label>
                    <Input
                      id="channel"
                      placeholder={automation.actions[showActionConfig].type === 'discord-send' ? 
                        "e.g., 123456789012345678" : "e.g., #general"}
                      value={automation.actions[showActionConfig].config?.channel || ''}
                      onChange={(e) => {
                        const updatedActions = [...automation.actions]
                        updatedActions[showActionConfig].config = {
                          ...updatedActions[showActionConfig].config,
                          channel: e.target.value
                        }
                        setAutomation({ ...automation, actions: updatedActions })
                      }}
                    />
                  </div>
                )}
                
                {/* Google Sheets Write Configuration */}
                {automation.actions[showActionConfig].type === 'gsheets-write' && (
                  <>
                    <div>
                      <Label htmlFor="sheets-id">Spreadsheet ID</Label>
                      <Input
                        id="sheets-id"
                        placeholder="Found in the spreadsheet URL"
                        value={automation.actions[showActionConfig].config?.spreadsheetId || ''}
                        onChange={(e) => {
                          const updatedActions = [...automation.actions]
                          updatedActions[showActionConfig].config = {
                            ...updatedActions[showActionConfig].config,
                            spreadsheetId: e.target.value
                          }
                          setAutomation({ ...automation, actions: updatedActions })
                        }}
                      />
                    </div>
                    <div>
                      <Label htmlFor="sheet-name">Sheet Name</Label>
                      <Input
                        id="sheet-name"
                        placeholder="e.g., Sheet1"
                        value={automation.actions[showActionConfig].config?.sheetName || ''}
                        onChange={(e) => {
                          const updatedActions = [...automation.actions]
                          updatedActions[showActionConfig].config = {
                            ...updatedActions[showActionConfig].config,
                            sheetName: e.target.value
                          }
                          setAutomation({ ...automation, actions: updatedActions })
                        }}
                      />
                    </div>
                  </>
                )}
                
                {/* Google Calendar Write Configuration */}
                {automation.actions[showActionConfig].type === 'gcalendar-write' && (
                  <>
                    <div>
                      <Label htmlFor="calendar-id">Calendar ID</Label>
                      <Input
                        id="calendar-id"
                        placeholder="e.g., primary or calendar ID"
                        value={automation.actions[showActionConfig].config?.calendarId || ''}
                        onChange={(e) => {
                          const updatedActions = [...automation.actions]
                          updatedActions[showActionConfig].config = {
                            ...updatedActions[showActionConfig].config,
                            calendarId: e.target.value
                          }
                          setAutomation({ ...automation, actions: updatedActions })
                        }}
                      />
                    </div>
                    <div>
                      <Label htmlFor="event-duration">Default Event Duration (minutes)</Label>
                      <Input
                        id="event-duration"
                        type="number"
                        placeholder="e.g., 60"
                        value={automation.actions[showActionConfig].config?.duration || ''}
                        onChange={(e) => {
                          const updatedActions = [...automation.actions]
                          updatedActions[showActionConfig].config = {
                            ...updatedActions[showActionConfig].config,
                            duration: e.target.value
                          }
                          setAutomation({ ...automation, actions: updatedActions })
                        }}
                      />
                    </div>
                  </>
                )}
                
                {/* HTTP Request Configuration */}
                {automation.actions[showActionConfig].type === 'http-request' && (
                  <>
                    <div>
                      <Label htmlFor="http-url">URL</Label>
                      <Input
                        id="http-url"
                        placeholder="e.g., https://api.example.com/webhook"
                        value={automation.actions[showActionConfig].config?.url || ''}
                        onChange={(e) => {
                          const updatedActions = [...automation.actions]
                          updatedActions[showActionConfig].config = {
                            ...updatedActions[showActionConfig].config,
                            url: e.target.value
                          }
                          setAutomation({ ...automation, actions: updatedActions })
                        }}
                      />
                    </div>
                    <div>
                      <Label htmlFor="http-method">Method</Label>
                      <Select
                        value={automation.actions[showActionConfig].config?.method || 'GET'}
                        onValueChange={(value) => {
                          const updatedActions = [...automation.actions]
                          updatedActions[showActionConfig].config = {
                            ...updatedActions[showActionConfig].config,
                            method: value
                          }
                          setAutomation({ ...automation, actions: updatedActions })
                        }}
                      >
                        <SelectTrigger id="http-method">
                          <SelectValue placeholder="Select method" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="GET">GET</SelectItem>
                          <SelectItem value="POST">POST</SelectItem>
                          <SelectItem value="PUT">PUT</SelectItem>
                          <SelectItem value="DELETE">DELETE</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}
                
                <div className="flex justify-end gap-2 mt-6">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowActionConfig(null)}
                  >
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => setShowActionConfig(null)}
                  >
                    Save Configuration
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </motion.div>
    )
  }

  const renderLLM = () => {
    const llmOptions = [
      { 
        id: 'openai', 
        name: 'OpenAI Models', 
        icon: Brain, 
        desc: 'GPT-4o, GPT-4, and GPT-3.5 models',
        models: ['gpt-4o', 'gpt-4', 'gpt-3.5-turbo']
      },
      { 
        id: 'anthropic', 
        name: 'Claude Models', 
        icon: Brain, 
        desc: 'Claude 3 Opus, Claude 3, and Claude 3 Haiku',
        models: ['claude-3-opus', 'claude-3', 'claude-3-haiku']
      },
      { 
        id: 'google', 
        name: 'Gemini Models', 
        icon: Brain, 
        desc: 'Gemini 1.5 Pro and Gemini 1.0',
        models: ['gemini-1.5-pro', 'gemini-1.0']
      }
    ]

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        {/* LLM Selection */}
        <div>
          <Label className="text-base mb-3 block">🧠 LLM</Label>
          <div className="grid gap-3">
            {llmOptions.map((llm) => (
              <Card
                key={llm.id}
                className={cn(
                  "cursor-pointer transition-all",
                  (automation.llm.model.includes('gpt') && llm.id === 'openai') ||
                  (automation.llm.model.includes('claude') && llm.id === 'anthropic') ||
                  (automation.llm.model.includes('gemini') && llm.id === 'google')
                    ? "ring-2 ring-primary" : ""
                )}
                onClick={() => setAutomation({
                  ...automation,
                  llm: { ...automation.llm, model: llm.models[0] }
                })}
              >
                <CardHeader className="p-4">
                  <div className="flex items-center gap-3">
                    <llm.icon className="h-5 w-5 text-primary" />
                    <div className="flex-1">
                      <CardTitle className="text-sm">{llm.name}</CardTitle>
                      <CardDescription className="text-xs mt-1">{llm.desc}</CardDescription>
                    </div>
                    {((automation.llm.model.includes('gpt') && llm.id === 'openai') ||
                      (automation.llm.model.includes('claude') && llm.id === 'anthropic') ||
                      (automation.llm.model.includes('gemini') && llm.id === 'google')) && 
                     <Check className="h-5 w-5 text-primary" />}
                  </div>
                </CardHeader>
              </Card>
            ))}
          </div>
        </div>

        {/* Model Configuration */}
        {automation.llm.model && (
          <div className="space-y-4 p-4 bg-muted/30 rounded-lg">
            <div>
              <Label>Model Version</Label>
              <Select
                value={automation.llm.model}
                onValueChange={(value) => setAutomation({
                  ...automation,
                  llm: { ...automation.llm, model: value }
                })}
              >
                <SelectTrigger className="mt-2">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {/* OpenAI Models */}
                  <SelectItem value="gpt-4o">GPT-4o (Latest & most capable)</SelectItem>
                  <SelectItem value="gpt-4">GPT-4 (Most capable)</SelectItem>
                  <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo (Fast & efficient)</SelectItem>
                  
                  {/* Claude Models */}
                  <SelectItem value="claude-3-opus">Claude 3 Opus (Most capable)</SelectItem>
                  <SelectItem value="claude-3">Claude 3 (Balanced)</SelectItem>
                  <SelectItem value="claude-3-haiku">Claude 3 Haiku (Fast & efficient)</SelectItem>
                  
                  {/* Gemini Models */}
                  <SelectItem value="gemini-1.5-pro">Gemini 1.5 Pro (Latest)</SelectItem>
                  <SelectItem value="gemini-1.0">Gemini 1.0 (Stable)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Response Style</Label>
              <RadioGroup
                value={automation.llm.creativity}
                onValueChange={(value: any) => setAutomation({
                  ...automation,
                  llm: { ...automation.llm, creativity: value }
                })}
                className="flex gap-4 mt-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="conservative" id="conservative" />
                  <Label htmlFor="conservative" className="cursor-pointer">Precise</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="balanced" id="balanced" />
                  <Label htmlFor="balanced" className="cursor-pointer">Balanced</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="creative" id="creative" />
                  <Label htmlFor="creative" className="cursor-pointer">Creative</Label>
                </div>
              </RadioGroup>
            </div>
          </div>
        )}

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg"
        >
          <p className="text-sm mb-3">
            Perfect! {automation.llm.model.includes('gpt') ? 'OpenAI' : 
                     automation.llm.model.includes('claude') ? 'Claude' : 
                     automation.llm.model.includes('gemini') ? 'Gemini' : 'AI'} model {automation.llm.model} is configured. 
            Let's set up how results should be delivered.
          </p>
          <Button size="sm" onClick={nextStep}>
            Configure Responder <ArrowRight className="ml-1 h-3 w-3" />
          </Button>
        </motion.div>
      </motion.div>
    )
  }

  const renderResponder = () => {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <div className="space-y-4">
          <div>
            <Label>Delivery Method</Label>
            <Select
              value={automation.responder.type}
              onValueChange={(value) => setAutomation({
                ...automation,
                responder: { ...automation.responder, type: value }
              })}
            >
              <SelectTrigger className="mt-2">
                <SelectValue placeholder="Choose delivery method" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="email">Email</SelectItem>
                <SelectItem value="slack">Slack Message</SelectItem>
                <SelectItem value="discord">Discord Message</SelectItem>
                <SelectItem value="webhook">Webhook</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {automation.responder.type && (
            <div>
              <Label>Destination</Label>
              <Input
                placeholder={automation.responder.type === 'email' ? '<EMAIL>' : 'Channel or webhook URL'}
                value={automation.responder.destination}
                onChange={(e) => setAutomation({
                  ...automation,
                  responder: { ...automation.responder, destination: e.target.value }
                })}
                className="mt-2"
              />
            </div>
          )}

          <div>
            <Label>Response Tone</Label>
            <RadioGroup
              value={automation.responder.tone}
              onValueChange={(value) => setAutomation({
                ...automation,
                responder: { ...automation.responder, tone: value }
              })}
              className="flex gap-4 mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="professional" id="professional" />
                <Label htmlFor="professional">Professional</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="casual" id="casual" />
                <Label htmlFor="casual">Casual</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="friendly" id="friendly" />
                <Label htmlFor="friendly">Friendly</Label>
              </div>
            </RadioGroup>
          </div>

          {automation.responder.destination && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="bg-green-50 dark:bg-green-950 p-4 rounded-lg"
            >
              <p className="text-sm mb-3">
                Excellent! Results will be sent to {automation.responder.destination}. 
                Ready to review everything?
              </p>
              <Button size="sm" onClick={nextStep}>
                Review & Create <Check className="ml-1 h-3 w-3" />
              </Button>
            </motion.div>
          )}
        </div>
      </motion.div>
    )
  }

  const renderReview = () => {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <Card>
          <CardHeader>
            <CardTitle>{automation.name}</CardTitle>
            <CardDescription>Review your automation configuration</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Trigger</span>
                <Badge variant="outline">{automation.trigger.type}</Badge>
              </div>
              <Separator />
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Agent</span>
                <span className="text-sm">{automation.agent.name} ({automation.agent.personality})</span>
              </div>
              <Separator />

              <div className="flex justify-between items-start">
                <span className="text-sm font-medium">Actions</span>
                <div className="flex flex-wrap gap-1 justify-end max-w-xs">
                  {automation.actions.map((action, i) => (
                    <Badge key={i} variant="secondary" className="text-xs">
                      {action.config.name || action.type}
                    </Badge>
                  ))}
                </div>
              </div>
              <Separator />
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">AI Model</span>
                <Badge>{automation.llm.model}</Badge>
              </div>
              <Separator />
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Delivery</span>
                <span className="text-sm">{automation.responder.type} to {automation.responder.destination}</span>
              </div>
            </div>

            <div className="pt-4">
              <p className="text-sm text-muted-foreground mb-4">
                Everything looks great! Would you like to create this automation?
              </p>
              <div className="flex gap-2">
                <Button variant="outline" onClick={prevStep}>
                  <ChevronLeft className="mr-1 h-4 w-4" />
                  Go Back
                </Button>
                <Button onClick={handleComplete} className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                  Create Automation
                  <Check className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  const renderComplete = () => (
    <>
      {showCelebration && (
        <EmojiCelebration
          onComplete={() => setShowCelebration(false)}
          duration={3000}
        />
      )}
      
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="text-center space-y-6"
      >
        <div className="w-20 h-20 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto">
          <Check className="h-10 w-10 text-green-600 dark:text-green-400" />
        </div>
        <div>
          <h2 className="text-2xl font-bold mb-2">Congratulations!</h2>
          <p className="text-muted-foreground">
            {automation.name} has been created and is ready to use.
          </p>
        </div>
        <div className="space-y-2">
          <p className="text-sm text-muted-foreground">Redirecting to your automations...</p>
          <Progress value={100} className="max-w-xs mx-auto" />
        </div>
      </motion.div>
    </>
  )

  const renderStepContent = () => {
    switch (currentStep) {
      case 'intro': return renderIntro()
      case 'trigger': return renderTrigger()
      case 'action': return renderActions()
      case 'responder': return renderResponder()
      case 'review': return renderReview()
      case 'complete': return renderComplete()
      default: return null
    }
  }

  const CurrentIcon = stepConfig[currentStep].icon

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => setLocation('/dashboard/automations')}
          className="mb-4"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back to Automations
        </Button>

        {currentStep !== 'complete' && (
          <>
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <CurrentIcon className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">{stepConfig[currentStep].title}</h1>
                <p className="text-muted-foreground text-sm">{stepConfig[currentStep].subtitle}</p>
              </div>
            </div>

            <Progress value={progress} className="h-2" />
          </>
        )}
      </div>

      {/* Content */}
      <AnimatePresence mode="wait">
        <Card key={currentStep} className="min-h-[400px]">
          <CardContent className="p-6">
            {renderStepContent()}
          </CardContent>
        </Card>
      </AnimatePresence>

      {/* Navigation */}
      {currentStep !== 'intro' && currentStep !== 'complete' && !showConfirmation && (
        <div className="flex justify-between mt-6">
          <Button variant="outline" onClick={prevStep}>
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button onClick={nextStep} disabled={currentStep === 'review'}>
            Next
            <ChevronRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  )
}