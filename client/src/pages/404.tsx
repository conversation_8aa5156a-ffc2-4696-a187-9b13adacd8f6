import { useLocation } from 'wouter'
import { Home, ArrowLeft, Search, FileX } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'

export default function NotFound() {
  const [, setLocation] = useLocation()

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-6 text-center">
        {/* Error Icon */}
        <div className="flex justify-center">
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-red-600 to-orange-600 rounded-full blur-lg opacity-20"></div>
            <div className="relative w-24 h-24 bg-card border-2 border-red-200 dark:border-red-800 rounded-full flex items-center justify-center">
              <FileX className="h-12 w-12 text-red-500" />
            </div>
          </div>
        </div>

        {/* Error Content */}
        <div className="space-y-3">
          <h1 className="text-4xl font-bold text-foreground">404</h1>
          <h2 className="text-xl font-semibold text-muted-foreground">
            Page Not Found
          </h2>
          <p className="text-sm text-muted-foreground max-w-sm mx-auto">
            The page you're looking for doesn't exist or has been moved. 
            Let's get you back to your automation platform.
          </p>
        </div>

        {/* Action Buttons */}
        <Card className="bg-card/50 backdrop-blur-sm">
          <CardContent className="pt-6 space-y-3">
            <Button 
              onClick={() => setLocation('/dashboard')}
              className="w-full bg-[#155DB8] hover:bg-[#155DB8]/90"
            >
              <Home className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
            
            <Button 
              onClick={() => window.history.back()}
              variant="outline" 
              className="w-full"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>

            <Button 
              onClick={() => setLocation('/dashboard/browse-templates')}
              variant="ghost" 
              className="w-full"
            >
              <Search className="h-4 w-4 mr-2" />
              Browse Templates
            </Button>
          </CardContent>
        </Card>

        {/* Help Text */}
        <p className="text-xs text-muted-foreground">
          Need help? Check our{' '}
          <button 
            onClick={() => setLocation('/dashboard/settings')}
            className="text-[#155DB8] hover:underline"
          >
            settings
          </button>{' '}
          or create a new automation.
        </p>
      </div>
    </div>
  )
}