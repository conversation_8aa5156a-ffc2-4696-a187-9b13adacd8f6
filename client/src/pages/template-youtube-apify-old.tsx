import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { ArrowRight, ArrowLeft, Check, Youtube, Settings, FileText, Video, Mic, Brain, Globe, Download, Key, Play } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Checkbox } from '@/components/ui/checkbox'
import { SiYoutube } from 'react-icons/si'
import { ActionsSelector } from '@/components/actions-selector'
import { useLocation } from 'wouter'
import { Progress } from '@/components/ui/progress'

type StepType = 'intro' | 'naming' | 'youtube-apify' | 'transcription-config' | 'content-output' | 'actions-question' | 'actions' | 'review' | 'complete'

interface YouTubeApifyConfig {
  name: string
  youtubeInput: {
    inputType: 'url' | 'channel' | 'playlist'
    videoUrl: string
    channelUrl: string
    playlistUrl: string
    maxVideos: number
  }
  apifyConnection: {
    apiKey: string
    isConfigured: boolean
    actorId: string
    runSettings: {
      maxConcurrency: number
      timeout: number
      memory: number
    }
  }
  transcriptionConfig: {
    service: 'whisper' | 'assemblyai' | 'rev'
    language: string
    format: 'text' | 'srt' | 'vtt'
    includeTimestamps: boolean
    speakerIdentification: boolean
    summaryLength: 'short' | 'medium' | 'long'
    summaryStyle: 'bullet' | 'paragraph' | 'executive'
  }
  contentOutput: {
    outputFormats: {
      transcription: boolean
      summary: boolean
      keyPoints: boolean
      quotes: boolean
      timestamps: boolean
    }
    deliveryMethod: 'download' | 'email' | 'webhook' | 'googlesheets'
    destination: string
    fileNaming: string
  }
  selectedActionsList: Array<{
    id: string
    type: string
    name: string
    description: string
    config: Record<string, any>
  }>
  wantsActions: boolean | null
}

const defaultConfig: YouTubeApifyConfig = {
  name: 'YouTube Video Transcription & Summary',
  youtubeInput: {
    inputType: 'url',
    videoUrl: '',
    channelUrl: '',
    playlistUrl: '',
    maxVideos: 10
  },
  apifyConnection: {
    apiKey: '',
    isConfigured: false,
    actorId: 'youtube-scraper',
    runSettings: {
      maxConcurrency: 5,
      timeout: 300,
      memory: 512
    }
  },
  transcriptionConfig: {
    service: 'whisper',
    language: 'auto',
    format: 'text',
    includeTimestamps: true,
    speakerIdentification: false,
    summaryLength: 'medium',
    summaryStyle: 'bullet'
  },
  contentOutput: {
    outputFormats: {
      transcription: true,
      summary: true,
      keyPoints: true,
      quotes: false,
      timestamps: true
    },
    deliveryMethod: 'download',
    destination: '',
    fileNaming: 'video-title-date'
  },
  selectedActionsList: [],
  wantsActions: null
}

const steps: { id: StepType; title: string; icon: React.ComponentType<{ className?: string }> }[] = [
  { id: 'intro', title: 'Introduction', icon: Play },
  { id: 'naming', title: 'Name Automation', icon: FileText },
  { id: 'youtube-apify', title: 'YouTube & Apify Setup', icon: Youtube },
  { id: 'transcription-config', title: 'Transcription Config', icon: Mic },
  { id: 'content-output', title: 'Content Output', icon: FileText },
  { id: 'custom-actions', title: 'Custom Actions', icon: Settings },
  { id: 'review', title: 'Review', icon: Check },
  { id: 'complete', title: 'Complete', icon: Check }
]

export default function YouTubeApifyTemplate() {
  const [currentStep, setCurrentStep] = useState<StepType>('intro')
  const [config, setConfig] = useState<YouTubeApifyConfig>(defaultConfig)

  const updateConfig = <K extends keyof YouTubeApifyConfig>(
    key: K,
    value: Partial<YouTubeApifyConfig[K]>
  ) => {
    setConfig(prev => ({
      ...prev,
      [key]: { ...prev[key], ...value }
    }))
  }

  const nextStep = () => {
    const stepIndex = steps.findIndex(step => step.id === currentStep)
    if (stepIndex < steps.length - 1) {
      setCurrentStep(steps[stepIndex + 1].id)
    }
  }

  const prevStep = () => {
    const stepIndex = steps.findIndex(step => step.id === currentStep)
    if (stepIndex > 0) {
      setCurrentStep(steps[stepIndex - 1].id)
    }
  }

  const renderIntro = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="flex justify-center items-center gap-4 mb-6">
        <div className="p-4 bg-red-100 dark:bg-red-900/20 rounded-full">
          <SiYoutube className="h-12 w-12 text-red-600" />
        </div>
        <ArrowRight className="h-6 w-6 text-muted-foreground" />
        <div className="p-4 bg-orange-100 dark:bg-orange-900/20 rounded-full">
          <Globe className="h-12 w-12 text-orange-600" />
        </div>
        <ArrowRight className="h-6 w-6 text-muted-foreground" />
        <div className="p-4 bg-blue-100 dark:bg-blue-900/20 rounded-full">
          <FileText className="h-12 w-12 text-blue-600" />
        </div>
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-4">YouTube Video Transcription & Summary</h2>
        <p className="text-muted-foreground text-lg mb-6">
          Automatically extract audio from YouTube videos, generate accurate transcriptions, 
          and create intelligent summaries using AI.
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-6 text-left">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <Youtube className="h-5 w-5 text-red-600" />
              YouTube Input
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Connect to YouTube videos, channels, or playlists using Apify's powerful scraping technology.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <Mic className="h-5 w-5 text-green-600" />
              AI Transcription
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Generate accurate transcriptions with timestamps, speaker identification, and multiple format options.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <Brain className="h-5 w-5 text-purple-600" />
              Smart Summaries
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Create intelligent summaries, extract key points, and generate actionable insights from video content.
            </p>
          </CardContent>
        </Card>
      </div>

      <Button onClick={nextStep} className="w-full max-w-md">
        Get Started <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
    </motion.div>
  )

  const renderYouTubeApify = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <div className="flex justify-center items-center gap-3 mb-4">
          <SiYoutube className="h-12 w-12 text-red-600" />
          <ArrowRight className="h-6 w-6 text-muted-foreground" />
          <Globe className="h-12 w-12 text-orange-600" />
        </div>
        <h3 className="text-lg font-semibold mb-2">YouTube Video Input & Apify API Connection</h3>
        <p className="text-muted-foreground text-sm">
          Configure your YouTube video source and connect to Apify for data extraction.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Youtube className="h-5 w-5 text-red-600" />
            YouTube Video Input
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-sm font-medium">Input Type</Label>
            <RadioGroup
              value={config.youtubeInput.inputType}
              onValueChange={(value: 'url' | 'channel' | 'playlist') => 
                updateConfig('youtubeInput', { inputType: value })
              }
              className="mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="url" id="url" />
                <Label htmlFor="url">Single Video URL</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="channel" id="channel" />
                <Label htmlFor="channel">YouTube Channel</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="playlist" id="playlist" />
                <Label htmlFor="playlist">YouTube Playlist</Label>
              </div>
            </RadioGroup>
          </div>

          {config.youtubeInput.inputType === 'url' && (
            <div>
              <Label htmlFor="video-url">Video URL</Label>
              <Input
                id="video-url"
                placeholder="https://www.youtube.com/watch?v=..."
                value={config.youtubeInput.videoUrl}
                onChange={(e) => updateConfig('youtubeInput', { videoUrl: e.target.value })}
              />
            </div>
          )}

          {config.youtubeInput.inputType === 'channel' && (
            <>
              <div>
                <Label htmlFor="channel-url">Channel URL</Label>
                <Input
                  id="channel-url"
                  placeholder="https://www.youtube.com/@channelname"
                  value={config.youtubeInput.channelUrl}
                  onChange={(e) => updateConfig('youtubeInput', { channelUrl: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="max-videos">Maximum Videos to Process</Label>
                <Select
                  value={config.youtubeInput.maxVideos.toString()}
                  onValueChange={(value) => updateConfig('youtubeInput', { maxVideos: parseInt(value) })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5 videos</SelectItem>
                    <SelectItem value="10">10 videos</SelectItem>
                    <SelectItem value="25">25 videos</SelectItem>
                    <SelectItem value="50">50 videos</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </>
          )}

          {config.youtubeInput.inputType === 'playlist' && (
            <div>
              <Label htmlFor="playlist-url">Playlist URL</Label>
              <Input
                id="playlist-url"
                placeholder="https://www.youtube.com/playlist?list=..."
                value={config.youtubeInput.playlistUrl}
                onChange={(e) => updateConfig('youtubeInput', { playlistUrl: e.target.value })}
              />
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5 text-orange-600" />
            Apify API Connection
          </CardTitle>
          <CardDescription>
            Configure your Apify account to enable YouTube data extraction
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="apify-key">Apify API Key</Label>
            <Input
              id="apify-key"
              type="password"
              placeholder="apify_api_..."
              value={config.apifyConnection.apiKey}
              onChange={(e) => updateConfig('apifyConnection', { 
                apiKey: e.target.value,
                isConfigured: e.target.value.length > 0
              })}
            />
            <p className="text-xs text-muted-foreground mt-1">
              Get your API key from the Apify console
            </p>
          </div>

          {config.apifyConnection.isConfigured && (
            <div className="p-3 border rounded-md bg-green-50 dark:bg-green-900/20">
              <div className="flex items-center gap-2">
                <Check className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-green-700 dark:text-green-300">
                  Apify connection configured
                </span>
              </div>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Max Concurrency</Label>
              <Select
                value={config.apifyConnection.runSettings.maxConcurrency.toString()}
                onValueChange={(value) => updateConfig('apifyConnection', {
                  runSettings: { ...config.apifyConnection.runSettings, maxConcurrency: parseInt(value) }
                })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1 (Slower, cheaper)</SelectItem>
                  <SelectItem value="3">3 (Balanced)</SelectItem>
                  <SelectItem value="5">5 (Faster, more expensive)</SelectItem>
                  <SelectItem value="10">10 (Fastest, most expensive)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Memory (MB)</Label>
              <Select
                value={config.apifyConnection.runSettings.memory.toString()}
                onValueChange={(value) => updateConfig('apifyConnection', {
                  runSettings: { ...config.apifyConnection.runSettings, memory: parseInt(value) }
                })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="256">256 MB</SelectItem>
                  <SelectItem value="512">512 MB</SelectItem>
                  <SelectItem value="1024">1 GB</SelectItem>
                  <SelectItem value="2048">2 GB</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button variant="outline" onClick={prevStep} className="flex-1">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <Button 
          onClick={nextStep} 
          className="flex-1"
          disabled={!config.apifyConnection.isConfigured}
        >
          Continue to Transcription Settings <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderTranscriptionConfig = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Mic className="h-16 w-16 mx-auto mb-4 text-green-600" />
        <h3 className="text-lg font-semibold mb-2">Transcription & Summary Configuration</h3>
        <p className="text-muted-foreground text-sm">
          Configure how the audio will be transcribed and summarized.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Transcription Service</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label>AI Transcription Service</Label>
            <Select
              value={config.transcriptionConfig.service}
              onValueChange={(value: 'whisper' | 'assemblyai' | 'rev') => 
                updateConfig('transcriptionConfig', { service: value })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="whisper">OpenAI Whisper (Most accurate)</SelectItem>
                <SelectItem value="assemblyai">AssemblyAI (Fast & accurate)</SelectItem>
                <SelectItem value="rev">Rev.ai (Professional grade)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Language</Label>
              <Select
                value={config.transcriptionConfig.language}
                onValueChange={(value) => updateConfig('transcriptionConfig', { language: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="auto">Auto-detect</SelectItem>
                  <SelectItem value="en">English</SelectItem>
                  <SelectItem value="es">Spanish</SelectItem>
                  <SelectItem value="fr">French</SelectItem>
                  <SelectItem value="de">German</SelectItem>
                  <SelectItem value="it">Italian</SelectItem>
                  <SelectItem value="pt">Portuguese</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Output Format</Label>
              <Select
                value={config.transcriptionConfig.format}
                onValueChange={(value: 'text' | 'srt' | 'vtt') => 
                  updateConfig('transcriptionConfig', { format: value })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="text">Plain Text</SelectItem>
                  <SelectItem value="srt">SRT Subtitles</SelectItem>
                  <SelectItem value="vtt">WebVTT Subtitles</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="timestamps"
                checked={config.transcriptionConfig.includeTimestamps}
                onCheckedChange={(checked) => 
                  updateConfig('transcriptionConfig', { includeTimestamps: !!checked })
                }
              />
              <Label htmlFor="timestamps">Include timestamps in transcription</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="speaker-id"
                checked={config.transcriptionConfig.speakerIdentification}
                onCheckedChange={(checked) => 
                  updateConfig('transcriptionConfig', { speakerIdentification: !!checked })
                }
              />
              <Label htmlFor="speaker-id">Enable speaker identification (when available)</Label>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>AI Summary Configuration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Summary Length</Label>
              <Select
                value={config.transcriptionConfig.summaryLength}
                onValueChange={(value: 'short' | 'medium' | 'long') => 
                  updateConfig('transcriptionConfig', { summaryLength: value })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="short">Short (2-3 sentences)</SelectItem>
                  <SelectItem value="medium">Medium (1-2 paragraphs)</SelectItem>
                  <SelectItem value="long">Long (Detailed summary)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Summary Style</Label>
              <Select
                value={config.transcriptionConfig.summaryStyle}
                onValueChange={(value: 'bullet' | 'paragraph' | 'executive') => 
                  updateConfig('transcriptionConfig', { summaryStyle: value })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="bullet">Bullet Points</SelectItem>
                  <SelectItem value="paragraph">Paragraph Form</SelectItem>
                  <SelectItem value="executive">Executive Summary</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button variant="outline" onClick={prevStep} className="flex-1">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <Button onClick={nextStep} className="flex-1">
          Continue to Output Settings <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderContentOutput = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <FileText className="h-16 w-16 mx-auto mb-4 text-blue-600" />
        <h3 className="text-lg font-semibold mb-2">Content Output & Format Options</h3>
        <p className="text-muted-foreground text-sm">
          Choose what content to generate and how to deliver it.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Output Content</CardTitle>
          <CardDescription>Select which content types to generate</CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          {[
            { key: 'transcription', label: 'Full Transcription', description: 'Complete text of the video content' },
            { key: 'summary', label: 'AI Summary', description: 'Intelligent summary of key points' },
            { key: 'keyPoints', label: 'Key Points', description: 'Bullet list of main takeaways' },
            { key: 'quotes', label: 'Notable Quotes', description: 'Important quotes and statements' },
            { key: 'timestamps', label: 'Timestamp Index', description: 'Clickable timestamp navigation' }
          ].map(({ key, label, description }) => (
            <div key={key} className="flex items-center space-x-3">
              <Checkbox
                id={key}
                checked={config.contentOutput.outputFormats[key as keyof typeof config.contentOutput.outputFormats]}
                onCheckedChange={(checked) => 
                  updateConfig('contentOutput', {
                    outputFormats: {
                      ...config.contentOutput.outputFormats,
                      [key]: !!checked
                    }
                  })
                }
              />
              <div>
                <Label htmlFor={key} className="font-medium">{label}</Label>
                <p className="text-sm text-muted-foreground">{description}</p>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Delivery Method</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label>How would you like to receive the results?</Label>
            <RadioGroup
              value={config.contentOutput.deliveryMethod}
              onValueChange={(value: 'download' | 'email' | 'webhook' | 'googlesheets') => 
                updateConfig('contentOutput', { deliveryMethod: value })
              }
              className="mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="download" id="download" />
                <Label htmlFor="download">Download Files</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="email" id="email" />
                <Label htmlFor="email">Email Delivery</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="webhook" id="webhook" />
                <Label htmlFor="webhook">Webhook</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="googlesheets" id="googlesheets" />
                <Label htmlFor="googlesheets">Google Sheets</Label>
              </div>
            </RadioGroup>
          </div>

          {config.contentOutput.deliveryMethod === 'email' && (
            <div>
              <Label htmlFor="email-dest">Email Address</Label>
              <Input
                id="email-dest"
                type="email"
                placeholder="<EMAIL>"
                value={config.contentOutput.destination}
                onChange={(e) => updateConfig('contentOutput', { destination: e.target.value })}
              />
            </div>
          )}

          {config.contentOutput.deliveryMethod === 'webhook' && (
            <div>
              <Label htmlFor="webhook-url">Webhook URL</Label>
              <Input
                id="webhook-url"
                placeholder="https://your-webhook-endpoint.com"
                value={config.contentOutput.destination}
                onChange={(e) => updateConfig('contentOutput', { destination: e.target.value })}
              />
            </div>
          )}

          {config.contentOutput.deliveryMethod === 'googlesheets' && (
            <div>
              <Label htmlFor="sheets-id">Google Sheets ID</Label>
              <Input
                id="sheets-id"
                placeholder="1A2B3C4D5E6F..."
                value={config.contentOutput.destination}
                onChange={(e) => updateConfig('contentOutput', { destination: e.target.value })}
              />
            </div>
          )}

          <div>
            <Label>File Naming Convention</Label>
            <Select
              value={config.contentOutput.fileNaming}
              onValueChange={(value) => updateConfig('contentOutput', { fileNaming: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="video-title-date">Video Title + Date</SelectItem>
                <SelectItem value="channel-video-date">Channel + Video + Date</SelectItem>
                <SelectItem value="custom-prefix">Custom Prefix + Video Title</SelectItem>
                <SelectItem value="timestamp-only">Timestamp Only</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button variant="outline" onClick={prevStep} className="flex-1">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <Button onClick={nextStep} className="flex-1">
          Review Configuration <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderReview = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Check className="h-16 w-16 mx-auto mb-4 text-green-600" />
        <h3 className="text-lg font-semibold mb-2">Review Your Configuration</h3>
        <p className="text-muted-foreground text-sm">
          Verify your settings before creating the automation.
        </p>
      </div>

      <div className="grid gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">YouTube Input</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Input Type:</span>
                <span className="capitalize">{config.youtubeInput.inputType}</span>
              </div>
              {config.youtubeInput.inputType === 'url' && config.youtubeInput.videoUrl && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Video URL:</span>
                  <span className="truncate max-w-xs">{config.youtubeInput.videoUrl}</span>
                </div>
              )}
              {config.youtubeInput.inputType === 'channel' && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Max Videos:</span>
                  <span>{config.youtubeInput.maxVideos}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Transcription Settings</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Service:</span>
                <span className="capitalize">{config.transcriptionConfig.service}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Language:</span>
                <span>{config.transcriptionConfig.language}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Summary Style:</span>
                <span className="capitalize">{config.transcriptionConfig.summaryStyle}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Output Configuration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Delivery:</span>
                <span className="capitalize">{config.contentOutput.deliveryMethod}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Content Types:</span>
                <span>
                  {Object.entries(config.contentOutput.outputFormats)
                    .filter(([_, enabled]) => enabled)
                    .length} selected
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex gap-3">
        <Button variant="outline" onClick={prevStep} className="flex-1">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <Button onClick={nextStep} className="flex-1">
          Create Automation <Check className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderComplete = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="flex justify-center">
        <div className="p-6 bg-green-100 dark:bg-green-900/20 rounded-full">
          <Check className="h-16 w-16 text-green-600" />
        </div>
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-2">Automation Created!</h2>
        <p className="text-muted-foreground mb-6">
          Your YouTube transcription automation is now active and ready to process videos.
        </p>
      </div>

      <Card className="text-left">
        <CardHeader>
          <CardTitle>What happens next?</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-start gap-3">
            <div className="p-1 bg-blue-100 dark:bg-blue-900/20 rounded">
              <Video className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <p className="font-medium">Video Processing</p>
              <p className="text-sm text-muted-foreground">
                Apify will extract audio from your specified YouTube content
              </p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <div className="p-1 bg-green-100 dark:bg-green-900/20 rounded">
              <Mic className="h-4 w-4 text-green-600" />
            </div>
            <div>
              <p className="font-medium">AI Transcription</p>
              <p className="text-sm text-muted-foreground">
                Audio will be converted to text with timestamps and speaker identification
              </p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <div className="p-1 bg-purple-100 dark:bg-purple-900/20 rounded">
              <Brain className="h-4 w-4 text-purple-600" />
            </div>
            <div>
              <p className="font-medium">Smart Summary</p>
              <p className="text-sm text-muted-foreground">
                AI will generate summaries, key points, and actionable insights
              </p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <div className="p-1 bg-orange-100 dark:bg-orange-900/20 rounded">
              <Download className="h-4 w-4 text-orange-600" />
            </div>
            <div>
              <p className="font-medium">Content Delivery</p>
              <p className="text-sm text-muted-foreground">
                Results will be delivered via your chosen method
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex flex-col sm:flex-row gap-3 justify-center mt-8">
        <Button 
          onClick={() => window.location.href = '/dashboard/automations'}
          size="lg"
          className="bg-primary hover:bg-primary/90"
        >
          View All Automations
        </Button>
        <Button 
          onClick={() => window.location.href = '/dashboard/templates'}
          variant="outline"
          size="lg"
        >
          Browse More Templates
        </Button>
      </div>
    </motion.div>
  )

  const renderNaming = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <FileText className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h2 className="text-xl font-semibold mb-2">Name Your Automation</h2>
        <p className="text-muted-foreground">
          Give your YouTube transcription automation a memorable name
        </p>
      </div>

      <div className="space-y-4 max-w-md mx-auto">
        <div>
          <Label htmlFor="automation-name">Automation Name *</Label>
          <Input
            id="automation-name"
            placeholder="e.g., Podcast Episode Transcriber, Video Content Analyzer"
            value={config.name}
            onChange={(e) => setConfig(prev => ({ ...prev, name: e.target.value }))}
            className="mt-2"
            autoFocus
          />
          <p className="text-xs text-muted-foreground mt-1">
            Choose a descriptive name that reflects your content processing goals
          </p>
        </div>
      </div>

      <div className="flex gap-3 justify-center">
        <Button variant="outline" onClick={prevStep}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <Button onClick={nextStep} disabled={!config.name}>
          Continue to YouTube Setup
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderStepContent = () => {
    switch (currentStep) {
      case 'intro':
        return renderIntro()
      case 'naming':
        return renderNaming()
      case 'youtube-apify':
        return renderYouTubeApify()
      case 'transcription-config':
        return renderTranscriptionConfig()
      case 'content-output':
        return renderContentOutput()
      case 'review':
        return renderReview()
      case 'complete':
        return renderComplete()
      default:
        return renderIntro()
    }
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-4xl mx-auto p-6">
        {/* Progress Steps */}
        {currentStep !== 'complete' && (
          <div className="mb-8">
            <div className="flex items-center justify-between">
              {steps.slice(0, -1).map((step, index) => {
                const Icon = step.icon
                const isActive = step.id === currentStep
                const isCompleted = steps.findIndex(s => s.id === currentStep) > index
                
                return (
                  <div key={step.id} className="flex items-center">
                    <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                      isActive 
                        ? 'border-primary bg-primary text-primary-foreground' 
                        : isCompleted
                        ? 'border-green-500 bg-green-500 text-white'
                        : 'border-muted-foreground text-muted-foreground'
                    }`}>
                      {isCompleted ? (
                        <Check className="h-5 w-5" />
                      ) : (
                        <Icon className="h-5 w-5" />
                      )}
                    </div>
                    <div className="ml-3 hidden sm:block">
                      <p className={`text-sm font-medium ${
                        isActive ? 'text-foreground' : isCompleted ? 'text-green-600' : 'text-muted-foreground'
                      }`}>
                        {step.title}
                      </p>
                    </div>
                    {index < steps.length - 2 && (
                      <div className={`mx-4 h-px w-12 ${
                        isCompleted ? 'bg-green-500' : 'bg-muted-foreground'
                      }`} />
                    )}
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {/* Step Content */}
        {renderStepContent()}
      </div>
    </div>
  )
}