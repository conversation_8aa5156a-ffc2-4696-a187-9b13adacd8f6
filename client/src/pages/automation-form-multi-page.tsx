import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useLocation } from 'wouter'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  Clock, Calendar, Webhook, FileSpreadsheet, MessageSquare, Mail, 
  Zap, Settings, Check, ArrowRight, ChevronLeft, Plus, X, Search,
  Brain, Globe, Hash, Send, Download, Bell, GitBranch, Filter,
  Equal, MoreVertical, ChevronRight, Play, Activity, AlertCircle, CheckCircle, Copy, HardDrive
} from 'lucide-react'
import { SiGooglesheets, SiSlack, SiZapier, SiWebhook, SiGoogledrive } from 'react-icons/si'
import { useToast } from "@/hooks/use-toast"
import { WorkflowSuggestions } from '@/components/workflow-suggestions'
import { HelpTooltip } from '@/components/ui/help-tooltip'
import { AIHelpSuggestions } from '@/components/ai-help-suggestions'
import { apiRequest, queryClient } from "@/lib/queryClient"
import { useQuery } from "@tanstack/react-query"
import { ExistingGoogleAccounts } from '@/components/ExistingGoogleAccounts'

type PageType = 'intro' | 'naming' | 'trigger' | 'actions' | 'conditions-question' | 'conditions' | 'output' | 'testing' | 'review' | 'complete'



interface Condition {
  id: string
  source: string // 'trigger' or action ID
  field: string
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'is_empty' | 'is_not_empty'
  value: any
  combineWith?: 'and' | 'or'
}

interface ConditionalBranch {
  id: string
  name: string
  conditions: Condition[]
  actions: Array<{
    id: string
    type: string
    name: string
    description: string
    config: Record<string, any>
  }>
}

interface AutomationConfig {
  name: string
  trigger: {
    type: string
    config: Record<string, any>
    description?: string
  }
  actions: Array<{
    id: string
    type: string
    name: string
    description: string
    config: Record<string, any>
  }>
  conditions: Array<ConditionalBranch>
  output: {
    deliveryMethod: string
    tone: string
    format: string
    destinations: Array<{
      type: string
      config: Record<string, any>
    }>
  }
}

export default function AutomationFormMultiPage() {
  const [location, setLocation] = useLocation()
  const { toast } = useToast()
  
  const [currentPage, setCurrentPage] = useState<PageType>('intro')
  const [automation, setAutomation] = useState<AutomationConfig>({
    name: '',
    trigger: { type: '', config: {} },
    actions: [],
    conditions: [],
    output: {
      deliveryMethod: 'email',
      tone: 'professional',
      format: 'text',
      destinations: []
    }
  })
  
  const [showTriggerConfig, setShowTriggerConfig] = useState(false)
  const [showActionConfig, setShowActionConfig] = useState<number | null>(null)
  const [actionSearchQuery, setActionSearchQuery] = useState('')
  const [wantsMoreActions, setWantsMoreActions] = useState(false)
  const [wantsConditions, setWantsConditions] = useState<boolean | null>(null)
  
  // Conditions state
  const [editingBranch, setEditingBranch] = useState<string | null>(null)
  const [selectedSource, setSelectedSource] = useState<string>('')
  const [selectedField, setSelectedField] = useState<string>('')
  const [selectedOperator, setSelectedOperator] = useState<Condition['operator']>('equals')
  const [conditionValue, setConditionValue] = useState<string>('')
  
  // Testing state
  const [isRunningTest, setIsRunningTest] = useState(false)
  const [testResults, setTestResults] = useState<{
    execution: { step: string; status: 'pending' | 'running' | 'success' | 'error'; time?: number }[]
    validation: { passed: boolean; message: string }[]
    performance: { metric: string; value: string; status: 'good' | 'warning' | 'error' }[]
  } | null>(null)
  
  // Saving state
  const [isSaving, setIsSaving] = useState(false)
  
  // Track if user has made changes to show warning on page leave
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [userHasInteracted, setUserHasInteracted] = useState(false)
  
  // Google integration specific state - separate for each trigger type
  const [selectedGoogleAccount, setSelectedGoogleAccount] = useState<any>(null)
  const [selectedSpreadsheet, setSelectedSpreadsheet] = useState<any>(null)
  const [selectedSheet, setSelectedSheet] = useState<string>('')
  
  // Get credentials for Google Drive account derivation
  const { data: credentials } = useQuery({
    queryKey: ['/api/credentials'],
    staleTime: 5 * 60 * 1000,
  });
  
  // Derive Google Drive account from automation config
  const selectedDriveAccount = React.useMemo(() => {
    if (automation.trigger.type === 'google-drive' && automation.trigger.config.googleAccountId && credentials) {
      const found = credentials.find((cred: any) => cred.id.toString() === automation.trigger.config.googleAccountId.toString());
      console.log('selectedDriveAccount memo:', { googleAccountId: automation.trigger.config.googleAccountId, found, credentialsCount: credentials?.length });
      return found || null;
    }
    return null;
  }, [automation.trigger.type, automation.trigger.config.googleAccountId, credentials]);

  // Derive Google Calendar account from automation config
  const selectedCalendarAccount = React.useMemo(() => {
    if (automation.trigger.type === 'google-calendar' && automation.trigger.config.googleAccountId && credentials) {
      const found = credentials.find((cred: any) => cred.id.toString() === automation.trigger.config.googleAccountId.toString());
      console.log('selectedCalendarAccount memo:', { googleAccountId: automation.trigger.config.googleAccountId, found, credentialsCount: credentials?.length });
      return found || null;
    }
    return null;
  }, [automation.trigger.type, automation.trigger.config.googleAccountId, credentials]);

  // Track Google account states for debugging
  useEffect(() => {
    console.log('Google Sheets - selectedGoogleAccount state changed:', {
      account: selectedGoogleAccount?.id || null,
      name: selectedGoogleAccount?.name || null,
      trigger: automation.trigger.type
    });
  }, [selectedGoogleAccount, automation.trigger.type]);

  useEffect(() => {
    console.log('Google Drive - selectedDriveAccount derived value:', {
      account: selectedDriveAccount?.id || null,
      name: selectedDriveAccount?.name || null,
      trigger: automation.trigger.type
    });
  }, [selectedDriveAccount, automation.trigger.type]);

  // Page leave warning system
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges && currentPage !== 'complete') {
        e.preventDefault()
        e.returnValue = 'You have unsaved changes. Are you sure you want to leave?'
        return 'You have unsaved changes. Are you sure you want to leave?'
      }
    }

    const handlePopState = (e: PopStateEvent) => {
      if (hasUnsavedChanges && currentPage !== 'complete') {
        const confirmLeave = window.confirm('You have unsaved changes. Are you sure you want to leave this page?')
        if (!confirmLeave) {
          e.preventDefault()
          window.history.pushState(null, '', window.location.pathname)
        }
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    window.addEventListener('popstate', handlePopState)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      window.removeEventListener('popstate', handlePopState)
    }
  }, [hasUnsavedChanges, currentPage])

  // Only track changes after user has actually interacted with the form
  useEffect(() => {
    if (userHasInteracted && currentPage !== 'intro' && currentPage !== 'complete') {
      setHasUnsavedChanges(true)
    }
  }, [automation, currentPage, userHasInteracted])

  // Restore Google account state from automation config when component loads
  useEffect(() => {
    if (automation.trigger.config.googleAccountId) {
      // Find the credential by ID and restore it for the appropriate trigger type
      const findCredential = async () => {
        try {
          const response = await apiRequest('GET', '/api/credentials');
          const credentials = await response.json();
          const matchingCredential = credentials.find((cred: any) => 
            cred.id.toString() === automation.trigger.config.googleAccountId
          );
          if (matchingCredential) {
            console.log('Restoring Google account from automation config:', matchingCredential, 'for trigger:', automation.trigger.type);
            if (automation.trigger.type === 'google-sheets' && !selectedGoogleAccount) {
              setSelectedGoogleAccount(matchingCredential);
            }
            // Google Drive account is now derived from automation config, no need to restore
          }
        } catch (error) {
          console.error('Error restoring Google account:', error);
        }
      };
      findCredential();
    }
  }, [automation.trigger.config.googleAccountId, automation.trigger.type, selectedGoogleAccount]);

  const pages: PageType[] = ['intro', 'naming', 'trigger', 'actions', 'conditions-question', 'conditions', 'output', 'testing', 'review', 'complete']
  const currentPageIndex = pages.indexOf(currentPage)
  const progress = ((currentPageIndex + 1) / pages.length) * 100

  const nextPage = () => {
    // Mark that user has interacted when they navigate beyond intro
    if (currentPage === 'intro') {
      setUserHasInteracted(true)
    }
    
    const nextIndex = currentPageIndex + 1
    if (nextIndex < pages.length) {
      // Skip conditions step if user doesn't want conditions
      if (currentPage === 'conditions-question' && wantsConditions === false) {
        setCurrentPage('output')
      } else {
        setCurrentPage(pages[nextIndex])
      }
      setWantsMoreActions(false) // Reset when moving to next page
    }
  }

  const prevPage = () => {
    const prevIndex = currentPageIndex - 1
    if (prevIndex >= 0) {
      setCurrentPage(pages[prevIndex])
    }
  }

  const renderIntro = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="flex justify-center">
        <div className="h-20 w-20 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center">
          <Zap className="h-10 w-10 text-white" />
        </div>
      </div>

      <div>
        <h1 className="text-3xl font-bold mb-3">Create Your Automation</h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Build powerful workflows that connect your favorite tools and automate repetitive tasks
        </p>
      </div>

      <Button onClick={nextPage} size="lg" className="mt-8">
        Get Started <ArrowRight className="ml-2 h-4 w-4" />
      </Button>

      <div className="mt-12 max-w-4xl mx-auto">
        <WorkflowSuggestions compact={true} />
      </div>
    </motion.div>
  )

  const renderNaming = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Zap className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h3 className="text-lg font-semibold mb-2">Name Your Automation</h3>
        <p className="text-muted-foreground text-sm">
          Choose a descriptive name to easily identify this workflow
        </p>
      </div>

      <div className="max-w-md mx-auto">
        <div className="flex items-center gap-2 mb-2">
          <Label htmlFor="automation-name">Automation Name *</Label>
          <HelpTooltip
            content="Give your automation a clear, descriptive name that explains what it does. This helps you identify it later."
            example="Daily Sales Report, New Customer Welcome Email, Invoice Processing"
            icon="info"
          />
        </div>
        <Input
          id="automation-name"
          placeholder="e.g., Daily Sales Report, Customer Onboarding"
          value={automation.name}
          onChange={(e) => {
            setUserHasInteracted(true)
            setAutomation({ ...automation, name: e.target.value })
          }}
        />
        <AIHelpSuggestions
          context="automation-name"
          currentValue={automation.name}
          className="mt-2"
          compact={true}
        />
      </div>

      <div className="flex gap-3 max-w-md mx-auto">
        <Button onClick={prevPage} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextPage} 
          className="w-full"
          disabled={!automation.name.trim()}
        >
          Continue <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderTrigger = () => {
    const triggers = [
      { id: 'schedule', name: 'Schedule', icon: Clock, desc: 'Run at specific times or intervals' },
      { id: 'webhook', name: 'Webhook', icon: Globe, desc: 'Trigger via HTTP webhook calls' },
      { id: 'chat', name: 'Chat Command', icon: MessageSquare, desc: 'Manual trigger via chat interface' },
      { id: 'gmail', name: 'Gmail Received', icon: Mail, desc: 'When new Gmail emails arrive' },
      { id: 'google-drive', name: 'Google Drive', icon: SiGoogledrive, desc: 'When files are added or modified' },
      { id: 'google-sheets', name: 'Google Sheets', icon: SiGooglesheets, desc: 'When row is added or updated' },
      { id: 'google-calendar', name: 'Google Calendar', icon: Calendar, desc: 'When calendar event occurs' },
      { id: 'slack', name: 'Slack Message', icon: SiSlack, desc: 'When message is posted' }
    ]

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <div className="text-center">
          <Zap className="h-16 w-16 mx-auto mb-4 text-purple-600" />
          <div className="flex items-center justify-center gap-2 mb-2">
            <h3 className="text-lg font-semibold">When should this automation run?</h3>
            <HelpTooltip
              content="Triggers determine when your automation starts. Choose based on how you want to initiate the workflow."
              icon="help"
              variant="ai"
            />
          </div>
          <p className="text-muted-foreground text-sm">
            Choose what will trigger your automation
          </p>
        </div>

        <div className="grid gap-3 max-w-2xl mx-auto">
          {triggers.map((trigger) => (
            <Card
              key={trigger.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                automation.trigger.type === trigger.id ? 'ring-2 ring-primary' : ''
              }`}
              onClick={() => {
                // Only update trigger type if it's different, preserve existing config
                if (automation.trigger.type !== trigger.id) {
                  setAutomation({
                    ...automation,
                    trigger: { type: trigger.id, config: {}, description: trigger.desc }
                  })
                  // Reset account selection when changing trigger types
                  setSelectedGoogleAccount(null)
                  setSelectedSpreadsheet(null) 
                  setSelectedSheet('')
                } else {
                  // Same trigger type, just open config - preserve all state
                }
                setUserHasInteracted(true)
                setShowTriggerConfig(true)
              }}
            >
              <CardHeader className="p-4">
                <div className="flex items-center gap-3">
                  {typeof trigger.icon === 'function' ? (
                    <trigger.icon className="h-5 w-5 text-primary" />
                  ) : (
                    <trigger.icon className="h-5 w-5" />
                  )}
                  <div className="flex-1">
                    <CardTitle className="text-sm">{trigger.name}</CardTitle>
                    <CardDescription className="text-xs mt-1">{trigger.desc}</CardDescription>
                  </div>
                  {automation.trigger.type === trigger.id && (
                    <Check className="h-5 w-5 text-primary" />
                  )}
                </div>
              </CardHeader>
            </Card>
          ))}
        </div>

        <div className="flex gap-3 max-w-2xl mx-auto">
          <Button onClick={prevPage} variant="outline" className="w-full min-h-[44px]">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button 
            onClick={nextPage} 
            className="w-full min-h-[44px]"
            disabled={!automation.trigger.type}
          >
            Continue <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>

        {/* Trigger Configuration Modal */}
        {showTriggerConfig && automation.trigger.type && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowTriggerConfig(false)}
          >
            <motion.div
              initial={{ scale: 0.95 }}
              animate={{ scale: 1 }}
              className="bg-card p-6 rounded-lg shadow-lg max-w-md w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">
                Configure {triggers.find(t => t.id === automation.trigger.type)?.name}
              </h3>
              
              {automation.trigger.type === 'schedule' && (
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <Label>Run scenario</Label>
                      <HelpTooltip
                        content="Choose when and how often this automation should run."
                        icon="info"
                      />
                    </div>
                    <Select
                      value={automation.trigger.config.runType || 'at-regular-intervals'}
                      onValueChange={(value) => setAutomation({
                        ...automation,
                        trigger: {
                          ...automation.trigger,
                          config: { ...automation.trigger.config, runType: value }
                        }
                      })}
                    >
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="at-regular-intervals">At regular intervals</SelectItem>
                        <SelectItem value="once">Once</SelectItem>
                        <SelectItem value="every-day">Every day</SelectItem>
                        <SelectItem value="days-of-week">Days of the week</SelectItem>
                        <SelectItem value="days-of-month">Days of the month</SelectItem>
                        <SelectItem value="specified-dates">Specified dates</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {automation.trigger.config.runType === 'at-regular-intervals' && (
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <Label>Interval</Label>
                        <HelpTooltip
                          content="How often should the automation run?"
                          icon="info"
                        />
                      </div>
                      <Select
                        value={automation.trigger.config.interval || 'hourly'}
                        onValueChange={(value) => setAutomation({
                          ...automation,
                          trigger: {
                            ...automation.trigger,
                            config: { ...automation.trigger.config, interval: value }
                          }
                        })}
                      >
                        <SelectTrigger className="mt-2">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="5min">Every 5 minutes</SelectItem>
                          <SelectItem value="15min">Every 15 minutes</SelectItem>
                          <SelectItem value="30min">Every 30 minutes</SelectItem>
                          <SelectItem value="hourly">Every hour</SelectItem>
                          <SelectItem value="daily">Every day</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                  
                  {automation.trigger.config.runType === 'once' && (
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <Label>Date & Time</Label>
                        <HelpTooltip
                          content="Select when this automation should run once."
                          icon="info"
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-2 mt-2">
                        <Input
                          type="date"
                          value={automation.trigger.config.runDate || ''}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, runDate: e.target.value }
                            }
                          })}
                        />
                        <Input
                          type="time"
                          value={automation.trigger.config.runTime || '09:00'}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, runTime: e.target.value }
                            }
                          })}
                        />
                      </div>
                    </div>
                  )}
                  
                  {automation.trigger.config.runType === 'every-day' && (
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <Label>Time (24-hour format)</Label>
                        <HelpTooltip
                          content="What time should this run every day?"
                          example="09:00 for 9 AM, 14:30 for 2:30 PM"
                          icon="info"
                        />
                      </div>
                      <Input
                        type="time"
                        value={automation.trigger.config.dailyTime || '09:00'}
                        onChange={(e) => setAutomation({
                          ...automation,
                          trigger: {
                            ...automation.trigger,
                            config: { ...automation.trigger.config, dailyTime: e.target.value }
                          }
                        })}
                        className="mt-2"
                      />
                    </div>
                  )}
                  
                  {automation.trigger.config.runType === 'days-of-week' && (
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <Label>Select Days & Time</Label>
                        <HelpTooltip
                          content="Choose which days of the week and what time."
                          icon="info"
                        />
                      </div>
                      <div className="space-y-3 mt-2">
                        <div className="grid grid-cols-7 gap-2">
                          {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => {
                            const dayValue = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'][index];
                            const selectedDays = automation.trigger.config.weeklyDays || [];
                            return (
                              <button
                                key={day}
                                type="button"
                                className={`p-2 text-xs rounded border ${
                                  selectedDays.includes(dayValue)
                                    ? 'bg-primary text-primary-foreground'
                                    : 'bg-muted hover:bg-muted/80'
                                }`}
                                onClick={() => {
                                  const currentDays = automation.trigger.config.weeklyDays || [];
                                  const newDays = currentDays.includes(dayValue)
                                    ? currentDays.filter(d => d !== dayValue)
                                    : [...currentDays, dayValue];
                                  setAutomation({
                                    ...automation,
                                    trigger: {
                                      ...automation.trigger,
                                      config: { ...automation.trigger.config, weeklyDays: newDays }
                                    }
                                  });
                                }}
                              >
                                {day}
                              </button>
                            );
                          })}
                        </div>
                        <Input
                          type="time"
                          value={automation.trigger.config.weeklyTime || '09:00'}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, weeklyTime: e.target.value }
                            }
                          })}
                        />
                      </div>
                    </div>
                  )}
                  
                  {automation.trigger.config.runType === 'days-of-month' && (
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <Label>Select Days & Time</Label>
                        <HelpTooltip
                          content="Choose which days of the month (1-31) and what time."
                          icon="info"
                        />
                      </div>
                      <div className="space-y-3 mt-2">
                        <div className="grid grid-cols-7 gap-2 max-h-32 overflow-y-auto">
                          {Array.from({ length: 31 }, (_, i) => {
                            const day = i + 1;
                            const selectedDays = automation.trigger.config.monthlyDays || [];
                            return (
                              <button
                                key={day}
                                type="button"
                                className={`p-2 text-xs rounded border ${
                                  selectedDays.includes(day)
                                    ? 'bg-primary text-primary-foreground'
                                    : 'bg-muted hover:bg-muted/80'
                                }`}
                                onClick={() => {
                                  const currentDays = automation.trigger.config.monthlyDays || [];
                                  const newDays = currentDays.includes(day)
                                    ? currentDays.filter(d => d !== day)
                                    : [...currentDays, day];
                                  setAutomation({
                                    ...automation,
                                    trigger: {
                                      ...automation.trigger,
                                      config: { ...automation.trigger.config, monthlyDays: newDays }
                                    }
                                  });
                                }}
                              >
                                {day}
                              </button>
                            );
                          })}
                        </div>
                        <Input
                          type="time"
                          value={automation.trigger.config.monthlyTime || '09:00'}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, monthlyTime: e.target.value }
                            }
                          })}
                        />
                      </div>
                    </div>
                  )}
                  
                  {automation.trigger.config.runType === 'specified-dates' && (
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <Label>Specific Dates & Time</Label>
                        <HelpTooltip
                          content="Add specific dates when this automation should run."
                          icon="info"
                        />
                      </div>
                      <div className="space-y-3 mt-2">
                        <div className="grid grid-cols-2 gap-2">
                          <Input
                            type="date"
                            value={automation.trigger.config.newDate || ''}
                            onChange={(e) => setAutomation({
                              ...automation,
                              trigger: {
                                ...automation.trigger,
                                config: { ...automation.trigger.config, newDate: e.target.value }
                              }
                            })}
                          />
                          <Input
                            type="time"
                            value={automation.trigger.config.specificTime || '09:00'}
                            onChange={(e) => setAutomation({
                              ...automation,
                              trigger: {
                                ...automation.trigger,
                                config: { ...automation.trigger.config, specificTime: e.target.value }
                              }
                            })}
                          />
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            if (automation.trigger.config.newDate) {
                              const currentDates = automation.trigger.config.specificDates || [];
                              const newDates = [...currentDates, {
                                date: automation.trigger.config.newDate,
                                time: automation.trigger.config.specificTime || '09:00'
                              }];
                              setAutomation({
                                ...automation,
                                trigger: {
                                  ...automation.trigger,
                                  config: { 
                                    ...automation.trigger.config, 
                                    specificDates: newDates,
                                    newDate: '',
                                    specificTime: '09:00'
                                  }
                                }
                              });
                            }
                          }}
                          disabled={!automation.trigger.config.newDate}
                        >
                          Add Date
                        </Button>
                        {automation.trigger.config.specificDates && automation.trigger.config.specificDates.length > 0 && (
                          <div className="space-y-2">
                            <Label className="text-sm">Scheduled Dates:</Label>
                            {automation.trigger.config.specificDates.map((dateTime, index) => (
                              <div key={index} className="flex items-center justify-between p-2 bg-muted rounded text-sm">
                                <span>{dateTime.date} at {dateTime.time}</span>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    const newDates = automation.trigger.config.specificDates.filter((_, i) => i !== index);
                                    setAutomation({
                                      ...automation,
                                      trigger: {
                                        ...automation.trigger,
                                        config: { ...automation.trigger.config, specificDates: newDates }
                                      }
                                    });
                                  }}
                                >
                                  Remove
                                </Button>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                  
                  {/* Time-based Conditions */}
                  <div className="mt-6 pt-4 border-t">
                    <div className="flex items-center gap-2 mb-3">
                      <Label className="text-base font-semibold">Time-based Conditions (Optional)</Label>
                      <HelpTooltip
                        content="Add additional conditions to control when your automation runs based on business hours, holidays, or custom time restrictions."
                        icon="info"
                      />
                    </div>
                    
                    {/* Business Hours Only */}
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <input
                          type="checkbox"
                          id="business-hours-only"
                          checked={automation.trigger.config.businessHoursOnly || false}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, businessHoursOnly: e.target.checked }
                            }
                          })}
                          className="rounded"
                        />
                        <Label htmlFor="business-hours-only" className="text-sm font-normal cursor-pointer">
                          Only run during business hours
                        </Label>
                      </div>
                      
                      {automation.trigger.config.businessHoursOnly && (
                        <div className="ml-6 space-y-3 p-3 bg-muted/50 rounded">
                          <div className="grid grid-cols-2 gap-3">
                            <div>
                              <Label className="text-xs">Start Time</Label>
                              <Input
                                type="time"
                                value={automation.trigger.config.businessStartTime || '09:00'}
                                onChange={(e) => setAutomation({
                                  ...automation,
                                  trigger: {
                                    ...automation.trigger,
                                    config: { ...automation.trigger.config, businessStartTime: e.target.value }
                                  }
                                })}
                                className="mt-1"
                              />
                            </div>
                            <div>
                              <Label className="text-xs">End Time</Label>
                              <Input
                                type="time"
                                value={automation.trigger.config.businessEndTime || '17:00'}
                                onChange={(e) => setAutomation({
                                  ...automation,
                                  trigger: {
                                    ...automation.trigger,
                                    config: { ...automation.trigger.config, businessEndTime: e.target.value }
                                  }
                                })}
                                className="mt-1"
                              />
                            </div>
                          </div>
                          
                          <div>
                            <Label className="text-xs mb-2 block">Timezone</Label>
                            <Select
                              value={automation.trigger.config.businessTimezone || 'America/New_York'}
                              onValueChange={(value) => setAutomation({
                                ...automation,
                                trigger: {
                                  ...automation.trigger,
                                  config: { ...automation.trigger.config, businessTimezone: value }
                                }
                              })}
                            >
                              <SelectTrigger className="text-xs">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="America/New_York">Eastern Time (ET)</SelectItem>
                                <SelectItem value="America/Chicago">Central Time (CT)</SelectItem>
                                <SelectItem value="America/Denver">Mountain Time (MT)</SelectItem>
                                <SelectItem value="America/Los_Angeles">Pacific Time (PT)</SelectItem>
                                <SelectItem value="Europe/London">London (GMT)</SelectItem>
                                <SelectItem value="Europe/Paris">Paris (CET)</SelectItem>
                                <SelectItem value="Asia/Tokyo">Tokyo (JST)</SelectItem>
                                <SelectItem value="Australia/Sydney">Sydney (AEST)</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      )}
                      
                      {/* Weekdays Only */}
                      <div className="flex items-center gap-3">
                        <input
                          type="checkbox"
                          id="weekdays-only"
                          checked={automation.trigger.config.weekdaysOnly || false}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, weekdaysOnly: e.target.checked }
                            }
                          })}
                          className="rounded"
                        />
                        <Label htmlFor="weekdays-only" className="text-sm font-normal cursor-pointer">
                          Only run on weekdays (Monday-Friday)
                        </Label>
                      </div>
                      
                      {/* Exclude Holidays */}
                      <div className="flex items-center gap-3">
                        <input
                          type="checkbox"
                          id="exclude-holidays"
                          checked={automation.trigger.config.excludeHolidays || false}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, excludeHolidays: e.target.checked }
                            }
                          })}
                          className="rounded"
                        />
                        <Label htmlFor="exclude-holidays" className="text-sm font-normal cursor-pointer">
                          Skip execution on holidays
                        </Label>
                      </div>
                      
                      {automation.trigger.config.excludeHolidays && (
                        <div className="ml-6 p-3 bg-muted/50 rounded">
                          <Label className="text-xs mb-2 block">Holiday Calendar</Label>
                          <Select
                            value={automation.trigger.config.holidayCalendar || 'US'}
                            onValueChange={(value) => setAutomation({
                              ...automation,
                              trigger: {
                                ...automation.trigger,
                                config: { ...automation.trigger.config, holidayCalendar: value }
                              }
                            })}
                          >
                            <SelectTrigger className="text-xs">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="US">🇺🇸 United States</SelectItem>
                              <SelectItem value="UK">🇬🇧 United Kingdom</SelectItem>
                              <SelectItem value="CA">🇨🇦 Canada</SelectItem>
                              <SelectItem value="AU">🇦🇺 Australia</SelectItem>
                              <SelectItem value="JP">🇯🇵 Japan</SelectItem>
                              <SelectItem value="DE">🇩🇪 Germany</SelectItem>
                              <SelectItem value="FR">🇫🇷 France</SelectItem>
                              <SelectItem value="IT">🇮🇹 Italy</SelectItem>
                              <SelectItem value="ES">🇪🇸 Spain</SelectItem>
                              <SelectItem value="NL">🇳🇱 Netherlands</SelectItem>
                              <SelectItem value="SE">🇸🇪 Sweden</SelectItem>
                              <SelectItem value="NO">🇳🇴 Norway</SelectItem>
                              <SelectItem value="DK">🇩🇰 Denmark</SelectItem>
                              <SelectItem value="FI">🇫🇮 Finland</SelectItem>
                              <SelectItem value="CH">🇨🇭 Switzerland</SelectItem>
                              <SelectItem value="AT">🇦🇹 Austria</SelectItem>
                              <SelectItem value="BE">🇧🇪 Belgium</SelectItem>
                              <SelectItem value="BR">🇧🇷 Brazil</SelectItem>
                              <SelectItem value="MX">🇲🇽 Mexico</SelectItem>
                              <SelectItem value="AR">🇦🇷 Argentina</SelectItem>
                              <SelectItem value="IN">🇮🇳 India</SelectItem>
                              <SelectItem value="CN">🇨🇳 China</SelectItem>
                              <SelectItem value="KR">🇰🇷 South Korea</SelectItem>
                              <SelectItem value="SG">🇸🇬 Singapore</SelectItem>
                              <SelectItem value="MY">🇲🇾 Malaysia</SelectItem>
                              <SelectItem value="TH">🇹🇭 Thailand</SelectItem>
                              <SelectItem value="ID">🇮🇩 Indonesia</SelectItem>
                              <SelectItem value="PH">🇵🇭 Philippines</SelectItem>
                              <SelectItem value="VN">🇻🇳 Vietnam</SelectItem>
                              <SelectItem value="NZ">🇳🇿 New Zealand</SelectItem>
                              <SelectItem value="ZA">🇿🇦 South Africa</SelectItem>
                              <SelectItem value="NG">🇳🇬 Nigeria</SelectItem>
                              <SelectItem value="EG">🇪🇬 Egypt</SelectItem>
                              <SelectItem value="IL">🇮🇱 Israel</SelectItem>
                              <SelectItem value="AE">🇦🇪 UAE</SelectItem>
                              <SelectItem value="SA">🇸🇦 Saudi Arabia</SelectItem>
                              <SelectItem value="TR">🇹🇷 Turkey</SelectItem>
                              <SelectItem value="RU">🇷🇺 Russia</SelectItem>
                              <SelectItem value="PL">🇵🇱 Poland</SelectItem>
                              <SelectItem value="CZ">🇨🇿 Czech Republic</SelectItem>
                              <SelectItem value="HU">🇭🇺 Hungary</SelectItem>
                              <SelectItem value="RO">🇷🇴 Romania</SelectItem>
                              <SelectItem value="GR">🇬🇷 Greece</SelectItem>
                              <SelectItem value="PT">🇵🇹 Portugal</SelectItem>
                              <SelectItem value="IE">🇮🇪 Ireland</SelectItem>
                              <SelectItem value="LU">🇱🇺 Luxembourg</SelectItem>
                              <SelectItem value="SK">🇸🇰 Slovakia</SelectItem>
                              <SelectItem value="SI">🇸🇮 Slovenia</SelectItem>
                              <SelectItem value="LV">🇱🇻 Latvia</SelectItem>
                              <SelectItem value="LT">🇱🇹 Lithuania</SelectItem>
                              <SelectItem value="EE">🇪🇪 Estonia</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      )}
                      
                      {/* Custom Date Exclusions */}
                      <div className="space-y-2">
                        <div className="flex items-center gap-3">
                          <input
                            type="checkbox"
                            id="exclude-custom-dates"
                            checked={automation.trigger.config.excludeCustomDates || false}
                            onChange={(e) => setAutomation({
                              ...automation,
                              trigger: {
                                ...automation.trigger,
                                config: { ...automation.trigger.config, excludeCustomDates: e.target.checked }
                              }
                            })}
                            className="rounded"
                          />
                          <Label htmlFor="exclude-custom-dates" className="text-sm font-normal cursor-pointer">
                            Exclude specific dates
                          </Label>
                        </div>
                        
                        {automation.trigger.config.excludeCustomDates && (
                          <div className="ml-6 space-y-3 p-3 bg-muted/50 rounded">
                            <div className="flex gap-2">
                              <Input
                                type="date"
                                value={automation.trigger.config.newExcludedDate || ''}
                                onChange={(e) => setAutomation({
                                  ...automation,
                                  trigger: {
                                    ...automation.trigger,
                                    config: { ...automation.trigger.config, newExcludedDate: e.target.value }
                                  }
                                })}
                                className="text-xs"
                              />
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  if (automation.trigger.config.newExcludedDate) {
                                    const currentExcluded = automation.trigger.config.excludedDates || [];
                                    const newExcluded = [...currentExcluded, automation.trigger.config.newExcludedDate];
                                    setAutomation({
                                      ...automation,
                                      trigger: {
                                        ...automation.trigger,
                                        config: { 
                                          ...automation.trigger.config, 
                                          excludedDates: newExcluded,
                                          newExcludedDate: ''
                                        }
                                      }
                                    });
                                  }
                                }}
                                disabled={!automation.trigger.config.newExcludedDate}
                                className="text-xs"
                              >
                                Add
                              </Button>
                            </div>
                            
                            {automation.trigger.config.excludedDates && automation.trigger.config.excludedDates.length > 0 && (
                              <div className="space-y-1">
                                <Label className="text-xs">Excluded Dates:</Label>
                                {automation.trigger.config.excludedDates.map((date, index) => (
                                  <div key={index} className="flex items-center justify-between p-2 bg-background rounded text-xs">
                                    <span>{date}</span>
                                    <Button
                                      type="button"
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => {
                                        const newExcluded = automation.trigger.config.excludedDates.filter((_, i) => i !== index);
                                        setAutomation({
                                          ...automation,
                                          trigger: {
                                            ...automation.trigger,
                                            config: { ...automation.trigger.config, excludedDates: newExcluded }
                                          }
                                        });
                                      }}
                                      className="text-xs h-6 px-2"
                                    >
                                      Remove
                                    </Button>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <AIHelpSuggestions
                    context="trigger-schedule"
                    className="mt-3"
                  />
                </div>
              )}

              {automation.trigger.type === 'webhook' && (
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <Label>Webhook Name</Label>
                      <HelpTooltip
                        content="Give this webhook a descriptive name for easy identification."
                        icon="info"
                      />
                    </div>
                    <Input
                      placeholder="e.g., Contact Form Submission"
                      value={automation.trigger.config.webhookName || ''}
                      onChange={(e) => setAutomation({
                        ...automation,
                        trigger: {
                          ...automation.trigger,
                          config: { ...automation.trigger.config, webhookName: e.target.value }
                        }
                      })}
                    />
                  </div>

                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <Label>Webhook URL Preview</Label>
                      <HelpTooltip
                        content="This unique URL will trigger your automation when called."
                        icon="info"
                      />
                    </div>
                    <div className="bg-muted/50 p-3 rounded border">
                      <div className="flex items-center gap-2">
                        <code className="text-sm flex-1 break-all">
                          {`${window.location.origin}/api/webhook/${automation.name?.toLowerCase().replace(/\s+/g, '-') || 'automation'}-${Date.now()}`}
                        </code>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const webhookUrl = `${window.location.origin}/api/webhook/${automation.name?.toLowerCase().replace(/\s+/g, '-') || 'automation'}-${Date.now()}`;
                            navigator.clipboard.writeText(webhookUrl);
                          }}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <Label>HTTP Methods</Label>
                      <HelpTooltip
                        content="Which HTTP methods should trigger this automation?"
                        icon="info"
                      />
                    </div>
                    <div className="flex gap-2 flex-wrap">
                      {['GET', 'POST', 'PUT', 'PATCH'].map((method) => (
                        <Button
                          key={method}
                          variant={automation.trigger.config.allowedMethods?.includes(method) ? "default" : "outline"}
                          size="sm"
                          onClick={() => {
                            const currentMethods = automation.trigger.config.allowedMethods || ['POST'];
                            const newMethods = currentMethods.includes(method)
                              ? currentMethods.filter(m => m !== method)
                              : [...currentMethods, method];
                            
                            setAutomation({
                              ...automation,
                              trigger: {
                                ...automation.trigger,
                                config: { ...automation.trigger.config, allowedMethods: newMethods.length > 0 ? newMethods : ['POST'] }
                              }
                            });
                          }}
                        >
                          {method}
                        </Button>
                      ))}
                    </div>
                  </div>

                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <Label>Authentication</Label>
                      <HelpTooltip
                        content="Add security to your webhook endpoint."
                        icon="info"
                      />
                    </div>
                    <Select
                      value={automation.trigger.config.authType || 'none'}
                      onValueChange={(value) => setAutomation({
                        ...automation,
                        trigger: {
                          ...automation.trigger,
                          config: { ...automation.trigger.config, authType: value }
                        }
                      })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">No Authentication</SelectItem>
                        <SelectItem value="api-key">API Key</SelectItem>
                        <SelectItem value="basic">Basic Auth</SelectItem>
                        <SelectItem value="bearer">Bearer Token</SelectItem>
                        <SelectItem value="hmac">HMAC Signature</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {automation.trigger.config.authType && automation.trigger.config.authType !== 'none' && (
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <Label>
                          {automation.trigger.config.authType === 'api-key' && 'API Key'}
                          {automation.trigger.config.authType === 'basic' && 'Username:Password'}
                          {automation.trigger.config.authType === 'bearer' && 'Bearer Token'}
                          {automation.trigger.config.authType === 'hmac' && 'HMAC Secret'}
                        </Label>
                        <HelpTooltip
                          content="This credential will be verified when the webhook is called."
                          icon="info"
                        />
                      </div>
                      <Input
                        type="password"
                        placeholder={
                          automation.trigger.config.authType === 'api-key' ? 'Enter API key' :
                          automation.trigger.config.authType === 'basic' ? 'username:password' :
                          automation.trigger.config.authType === 'bearer' ? 'Enter bearer token' :
                          'Enter HMAC secret'
                        }
                        value={automation.trigger.config.authCredential || ''}
                        onChange={(e) => setAutomation({
                          ...automation,
                          trigger: {
                            ...automation.trigger,
                            config: { ...automation.trigger.config, authCredential: e.target.value }
                          }
                        })}
                      />
                    </div>
                  )}

                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <Label>Data Filters (Optional)</Label>
                      <HelpTooltip
                        content="Only trigger when specific conditions in the request data are met."
                        icon="info"
                      />
                    </div>
                    <textarea
                      className="w-full p-2 border rounded text-sm min-h-[80px]"
                      placeholder={`Example filters:
• payload.type === "contact_form"
• data.email.includes("@company.com")
• headers["x-event-type"] === "user.created"`}
                      value={automation.trigger.config.dataFilters || ''}
                      onChange={(e) => setAutomation({
                        ...automation,
                        trigger: {
                          ...automation.trigger,
                          config: { ...automation.trigger.config, dataFilters: e.target.value }
                        }
                      })}
                    />
                  </div>

                  <AIHelpSuggestions
                    context="trigger-webhook"
                    className="mt-3"
                  />
                </div>
              )}

              {automation.trigger.type === 'chat' && (
                <div className="space-y-4">
                  <div className="text-center p-6 bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg border">
                    <MessageSquare className="h-12 w-12 mx-auto mb-4 text-purple-600" />
                    <h3 className="text-lg font-semibold mb-2">Chat Command Trigger</h3>
                    <p className="text-muted-foreground text-sm">
                      This automation will be triggered manually through the chat interface when you send a command or message.
                    </p>
                  </div>

                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <Label>Command Configuration</Label>
                      <HelpTooltip
                        content="Set up how this automation can be triggered via chat commands."
                        icon="info"
                      />
                    </div>
                    
                    <div className="space-y-3">
                      <div>
                        <Label className="text-sm">Command Name (optional)</Label>
                        <Input
                          placeholder="e.g., /generate-report, /send-update, or leave empty for any message"
                          value={automation.trigger.config.commandName || ''}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, commandName: e.target.value }
                            }
                          })}
                          className="mt-1"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Specific command to trigger this automation, or leave empty to trigger on any chat interaction
                        </p>
                      </div>

                      <div>
                        <Label className="text-sm">Description (optional)</Label>
                        <Input
                          placeholder="e.g., Generate monthly sales report"
                          value={automation.trigger.config.description || ''}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, description: e.target.value }
                            }
                          })}
                          className="mt-1"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Help text that will be shown in the chat interface
                        </p>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="require-confirmation"
                          checked={automation.trigger.config.requireConfirmation || false}
                          onCheckedChange={(checked) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, requireConfirmation: checked }
                            }
                          })}
                        />
                        <Label htmlFor="require-confirmation" className="text-sm">
                          Require confirmation before running
                        </Label>
                      </div>
                    </div>
                  </div>

                  <AIHelpSuggestions
                    context="trigger-chat"
                    className="mt-3"
                  />
                </div>
              )}

              {automation.trigger.type === 'gmail' && (
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <Label>Authentication Type *</Label>
                      <HelpTooltip
                        content="Gmail requires Google OAuth authentication for secure access."
                        icon="info"
                      />
                    </div>
                    <RadioGroup
                      value={automation.trigger.config.gmailAuthType || 'google-oauth'}
                      onValueChange={(value) => setAutomation({
                        ...automation,
                        trigger: {
                          ...automation.trigger,
                          config: { ...automation.trigger.config, gmailAuthType: value }
                        }
                      })}
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="google-oauth" id="google-oauth" />
                        <Label htmlFor="google-oauth">Google OAuth (Recommended)</Label>
                      </div>
                    </RadioGroup>
                  </div>

                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <Label>Google Account</Label>
                      <HelpTooltip
                        content="Connect your Google account to access Gmail data."
                        icon="info"
                      />
                    </div>
                    
                    {automation.trigger.config.googleAccountEmail ? (
                      <div className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-green-900 dark:text-green-100">
                              Google account: {automation.trigger.config.googleAccountName || 'Connected User'} ({automation.trigger.config.googleAccountEmail})
                            </p>
                            <Badge variant="secondary" className="mt-1 bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                              Connected
                            </Badge>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setAutomation({
                              ...automation,
                              trigger: {
                                ...automation.trigger,
                                config: {
                                  ...automation.trigger.config,
                                  googleAccountId: undefined,
                                  googleAccountName: undefined,
                                  googleAccountEmail: undefined
                                }
                              }
                            })}
                          >
                            Disconnect
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <p className="text-sm text-muted-foreground">
                          Note: Select existing Google account from below or Signin with a different account
                        </p>
                        
                        {/* Existing Google Accounts */}
                        <ExistingGoogleAccounts 
                          onAccountSelect={(account) => {
                            // Extract email from name like "Google - <EMAIL>"
                            const emailMatch = account.name.match(/Google - (.+)/);
                            const email = emailMatch ? emailMatch[1] : account.name;
                            const displayName = email.split('@')[0]; // Use part before @ as display name
                            
                            setAutomation({
                              ...automation,
                              trigger: {
                                ...automation.trigger,
                                config: {
                                  ...automation.trigger.config,
                                  googleAccountId: account.id?.toString(),
                                  googleAccountName: displayName,
                                  googleAccountEmail: email
                                }
                              }
                            });
                            toast({
                              title: "Google Account Selected",
                              description: `Selected ${email}`,
                            });
                          }}
                        />
                        
                        <Button
                          variant="outline"
                          className="w-full"
                          onClick={async () => {
                            try {
                              // First save the current automation to get an ID
                              const tempAutomation = {
                                ...automation,
                                name: automation.name || 'Gmail Automation'
                              };
                              
                              // Save automation and get ID for OAuth flow
                              const response = await apiRequest('POST', '/api/automations', tempAutomation);
                              const savedAutomation = await response.json();
                              
                              // Start Google OAuth flow
                              const oauthResponse = await apiRequest('GET', `/api/auth/google/start?automationId=${savedAutomation.id}`);
                              const oauthData = await oauthResponse.json();
                              
                              if (oauthData.authUrl) {
                                // Open OAuth popup
                                const popup = window.open(
                                  oauthData.authUrl, 
                                  'google-oauth', 
                                  'width=500,height=600,scrollbars=yes,resizable=yes'
                                );
                                
                                // Listen for OAuth completion
                                const handleOAuthMessage = (event: MessageEvent) => {
                                  if (event.origin !== window.location.origin) return;
                                  
                                  if (event.data.type === 'oauth-success' && event.data.automationId === savedAutomation.id) {
                                    setAutomation({
                                      ...automation,
                                      id: savedAutomation.id,
                                      trigger: {
                                        ...automation.trigger,
                                        config: {
                                          ...automation.trigger.config,
                                          googleAccountId: event.data.googleAccountId,
                                          googleAccountName: event.data.googleAccountName,
                                          googleAccountEmail: event.data.googleAccountEmail
                                        }
                                      }
                                    });
                                    window.removeEventListener('message', handleOAuthMessage);
                                    toast({
                                      title: "Google Account Connected",
                                      description: `Successfully connected to ${event.data.googleAccountEmail}`,
                                    });
                                  } else if (event.data.type === 'oauth-error') {
                                    toast({
                                      title: "Authentication Error",
                                      description: event.data.error || "OAuth authentication failed",
                                      variant: "destructive"
                                    });
                                    window.removeEventListener('message', handleOAuthMessage);
                                  }
                                };
                                
                                window.addEventListener('message', handleOAuthMessage);
                                
                                // Check if popup was closed without completion
                                const checkClosed = setInterval(() => {
                                  if (popup?.closed) {
                                    clearInterval(checkClosed);
                                    window.removeEventListener('message', handleOAuthMessage);
                                  }
                                }, 1000);
                              }
                            } catch (error) {
                              console.error('OAuth flow error:', error);
                              toast({
                                title: "Authentication Error",
                                description: "Failed to start Google OAuth flow. Please try again.",
                                variant: "destructive"
                              });
                            }
                          }}
                        >
                          <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
                            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                          </svg>
                          Sign in with Google
                        </Button>
                      </div>
                    )}
                  </div>

                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <Label>Email Filters</Label>
                      <HelpTooltip
                        content="Set conditions to only trigger for specific emails."
                        icon="info"
                      />
                    </div>
                    
                    <div className="space-y-3">
                      <div>
                        <Label className="text-sm">Labels (optional)</Label>
                        <Input
                          placeholder="e.g., inbox, important, work"
                          value={automation.trigger.config.labelFilters?.join(', ') || ''}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: {
                                ...automation.trigger.config,
                                labelFilters: e.target.value ? e.target.value.split(',').map(s => s.trim()) : []
                              }
                            }
                          })}
                          className="mt-1"
                        />
                      </div>

                      <div>
                        <Label className="text-sm">From addresses (optional)</Label>
                        <Input
                          placeholder="e.g., <EMAIL>, <EMAIL>"
                          value={automation.trigger.config.senderFilters?.join(', ') || ''}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: {
                                ...automation.trigger.config,
                                senderFilters: e.target.value ? e.target.value.split(',').map(s => s.trim()) : []
                              }
                            }
                          })}
                          className="mt-1"
                        />
                      </div>

                      <div>
                        <Label className="text-sm">Subject contains (optional)</Label>
                        <Input
                          placeholder="e.g., invoice, urgent, meeting"
                          value={automation.trigger.config.subjectFilters?.join(', ') || ''}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: {
                                ...automation.trigger.config,
                                subjectFilters: e.target.value ? e.target.value.split(',').map(s => s.trim()) : []
                              }
                            }
                          })}
                          className="mt-1"
                        />
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="has-attachment"
                          checked={automation.trigger.config.hasAttachment || false}
                          onCheckedChange={(checked) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, hasAttachment: checked }
                            }
                          })}
                        />
                        <Label htmlFor="has-attachment" className="text-sm">Only emails with attachments</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="is-unread"
                          checked={automation.trigger.config.isUnread || false}
                          onCheckedChange={(checked) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, isUnread: checked }
                            }
                          })}
                        />
                        <Label htmlFor="is-unread" className="text-sm">Only unread emails</Label>
                      </div>
                    </div>
                  </div>

                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <Label>Polling Settings</Label>
                      <HelpTooltip
                        content="Configure how often to check for new emails."
                        icon="info"
                      />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <Label className="text-sm">Check frequency</Label>
                        <Select
                          value={automation.trigger.config.pollInterval || '5min'}
                          onValueChange={(value) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, pollInterval: value }
                            }
                          })}
                        >
                          <SelectTrigger className="mt-1">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1min">Every minute</SelectItem>
                            <SelectItem value="5min">Every 5 minutes</SelectItem>
                            <SelectItem value="15min">Every 15 minutes</SelectItem>
                            <SelectItem value="30min">Every 30 minutes</SelectItem>
                            <SelectItem value="1hour">Every hour</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label className="text-sm">Max emails per check</Label>
                        <Input
                          type="number"
                          min="1"
                          max="100"
                          value={automation.trigger.config.maxEmails || 10}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, maxEmails: parseInt(e.target.value) || 10 }
                            }
                          })}
                          className="mt-1"
                        />
                      </div>
                    </div>
                  </div>

                  <AIHelpSuggestions
                    context="trigger-gmail"
                    className="mt-3"
                  />
                </div>
              )}

              {automation.trigger.type === 'google-sheets' && (
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <Label>Authentication Type *</Label>
                      <HelpTooltip
                        content="Google Sheets requires Google OAuth authentication for secure access."
                        icon="info"
                      />
                    </div>
                    <RadioGroup
                      value={automation.trigger.config.authType || 'google-oauth'}
                      onValueChange={(value) => setAutomation({
                        ...automation,
                        trigger: {
                          ...automation.trigger,
                          config: { ...automation.trigger.config, authType: value }
                        }
                      })}
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="google-oauth" id="sheets-google-oauth" />
                        <Label htmlFor="sheets-google-oauth">Google OAuth (Recommended)</Label>
                      </div>
                    </RadioGroup>
                  </div>

                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <Label>Google Account</Label>
                      <HelpTooltip
                        content="Connect your Google account to access Google Sheets data."
                        icon="info"
                      />
                    </div>
                    
                    {automation.trigger.config.googleAccountEmail ? (
                      <div className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-green-900 dark:text-green-100">
                              Google account: {automation.trigger.config.googleAccountName || 'Connected User'} ({automation.trigger.config.googleAccountEmail})
                            </p>
                            <Badge variant="secondary" className="mt-1 bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                              Connected
                            </Badge>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              // Clear both automation config and local state
                              setSelectedGoogleAccount(null);
                              setSelectedSpreadsheet(null);
                              setSelectedSheet('');
                              setAutomation({
                                ...automation,
                                trigger: {
                                  ...automation.trigger,
                                  config: {
                                    ...automation.trigger.config,
                                    googleAccountId: undefined,
                                    googleAccountName: undefined,
                                    googleAccountEmail: undefined,
                                    spreadsheetId: undefined,
                                    spreadsheetName: undefined,
                                    sheetName: undefined
                                  }
                                }
                              });
                            }}
                          >
                            Disconnect
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <p className="text-sm text-muted-foreground">
                          Note: Select existing Google account from below or Signin with a different account
                        </p>
                        
                        {/* Existing Google Accounts */}
                        <ExistingGoogleAccounts 
                          key={`google-sheets-${selectedGoogleAccount?.id || 'none'}`}
                          trigger="google-sheets"
                          selectedAccount={selectedGoogleAccount}
                          selectedSpreadsheet={selectedSpreadsheet}
                          selectedSheet={selectedSheet}
                          onAccountSelect={(account) => {
                            console.log('Parent onAccountSelect called with:', account);
                            // Extract email from name patterns:
                            // "kaizarbharmal21 (<EMAIL>)" or "Google - email"
                            let email = '';
                            let displayName = '';
                            
                            if (account.name.includes('(') && account.name.includes('@')) {
                              // Pattern: "name (<EMAIL>)"
                              const emailMatch = account.name.match(/\(([^)]+@[^)]+)\)/);
                              email = emailMatch ? emailMatch[1] : account.name;
                              displayName = account.name.split(' (')[0];
                            } else if (account.name.includes('Google - ')) {
                              // Pattern: "Google - <EMAIL>"  
                              email = account.name.replace('Google - ', '');
                              displayName = email.split('@')[0];
                            } else {
                              // Fallback
                              email = account.name;
                              displayName = account.name.split('@')[0];
                            }
                            
                            console.log('Extracted email:', email, 'displayName:', displayName);
                            
                            console.log('Setting selectedGoogleAccount to:', account);
                            console.log('Before state update - current selectedGoogleAccount:', selectedGoogleAccount);
                            
                            // Set local state first
                            setSelectedGoogleAccount(account);
                            console.log('After state update call - selectedGoogleAccount should be:', account);
                            
                            // Then update automation config - use functional update to avoid stale closure
                            setAutomation(prevAutomation => ({
                              ...prevAutomation,
                              trigger: {
                                ...prevAutomation.trigger,
                                config: {
                                  ...prevAutomation.trigger.config,
                                  googleAccountId: account.id?.toString(),
                                  googleAccountName: displayName,
                                  googleAccountEmail: email
                                }
                              }
                            }));
                            toast({
                              title: "Google Account Selected",
                              description: `Selected ${email}`,
                            });
                          }}
                          onSpreadsheetSelect={(spreadsheet) => {
                            setSelectedSpreadsheet(spreadsheet);
                            setAutomation(prevAutomation => ({
                              ...prevAutomation,
                              trigger: {
                                ...prevAutomation.trigger,
                                config: {
                                  ...prevAutomation.trigger.config,
                                  spreadsheetId: spreadsheet.id,
                                  spreadsheetName: spreadsheet.name
                                }
                              }
                            }));
                            toast({
                              title: "Spreadsheet Selected",
                              description: `Selected ${spreadsheet.name}`,
                            });
                          }}
                          onSheetSelect={(sheetName) => {
                            setSelectedSheet(sheetName);
                            setAutomation(prevAutomation => ({
                              ...prevAutomation,
                              trigger: {
                                ...prevAutomation.trigger,
                                config: {
                                  ...prevAutomation.trigger.config,
                                  sheetName: sheetName
                                }
                              }
                            }));
                            toast({
                              title: "Sheet Selected",
                              description: `Selected ${sheetName}`,
                            });
                          }}
                        />
                        
                        <Button
                          variant="outline"
                          className="w-full"
                          onClick={async () => {
                            try {
                              // First save the current automation to get an ID
                              const tempAutomation = {
                                ...automation,
                                name: automation.name || 'Google Sheets Automation'
                              };
                              
                              // Save automation and get ID for OAuth flow
                              const response = await apiRequest('POST', '/api/automations', tempAutomation);
                              const savedAutomation = await response.json();
                              
                              // Start Google OAuth flow with expanded scopes
                              const oauthResponse = await apiRequest('GET', `/api/auth/google/start?automationId=${savedAutomation.id}&reauth=true`);
                              const oauthData = await oauthResponse.json();
                              
                              if (oauthData.authUrl) {
                                // Open OAuth popup
                                const popup = window.open(
                                  oauthData.authUrl, 
                                  'google-oauth', 
                                  'width=500,height=600,scrollbars=yes,resizable=yes'
                                );
                                
                                // Listen for OAuth completion
                                const handleOAuthMessage = (event: MessageEvent) => {
                                  if (event.origin !== window.location.origin) return;
                                  
                                  if (event.data.type === 'oauth-success' && event.data.automationId === savedAutomation.id) {
                                    setAutomation({
                                      ...automation,
                                      id: savedAutomation.id,
                                      trigger: {
                                        ...automation.trigger,
                                        config: {
                                          ...automation.trigger.config,
                                          googleAccountId: event.data.googleAccountId,
                                          googleAccountName: event.data.googleAccountName,
                                          googleAccountEmail: event.data.googleAccountEmail
                                        }
                                      }
                                    });
                                    window.removeEventListener('message', handleOAuthMessage);
                                    toast({
                                      title: "Google Account Connected",
                                      description: `Successfully connected to ${event.data.googleAccountEmail}`,
                                    });
                                  } else if (event.data.type === 'oauth-error') {
                                    toast({
                                      title: "Authentication Error",
                                      description: event.data.error || "OAuth authentication failed",
                                      variant: "destructive"
                                    });
                                    window.removeEventListener('message', handleOAuthMessage);
                                  }
                                };
                                
                                window.addEventListener('message', handleOAuthMessage);
                                
                                // Check if popup was closed without completion
                                const checkClosed = setInterval(() => {
                                  if (popup?.closed) {
                                    clearInterval(checkClosed);
                                    window.removeEventListener('message', handleOAuthMessage);
                                  }
                                }, 1000);
                              }
                            } catch (error) {
                              console.error('OAuth flow error:', error);
                              toast({
                                title: "Authentication Error",
                                description: "Failed to start Google OAuth flow. Please try again.",
                                variant: "destructive"
                              });
                            }
                          }}
                        >
                          <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
                            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                          </svg>
                          Sign in with Google
                        </Button>
                      </div>
                    )}
                  </div>

                  {/* Spreadsheet and sheet selection is now handled by ExistingGoogleAccounts component */}
                  
                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <Label>Additional Settings</Label>
                      <HelpTooltip
                        content="Configure additional options for Google Sheets monitoring."
                        icon="info"
                      />
                    </div>
                    
                    <div className="space-y-3">
                      <div>
                        <Label className="text-sm">Watch Range (optional)</Label>
                        <Input
                          placeholder="e.g., A:Z, A1:E100"
                          value={automation.trigger.config.watchRange || ''}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: {
                                ...automation.trigger.config,
                                watchRange: e.target.value
                              }
                            }
                          })}
                          className="mt-1"
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <Label>Trigger Settings</Label>
                      <HelpTooltip
                        content="Configure when the automation should run."
                        icon="info"
                      />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <Label className="text-sm">Trigger Type</Label>
                        <Select
                          value={automation.trigger.config.triggerType || 'row-added'}
                          onValueChange={(value) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, triggerType: value }
                            }
                          })}
                        >
                          <SelectTrigger className="mt-1">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="row-added">New row added</SelectItem>
                            <SelectItem value="row-updated">Row updated</SelectItem>
                            <SelectItem value="any-change">Any change</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label className="text-sm">Check frequency</Label>
                        <Select
                          value={automation.trigger.config.pollInterval || '5min'}
                          onValueChange={(value) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, pollInterval: value }
                            }
                          })}
                        >
                          <SelectTrigger className="mt-1">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1min">Every minute</SelectItem>
                            <SelectItem value="5min">Every 5 minutes</SelectItem>
                            <SelectItem value="15min">Every 15 minutes</SelectItem>
                            <SelectItem value="30min">Every 30 minutes</SelectItem>
                            <SelectItem value="1hour">Every hour</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  <AIHelpSuggestions
                    context="trigger-google-sheets"
                    className="mt-3"
                  />
                </div>
              )}

              {automation.trigger.type === 'google-drive' && (
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <Label>Authentication Type *</Label>
                      <HelpTooltip
                        content="Google Drive requires Google OAuth authentication for secure access."
                        icon="info"
                      />
                    </div>
                    <RadioGroup
                      value={automation.trigger.config.driveAuthType || 'google-oauth'}
                      onValueChange={(value) => setAutomation({
                        ...automation,
                        trigger: {
                          ...automation.trigger,
                          config: { ...automation.trigger.config, driveAuthType: value }
                        }
                      })}
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="google-oauth" id="drive-google-oauth" />
                        <Label htmlFor="drive-google-oauth">Google OAuth (Recommended)</Label>
                      </div>
                    </RadioGroup>
                  </div>

                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <Label>Google Account</Label>
                      <HelpTooltip
                        content="Connect your Google account to access Google Drive files."
                        icon="info"
                      />
                    </div>
                    
                    {automation.trigger.config.googleAccountEmail ? (
                      <div className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-green-900 dark:text-green-100">
                              Google account: {automation.trigger.config.googleAccountName || 'Connected User'} ({automation.trigger.config.googleAccountEmail})
                            </p>
                            <Badge variant="secondary" className="mt-1 bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                              Connected
                            </Badge>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              // Clear automation config for Drive
                              setAutomation({
                                ...automation,
                                trigger: {
                                  ...automation.trigger,
                                  config: {
                                    ...automation.trigger.config,
                                    googleAccountId: undefined,
                                    googleAccountName: undefined,
                                    googleAccountEmail: undefined,
                                    folderId: undefined,
                                    folderName: undefined
                                  }
                                }
                              });
                            }}
                          >
                            Disconnect
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <p className="text-sm text-muted-foreground">
                          Note: Select existing Google account from below or Sign in with a different account
                        </p>
                        
                        {/* Existing Google Accounts */}
                        {console.log('Google Drive - RENDER: selectedDriveAccount =', selectedDriveAccount)}
                        {console.log('Google Drive - automation.trigger.config =', automation.trigger.config)}
                        {selectedDriveAccount && (
                          <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                            <p className="text-sm text-green-800 dark:text-green-200">
                              ✓ Google Drive account selected: {selectedDriveAccount.name}
                            </p>
                          </div>
                        )}
                        <ExistingGoogleAccounts 
                          key={`google-drive-${automation.trigger.config.googleAccountId || 'none'}`}
                          trigger="google-drive"
                          selectedAccount={automation.trigger.config.googleAccountId ? selectedDriveAccount : null}
                          selectedAccountId={automation.trigger.config.googleAccountId}
                          onAccountSelect={(account) => {
                            console.log('Google Drive - Parent onAccountSelect called with:', account);
                            console.log('Google Drive - Current selected account:', selectedDriveAccount?.id);
                            
                            // Extract email from name patterns
                            let email = '';
                            let displayName = '';
                            
                            if (account.name.includes('(') && account.name.includes('@')) {
                              const emailMatch = account.name.match(/\(([^)]+@[^)]+)\)/);
                              email = emailMatch ? emailMatch[1] : account.name;
                              displayName = account.name.split(' (')[0];
                            } else if (account.name.includes('Google - ')) {
                              email = account.name.replace('Google - ', '');
                              displayName = email.split('@')[0];
                            } else {
                              email = account.name;
                              displayName = account.name.split('@')[0];
                            }
                            
                            console.log('Google Drive - Selected account:', account.id);
                            // No need to set state, directly update automation config
                            
                            // Update automation config
                            setAutomation(prevAutomation => {
                              console.log('Google Drive - Updating automation config with account:', account.id);
                              return {
                                ...prevAutomation,
                                trigger: {
                                  ...prevAutomation.trigger,
                                  config: {
                                    ...prevAutomation.trigger.config,
                                    googleAccountId: account.id?.toString(),
                                    googleAccountName: displayName,
                                    googleAccountEmail: email
                                  }
                                }
                              };
                            });
                            
                            toast({
                              title: "Google Account Selected",
                              description: `Selected ${email}`,
                            });
                          }}
                          onFolderSelect={(folder) => {
                            console.log('Google Drive - Folder selected:', folder);
                            setAutomation(prevAutomation => ({
                              ...prevAutomation,
                              trigger: {
                                ...prevAutomation.trigger,
                                config: {
                                  ...prevAutomation.trigger.config,
                                  folderId: folder.id,
                                  folderName: folder.name
                                }
                              }
                            }));
                          }}
                          onFileSelect={(file) => {
                            console.log('Google Drive - File selected for preview:', file);
                            // This is just for preview, not setting in config
                          }}
                        />
                        
                        <Button
                          variant="outline"
                          className="w-full"
                          onClick={async () => {
                            try {
                              // Save automation first
                              const tempAutomation = {
                                ...automation,
                                name: automation.name || 'Google Drive Automation'
                              };
                              
                              const response = await apiRequest('POST', '/api/automations', tempAutomation);
                              const savedAutomation = await response.json();
                              
                              // Start Google OAuth flow
                              const oauthResponse = await apiRequest('GET', `/api/auth/google/start?automationId=${savedAutomation.id}&reauth=true`);
                              const oauthData = await oauthResponse.json();
                              
                              if (oauthData.authUrl) {
                                const popup = window.open(
                                  oauthData.authUrl, 
                                  'google-oauth', 
                                  'width=500,height=600,scrollbars=yes,resizable=yes'
                                );
                                
                                const handleOAuthMessage = (event: MessageEvent) => {
                                  if (event.origin !== window.location.origin) return;
                                  
                                  if (event.data.type === 'oauth-success' && event.data.automationId === savedAutomation.id) {
                                    setAutomation({
                                      ...automation,
                                      id: savedAutomation.id,
                                      trigger: {
                                        ...automation.trigger,
                                        config: {
                                          ...automation.trigger.config,
                                          googleAccountId: event.data.googleAccountId,
                                          googleAccountName: event.data.googleAccountName,
                                          googleAccountEmail: event.data.googleAccountEmail
                                        }
                                      }
                                    });
                                    window.removeEventListener('message', handleOAuthMessage);
                                    toast({
                                      title: "Google Account Connected",
                                      description: `Successfully connected to ${event.data.googleAccountEmail}`,
                                    });
                                  } else if (event.data.type === 'oauth-error') {
                                    toast({
                                      title: "Authentication Error",
                                      description: event.data.error || "OAuth authentication failed",
                                      variant: "destructive"
                                    });
                                    window.removeEventListener('message', handleOAuthMessage);
                                  }
                                };
                                
                                window.addEventListener('message', handleOAuthMessage);
                              }
                            } catch (error) {
                              console.error('OAuth flow error:', error);
                              toast({
                                title: "Authentication Error",
                                description: "Failed to start Google OAuth flow. Please try again.",
                                variant: "destructive"
                              });
                            }
                          }}
                        >
                          <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
                            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                          </svg>
                          Sign in with Google
                        </Button>
                      </div>
                    )}
                  </div>

                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <Label>Folder Settings</Label>
                      <HelpTooltip
                        content="Configure which folder to monitor for file changes."
                        icon="info"
                      />
                    </div>
                    
                    <div className="space-y-3">
                      <div>
                        <Label className="text-sm">Folder to Monitor</Label>
                        <Input
                          placeholder="e.g., /Documents/Invoices or folder ID"
                          value={automation.trigger.config.folderId || ''}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: {
                                ...automation.trigger.config,
                                folderId: e.target.value
                              }
                            }
                          })}
                          className="mt-1"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Leave empty to monitor entire Google Drive
                        </p>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="include-subfolders"
                          checked={automation.trigger.config.includeSubfolders || false}
                          onCheckedChange={(checked) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: {
                                ...automation.trigger.config,
                                includeSubfolders: checked
                              }
                            }
                          })}
                        />
                        <Label htmlFor="include-subfolders" className="text-sm">Include subfolders</Label>
                      </div>
                    </div>
                  </div>

                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <Label>File Filters</Label>
                      <HelpTooltip
                        content="Configure which types of files to monitor."
                        icon="info"
                      />
                    </div>
                    
                    <div className="space-y-3">
                      <div>
                        <Label className="text-sm">File Types (optional)</Label>
                        <Input
                          placeholder="e.g., pdf, docx, xlsx (separated by commas)"
                          value={automation.trigger.config.fileTypes?.join(', ') || ''}
                          onChange={(e) => {
                            const types = e.target.value.split(',').map(t => t.trim()).filter(t => t.length > 0);
                            setAutomation({
                              ...automation,
                              trigger: {
                                ...automation.trigger,
                                config: {
                                  ...automation.trigger.config,
                                  fileTypes: types
                                }
                              }
                            });
                          }}
                          className="mt-1"
                        />
                      </div>
                      
                      <div>
                        <Label className="text-sm">Filename Pattern (optional)</Label>
                        <Input
                          placeholder="e.g., invoice_*, *.pdf, *report*"
                          value={automation.trigger.config.filenamePattern || ''}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: {
                                ...automation.trigger.config,
                                filenamePattern: e.target.value
                              }
                            }
                          })}
                          className="mt-1"
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <Label>Trigger Settings</Label>
                      <HelpTooltip
                        content="Configure when the automation should run."
                        icon="info"
                      />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <Label className="text-sm">Trigger Type</Label>
                        <Select
                          value={automation.trigger.config.triggerType || 'file-added'}
                          onValueChange={(value) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, triggerType: value }
                            }
                          })}
                        >
                          <SelectTrigger className="mt-1">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="file-added">File added</SelectItem>
                            <SelectItem value="file-modified">File modified</SelectItem>
                            <SelectItem value="file-deleted">File deleted</SelectItem>
                            <SelectItem value="folder-created">Folder created</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label className="text-sm">Check frequency</Label>
                        <Select
                          value={automation.trigger.config.pollInterval || '5min'}
                          onValueChange={(value) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, pollInterval: value }
                            }
                          })}
                        >
                          <SelectTrigger className="mt-1">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1min">Every minute</SelectItem>
                            <SelectItem value="5min">Every 5 minutes</SelectItem>
                            <SelectItem value="15min">Every 15 minutes</SelectItem>
                            <SelectItem value="30min">Every 30 minutes</SelectItem>
                            <SelectItem value="1hour">Every hour</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  <AIHelpSuggestions
                    context="trigger-google-drive"
                    className="mt-3"
                  />
                </div>
              )}

              {automation.trigger.type === 'google-calendar' && (
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <Label>Authentication Type *</Label>
                      <HelpTooltip
                        content="Select how to authenticate with Google Calendar."
                        icon="info"
                      />
                    </div>
                    <RadioGroup
                      value={automation.trigger.config.authType || 'existing'}
                      onValueChange={(value) => setAutomation({
                        ...automation,
                        trigger: {
                          ...automation.trigger,
                          config: { ...automation.trigger.config, authType: value }
                        }
                      })}
                      className="flex gap-4"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="existing" id="existing" />
                        <Label htmlFor="existing">Use existing Google account</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="new" id="new" />
                        <Label htmlFor="new">Sign in with different account</Label>
                      </div>
                    </RadioGroup>
                    
                    {automation.trigger.config.authType === 'existing' && (
                      <div className="mt-3">
                        <p className="text-sm text-muted-foreground mb-2">
                          Note: Select existing Google account from below or Sign in with a different account
                        </p>
                        
                        {selectedCalendarAccount && (
                          <div className="mb-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                            <p className="text-sm text-green-800 dark:text-green-200">
                              ✓ Google Calendar account selected: {selectedCalendarAccount.name}
                            </p>
                          </div>
                        )}
                        <ExistingGoogleAccounts 
                          key={`google-calendar-${automation.trigger.config.googleAccountId || 'none'}`}
                          trigger="google-calendar"
                          selectedAccount={automation.trigger.config.googleAccountId ? selectedCalendarAccount : null}
                          selectedAccountId={automation.trigger.config.googleAccountId}
                          onAccountSelect={(account) => {
                            console.log('Google Calendar - Parent onAccountSelect called with:', account);
                            
                            // Extract email from name patterns
                            let email = '';
                            let displayName = '';
                            
                            if (account.name.includes('(') && account.name.includes('@')) {
                              const emailMatch = account.name.match(/\(([^)]+@[^)]+)\)/);
                              email = emailMatch ? emailMatch[1] : account.name;
                              displayName = account.name.split(' (')[0];
                            } else if (account.name.includes('Google - ')) {
                              email = account.name.replace('Google - ', '');
                              displayName = email.split('@')[0];
                            } else {
                              email = account.name;
                              displayName = account.name.split('@')[0];
                            }
                            
                            console.log('Google Calendar - Selected account:', account.id);
                            
                            // Update automation config
                            setAutomation(prevAutomation => {
                              console.log('Google Calendar - Updating automation config with account:', account.id);
                              return {
                                ...prevAutomation,
                                trigger: {
                                  ...prevAutomation.trigger,
                                  config: {
                                    ...prevAutomation.trigger.config,
                                    googleAccountId: account.id?.toString(),
                                    googleAccountName: displayName,
                                    googleAccountEmail: email
                                  }
                                }
                              };
                            });
                            
                            toast({
                              title: "Google Account Selected",
                              description: `Selected ${email}`,
                            });
                          }}
                          onCalendarSelect={(calendar) => {
                            console.log('Google Calendar - Calendar selected:', calendar);
                            setAutomation(prevAutomation => ({
                              ...prevAutomation,
                              trigger: {
                                ...prevAutomation.trigger,
                                config: {
                                  ...prevAutomation.trigger.config,
                                  calendarId: calendar.id,
                                  calendarName: calendar.name
                                }
                              }
                            }));
                          }}
                        />
                      </div>
                    )}
                    
                    {automation.trigger.config.authType === 'new' && (
                      <div className="mt-3">
                        <Button
                          variant="outline"
                          className="w-full"
                          onClick={async () => {
                            try {
                              // Save automation first
                              const tempAutomation = {
                                ...automation,
                                name: automation.name || 'Google Calendar Automation'
                              };
                              
                              const response = await apiRequest('POST', '/api/automations', tempAutomation);
                              const savedAutomation = await response.json();
                              
                              // Start Google OAuth flow
                              const oauthResponse = await apiRequest('GET', `/api/auth/google/start?automationId=${savedAutomation.id}&reauth=true`);
                              const oauthData = await oauthResponse.json();
                              
                              if (oauthData.authUrl) {
                                const popup = window.open(
                                  oauthData.authUrl, 
                                  'google-oauth', 
                                  'width=500,height=600,scrollbars=yes,resizable=yes'
                                );
                                
                                const handleOAuthMessage = (event: MessageEvent) => {
                                  if (event.origin !== window.location.origin) return;
                                  
                                  if (event.data.type === 'oauth-success' && event.data.automationId === savedAutomation.id) {
                                    setAutomation({
                                      ...automation,
                                      id: savedAutomation.id,
                                      trigger: {
                                        ...automation.trigger,
                                        config: {
                                          ...automation.trigger.config,
                                          googleAccountId: event.data.googleAccountId,
                                          googleAccountName: event.data.googleAccountName,
                                          googleAccountEmail: event.data.googleAccountEmail
                                        }
                                      }
                                    });
                                    window.removeEventListener('message', handleOAuthMessage);
                                    toast({
                                      title: "Google Account Connected",
                                      description: `Successfully connected to ${event.data.googleAccountEmail}`,
                                    });
                                  } else if (event.data.type === 'oauth-error') {
                                    toast({
                                      title: "Authentication Error",
                                      description: event.data.error || "OAuth authentication failed",
                                      variant: "destructive"
                                    });
                                    window.removeEventListener('message', handleOAuthMessage);
                                  }
                                };
                                
                                window.addEventListener('message', handleOAuthMessage);
                              }
                            } catch (error) {
                              console.error('OAuth flow error:', error);
                              toast({
                                title: "Authentication Error",
                                description: "Failed to start Google OAuth flow. Please try again.",
                                variant: "destructive"
                              });
                            }
                          }}
                        >
                          <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
                            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                          </svg>
                          Sign in with Google
                        </Button>
                      </div>
                    )}
                  </div>

                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <Label>Event Trigger Settings</Label>
                      <HelpTooltip
                        content="Configure which calendar events should trigger the automation."
                        icon="info"
                      />
                    </div>
                    
                    <div className="space-y-3">
                      <div>
                        <Label className="text-sm">Trigger When</Label>
                        <Select
                          value={automation.trigger.config.eventTrigger || 'event-created'}
                          onValueChange={(value) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, eventTrigger: value }
                            }
                          })}
                        >
                          <SelectTrigger className="mt-1">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="event-created">Event is created</SelectItem>
                            <SelectItem value="event-updated">Event is updated</SelectItem>
                            <SelectItem value="event-deleted">Event is deleted</SelectItem>
                            <SelectItem value="event-starts">Event starts (reminder)</SelectItem>
                            <SelectItem value="event-ends">Event ends</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div>
                        <Label className="text-sm">Calendar Filter (optional)</Label>
                        <Input
                          placeholder="e.g., Work, Personal, or leave empty for all calendars"
                          value={automation.trigger.config.calendarFilter || ''}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: {
                                ...automation.trigger.config,
                                calendarFilter: e.target.value
                              }
                            }
                          })}
                          className="mt-1"
                        />
                      </div>
                      
                      <div>
                        <Label className="text-sm">Event Title Filter (optional)</Label>
                        <Input
                          placeholder="e.g., Meeting, Call, or keywords to match"
                          value={automation.trigger.config.titleFilter || ''}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: {
                                ...automation.trigger.config,
                                titleFilter: e.target.value
                              }
                            }
                          })}
                          className="mt-1"
                        />
                      </div>
                      
                      <div>
                        <Label className="text-sm">Check frequency</Label>
                        <Select
                          value={automation.trigger.config.pollInterval || '5min'}
                          onValueChange={(value) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, pollInterval: value }
                            }
                          })}
                        >
                          <SelectTrigger className="mt-1">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1min">Every minute</SelectItem>
                            <SelectItem value="5min">Every 5 minutes</SelectItem>
                            <SelectItem value="15min">Every 15 minutes</SelectItem>
                            <SelectItem value="30min">Every 30 minutes</SelectItem>
                            <SelectItem value="1hour">Every hour</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  <AIHelpSuggestions
                    context="trigger-google-calendar"
                    className="mt-3"
                  />
                </div>
              )}

              {automation.trigger.type === 'slack' && (
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <Label>Authentication Type *</Label>
                      <HelpTooltip
                        content="Choose how to authenticate with your Slack workspace."
                        icon="info"
                      />
                    </div>
                    <RadioGroup
                      value={automation.trigger.config.authType || 'bot-token'}
                      onValueChange={(value) => setAutomation({
                        ...automation,
                        trigger: {
                          ...automation.trigger,
                          config: { ...automation.trigger.config, authType: value }
                        }
                      })}
                      className="flex gap-4"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="bot-token" id="bot-token" />
                        <Label htmlFor="bot-token">Bot Token (Recommended)</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="oauth" id="oauth" />
                        <Label htmlFor="oauth">OAuth App</Label>
                      </div>
                    </RadioGroup>
                  </div>

                  {automation.trigger.config.authType === 'bot-token' && (
                    <div>
                      <div className="flex items-center gap-2 mb-3">
                        <Label>Slack Configuration</Label>
                        <HelpTooltip
                          content="Configure your Slack workspace and bot token for message monitoring."
                          icon="info"
                        />
                      </div>
                      
                      <div className="space-y-3">
                        <div>
                          <Label className="text-sm">Workspace Name (optional)</Label>
                          <Input
                            placeholder="e.g., My Company Workspace"
                            value={automation.trigger.config.workspaceName || ''}
                            onChange={(e) => setAutomation({
                              ...automation,
                              trigger: {
                                ...automation.trigger,
                                config: { ...automation.trigger.config, workspaceName: e.target.value }
                              }
                            })}
                            className="mt-1"
                          />
                        </div>

                        <div>
                          <Label className="text-sm">Bot Token *</Label>
                          <Input
                            type="password"
                            placeholder="xoxb-your-bot-token-here"
                            value={automation.trigger.config.botToken || ''}
                            onChange={(e) => setAutomation({
                              ...automation,
                              trigger: {
                                ...automation.trigger,
                                config: { ...automation.trigger.config, botToken: e.target.value }
                              }
                            })}
                            className="mt-1"
                          />
                          <p className="text-xs text-muted-foreground mt-1">
                            Get your bot token from <a href="https://api.slack.com/apps" target="_blank" rel="noopener" className="text-blue-600 hover:underline">Slack API</a>
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <Label>Channel Selection</Label>
                      <HelpTooltip
                        content="Choose which Slack channel to monitor for messages."
                        icon="info"
                      />
                    </div>
                    
                    <div className="space-y-3">
                      <div>
                        <Label className="text-sm">Channel ID</Label>
                        <Input
                          placeholder="e.g., C1234567890 or #general"
                          value={automation.trigger.config.channelId || ''}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, channelId: e.target.value }
                            }
                          })}
                          className="mt-1"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Right-click channel → Copy Link → Channel ID is in the URL
                        </p>
                      </div>

                      <div>
                        <Label className="text-sm">Channel Name (optional)</Label>
                        <Input
                          placeholder="e.g., general, dev-team, announcements"
                          value={automation.trigger.config.channelName || ''}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, channelName: e.target.value }
                            }
                          })}
                          className="mt-1"
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <Label>Message Filters</Label>
                      <HelpTooltip
                        content="Configure which messages should trigger the automation."
                        icon="info"
                      />
                    </div>
                    
                    <div className="space-y-3">
                      <div>
                        <Label className="text-sm">Keywords (optional)</Label>
                        <Input
                          placeholder="e.g., urgent, help, deploy, bug"
                          value={automation.trigger.config.messageFilter?.keywords?.join(', ') || ''}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: {
                                ...automation.trigger.config,
                                messageFilter: {
                                  ...automation.trigger.config.messageFilter,
                                  keywords: e.target.value ? e.target.value.split(',').map(k => k.trim()) : []
                                }
                              }
                            }
                          })}
                          className="mt-1"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Comma-separated keywords to match in messages
                        </p>
                      </div>

                      <div>
                        <Label className="text-sm">User Filter (optional)</Label>
                        <Input
                          placeholder="e.g., U1234567890, U0987654321"
                          value={automation.trigger.config.messageFilter?.userFilter?.join(', ') || ''}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: {
                                ...automation.trigger.config,
                                messageFilter: {
                                  ...automation.trigger.config.messageFilter,
                                  userFilter: e.target.value ? e.target.value.split(',').map(u => u.trim()) : []
                                }
                              }
                            }
                          })}
                          className="mt-1"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Specific user IDs to monitor (leave empty for all users)
                        </p>
                      </div>

                      <div className="grid grid-cols-2 gap-3">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="exclude-bots"
                            checked={automation.trigger.config.messageFilter?.excludeBots !== false}
                            onCheckedChange={(checked) => setAutomation({
                              ...automation,
                              trigger: {
                                ...automation.trigger,
                                config: {
                                  ...automation.trigger.config,
                                  messageFilter: {
                                    ...automation.trigger.config.messageFilter,
                                    excludeBots: checked !== false
                                  }
                                }
                              }
                            })}
                          />
                          <Label htmlFor="exclude-bots" className="text-sm">Exclude bot messages</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="mention-only"
                            checked={automation.trigger.config.messageFilter?.mentionOnly || false}
                            onCheckedChange={(checked) => setAutomation({
                              ...automation,
                              trigger: {
                                ...automation.trigger,
                                config: {
                                  ...automation.trigger.config,
                                  messageFilter: {
                                    ...automation.trigger.config.messageFilter,
                                    mentionOnly: checked
                                  }
                                }
                              }
                            })}
                          />
                          <Label htmlFor="mention-only" className="text-sm">Only @mentions</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="thread-only"
                            checked={automation.trigger.config.messageFilter?.threadOnly || false}
                            onCheckedChange={(checked) => setAutomation({
                              ...automation,
                              trigger: {
                                ...automation.trigger,
                                config: {
                                  ...automation.trigger.config,
                                  messageFilter: {
                                    ...automation.trigger.config.messageFilter,
                                    threadOnly: checked
                                  }
                                }
                              }
                            })}
                          />
                          <Label htmlFor="thread-only" className="text-sm">Threads only</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="has-attachments"
                            checked={automation.trigger.config.messageFilter?.hasAttachments || false}
                            onCheckedChange={(checked) => setAutomation({
                              ...automation,
                              trigger: {
                                ...automation.trigger,
                                config: {
                                  ...automation.trigger.config,
                                  messageFilter: {
                                    ...automation.trigger.config.messageFilter,
                                    hasAttachments: checked
                                  }
                                }
                              }
                            })}
                          />
                          <Label htmlFor="has-attachments" className="text-sm">Has attachments</Label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <Label>Polling Settings</Label>
                      <HelpTooltip
                        content="Configure how often to check for new messages."
                        icon="info"
                      />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <Label className="text-sm">Check frequency</Label>
                        <Select
                          value={automation.trigger.config.pollInterval || '1min'}
                          onValueChange={(value) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, pollInterval: value }
                            }
                          })}
                        >
                          <SelectTrigger className="mt-1">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="30sec">Every 30 seconds</SelectItem>
                            <SelectItem value="1min">Every minute</SelectItem>
                            <SelectItem value="5min">Every 5 minutes</SelectItem>
                            <SelectItem value="15min">Every 15 minutes</SelectItem>
                            <SelectItem value="30min">Every 30 minutes</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label className="text-sm">Max messages per check</Label>
                        <Input
                          type="number"
                          min="1"
                          max="50"
                          value={automation.trigger.config.maxMessages || 10}
                          onChange={(e) => setAutomation({
                            ...automation,
                            trigger: {
                              ...automation.trigger,
                              config: { ...automation.trigger.config, maxMessages: parseInt(e.target.value) || 10 }
                            }
                          })}
                          className="mt-1"
                        />
                      </div>
                    </div>
                  </div>

                  <AIHelpSuggestions
                    context="trigger-slack"
                    className="mt-3"
                  />
                </div>
              )}

              <Button onClick={() => setShowTriggerConfig(false)} className="w-full mt-4">
                Done
              </Button>
            </motion.div>
          </motion.div>
        )}
      </motion.div>
    )
  }

  const renderActions = () => {
    const availableActions = [
      { id: 'ai-analyze', name: 'AI Analysis', icon: Brain, desc: 'Analyze content with AI models' },
      { id: 'send-email', name: 'Send Email', icon: Mail, desc: 'Send emails to recipients' },
      { id: 'sheets-write', name: 'Update Google Sheets', icon: SiGooglesheets, desc: 'Write data to spreadsheet' },
      { id: 'slack-message', name: 'Send Slack Message', icon: SiSlack, desc: 'Post to Slack channel' },
      { id: 'webhook-call', name: 'Call Webhook', icon: Globe, desc: 'Make HTTP request' },
      { id: 'data-transform', name: 'Transform Data', icon: Settings, desc: 'Modify or format data' },
      { id: 'filter-data', name: 'Filter Data', icon: Search, desc: 'Filter based on conditions' },
      { id: 'delay', name: 'Add Delay', icon: Clock, desc: 'Wait before next action' }
    ]

    // Filter actions based on search
    const filteredActions = availableActions.filter(action => {
      const query = actionSearchQuery.toLowerCase()
      return (
        action.name.toLowerCase().includes(query) ||
        action.desc.toLowerCase().includes(query)
      )
    })

    const addAction = (action: any) => {
      const newAction = {
        id: Date.now().toString(),
        type: action.id,
        name: action.name,
        description: action.desc,
        config: {}
      }
      setAutomation({
        ...automation,
        actions: [...automation.actions, newAction]
      })
      setShowActionConfig(automation.actions.length)
      setWantsMoreActions(false)
    }

    const removeAction = (index: number) => {
      setAutomation({
        ...automation,
        actions: automation.actions.filter((_, i) => i !== index)
      })
    }

    // If no actions yet, show initial action selection
    if (automation.actions.length === 0 || wantsMoreActions) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          <div className="text-center">
            <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
            <h3 className="text-lg font-semibold mb-2">
              {automation.actions.length === 0 ? 'What should happen next?' : 'Add another action?'}
            </h3>
            <p className="text-muted-foreground text-sm">
              {automation.actions.length === 0 
                ? 'Choose an action to perform when your trigger fires'
                : 'Chain multiple actions to create powerful workflows'}
            </p>
          </div>

          {/* Search */}
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search actions..."
                value={actionSearchQuery}
                onChange={(e) => setActionSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Available Actions */}
          {filteredActions.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No actions found matching "{actionSearchQuery}"</p>
            </div>
          ) : (
            <div className="grid gap-3 max-w-2xl mx-auto">
              {filteredActions.map((action) => (
                <Card
                  key={action.id}
                  className="cursor-pointer transition-all hover:shadow-md"
                  onClick={() => addAction(action)}
                >
                  <CardHeader className="p-4">
                    <div className="flex items-center gap-3">
                      {typeof action.icon === 'function' ? (
                        <action.icon className="h-5 w-5 text-primary" />
                      ) : (
                        <action.icon className="h-5 w-5" />
                      )}
                      <div className="flex-1">
                        <CardTitle className="text-sm">{action.name}</CardTitle>
                        <CardDescription className="text-xs mt-1">{action.desc}</CardDescription>
                      </div>
                      <Plus className="h-5 w-5 text-muted-foreground" />
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          )}

          <div className="flex gap-3 max-w-2xl mx-auto">
            <Button onClick={prevPage} variant="outline" className="w-full min-h-[44px]">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Previous
            </Button>
            {automation.actions.length > 0 && (
              <Button 
                onClick={() => setWantsMoreActions(false)} 
                variant="outline"
                className="w-full min-h-[44px]"
              >
                Continue without adding
              </Button>
            )}
          </div>
        </motion.div>
      )
    }

    // Show current actions with option to add more
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <div className="text-center">
          <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
          <h3 className="text-lg font-semibold mb-2">Your Action Chain</h3>
          <p className="text-muted-foreground text-sm">
            {automation.actions.length} action{automation.actions.length !== 1 ? 's' : ''} configured
          </p>
        </div>

        {/* Action Chain Visualization */}
        <div className="max-w-2xl mx-auto space-y-3">
          {/* Trigger */}
          <div className="flex items-center gap-3 p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg border-2 border-purple-200 dark:border-purple-800">
            <Zap className="h-5 w-5 text-purple-600" />
            <div className="flex-1">
              <p className="font-medium">Trigger: {automation.trigger.type}</p>
              <p className="text-xs text-muted-foreground">{automation.trigger.description}</p>
            </div>
          </div>

          {/* Arrow */}
          <div className="flex justify-center">
            <ArrowRight className="h-4 w-4 text-muted-foreground" />
          </div>

          {/* Actions */}
          {automation.actions.map((action, index) => (
            <div key={action.id}>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center gap-3 p-4 bg-muted/30 rounded-lg border"
              >
                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-xs font-medium">
                  {index + 1}
                </div>
                <div className="flex-1">
                  <p className="font-medium">{action.name}</p>
                  <p className="text-xs text-muted-foreground">{action.description}</p>
                </div>
                <div className="flex gap-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setShowActionConfig(index)}
                    className="h-8 px-2"
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => removeAction(index)}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </motion.div>
              {index < automation.actions.length - 1 && (
                <div className="flex justify-center my-2">
                  <ArrowRight className="h-4 w-4 text-muted-foreground" />
                </div>
              )}
            </div>
          ))}

          {/* Add Another Action Button */}
          <Button
            variant="outline"
            onClick={() => setWantsMoreActions(true)}
            className="w-full min-h-[44px]"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Another Action
          </Button>
        </div>

        <div className="flex gap-3 max-w-2xl mx-auto">
          <Button onClick={prevPage} variant="outline" className="w-full min-h-[44px]">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button 
            onClick={nextPage} 
            className="w-full min-h-[44px]"
            disabled={automation.actions.length === 0}
          >
            Continue <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>

        {/* Action Configuration Modal */}
        {showActionConfig !== null && automation.actions[showActionConfig] && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowActionConfig(null)}
          >
            <motion.div
              initial={{ scale: 0.95 }}
              animate={{ scale: 1 }}
              className="bg-card p-6 rounded-lg shadow-lg max-w-md w-full max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">
                Configure {automation.actions[showActionConfig].name}
              </h3>
              
              {automation.actions[showActionConfig].type === 'ai-analyze' && (
                <div className="space-y-4">
                  <div>
                    <Label>AI Provider</Label>
                    <Select
                      value={automation.actions[showActionConfig].config.provider || 'openai'}
                      onValueChange={(value) => {
                        const updatedActions = [...automation.actions]
                        updatedActions[showActionConfig].config.provider = value
                        setAutomation({ ...automation, actions: updatedActions })
                      }}
                    >
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="openai">OpenAI (GPT-4)</SelectItem>
                        <SelectItem value="anthropic">Anthropic (Claude)</SelectItem>
                        <SelectItem value="google">Google (Gemini)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label>Analysis Type</Label>
                    <Select
                      value={automation.actions[showActionConfig].config.analysisType || 'summarize'}
                      onValueChange={(value) => {
                        const updatedActions = [...automation.actions]
                        updatedActions[showActionConfig].config.analysisType = value
                        setAutomation({ ...automation, actions: updatedActions })
                      }}
                    >
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="summarize">Summarize</SelectItem>
                        <SelectItem value="extract">Extract Data</SelectItem>
                        <SelectItem value="classify">Classify</SelectItem>
                        <SelectItem value="sentiment">Sentiment Analysis</SelectItem>
                        <SelectItem value="custom">Custom Prompt</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  {automation.actions[showActionConfig].config.analysisType === 'custom' && (
                    <div>
                      <Label>Custom Prompt</Label>
                      <Textarea
                        placeholder="Enter your custom prompt..."
                        value={automation.actions[showActionConfig].config.customPrompt || ''}
                        onChange={(e) => {
                          const updatedActions = [...automation.actions]
                          updatedActions[showActionConfig].config.customPrompt = e.target.value
                          setAutomation({ ...automation, actions: updatedActions })
                        }}
                        className="mt-2"
                        rows={4}
                      />
                    </div>
                  )}
                </div>
              )}

              {automation.actions[showActionConfig].type === 'send-email' && (
                <div className="space-y-4">
                  <div>
                    <Label>Recipients</Label>
                    <Input
                      placeholder="<EMAIL>"
                      value={automation.actions[showActionConfig].config.recipients || ''}
                      onChange={(e) => {
                        const updatedActions = [...automation.actions]
                        updatedActions[showActionConfig].config.recipients = e.target.value
                        setAutomation({ ...automation, actions: updatedActions })
                      }}
                      className="mt-2"
                    />
                  </div>
                  <div>
                    <Label>Subject</Label>
                    <Input
                      placeholder="Email subject..."
                      value={automation.actions[showActionConfig].config.subject || ''}
                      onChange={(e) => {
                        const updatedActions = [...automation.actions]
                        updatedActions[showActionConfig].config.subject = e.target.value
                        setAutomation({ ...automation, actions: updatedActions })
                      }}
                      className="mt-2"
                    />
                  </div>
                  <div>
                    <Label>Email Provider</Label>
                    <Select
                      value={automation.actions[showActionConfig].config.emailProvider || 'gmail'}
                      onValueChange={(value) => {
                        const updatedActions = [...automation.actions]
                        updatedActions[showActionConfig].config.emailProvider = value
                        setAutomation({ ...automation, actions: updatedActions })
                      }}
                    >
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="gmail">Gmail</SelectItem>
                        <SelectItem value="sendgrid">SendGrid</SelectItem>
                        <SelectItem value="smtp">Custom SMTP</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}

              <Button onClick={() => setShowActionConfig(null)} className="w-full mt-4">
                Done
              </Button>
            </motion.div>
          </motion.div>
        )}
      </motion.div>
    )
  }

  const renderConditionsQuestion = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <GitBranch className="h-16 w-16 mx-auto mb-4 text-blue-600" />
        <h3 className="text-lg font-semibold mb-2">Do you want to add conditional logic?</h3>
        <p className="text-muted-foreground text-sm max-w-md mx-auto">
          Add if/then branches to handle different scenarios based on your data
        </p>
      </div>

      <div className="flex gap-3 max-w-md mx-auto">
        <Button
          onClick={() => {
            setWantsConditions(true)
            nextPage()
          }}
          size="lg"
          className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
        >
          Yes
        </Button>
        <Button
          onClick={() => {
            setWantsConditions(false)
            setCurrentPage('output')  // Skip directly to output
          }}
          size="lg"
          variant="outline"
          className="w-full"
        >
          No
        </Button>
      </div>

      <div className="flex justify-center mt-8">
        <Button onClick={prevPage} variant="ghost" size="sm">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
      </div>
    </motion.div>
  )

  const renderConditions = () => {
    const addBranch = () => {
      const newBranch: ConditionalBranch = {
        id: `branch-${Date.now()}`,
        name: `Branch ${automation.conditions.length + 1}`,
        conditions: [],
        actions: []
      }
      setAutomation({
        ...automation,
        conditions: [...automation.conditions, newBranch]
      })
      setEditingBranch(newBranch.id)
    }
    
    const addCondition = (branchId: string) => {
      if (!selectedSource || !selectedField) return
      
      const newCondition: Condition = {
        id: `condition-${Date.now()}`,
        source: selectedSource,
        field: selectedField,
        operator: selectedOperator,
        value: conditionValue,
        combineWith: 'and'
      }
      
      const updatedConditions = automation.conditions.map(branch => {
        if (branch.id === branchId) {
          return {
            ...branch,
            conditions: [...branch.conditions, newCondition]
          }
        }
        return branch
      })
      
      setAutomation({
        ...automation,
        conditions: updatedConditions
      })
      
      // Reset form
      setSelectedField('')
      setConditionValue('')
    }
    
    const removeBranch = (branchId: string) => {
      setAutomation({
        ...automation,
        conditions: automation.conditions.filter(b => b.id !== branchId)
      })
    }
    
    const removeCondition = (branchId: string, conditionId: string) => {
      const updatedConditions = automation.conditions.map(branch => {
        if (branch.id === branchId) {
          return {
            ...branch,
            conditions: branch.conditions.filter(c => c.id !== conditionId)
          }
        }
        return branch
      })
      
      setAutomation({
        ...automation,
        conditions: updatedConditions
      })
    }
    
    const getSourceOptions = () => {
      const options = [{ value: 'trigger', label: 'Trigger Data' }]
      automation.actions.forEach((action) => {
        options.push({ value: action.id, label: action.name })
      })
      return options
    }
    
    const getFieldOptions = (source: string) => {
      if (source === 'trigger') {
        if (automation.trigger.type === 'webhook') {
          return ['body', 'headers', 'query', 'method']
        } else if (automation.trigger.type === 'schedule') {
          return ['time', 'date', 'day']
        }
      }
      // For actions, return common fields
      return ['output', 'status', 'data', 'error']
    }
    
    const operatorLabels = {
      equals: 'Equals',
      not_equals: 'Not Equals',
      contains: 'Contains',
      not_contains: 'Does Not Contain',
      greater_than: 'Greater Than',
      less_than: 'Less Than',
      is_empty: 'Is Empty',
      is_not_empty: 'Is Not Empty'
    }
    
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <div className="text-center">
          <GitBranch className="h-16 w-16 mx-auto mb-4 text-blue-600" />
          <h3 className="text-lg font-semibold mb-2">Add Conditional Logic</h3>
          <p className="text-muted-foreground text-sm">
            Create if/then branches to handle different scenarios
          </p>
        </div>
        
        {/* Visual Flow Example */}
        <div className="max-w-3xl mx-auto mb-6">
          <Card className="bg-muted/30">
            <CardContent className="pt-6">
              <p className="text-sm font-medium mb-4">How conditional branching works:</p>
              <div className="flex items-center justify-center gap-4 text-xs">
                <div className="text-center">
                  <div className="w-20 h-10 bg-primary/10 rounded flex items-center justify-center mb-2">
                    Trigger
                  </div>
                </div>
                <ArrowRight className="h-4 w-4 text-muted-foreground" />
                <div className="text-center">
                  <div className="w-20 h-10 bg-primary/10 rounded flex items-center justify-center mb-2">
                    Actions
                  </div>
                </div>
                <ArrowRight className="h-4 w-4 text-muted-foreground" />
                <div className="text-center">
                  <div className="w-24 h-10 bg-blue-600/10 border-2 border-blue-600/50 rounded flex items-center justify-center mb-2">
                    If/Then
                  </div>
                  <div className="flex gap-2 mt-2">
                    <div className="w-20 h-8 bg-green-600/10 rounded text-[10px] flex items-center justify-center">
                      True → Action A
                    </div>
                    <div className="w-20 h-8 bg-red-600/10 rounded text-[10px] flex items-center justify-center">
                      False → Action B
                    </div>
                  </div>
                </div>
                <ArrowRight className="h-4 w-4 text-muted-foreground" />
                <div className="text-center">
                  <div className="w-20 h-10 bg-primary/10 rounded flex items-center justify-center mb-2">
                    Output
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div className="max-w-3xl mx-auto space-y-6">
          {/* Existing Branches */}
          {automation.conditions.map((branch, branchIndex) => (
            <Card key={branch.id} className="relative">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <GitBranch className="h-5 w-5 text-blue-600" />
                    <Input
                      value={branch.name}
                      onChange={(e) => {
                        const updatedConditions = automation.conditions.map(b => 
                          b.id === branch.id ? { ...b, name: e.target.value } : b
                        )
                        setAutomation({ ...automation, conditions: updatedConditions })
                      }}
                      className="max-w-[200px] font-medium"
                    />
                  </div>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => removeBranch(branch.id)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Conditions List */}
                {branch.conditions.length > 0 && (
                  <div className="space-y-2">
                    {branch.conditions.map((condition, index) => (
                      <div key={condition.id} className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg">
                        {index > 0 && (
                          <Badge variant="outline" className="shrink-0">
                            {condition.combineWith?.toUpperCase()}
                          </Badge>
                        )}
                        <div className="flex-1 flex items-center gap-2 text-sm">
                          <span className="font-medium">
                            {getSourceOptions().find(o => o.value === condition.source)?.label}
                          </span>
                          <span>→</span>
                          <span>{condition.field}</span>
                          <Badge variant="secondary">
                            {operatorLabels[condition.operator]}
                          </Badge>
                          {!['is_empty', 'is_not_empty'].includes(condition.operator) && (
                            <span className="font-mono bg-background px-2 py-1 rounded">
                              {condition.value}
                            </span>
                          )}
                        </div>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => removeCondition(branch.id, condition.id)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
                
                {/* Add Condition Form */}
                {editingBranch === branch.id && (
                  <div className="space-y-4 p-4 border-2 border-dashed rounded-lg">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>Source</Label>
                        <Select value={selectedSource} onValueChange={setSelectedSource}>
                          <SelectTrigger className="mt-2">
                            <SelectValue placeholder="Select source..." />
                          </SelectTrigger>
                          <SelectContent>
                            {getSourceOptions().map(option => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div>
                        <Label>Field</Label>
                        <Select 
                          value={selectedField} 
                          onValueChange={setSelectedField}
                          disabled={!selectedSource}
                        >
                          <SelectTrigger className="mt-2">
                            <SelectValue placeholder="Select field..." />
                          </SelectTrigger>
                          <SelectContent>
                            {selectedSource && getFieldOptions(selectedSource).map(field => (
                              <SelectItem key={field} value={field}>
                                {field}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>Operator</Label>
                        <Select value={selectedOperator} onValueChange={(v) => setSelectedOperator(v as Condition['operator'])}>
                          <SelectTrigger className="mt-2">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {Object.entries(operatorLabels).map(([value, label]) => (
                              <SelectItem key={value} value={value}>
                                {label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      
                      {!['is_empty', 'is_not_empty'].includes(selectedOperator) && (
                        <div>
                          <Label>Value</Label>
                          <Input
                            value={conditionValue}
                            onChange={(e) => setConditionValue(e.target.value)}
                            placeholder="Enter value..."
                            className="mt-2"
                          />
                        </div>
                      )}
                    </div>
                    
                    <div className="flex gap-2">
                      <Button
                        onClick={() => addCondition(branch.id)}
                        disabled={!selectedSource || !selectedField}
                        size="sm"
                      >
                        Add Condition
                      </Button>
                      <Button
                        onClick={() => setEditingBranch(null)}
                        variant="outline"
                        size="sm"
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}
                
                {editingBranch !== branch.id && branch.conditions.length > 0 && (
                  <Button
                    onClick={() => setEditingBranch(branch.id)}
                    variant="outline"
                    size="sm"
                    className="w-full"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Another Condition
                  </Button>
                )}
                
                {editingBranch !== branch.id && branch.conditions.length === 0 && (
                  <Button
                    onClick={() => setEditingBranch(branch.id)}
                    variant="outline"
                    className="w-full"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add First Condition
                  </Button>
                )}
                
                {/* Branch Actions */}
                {branch.conditions.length > 0 && (
                  <div className="mt-4 pt-4 border-t">
                    <h4 className="text-sm font-medium mb-3">Actions for this branch:</h4>
                    {branch.actions.length > 0 ? (
                      <div className="space-y-2">
                        {branch.actions.map((action, index) => (
                          <div key={action.id} className="flex items-center gap-2 p-2 bg-muted/30 rounded">
                            <span className="text-xs font-medium text-muted-foreground">{index + 1}.</span>
                            <span className="text-sm flex-1">{action.name}</span>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => {
                                const updatedConditions = automation.conditions.map(b => {
                                  if (b.id === branch.id) {
                                    return {
                                      ...b,
                                      actions: b.actions.filter(a => a.id !== action.id)
                                    }
                                  }
                                  return b
                                })
                                setAutomation({ ...automation, conditions: updatedConditions })
                              }}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">No actions yet. Actions from the main flow will be used.</p>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full mt-3"
                      onClick={() => {
                        // In a real implementation, this would open an action selector modal
                        const dummyAction = {
                          id: `action-${Date.now()}`,
                          type: 'send-email',
                          name: 'Send Email Notification',
                          description: 'Send custom email based on condition',
                          config: {}
                        }
                        const updatedConditions = automation.conditions.map(b => {
                          if (b.id === branch.id) {
                            return {
                              ...b,
                              actions: [...b.actions, dummyAction]
                            }
                          }
                          return b
                        })
                        setAutomation({ ...automation, conditions: updatedConditions })
                      }}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Branch Action
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
          
          {/* Add Branch Button */}
          <Button
            onClick={addBranch}
            variant="outline"
            className="w-full"
          >
            <GitBranch className="h-4 w-4 mr-2" />
            Add New Branch
          </Button>
          
          {/* Optional: Skip conditions */}
          <div className="text-center text-sm text-muted-foreground">
            Conditions are optional. You can skip this step if you don't need branching logic.
          </div>
        </div>
        
        <div className="flex gap-3 max-w-2xl mx-auto">
          <Button onClick={prevPage} variant="outline" className="w-full min-h-[44px]">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button 
            onClick={nextPage} 
            className="w-full min-h-[44px]"
          >
            Continue <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </motion.div>
    )
  }

  const renderOutput = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Send className="h-16 w-16 mx-auto mb-4 text-green-600" />
        <h3 className="text-lg font-semibold mb-2">How should results be delivered?</h3>
        <p className="text-muted-foreground text-sm">
          Configure the final output of your automation
        </p>
      </div>

      <div className="max-w-2xl mx-auto space-y-6">
        {/* Delivery Method */}
        <div>
          <Label>Delivery Method</Label>
          <RadioGroup
            value={automation.output.deliveryMethod}
            onValueChange={(value) => setAutomation({
              ...automation,
              output: { ...automation.output, deliveryMethod: value }
            })}
            className="mt-2"
          >
            <div className="flex items-center space-x-3 p-4 rounded-lg border hover:bg-muted/30">
              <RadioGroupItem value="email" id="delivery-email" />
              <Label htmlFor="delivery-email" className="cursor-pointer flex items-center gap-3 flex-1">
                <Mail className="h-5 w-5" />
                <div>
                  <p className="font-medium">Email</p>
                  <p className="text-sm text-muted-foreground">Send results via email</p>
                </div>
              </Label>
            </div>

            <div className="flex items-center space-x-3 p-4 rounded-lg border hover:bg-muted/30">
              <RadioGroupItem value="webhook" id="delivery-webhook" />
              <Label htmlFor="delivery-webhook" className="cursor-pointer flex items-center gap-3 flex-1">
                <Globe className="h-5 w-5" />
                <div>
                  <p className="font-medium">Webhook</p>
                  <p className="text-sm text-muted-foreground">Send to API endpoint</p>
                </div>
              </Label>
            </div>

            <div className="flex items-center space-x-3 p-4 rounded-lg border hover:bg-muted/30">
              <RadioGroupItem value="database" id="delivery-database" />
              <Label htmlFor="delivery-database" className="cursor-pointer flex items-center gap-3 flex-1">
                <FileSpreadsheet className="h-5 w-5" />
                <div>
                  <p className="font-medium">Database</p>
                  <p className="text-sm text-muted-foreground">Store in database</p>
                </div>
              </Label>
            </div>

            <div className="flex items-center space-x-3 p-4 rounded-lg border hover:bg-muted/30">
              <RadioGroupItem value="download" id="delivery-download" />
              <Label htmlFor="delivery-download" className="cursor-pointer flex items-center gap-3 flex-1">
                <Download className="h-5 w-5" />
                <div>
                  <p className="font-medium">Download</p>
                  <p className="text-sm text-muted-foreground">Download as file</p>
                </div>
              </Label>
            </div>
          </RadioGroup>
        </div>

        {/* Response Tone */}
        <div>
          <Label>Response Tone</Label>
          <Select
            value={automation.output.tone}
            onValueChange={(value) => setAutomation({
              ...automation,
              output: { ...automation.output, tone: value }
            })}
          >
            <SelectTrigger className="mt-2">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="professional">Professional</SelectItem>
              <SelectItem value="casual">Casual</SelectItem>
              <SelectItem value="friendly">Friendly</SelectItem>
              <SelectItem value="formal">Formal</SelectItem>
              <SelectItem value="technical">Technical</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Format */}
        <div>
          <Label>Output Format</Label>
          <Select
            value={automation.output.format}
            onValueChange={(value) => setAutomation({
              ...automation,
              output: { ...automation.output, format: value }
            })}
          >
            <SelectTrigger className="mt-2">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="text">Plain Text</SelectItem>
              <SelectItem value="html">HTML</SelectItem>
              <SelectItem value="markdown">Markdown</SelectItem>
              <SelectItem value="json">JSON</SelectItem>
              <SelectItem value="csv">CSV</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Delivery-specific configuration */}
        {automation.output.deliveryMethod === 'email' && (
          <div className="space-y-4 p-4 bg-muted/30 rounded-lg">
            <div>
              <Label>Email Recipients</Label>
              <Input
                placeholder="<EMAIL>, <EMAIL>"
                className="mt-2"
              />
            </div>
            <div>
              <Label>Email Subject</Label>
              <Input
                placeholder="Automation Results - {date}"
                className="mt-2"
              />
            </div>
          </div>
        )}

        {automation.output.deliveryMethod === 'webhook' && (
          <div className="p-4 bg-muted/30 rounded-lg">
            <Label>Webhook URL</Label>
            <Input
              placeholder="https://api.example.com/webhook"
              className="mt-2"
            />
          </div>
        )}
      </div>

      <div className="flex gap-3 max-w-2xl mx-auto">
        <Button onClick={prevPage} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={nextPage} className="w-full">
          Review <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderTesting = () => {
    const runTest = async () => {
      setIsRunningTest(true)
      
      // Simulate test execution
      const steps = [
        { step: 'Initialize trigger', delay: 500 },
        { step: 'Execute actions', delay: 1000 },
        ...(automation.conditions.length > 0 ? [{ step: 'Evaluate conditions', delay: 750 }] : []),
        { step: 'Generate output', delay: 800 },
        { step: 'Validate results', delay: 600 }
      ]

      const execution: typeof testResults.execution = []
      
      for (let i = 0; i < steps.length; i++) {
        execution.push({ step: steps[i].step, status: 'running' as const })
        setTestResults({
          execution: [...execution],
          validation: [],
          performance: []
        })
        
        await new Promise(resolve => setTimeout(resolve, steps[i].delay))
        
        execution[i] = { 
          step: steps[i].step, 
          status: 'success' as const, 
          time: steps[i].delay 
        }
        setTestResults({
          execution: [...execution],
          validation: [],
          performance: []
        })
      }

      // Add validation results
      const validation = [
        { passed: true, message: 'Trigger configuration is valid' },
        { passed: true, message: 'All actions have required settings' },
        { passed: automation.output.destinations.length > 0, message: 'Output destination configured' },
        { passed: true, message: 'No circular dependencies detected' }
      ]

      // Add performance metrics
      const performance = [
        { metric: 'Estimated execution time', value: '2.5 seconds', status: 'good' as const },
        { metric: 'API calls per run', value: `${automation.actions.length + 1}`, status: automation.actions.length > 5 ? 'warning' as const : 'good' as const },
        { metric: 'Data processing', value: 'Light', status: 'good' as const },
        { metric: 'Error handling', value: 'Configured', status: 'good' as const }
      ]

      setTestResults({
        execution,
        validation,
        performance
      })
      
      setIsRunningTest(false)
    }

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <Card>
          <CardHeader>
            <CardTitle>Test Your Automation</CardTitle>
            <CardDescription>
              Run a test to validate your automation before going live
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {!testResults ? (
              <div className="text-center py-8">
                <Play className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground mb-4">
                  Test your automation to see how it will perform
                </p>
                <Button 
                  onClick={runTest} 
                  disabled={isRunningTest}
                  className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
                >
                  {isRunningTest ? (
                    <>
                      <Activity className="mr-2 h-4 w-4 animate-pulse" />
                      Running Test...
                    </>
                  ) : (
                    <>
                      <Play className="mr-2 h-4 w-4" />
                      Run Test
                    </>
                  )}
                </Button>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Execution Steps */}
                <div>
                  <h4 className="text-sm font-medium mb-3">Execution Preview</h4>
                  <div className="space-y-2">
                    {testResults.execution.map((step, index) => (
                      <div key={index} className="flex items-center gap-3">
                        {step.status === 'running' ? (
                          <Activity className="h-4 w-4 text-blue-600 animate-pulse" />
                        ) : step.status === 'success' ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : step.status === 'error' ? (
                          <AlertCircle className="h-4 w-4 text-red-600" />
                        ) : (
                          <div className="h-4 w-4 rounded-full border-2 border-muted" />
                        )}
                        <span className="text-sm flex-1">{step.step}</span>
                        {step.time && (
                          <span className="text-xs text-muted-foreground">{step.time}ms</span>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Validation Results */}
                <div>
                  <h4 className="text-sm font-medium mb-3">Validation Results</h4>
                  <div className="space-y-2">
                    {testResults.validation.map((result, index) => (
                      <div key={index} className="flex items-center gap-3">
                        {result.passed ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : (
                          <AlertCircle className="h-4 w-4 text-amber-600" />
                        )}
                        <span className="text-sm">{result.message}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Performance Impact */}
                <div>
                  <h4 className="text-sm font-medium mb-3">Performance Impact</h4>
                  <div className="grid grid-cols-2 gap-3">
                    {testResults.performance.map((metric, index) => (
                      <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                        <span className="text-sm">{metric.metric}</span>
                        <Badge 
                          variant={metric.status === 'good' ? 'default' : metric.status === 'warning' ? 'secondary' : 'destructive'}
                        >
                          {metric.value}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <div className="flex items-center gap-2 text-sm text-green-600">
                    <CheckCircle className="h-4 w-4" />
                    <span className="font-medium">All tests passed! Your automation is ready to go live.</span>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <div className="flex gap-3 max-w-2xl mx-auto">
          <Button onClick={prevPage} variant="outline" className="w-full">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button 
            onClick={nextPage} 
            className="w-full"
            disabled={!testResults}
          >
            Continue to Review <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </motion.div>
    )
  }

  const renderReview = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Check className="h-16 w-16 mx-auto mb-4 text-green-600" />
        <h3 className="text-lg font-semibold mb-2">Review Your Automation</h3>
        <p className="text-muted-foreground text-sm">
          Make sure everything looks good before creating
        </p>
      </div>

      <div className="space-y-4 max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Automation Name</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm">{automation.name}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Trigger</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm capitalize">{automation.trigger.type}</p>
            <p className="text-xs text-muted-foreground mt-1">{automation.trigger.description}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Actions ({automation.actions.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {automation.actions.map((action, index) => (
                <div key={action.id} className="flex items-center gap-2">
                  <span className="text-xs font-medium text-muted-foreground">{index + 1}.</span>
                  <span className="text-sm">{action.name}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {automation.conditions.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Conditional Branches ({automation.conditions.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {automation.conditions.map((branch) => (
                  <div key={branch.id} className="space-y-1">
                    <p className="text-sm font-medium">{branch.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {branch.conditions.length} condition{branch.conditions.length !== 1 ? 's' : ''}
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Output</CardTitle>
          </CardHeader>
          <CardContent className="space-y-1">
            <p className="text-sm">Delivery: {automation.output.deliveryMethod}</p>
            <p className="text-sm">Tone: {automation.output.tone}</p>
            <p className="text-sm">Format: {automation.output.format}</p>
          </CardContent>
        </Card>
      </div>

      <div className="flex gap-3 max-w-2xl mx-auto">
        <Button onClick={prevPage} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={async () => {
            setIsSaving(true)
            try {
              // Prepare automation data for API to match database schema
              const automationData = {
                name: automation.name,
                description: `Automation with ${automation.actions.length} actions`,
                trigger: automation.trigger, // Send full trigger object, not just type
                actions: automation.actions,
                conditions: automation.conditions.length > 0 ? automation.conditions : null,
                status: 'active' as const
              }
              
              // Save to database
              const response = await apiRequest('POST', '/api/automations', automationData)
              
              // Invalidate cache to refresh the automations list
              queryClient.invalidateQueries({ queryKey: ['/api/automations'] })
              
              toast({
                title: "Automation Created!",
                description: `Your automation "${automation.name}" has been saved successfully.`,
              })
              setHasUnsavedChanges(false) // Clear unsaved changes flag
              setCurrentPage('complete')
            } catch (error: any) {
              console.error('Failed to save automation:', error)
              toast({
                title: "Failed to Create Automation",
                description: error.message || "Something went wrong. Please try again.",
                variant: "destructive"
              })
            } finally {
              setIsSaving(false)
            }
          }} 
          className="w-full"
          disabled={isSaving}
        >
          {isSaving ? (
            <>
              <Activity className="mr-2 h-4 w-4 animate-spin" />
              Creating...
            </>
          ) : (
            <>
              Create Automation <Check className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>
      </div>
    </motion.div>
  )

  const renderComplete = () => (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className="text-center space-y-6"
    >
      <div className="h-20 w-20 bg-green-100 rounded-full flex items-center justify-center mx-auto">
        <Check className="h-10 w-10 text-green-600" />
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-2">Automation Created!</h2>
        <p className="text-muted-foreground">
          Your automation "{automation.name}" is ready to use
        </p>
      </div>

      <div className="flex gap-3 max-w-md mx-auto">
        <Button
          onClick={() => setLocation('/dashboard/automations')}
          variant="outline"
          className="w-full"
        >
          View Automations
        </Button>
        <Button
          onClick={() => setLocation('/dashboard')}
          className="w-full"
        >
          Go to Dashboard
        </Button>
      </div>
    </motion.div>
  )

  const renderPage = () => {
    switch (currentPage) {
      case 'intro': return renderIntro()
      case 'naming': return renderNaming()
      case 'trigger': return renderTrigger()
      case 'actions': return renderActions()
      case 'conditions-question': return renderConditionsQuestion()
      case 'conditions': return renderConditions()
      case 'output': return renderOutput()
      case 'testing': return renderTesting()
      case 'review': return renderReview()
      case 'complete': return renderComplete()
      default: return null
    }
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container max-w-4xl mx-auto py-8 px-4">
        {currentPage !== 'intro' && currentPage !== 'complete' && (
          <Progress value={progress} className="mb-8" />
        )}
        {renderPage()}
      </div>
    </div>
  )
}