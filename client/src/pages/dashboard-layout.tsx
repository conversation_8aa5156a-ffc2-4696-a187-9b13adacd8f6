import { useState, useEffect } from 'react'
import { useLocation, Link } from 'wouter'
import { useAuth } from '@/contexts/AuthContext'
import { useTheme } from '@/contexts/ThemeContext'
import { 
  Menu, 
  X, 
  LayoutTemplate, 
  Zap, 
  Settings, 
  Activity,
  Users,
  Sun,
  Moon,
  Monitor,
  LogOut,
  ChevronLeft,
  Search,
  Plus,
  Key,
  Sparkles,
  HelpCircle,
  BookOpen,
  MessageCircle,
  ExternalLink
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { cn } from '@/lib/utils'

// Extend window object for Intercom
declare global {
  interface Window {
    Intercom?: any
  }
}

interface NavItem {
  name: string
  href: string
  icon: React.ComponentType<{ className?: string }>
}

const navItems: NavItem[] = [
  { name: 'Browse Templates', href: '/dashboard/browse-templates', icon: LayoutTemplate },
  { name: 'My Automations', href: '/dashboard/automations', icon: Zap },
  { name: 'Activity Logs', href: '/dashboard/logs', icon: Activity },

  { name: 'Credentials', href: '/dashboard/credentials', icon: Key },
  { name: 'Team', href: '/dashboard/team', icon: Users },
  { name: 'Settings', href: '/dashboard/settings', icon: Settings },
]

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [collapsed, setCollapsed] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [helpOpen, setHelpOpen] = useState(false)
  const [location, setLocation] = useLocation()
  const { user, signOut } = useAuth()
  const { theme, setTheme } = useTheme()

  // Close sidebar on mobile when route changes
  useEffect(() => {
    setSidebarOpen(false)
  }, [location])

  // Get user initials for avatar
  const userInitials = user?.user_metadata?.full_name
    ?.split(' ')
    .map((n: string) => n[0])
    .join('')
    .toUpperCase() || user?.email?.[0]?.toUpperCase() || 'U'

  const handleSignOut = async () => {
    await signOut()
    setLocation('/auth')
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <Button
          variant="outline"
          size="icon"
          onClick={() => setSidebarOpen(!sidebarOpen)}
          className="bg-background"
        >
          {sidebarOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
        </Button>
      </div>

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed inset-y-0 left-0 z-40 flex flex-col bg-card border-r transition-all duration-300 animate-slide-in-left",
          collapsed ? "w-16" : "w-64",
          sidebarOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"
        )}
      >
        {/* Logo */}
        <div className={cn(
          "flex h-16 items-center border-b relative",
          collapsed ? "justify-center px-2" : "justify-between px-4"
        )}>
          <Link 
            href="/dashboard"
            className={cn(
              "flex items-center gap-2 font-semibold text-xl hover-scale",
              collapsed && "justify-center"
            )}
          >
            <div className="w-8 h-8 bg-[#155DB8] rounded-lg flex items-center justify-center text-white font-bold animate-scale-in hover-glow">
              F
            </div>
            {!collapsed && <span className="animate-fade-in">Filorina</span>}
          </Link>
          
          {/* Collapse button - positioned differently based on state */}
          {collapsed ? (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setCollapsed(!collapsed)}
              className="hidden lg:flex absolute -right-3 top-1/2 transform -translate-y-1/2 bg-card border border-border rounded-full shadow-sm hover:bg-accent z-10"
              style={{ width: '24px', height: '24px' }}
            >
              <ChevronLeft className="h-3 w-3 rotate-180" />
            </Button>
          ) : (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setCollapsed(!collapsed)}
              className="hidden lg:flex"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2">
          {navItems.map((item, index) => {
            const Icon = item.icon
            const isActive = location.startsWith(item.href)
            
            return (
              <Link 
                key={item.href} 
                href={item.href}
                className={cn(
                  "flex items-center gap-3 px-3 py-2 rounded-lg transition-colors",
                  isActive 
                    ? "bg-[#155DB8] text-white hover:bg-[#155DB8]/90" 
                    : "text-muted-foreground hover:bg-accent hover:text-foreground",
                  collapsed && "justify-center"
                )}
                title={collapsed ? item.name : undefined}
              >
                <Icon className="h-5 w-5 flex-shrink-0" />
                {!collapsed && <span>{item.name}</span>}
              </Link>
            )
          })}
        </nav>

        {/* Help section */}
        <div className="p-4 border-t">
          <Dialog open={helpOpen} onOpenChange={setHelpOpen}>
            <DialogTrigger asChild>
              <Button
                variant="ghost"
                className={cn(
                  "w-full justify-start p-2 text-muted-foreground hover:bg-accent hover:text-foreground",
                  collapsed && "justify-center"
                )}
              >
                <HelpCircle className="h-5 w-5 flex-shrink-0" />
                {!collapsed && <span className="ml-3">Help & Support</span>}
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <HelpCircle className="h-5 w-5" />
                  Help & Support
                </DialogTitle>
                <DialogDescription>
                  Get help with Filorina automation platform
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-3">
                {/* Documentation */}
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => {
                    window.open('https://docs.filorina.com', '_blank')
                    setHelpOpen(false)
                  }}
                >
                  <BookOpen className="h-4 w-4 mr-3" />
                  <div className="text-left">
                    <div className="font-medium">Documentation</div>
                    <div className="text-xs text-muted-foreground">
                      Guides, tutorials, and API reference
                    </div>
                  </div>
                  <ExternalLink className="h-3 w-3 ml-auto" />
                </Button>

                {/* Live Chat */}
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => {
                    // Initialize live chat (could be Intercom, Zendesk, etc.)
                    if (window.Intercom) {
                      window.Intercom('show')
                    } else {
                      // Fallback to mailto or support system
                      window.open('mailto:<EMAIL>?subject=Support Request', '_blank')
                    }
                    setHelpOpen(false)
                  }}
                >
                  <MessageCircle className="h-4 w-4 mr-3" />
                  <div className="text-left">
                    <div className="font-medium">Live Chat</div>
                    <div className="text-xs text-muted-foreground">
                      Chat with our support team
                    </div>
                  </div>
                </Button>

                {/* Contact Support */}
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => {
                    window.open('mailto:<EMAIL>?subject=Support Request&body=Describe your issue here...', '_blank')
                    setHelpOpen(false)
                  }}
                >
                  <ExternalLink className="h-4 w-4 mr-3" />
                  <div className="text-left">
                    <div className="font-medium">Email Support</div>
                    <div className="text-xs text-muted-foreground">
                      Send us an email for detailed help
                    </div>
                  </div>
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* User section */}
        <div className="p-4 border-t">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className={cn(
                  "w-full justify-start p-2",
                  collapsed && "justify-center"
                )}
              >
                <Avatar className="h-8 w-8">
                  <AvatarImage src={user?.user_metadata?.avatar_url} />
                  <AvatarFallback>{userInitials}</AvatarFallback>
                </Avatar>
                {!collapsed && (
                  <div className="ml-3 text-left overflow-hidden">
                    <p className="text-sm font-medium truncate">
                      {user?.user_metadata?.full_name || user?.email}
                    </p>
                    <p className="text-xs text-muted-foreground truncate">
                      {user?.email}
                    </p>
                  </div>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuItem onClick={handleSignOut}>
                <LogOut className="mr-2 h-4 w-4" />
                Sign out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </aside>

      {/* Main content */}
      <div className={cn(
        "transition-all duration-300",
        collapsed ? "lg:ml-16" : "lg:ml-64"
      )}>
        {/* Theme Header */}
        <div className="h-16 bg-gradient-to-r from-transparent via-card/50 to-card border-b backdrop-blur-sm flex items-center justify-end px-3 sm:px-4 md:px-6 lg:px-8">
          <div className="flex items-center gap-2 sm:gap-3">
            {/* Create Automation Button */}
            <Button 
              onClick={() => setLocation('/dashboard/automations/new')}
              className="bg-[#155DB8] hover:bg-[#155DB8]/90 text-white text-xs sm:text-sm"
              size="sm"
            >
              <Plus className="h-3 w-3 sm:h-4 sm:w-4 mr-1.5 sm:mr-2" />
              Create Automation
            </Button>

            {/* Theme Toggle */}
            <div className="relative">
              <Button 
                variant="outline" 
                size="icon"
                onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
                className="relative bg-card hover:bg-accent border-muted-foreground/20 transition-colors h-8 w-8 sm:h-9 sm:w-9"
              >
                {theme === 'dark' ? (
                  <Sun className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-yellow-500" />
                ) : (
                  <Moon className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-blue-600" />
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="p-3 sm:p-4 lg:p-6">
          {children}
        </main>
      </div>

      {/* Mobile overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-30 bg-black/50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

    </div>
  )
}