import { useState } from 'react'
import { motion } from 'framer-motion'
import { useLocation } from 'wouter'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Progress } from "@/components/ui/progress"
import { 
  FileSpreadsheet, Settings, ArrowRight, ChevronLeft, Check, 
  Calendar, Clock, Mail, Brain, BarChart3, TrendingUp, 
  PieChart, Activity, Zap, Plus, X, Search, MessageSquare
} from 'lucide-react'
import { SiGooglesheets, SiGmail, SiMicrosoft } from 'react-icons/si'
import { ActionsSelector } from '@/components/actions-selector'
import { useToast } from "@/hooks/use-toast"

type StepType = 'intro' | 'naming' | 'google-auth' | 'sheets-config' | 'report-settings' | 'ai-formatting' | 'schedule-delivery' | 'actions-question' | 'actions' | 'review' | 'complete'

interface ReportsConfig {
  name: string
  googleAuth: {
    isAuthenticated: boolean
    userEmail: string
    userName: string
    accountId: string
  }
  sheetsConfig: {
    spreadsheetId: string
    sheetName: string
    dataRange: string
    reportType: 'summary' | 'dashboard' | 'trend' | 'comparison'
    keyMetrics: string[]
  }
  reportSettings: {
    reportName: string
    description: string
    includeCharts: boolean
    chartTypes: string[]
    dateRange: string
    groupBy: string
  }
  aiFormatting: {
    provider: 'openai' | 'anthropic' | 'google'
    model: string
    style: 'executive' | 'detailed' | 'visual' | 'analytical'
    tone: 'professional' | 'casual' | 'technical'
    includeInsights: boolean
    customPrompt: string
  }
  scheduleDelivery: {
    frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly'
    dayOfWeek: string
    time: string
    timezone: string
    recipients: string[]
    subject: string
    format: 'pdf' | 'html' | 'excel'
  }
  selectedActionsList: Array<{
    id: string
    type: string
    name: string
    description: string
    config: Record<string, any>
  }>
  wantsActions: boolean | null
}

const defaultConfig: ReportsConfig = {
  name: '',
  googleAuth: {
    isAuthenticated: false,
    userEmail: '',
    userName: '',
    accountId: ''
  },
  sheetsConfig: {
    spreadsheetId: '',
    sheetName: '',
    dataRange: 'A1:Z1000',
    reportType: 'summary',
    keyMetrics: []
  },
  reportSettings: {
    reportName: '',
    description: '',
    includeCharts: true,
    chartTypes: ['bar', 'line'],
    dateRange: 'last_week',
    groupBy: 'date'
  },
  aiFormatting: {
    provider: 'openai',
    model: 'gpt-4o',
    style: 'executive',
    tone: 'professional',
    includeInsights: true,
    customPrompt: ''
  },
  scheduleDelivery: {
    frequency: 'weekly',
    dayOfWeek: 'monday',
    time: '09:00',
    timezone: 'UTC',
    recipients: [],
    subject: 'Weekly Report - {report_name}',
    format: 'pdf'
  },
  selectedActionsList: [],
  wantsActions: null
}

export default function GoogleSheetsReportsTemplate() {
  const [, setLocation] = useLocation()
  const [currentStep, setCurrentStep] = useState<StepType>('intro')
  const [config, setConfig] = useState<ReportsConfig>(defaultConfig)
  const [showActionConfig, setShowActionConfig] = useState<number | null>(null)
  const [actionSearchQuery, setActionSearchQuery] = useState('')
  const [wantsMoreActions, setWantsMoreActions] = useState(false)
  const { toast } = useToast()

  const stepInfo = {
    intro: { 
      title: 'Google Sheets Reports', 
      subtitle: 'Automated report generation and delivery',
      icon: FileSpreadsheet,
      progress: 10
    },
    naming: { 
      title: 'Name Your Automation', 
      subtitle: 'Give your report automation a name',
      icon: Settings,
      progress: 15
    },
    'google-auth': { 
      title: 'Google Authentication', 
      subtitle: 'Connect your Google account',
      icon: SiGooglesheets,
      progress: 25
    },
    'sheets-config': { 
      title: 'Sheets Configuration', 
      subtitle: 'Select data source and metrics',
      icon: FileSpreadsheet,
      progress: 40
    },
    'report-settings': { 
      title: 'Report Settings', 
      subtitle: 'Configure report structure and content',
      icon: BarChart3,
      progress: 50
    },
    'ai-formatting': { 
      title: 'AI Formatting', 
      subtitle: 'Set up intelligent report formatting',
      icon: Brain,
      progress: 60
    },
    'schedule-delivery': { 
      title: 'Schedule & Delivery', 
      subtitle: 'Set when and how to send reports',
      icon: Calendar,
      progress: 70
    },
    'actions-question': {
      title: 'Additional Actions',
      subtitle: 'Extend your automation capabilities',
      icon: Zap,
      progress: 75
    },
    'actions': { 
      title: 'Select Actions', 
      subtitle: 'Define what actions to take',
      icon: Zap, 
      progress: 85 
    },
    review: { 
      title: 'Review Configuration', 
      subtitle: 'Confirm your workflow settings',
      icon: Settings,
      progress: 95
    },
    complete: { 
      title: 'Automation Created!', 
      subtitle: 'Your automation is ready to use',
      icon: Check,
      progress: 100
    }
  }

  const nextStep = () => {
    const steps: StepType[] = ['intro', 'naming', 'google-auth', 'sheets-config', 'report-settings', 'ai-formatting', 'schedule-delivery', 'actions-question', 'actions', 'review', 'complete']
    const currentIndex = steps.indexOf(currentStep)
    
    if (currentStep === 'actions-question' && config.wantsActions === false) {
      setCurrentStep('review')
    } else if (currentStep === 'actions-question' && config.wantsActions === true) {
      setWantsMoreActions(true)
      setCurrentStep('actions')
    } else if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1])
    }
  }

  const prevStep = () => {
    const steps: StepType[] = ['intro', 'naming', 'google-auth', 'sheets-config', 'report-settings', 'ai-formatting', 'schedule-delivery', 'actions-question', 'actions', 'review', 'complete']
    const currentIndex = steps.indexOf(currentStep)
    
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1])
    }
  }

  const renderIntro = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center mx-auto">
        <FileSpreadsheet className="h-10 w-10 text-white" />
      </div>
      <div>
        <h2 className="text-2xl font-bold mb-2">Google Sheets Recurring Reports</h2>
        <p className="text-muted-foreground max-w-md mx-auto">
          Automatically generate professional reports from your Google Sheets data. 
          AI formats and enhances your reports, then delivers them on schedule via email.
        </p>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <SiGooglesheets className="h-8 w-8 mx-auto mb-2 text-green-600" />
          <p className="text-xs font-medium">Google Sheets</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <BarChart3 className="h-8 w-8 mx-auto mb-2 text-blue-600" />
          <p className="text-xs font-medium">Auto Reports</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Brain className="h-8 w-8 mx-auto mb-2 text-purple-600" />
          <p className="text-xs font-medium">AI Formatting</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Mail className="h-8 w-8 mx-auto mb-2 text-orange-600" />
          <p className="text-xs font-medium">Email Delivery</p>
        </div>
      </div>

      <Button 
        onClick={nextStep} 
        size="lg"
        className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
      >
        Start Setup
        <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
    </motion.div>
  )

  const renderNaming = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Settings className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h2 className="text-xl font-semibold mb-2">Name Your Automation</h2>
        <p className="text-muted-foreground">
          Give your automation a memorable name that describes what it does
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="automation-name">Automation Name *</Label>
          <Input
            id="automation-name"
            placeholder="e.g., Weekly Sales Performance Reports"
            value={config.name}
            onChange={(e) => setConfig({ ...config, name: e.target.value })}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && config.name) {
                nextStep()
              }
            }}
            className="mt-2"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Choose a descriptive name you'll easily recognize
          </p>
        </div>
      </div>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          disabled={!config.name}
          className="w-full"
        >
          Continue to Google Authentication
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderGoogleAuth = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <SiGooglesheets className="h-16 w-16 mx-auto mb-4 text-green-600" />
        <h2 className="text-xl font-semibold mb-2">Google Authentication</h2>
        <p className="text-muted-foreground">
          Connect your Google account to access Google Sheets
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label>Authentication Type *</Label>
          <RadioGroup value="google" className="mt-2">
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="google" id="google" />
              <Label htmlFor="google">Google Sign-In (Required for Google Sheets)</Label>
            </div>
          </RadioGroup>
        </div>

        {config.googleAuth.isAuthenticated ? (
          <div className="p-4 border rounded-lg bg-green-50 dark:bg-green-950/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Google account: {config.googleAuth.userName} ({config.googleAuth.userEmail})</p>
                <Badge variant="secondary" className="mt-1">Connected</Badge>
              </div>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setConfig({
                  ...config,
                  googleAuth: { ...config.googleAuth, isAuthenticated: false }
                })}
              >
                Disconnect
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            <p className="text-sm text-muted-foreground">
              Note: Select existing Google account from below or Sign in with a different account
            </p>
            <Button 
              onClick={() => {
                setConfig({
                  ...config,
                  googleAuth: {
                    isAuthenticated: true,
                    userEmail: '<EMAIL>',
                    userName: 'John Doe',
                    accountId: 'google_123'
                  }
                })
              }}
              className="w-full"
              variant="outline"
            >
              <SiGooglesheets className="mr-2 h-4 w-4" />
              Sign in with Google
            </Button>
          </div>
        )}
      </div>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          disabled={!config.googleAuth.isAuthenticated}
          className="w-full"
        >
          Continue to Sheets Configuration
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderSheetsConfig = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <FileSpreadsheet className="h-16 w-16 mx-auto mb-4 text-green-600" />
        <h2 className="text-xl font-semibold mb-2">Sheets Configuration</h2>
        <p className="text-muted-foreground">
          Select your Google Sheets data source and key metrics
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="spreadsheet-id">Spreadsheet ID *</Label>
          <Input
            id="spreadsheet-id"
            placeholder="1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
            value={config.sheetsConfig.spreadsheetId}
            onChange={(e) => setConfig({
              ...config,
              sheetsConfig: { ...config.sheetsConfig, spreadsheetId: e.target.value }
            })}
            className="mt-2"
          />
        </div>

        <div>
          <Label htmlFor="sheet-name">Sheet Name *</Label>
          <Input
            id="sheet-name"
            placeholder="Sales Data"
            value={config.sheetsConfig.sheetName}
            onChange={(e) => setConfig({
              ...config,
              sheetsConfig: { ...config.sheetsConfig, sheetName: e.target.value }
            })}
            className="mt-2"
          />
        </div>

        <div>
          <Label htmlFor="data-range">Data Range *</Label>
          <Input
            id="data-range"
            placeholder="A1:Z1000"
            value={config.sheetsConfig.dataRange}
            onChange={(e) => setConfig({
              ...config,
              sheetsConfig: { ...config.sheetsConfig, dataRange: e.target.value }
            })}
            className="mt-2"
          />
        </div>

        <div>
          <Label>Report Type *</Label>
          <Select 
            value={config.sheetsConfig.reportType} 
            onValueChange={(value: 'summary' | 'dashboard' | 'trend' | 'comparison') => 
              setConfig({
                ...config,
                sheetsConfig: { ...config.sheetsConfig, reportType: value }
              })
            }
          >
            <SelectTrigger className="mt-2">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="summary">Summary Report</SelectItem>
              <SelectItem value="dashboard">Performance Dashboard</SelectItem>
              <SelectItem value="trend">Trend Analysis</SelectItem>
              <SelectItem value="comparison">Comparison Report</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="key-metrics">Key Metrics (comma-separated)</Label>
          <Input
            id="key-metrics"
            placeholder="Revenue, Orders, Conversion Rate"
            value={config.sheetsConfig.keyMetrics.join(', ')}
            onChange={(e) => setConfig({
              ...config,
              sheetsConfig: { 
                ...config.sheetsConfig, 
                keyMetrics: e.target.value.split(',').map(m => m.trim()).filter(Boolean)
              }
            })}
            className="mt-2"
          />
        </div>
      </div>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          disabled={!config.sheetsConfig.spreadsheetId || !config.sheetsConfig.sheetName}
          className="w-full"
        >
          Continue to Report Settings
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderReportSettings = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <BarChart3 className="h-16 w-16 mx-auto mb-4 text-blue-600" />
        <h2 className="text-xl font-semibold mb-2">Report Settings</h2>
        <p className="text-muted-foreground">
          Configure how your reports will be structured and presented
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="report-name">Report Title *</Label>
          <Input
            id="report-name"
            placeholder="Weekly Sales Performance Report"
            value={config.reportSettings.reportName}
            onChange={(e) => setConfig({
              ...config,
              reportSettings: { ...config.reportSettings, reportName: e.target.value }
            })}
            className="mt-2"
          />
        </div>

        <div>
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            placeholder="Comprehensive analysis of weekly sales metrics and performance indicators"
            value={config.reportSettings.description}
            onChange={(e) => setConfig({
              ...config,
              reportSettings: { ...config.reportSettings, description: e.target.value }
            })}
            className="mt-2"
          />
        </div>

        <div>
          <Label>Date Range *</Label>
          <Select 
            value={config.reportSettings.dateRange} 
            onValueChange={(value) => 
              setConfig({
                ...config,
                reportSettings: { ...config.reportSettings, dateRange: value }
              })
            }
          >
            <SelectTrigger className="mt-2">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="last_week">Last Week</SelectItem>
              <SelectItem value="last_month">Last Month</SelectItem>
              <SelectItem value="last_quarter">Last Quarter</SelectItem>
              <SelectItem value="custom">Custom Range</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="include-charts"
            checked={config.reportSettings.includeCharts}
            onCheckedChange={(checked) => setConfig({
              ...config,
              reportSettings: { ...config.reportSettings, includeCharts: !!checked }
            })}
          />
          <Label htmlFor="include-charts">Include Charts and Visualizations</Label>
        </div>

        {config.reportSettings.includeCharts && (
          <div>
            <Label>Chart Types</Label>
            <div className="grid grid-cols-2 gap-2 mt-2">
              {['bar', 'line', 'pie', 'area'].map((type) => (
                <div key={type} className="flex items-center space-x-2">
                  <Checkbox
                    id={type}
                    checked={config.reportSettings.chartTypes.includes(type)}
                    onCheckedChange={(checked) => {
                      const types = checked 
                        ? [...config.reportSettings.chartTypes, type]
                        : config.reportSettings.chartTypes.filter(t => t !== type)
                      setConfig({
                        ...config,
                        reportSettings: { ...config.reportSettings, chartTypes: types }
                      })
                    }}
                  />
                  <Label htmlFor={type} className="capitalize">{type} Chart</Label>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          disabled={!config.reportSettings.reportName}
          className="w-full"
        >
          Continue to AI Formatting
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderAIFormatting = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Brain className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h2 className="text-xl font-semibold mb-2">AI Formatting</h2>
        <p className="text-muted-foreground">
          Configure how AI will format and enhance your reports
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label>AI Provider *</Label>
          <Select 
            value={config.aiFormatting.provider} 
            onValueChange={(value: 'openai' | 'anthropic' | 'google') => 
              setConfig({
                ...config,
                aiFormatting: { ...config.aiFormatting, provider: value }
              })
            }
          >
            <SelectTrigger className="mt-2">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="openai">OpenAI (GPT-4o)</SelectItem>
              <SelectItem value="anthropic">Anthropic (Claude)</SelectItem>
              <SelectItem value="google">Google (Gemini)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label>Report Style *</Label>
          <Select 
            value={config.aiFormatting.style} 
            onValueChange={(value: 'executive' | 'detailed' | 'visual' | 'analytical') => 
              setConfig({
                ...config,
                aiFormatting: { ...config.aiFormatting, style: value }
              })
            }
          >
            <SelectTrigger className="mt-2">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="executive">Executive Summary</SelectItem>
              <SelectItem value="detailed">Detailed Analysis</SelectItem>
              <SelectItem value="visual">Visual Dashboard</SelectItem>
              <SelectItem value="analytical">Analytical Deep-dive</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label>Tone *</Label>
          <Select 
            value={config.aiFormatting.tone} 
            onValueChange={(value: 'professional' | 'casual' | 'technical') => 
              setConfig({
                ...config,
                aiFormatting: { ...config.aiFormatting, tone: value }
              })
            }
          >
            <SelectTrigger className="mt-2">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="professional">Professional</SelectItem>
              <SelectItem value="casual">Casual</SelectItem>
              <SelectItem value="technical">Technical</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="include-insights"
            checked={config.aiFormatting.includeInsights}
            onCheckedChange={(checked) => setConfig({
              ...config,
              aiFormatting: { ...config.aiFormatting, includeInsights: !!checked }
            })}
          />
          <Label htmlFor="include-insights">Include AI-powered insights and recommendations</Label>
        </div>

        <div>
          <Label htmlFor="custom-prompt">Custom AI Instructions (Optional)</Label>
          <Textarea
            id="custom-prompt"
            placeholder="Add any specific instructions for how AI should format your reports..."
            value={config.aiFormatting.customPrompt}
            onChange={(e) => setConfig({
              ...config,
              aiFormatting: { ...config.aiFormatting, customPrompt: e.target.value }
            })}
            className="mt-2"
          />
        </div>
      </div>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          className="w-full"
        >
          Continue to Schedule & Delivery
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderScheduleDelivery = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Calendar className="h-16 w-16 mx-auto mb-4 text-orange-600" />
        <h2 className="text-xl font-semibold mb-2">Schedule & Delivery</h2>
        <p className="text-muted-foreground">
          Set when and how your reports will be generated and delivered
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label>Frequency *</Label>
          <Select 
            value={config.scheduleDelivery.frequency} 
            onValueChange={(value: 'daily' | 'weekly' | 'monthly' | 'quarterly') => 
              setConfig({
                ...config,
                scheduleDelivery: { ...config.scheduleDelivery, frequency: value }
              })
            }
          >
            <SelectTrigger className="mt-2">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">Daily</SelectItem>
              <SelectItem value="weekly">Weekly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {config.scheduleDelivery.frequency === 'weekly' && (
          <div>
            <Label>Day of Week *</Label>
            <Select 
              value={config.scheduleDelivery.dayOfWeek} 
              onValueChange={(value) => 
                setConfig({
                  ...config,
                  scheduleDelivery: { ...config.scheduleDelivery, dayOfWeek: value }
                })
              }
            >
              <SelectTrigger className="mt-2">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="monday">Monday</SelectItem>
                <SelectItem value="tuesday">Tuesday</SelectItem>
                <SelectItem value="wednesday">Wednesday</SelectItem>
                <SelectItem value="thursday">Thursday</SelectItem>
                <SelectItem value="friday">Friday</SelectItem>
                <SelectItem value="saturday">Saturday</SelectItem>
                <SelectItem value="sunday">Sunday</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        <div>
          <Label htmlFor="time">Time *</Label>
          <Input
            id="time"
            type="time"
            value={config.scheduleDelivery.time}
            onChange={(e) => setConfig({
              ...config,
              scheduleDelivery: { ...config.scheduleDelivery, time: e.target.value }
            })}
            className="mt-2"
          />
        </div>

        <div>
          <Label htmlFor="recipients">Email Recipients *</Label>
          <Textarea
            id="recipients"
            placeholder="Enter email addresses, one per line"
            value={config.scheduleDelivery.recipients.join('\n')}
            onChange={(e) => setConfig({
              ...config,
              scheduleDelivery: { 
                ...config.scheduleDelivery, 
                recipients: e.target.value.split('\n').filter(Boolean)
              }
            })}
            className="mt-2"
          />
        </div>

        <div>
          <Label htmlFor="subject">Email Subject *</Label>
          <Input
            id="subject"
            placeholder="Weekly Report - {report_name}"
            value={config.scheduleDelivery.subject}
            onChange={(e) => setConfig({
              ...config,
              scheduleDelivery: { ...config.scheduleDelivery, subject: e.target.value }
            })}
            className="mt-2"
          />
        </div>

        <div>
          <Label>Report Format *</Label>
          <Select 
            value={config.scheduleDelivery.format} 
            onValueChange={(value: 'pdf' | 'html' | 'excel') => 
              setConfig({
                ...config,
                scheduleDelivery: { ...config.scheduleDelivery, format: value }
              })
            }
          >
            <SelectTrigger className="mt-2">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pdf">PDF Document</SelectItem>
              <SelectItem value="html">HTML Email</SelectItem>
              <SelectItem value="excel">Excel Spreadsheet</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          disabled={!config.scheduleDelivery.recipients.length || !config.scheduleDelivery.subject}
          className="w-full"
        >
          Continue
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderActionsQuestion = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Zap className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h3 className="text-lg font-semibold mb-2">Do you want to add any actions?</h3>
        <p className="text-muted-foreground text-sm mb-6">
          Extend your automation with additional actions (optional)
        </p>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-6">
        <Button
          variant={config.wantsActions === true ? "default" : "outline"}
          size="lg"
          onClick={() => {
            setConfig({ ...config, wantsActions: true })
            nextStep()
          }}
          className="h-auto py-6"
        >
          <div className="flex flex-col items-center gap-2">
            <Check className="h-8 w-8" />
            <span>Yes, I want to add actions</span>
          </div>
        </Button>
        <Button
          variant={config.wantsActions === false ? "default" : "outline"}
          size="lg"
          onClick={() => {
            setConfig({ ...config, wantsActions: false })
            setCurrentStep('review')  // Skip directly to review
          }}
          className="h-auto py-6"
        >
          <div className="flex flex-col items-center gap-2">
            <ArrowRight className="h-8 w-8" />
            <span>No, continue without</span>
          </div>
        </Button>
      </div>

      <div>
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
      </div>
    </motion.div>
  )

  const renderActions = () => {
    const selectedActionsList = config.selectedActionsList || []

    const availableActionsList = [
      { id: 'discord-read', name: 'Discord Read', icon: MessageSquare, desc: 'Read messages from any Discord channel' },
      { id: 'discord-send', name: 'Discord Send', icon: MessageSquare, desc: 'Send messages to any Discord channel' },
      { id: 'gmail-read', name: 'Gmail Read', icon: Mail, desc: 'Read emails from your Gmail account' },
      { id: 'gmail-send', name: 'Gmail Send', icon: Mail, desc: 'Send emails via your Gmail account' },
      { id: 'gcalendar-read', name: 'Google Calendar Read', icon: Calendar, desc: 'Read events from any Google Calendar' },
      { id: 'gcalendar-write', name: 'Google Calendar Write', icon: Calendar, desc: 'Create events on any Google Calendar' },
      { id: 'http-request', name: 'HTTP Request', icon: Globe, desc: 'Make GET, POST, PUT, or DELETE HTTP requests' },
      { id: 'slack-read', name: 'Slack Read', icon: Hash, desc: 'Read messages from a specified Slack channel' },
      { id: 'slack-send', name: 'Slack Send', icon: Hash, desc: 'Send a message to a specified Slack channel' }
    ]

    // Filter actions based on search query
    const filteredActions = availableActionsList.filter(action => {
      const query = actionSearchQuery.toLowerCase()
      return (
        action.name.toLowerCase().includes(query) ||
        action.desc.toLowerCase().includes(query)
      )
    })

    const addAction = (action: any) => {
      const newAction = {
        id: Date.now().toString(),
        type: action.id,
        name: action.name,
        description: action.desc,
        config: {}
      }
      const updatedList = [...selectedActionsList, newAction]
      setConfig({ ...config, selectedActionsList: updatedList })
      setShowActionConfig(updatedList.length - 1)
      setWantsMoreActions(false)
    }

    const removeAction = (index: number) => {
      const updatedList = selectedActionsList.filter((_, i) => i !== index)
      setConfig({ ...config, selectedActionsList: updatedList })
    }

    // If no actions yet or wants more actions, show action selection
    if (selectedActionsList.length === 0 || wantsMoreActions) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          <div className="text-center">
            <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
            <h3 className="text-lg font-semibold mb-2">
              {selectedActionsList.length === 0 ? 'What should happen next?' : 'Add another action?'}
            </h3>
            <p className="text-muted-foreground text-sm">
              {selectedActionsList.length === 0 
                ? 'Choose an action to perform when your trigger fires'
                : 'Chain multiple actions to create powerful workflows'}
            </p>
          </div>

          {/* Search */}
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search actions..."
                value={actionSearchQuery}
                onChange={(e) => setActionSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Available Actions */}
          {filteredActions.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No actions found matching "{actionSearchQuery}"</p>
            </div>
          ) : (
            <div className="grid gap-3 max-w-2xl mx-auto">
              {filteredActions.map((action) => (
                <Card
                  key={action.id}
                  className="cursor-pointer transition-all hover:shadow-md border-2 hover:border-primary/20"
                  onClick={() => addAction(action)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <action.icon className="h-6 w-6 text-primary" />
                      <div className="flex-1">
                        <h4 className="font-medium">{action.name}</h4>
                        <p className="text-sm text-muted-foreground">{action.desc}</p>
                      </div>
                      <Plus className="h-5 w-5 text-muted-foreground" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          <div className="flex gap-3 max-w-2xl mx-auto">
            <Button onClick={prevStep} variant="outline" className="w-full">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Previous
            </Button>
            {selectedActionsList.length > 0 && (
              <Button 
                onClick={() => {
                  setWantsMoreActions(false)
                  nextStep()
                }}
                variant="outline"
                className="w-full"
              >
                Continue without adding more
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            )}
          </div>
        </motion.div>
      )
    }

    // Show selected actions with option to add more
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <div className="text-center">
          <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
          <h3 className="text-lg font-semibold mb-2">Your Action Chain</h3>
          <p className="text-muted-foreground text-sm">
            Review and configure your selected actions
          </p>
        </div>

        {/* Selected Actions */}
        <div className="space-y-3 max-w-2xl mx-auto">
          {selectedActionsList.map((action, index) => (
            <Card key={action.id} className="border-2">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-primary">{index + 1}</span>
                    </div>
                    <div>
                      <h4 className="font-medium">{action.name}</h4>
                      <p className="text-sm text-muted-foreground">{action.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setShowActionConfig(index)}
                    >
                      <Settings className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => removeAction(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Add another action */}
        <div className="max-w-2xl mx-auto">
          <Card 
            className="border-2 border-dashed cursor-pointer hover:border-primary/50 transition-colors"
            onClick={() => setWantsMoreActions(true)}
          >
            <CardContent className="p-4">
              <div className="flex items-center justify-center gap-3 text-muted-foreground">
                <Plus className="h-5 w-5" />
                <span>Add another action</span>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="flex gap-3 max-w-2xl mx-auto">
          <Button onClick={prevStep} variant="outline" className="w-full">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button onClick={nextStep} className="w-full">
            Continue to Review
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </motion.div>
    )
  }

  const renderReview = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Check className="h-16 w-16 mx-auto mb-4 text-green-600" />
        <h2 className="text-xl font-semibold mb-2">Review Configuration</h2>
        <p className="text-muted-foreground">
          Review your automation settings before creating
        </p>
      </div>

      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Automation Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Name:</span>
              <span className="font-medium">{config.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Google Account:</span>
              <span className="font-medium">{config.googleAuth.userEmail}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Sheet:</span>
              <span className="font-medium">{config.sheetsConfig.sheetName}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Report Type:</span>
              <span className="font-medium capitalize">{config.sheetsConfig.reportType}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Schedule & Delivery</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Frequency:</span>
              <span className="font-medium capitalize">{config.scheduleDelivery.frequency}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Recipients:</span>
              <span className="font-medium">{config.scheduleDelivery.recipients.length} recipient(s)</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Format:</span>
              <span className="font-medium uppercase">{config.scheduleDelivery.format}</span>
            </div>
          </CardContent>
        </Card>

        {config.selectedActionsList.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Additional Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {config.selectedActionsList.map((action, index) => (
                  <div key={action.id} className="flex items-center gap-2">
                    <Badge variant="secondary">{index + 1}</Badge>
                    <span className="text-sm">{action.name}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
        >
          Create Automation
          <Check className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderComplete = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center mx-auto">
        <Check className="h-10 w-10 text-white" />
      </div>
      
      <div>
        <h2 className="text-2xl font-bold mb-2 text-green-600">Automation Created!</h2>
        <p className="text-muted-foreground max-w-md mx-auto">
          Your Google Sheets recurring reports automation has been successfully created and is now active.
        </p>
      </div>

      <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
        <div className="flex items-center justify-center gap-2 text-green-700 dark:text-green-300">
          <Check className="h-5 w-5" />
          <span className="font-medium">Next report scheduled for: {config.scheduleDelivery.frequency === 'weekly' ? 'Next Monday at 9:00 AM' : 'Tomorrow at 9:00 AM'}</span>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-3">
        <Button 
          onClick={() => setLocation('/dashboard/automations')}
          className="flex-1"
        >
          View My Automations
        </Button>
        <Button 
          onClick={() => setLocation('/dashboard/browse-templates')}
          variant="outline"
          className="flex-1"
        >
          Browse More Templates
        </Button>
      </div>
    </motion.div>
  )

  const currentStepInfo = stepInfo[currentStep]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center gap-3 mb-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setLocation('/dashboard/browse-templates')}
                className="text-muted-foreground hover:text-foreground"
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Back to templates
              </Button>
            </div>
            
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                <currentStepInfo.icon className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">{currentStepInfo.title}</h1>
                <p className="text-muted-foreground">{currentStepInfo.subtitle}</p>
              </div>
            </div>
            
            <Progress value={currentStepInfo.progress} className="w-full" />
          </div>

          {/* Content */}
          <Card className="border-0 shadow-xl bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
            <CardContent className="p-8">
              {currentStep === 'intro' && renderIntro()}
              {currentStep === 'naming' && renderNaming()}
              {currentStep === 'google-auth' && renderGoogleAuth()}
              {currentStep === 'sheets-config' && renderSheetsConfig()}
              {currentStep === 'report-settings' && renderReportSettings()}
              {currentStep === 'ai-formatting' && renderAIFormatting()}
              {currentStep === 'schedule-delivery' && renderScheduleDelivery()}
              {currentStep === 'actions-question' && renderActionsQuestion()}
              {currentStep === 'actions' && renderActions()}
              {currentStep === 'review' && renderReview()}
              {currentStep === 'complete' && renderComplete()}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}