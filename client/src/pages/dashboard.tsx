import { useLocation } from 'wouter'
import { 
  Activity,
  Zap,
  TrendingUp,
  Users,
  Plus,
  ArrowRight,
  Play,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import DashboardLayout from './dashboard-layout'
import { useAuth } from '@/contexts/AuthContext'
import { WorkflowSuggestions } from '@/components/workflow-suggestions'
import { ErrorHandler } from '@/components/error-handler'
import { useState } from 'react'
import { HelpTooltip } from '@/components/ui/help-tooltip'
import { AIHelpSuggestions } from '@/components/ai-help-suggestions'
import OnboardingModal from '@/components/onboarding-modal'
import BackendStatus from '@/components/backend-status'

// Mock data for stats
const stats = [
  {
    title: 'Active Automations',
    value: '12',
    change: '+2 this week',
    icon: Zap,
    color: 'text-blue-600'
  },
  {
    title: 'Total Executions',
    value: '1,847',
    change: '+312 today',
    icon: Activity,
    color: 'text-green-600'
  },
  {
    title: 'Success Rate',
    value: '98.5%',
    change: '+0.3% from last week',
    icon: TrendingUp,
    color: 'text-purple-600'
  },
  {
    title: 'Team Members',
    value: '4',
    change: '2 admins, 2 members',
    icon: Users,
    color: 'text-orange-600'
  },
]

// Mock recent automations
const recentAutomations = [
  {
    id: '1',
    name: 'Daily Sales Report',
    status: 'success',
    lastRun: '2 hours ago',
    nextRun: 'in 22 hours',
    executions: 48
  },
  {
    id: '2',
    name: 'Customer Welcome Email',
    status: 'success',
    lastRun: '5 hours ago',
    nextRun: 'on new signup',
    executions: 312
  },
  {
    id: '3',
    name: 'Inventory Alert',
    status: 'error',
    lastRun: '1 day ago',
    nextRun: 'paused',
    executions: 23,
    error: 'API rate limit exceeded'
  },
]

// Mock activity log
const recentActivity = [
  {
    id: '1',
    action: 'Automation completed',
    details: 'Daily Sales Report sent to 5 recipients',
    time: '2 hours ago',
    status: 'success'
  },
  {
    id: '2',
    action: 'New automation created',
    details: 'Social Media Scheduler by John Doe',
    time: '5 hours ago',
    status: 'info'
  },
  {
    id: '3',
    action: 'Automation failed',
    details: 'Inventory Alert - API rate limit',
    time: '1 day ago',
    status: 'error'
  },
]

export default function Dashboard() {
  const [location, setLocation] = useLocation()
  const { user } = useAuth()
  const [showSuggestions, setShowSuggestions] = useState(true)

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'running':
        return <Play className="h-4 w-4 text-blue-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto space-y-4 sm:space-y-6 lg:space-y-8">
        {/* Welcome Header */}
        <div className="animate-fade-in">
          <h1 className="text-xl sm:text-2xl md:text-3xl font-bold tracking-tight">
            Welcome back, {user?.user_metadata?.name || 'User'}
          </h1>
          <p className="text-xs sm:text-sm md:text-base text-muted-foreground mt-1">
            Here's what's happening with your automations today
          </p>
        </div>

        {/* Backend Status */}
        <BackendStatus />

        {/* Stats Grid */}
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <Card key={stat.title} className="hover-lift">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    {stat.title}
                    <HelpTooltip 
                      content={
                        stat.title === 'Active Automations' ? 'Number of automations currently enabled and running' :
                        stat.title === 'Total Executions' ? 'Total number of times your automations have run' :
                        stat.title === 'Success Rate' ? 'Percentage of successful automation runs' :
                        'Number of team members with access to your workspace'
                      }
                      size="sm"
                    />
                  </CardTitle>
                  <Icon className={`h-4 w-4 ${stat.color}`} />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stat.value}</div>
                  <p className="text-xs text-muted-foreground">
                    {stat.change}
                  </p>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* AI Workflow Suggestions */}
        {showSuggestions && (
          <WorkflowSuggestions 
            onDismiss={() => setShowSuggestions(false)}
            compact={true}
          />
        )}

        {/* Error Handler */}
        <ErrorHandler compact={true} />

        <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
          {/* Recent Automations */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Recent Automations</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setLocation('/dashboard/automations')}
                >
                  View all
                  <ArrowRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
              <CardDescription>
                Your most active automation workflows
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentAutomations.map((automation) => (
                  <div
                    key={automation.id}
                    className="flex items-center justify-between p-3 rounded-lg border hover:bg-accent cursor-pointer transition-colors"
                    onClick={() => setLocation(`/dashboard/automations/${automation.id}/chat`)}
                  >
                    <div className="flex items-center gap-3">
                      {getStatusIcon(automation.status)}
                      <div>
                        <p className="font-medium">{automation.name}</p>
                        <p className="text-sm text-muted-foreground">
                          Last run: {automation.lastRun}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{automation.executions}</p>
                      <p className="text-xs text-muted-foreground">runs</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Recent Activity</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setLocation('/dashboard/logs')}
                >
                  View logs
                  <ArrowRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
              <CardDescription>
                Latest events across your workspace
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start gap-3">
                    <div className="mt-0.5">
                      {activity.status === 'success' && (
                        <div className="p-1.5 bg-green-100 dark:bg-green-900/20 rounded-full">
                          <CheckCircle className="h-3 w-3 text-green-600 dark:text-green-400" />
                        </div>
                      )}
                      {activity.status === 'error' && (
                        <div className="p-1.5 bg-red-100 dark:bg-red-900/20 rounded-full">
                          <XCircle className="h-3 w-3 text-red-600 dark:text-red-400" />
                        </div>
                      )}
                      {activity.status === 'info' && (
                        <div className="p-1.5 bg-blue-100 dark:bg-blue-900/20 rounded-full">
                          <Zap className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 space-y-1">
                      <p className="text-sm font-medium">{activity.action}</p>
                      <p className="text-sm text-muted-foreground">
                        {activity.details}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {activity.time}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks and helpful resources
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
              <Button
                variant="outline"
                className="justify-start"
                onClick={() => setLocation('/dashboard/browse-templates')}
              >
                <Zap className="h-4 w-4 mr-2 text-[#155DB8]" />
                Browse Templates
              </Button>
              <Button
                variant="outline"
                className="justify-start"
                onClick={() => setLocation('/dashboard/team')}
              >
                <Users className="h-4 w-4 mr-2 text-[#155DB8]" />
                Invite Team Member
              </Button>
              <Button
                variant="outline"
                className="justify-start"
                onClick={() => setLocation('/dashboard/settings')}
              >
                <Activity className="h-4 w-4 mr-2 text-[#155DB8]" />
                View Analytics
              </Button>
              <Button
                variant="outline"
                className="justify-start"
                onClick={() => window.open('https://docs.filorina.com', '_blank')}
              >
                <ArrowRight className="h-4 w-4 mr-2 text-[#155DB8]" />
                Documentation
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
      <OnboardingModal />
    </DashboardLayout>
  )
}