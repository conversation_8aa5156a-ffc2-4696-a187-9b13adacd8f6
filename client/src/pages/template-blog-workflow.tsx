import { useState } from 'react'
import { motion } from 'framer-motion'
import { useLocation } from 'wouter'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  ChevronLeft, ArrowRight, Table, Brain, Mail, Calendar, 
  FileText, Settings, Check, Zap, Globe, Clock, Users, Shield, Search,
  Play, Activity, AlertCircle, CheckCircle
} from 'lucide-react'
import { SiGooglesheets, SiGmail, SiWordpress, SiNotion } from 'react-icons/si'

import { Plus, X, MessageSquare, Hash } from 'lucide-react'
import { EmojiCelebration } from '@/components/emoji-celebration'

type StepType = 'intro' | 'naming' | 'sheets' | 'ai-settings' | 'email-approval' | 'automation' | 'actions-question' | 'actions' | 'testing' | 'review' | 'complete'

interface WorkflowConfig {
  name: string
  googleAuth: {
    isAuthenticated: boolean
    userEmail: string
    userName: string
    accountId: string
  }
  googleSheets: {
    spreadsheetId: string
    sheetName: string
    keywordColumn: string
    filesList: string[]
  }
  aiSettings: {
    model: string
    tone: string
    length: string
    structure: string
    language: string
  }
  emailApproval: {
    enabled: boolean
    reviewerEmail: string
    autoPublish: boolean
    approvalTimeout: number
  }
  automation: {
    schedule: string
    frequency: string
    outputLocation: string
    publishTo: string[]
  }
  selectedActionsList: Array<{
    id: string
    type: string
    name: string
    description: string
    config: Record<string, any>
  }>
  wantsActions: boolean | null
}

export default function BlogWorkflowTemplate() {
  const [, setLocation] = useLocation()
  const [currentStep, setCurrentStep] = useState<StepType>('intro')
  const [showActionConfig, setShowActionConfig] = useState<number | null>(null)
  const [actionSearchQuery, setActionSearchQuery] = useState('')
  const [wantsMoreActions, setWantsMoreActions] = useState(false)
  const [showCelebration, setShowCelebration] = useState(false)
  
  // Testing state
  const [isRunningTest, setIsRunningTest] = useState(false)
  const [testResults, setTestResults] = useState<{
    execution: { step: string; status: 'pending' | 'running' | 'success' | 'error'; time?: number }[]
    validation: { passed: boolean; message: string }[]
    performance: { metric: string; value: string; status: 'good' | 'warning' | 'error' }[]
  } | null>(null)
  const [workflow, setWorkflow] = useState<WorkflowConfig>({
    name: '',
    googleAuth: {
      isAuthenticated: false,
      userEmail: '',
      userName: '',
      accountId: ''
    },
    googleSheets: {
      spreadsheetId: '',
      sheetName: 'Keywords',
      keywordColumn: 'A',
      filesList: []
    },
    aiSettings: {
      model: 'gpt-4o',
      tone: 'professional',
      length: 'medium',
      structure: 'intro-body-conclusion',
      language: 'english'
    },
    emailApproval: {
      enabled: true,
      reviewerEmail: '',
      autoPublish: false,
      approvalTimeout: 24
    },
    automation: {
      schedule: 'daily',
      frequency: '09:00',
      outputLocation: 'google-docs',
      publishTo: ['wordpress', 'medium']
    },
    selectedActionsList: [],
    wantsActions: null
  })

  const stepConfig = {
    intro: { 
      title: 'Blog Content Automation', 
      subtitle: 'Generate high-quality blog content from keywords automatically',
      icon: FileText,
      progress: 0
    },
    naming: {
      title: 'Name Your Automation',
      subtitle: 'Give your automation a memorable name',
      icon: Settings,
      progress: 15
    },
    sheets: { 
      title: 'Google Account & Sheets', 
      subtitle: 'Authenticate with Google and select your spreadsheet',
      icon: Table,
      progress: 30
    },
    'ai-settings': { 
      title: 'AI Content Settings', 
      subtitle: 'Configure tone, length, and content structure',
      icon: Brain,
      progress: 45
    },
    'email-approval': { 
      title: 'Email Approval Workflow', 
      subtitle: 'Set up content review and approval process',
      icon: Mail,
      progress: 60
    },
    automation: { 
      title: 'Automation Settings', 
      subtitle: 'Configure publishing schedule and output destinations',
      icon: Calendar,
      progress: 60
    },
    'actions-question': {
      title: 'Additional Actions',
      subtitle: 'Extend your automation capabilities',
      icon: Zap,
      progress: 70
    },
    'actions': { 
      title: 'Select Actions', 
      subtitle: 'Define what actions to take',
      icon: Zap, 
      progress: 80 
    },
    testing: {
      title: 'Test Your Automation',
      subtitle: 'Validate your workflow before going live',
      icon: Play,
      progress: 85
    },
    review: { 
      title: 'Review Configuration', 
      subtitle: 'Confirm your workflow settings',
      icon: Settings,
      progress: 90
    },
    complete: { 
      title: 'Automation Created!', 
      subtitle: 'Your automation is ready to use',
      icon: Check,
      progress: 100
    }
  }

  const nextStep = () => {
    const steps: StepType[] = ['intro', 'naming', 'sheets', 'ai-settings', 'email-approval', 'automation', 'actions-question', 'actions', 'testing', 'review', 'complete']
    const currentIndex = steps.indexOf(currentStep)
    
    if (currentStep === 'actions-question' && workflow.wantsActions === false) {
      setCurrentStep('testing')
    } else if (currentStep === 'actions-question' && workflow.wantsActions === true) {
      setWantsMoreActions(true)
      setCurrentStep('actions')
    } else if (currentStep === 'review') {
      setShowCelebration(true)
      setCurrentStep('complete')
    } else if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1])
    }
  }

  const prevStep = () => {
    const steps: StepType[] = ['intro', 'naming', 'sheets', 'ai-settings', 'email-approval', 'automation', 'actions-question', 'actions', 'testing', 'review', 'complete']
    const currentIndex = steps.indexOf(currentStep)
    
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1])
    }
  }

  const renderIntro = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center mx-auto">
        <FileText className="h-10 w-10 text-white" />
      </div>
      <div>
        <h2 className="text-2xl font-bold mb-2">AI Blog Content Generator</h2>
        <p className="text-muted-foreground max-w-md mx-auto">
          This template creates an automated workflow that reads keywords from Google Sheets, 
          generates high-quality blog content using AI, sends it for approval, and publishes 
          automatically on your chosen platforms.
        </p>
      </div>
      
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <SiGooglesheets className="h-8 w-8 mx-auto mb-2 text-green-600" />
          <p className="text-xs font-medium">Google Sheets</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Brain className="h-8 w-8 mx-auto mb-2 text-purple-600" />
          <p className="text-xs font-medium">AI Generation</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <SiGmail className="h-8 w-8 mx-auto mb-2 text-red-600" />
          <p className="text-xs font-medium">Email Approval</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <SiWordpress className="h-8 w-8 mx-auto mb-2 text-blue-600" />
          <p className="text-xs font-medium">Auto Publish</p>
        </div>
      </div>

      <Button 
        onClick={nextStep} 
        size="lg"
        className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 min-h-[48px]"
      >
        Start Setup
        <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
    </motion.div>
  )

  const renderNaming = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Settings className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h2 className="text-xl font-semibold mb-2">Name Your Automation</h2>
        <p className="text-muted-foreground">
          Give your automation a memorable name that describes what it does
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="automation-name">Automation Name *</Label>
          <Input
            id="automation-name"
            placeholder="e.g., Weekly Blog Content Generator"
            value={workflow.name}
            onChange={(e) => setWorkflow({ ...workflow, name: e.target.value })}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && workflow.name) {
                nextStep()
              }
            }}
            className="mt-2"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Choose a descriptive name you'll easily recognize
          </p>
        </div>
      </div>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full min-h-[44px]"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          disabled={!workflow.name}
          className="w-full min-h-[44px]"
        >
          Continue to Google Sheets Setup
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderSheets = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <SiGooglesheets className="h-16 w-16 mx-auto mb-4 text-green-600" />
        <h3 className="text-lg font-semibold mb-2">Connect Google Sheets</h3>
        <p className="text-muted-foreground text-sm">
          Your spreadsheet should contain keywords or topics that the AI will use to generate blog content.
        </p>
      </div>

      <Card>
        <CardContent className="p-6 space-y-4">
          <div>
            <Label className="text-sm font-medium">Authentication Type <span className="text-red-500">*</span></Label>
            <div className="mt-2">
              <div className="flex items-center space-x-2">
                <input type="radio" id="google-signin" checked readOnly className="text-blue-600" />
                <Label htmlFor="google-signin" className="text-sm">Google Sign-In</Label>
              </div>
            </div>
          </div>

          <div className="text-xs text-muted-foreground">
            <strong>Note:</strong> Select existing Google account from below or Signin with a different account.
          </div>

          {!workflow.googleAuth.isAuthenticated ? (
            <Button
              onClick={() => {
                setWorkflow({
                  ...workflow,
                  googleAuth: {
                    isAuthenticated: true,
                    userEmail: '<EMAIL>',
                    userName: 'Kaizar Bharmal',
                    accountId: 'gauth-12345'
                  }
                })
              }}
              className="w-full"
            >
              <SiGooglesheets className="mr-2 h-4 w-4" />
              Sign in with Google
            </Button>
          ) : (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Badge variant="default" className="bg-green-600">Connected</Badge>
                <span className="text-sm font-medium">Google</span>
              </div>
              
              <div className="space-y-2">
                <Label className="text-sm font-medium">Google account</Label>
                <div className="p-3 border rounded-md bg-muted/50">
                  <div className="text-sm font-medium">{workflow.googleAuth.userName}</div>
                  <div className="text-sm text-muted-foreground">({workflow.googleAuth.userEmail})</div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {workflow.googleAuth.isAuthenticated && (
        <>
        <Card>
          <CardHeader>
            <CardTitle className="text-base">File</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {workflow.googleSheets.filesList.length === 0 ? (
              <div className="text-center py-4">
                <p className="text-sm text-muted-foreground">
                  There are no files, please refresh
                </p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mt-2"
                  onClick={() => setWorkflow({
                    ...workflow,
                    googleSheets: { 
                      ...workflow.googleSheets, 
                      filesList: ['sheet1', 'sheet2', 'sheet3'] 
                    }
                  })}
                >
                  Refresh Files
                </Button>
              </div>
            ) : (
              <Select
                value={workflow.googleSheets.spreadsheetId}
                onValueChange={(value) => setWorkflow({
                  ...workflow,
                  googleSheets: { ...workflow.googleSheets, spreadsheetId: value }
                })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a spreadsheet" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sheet1">Marketing Keywords - Q1 2025</SelectItem>
                  <SelectItem value="sheet2">Blog Topics Database</SelectItem>
                  <SelectItem value="sheet3">Content Calendar</SelectItem>
                </SelectContent>
              </Select>
            )}
          </CardContent>
        </Card>
          
        <Card>
          <CardContent className="p-6 space-y-4">
            <div>
              <Label htmlFor="sheet-name">Sheet Name</Label>
              <Input
                id="sheet-name"
                placeholder="e.g., Keywords, Topics"
                value={workflow.googleSheets.sheetName}
                onChange={(e) => setWorkflow({
                  ...workflow,
                  googleSheets: { ...workflow.googleSheets, sheetName: e.target.value }
                })}
              />
            </div>

            <div>
              <Label htmlFor="keyword-column">Keyword Column</Label>
              <Select
                value={workflow.googleSheets.keywordColumn}
                onValueChange={(value) => setWorkflow({
                  ...workflow,
                  googleSheets: { ...workflow.googleSheets, keywordColumn: value }
                })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="A">Column A</SelectItem>
                  <SelectItem value="B">Column B</SelectItem>
                  <SelectItem value="C">Column C</SelectItem>
                  <SelectItem value="D">Column D</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
        
        <div className="flex gap-3">
          <Button onClick={prevStep} variant="outline" className="w-full">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button onClick={nextStep} className="w-full">
            Continue to AI Settings <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
        </>
      )}
    </motion.div>
  )

  const renderAISettings = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Brain className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h3 className="text-lg font-semibold mb-2">AI Content Settings</h3>
        <p className="text-muted-foreground text-sm">
          Configure how the AI should generate your blog content.
        </p>
      </div>

      <div className="grid gap-6">
        <div>
          <Label>AI Model</Label>
          <Select
            value={workflow.aiSettings.model}
            onValueChange={(value) => setWorkflow({
              ...workflow,
              aiSettings: { ...workflow.aiSettings, model: value }
            })}
          >
            <SelectTrigger className="mt-2">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="gpt-4o">GPT-4o (Latest & most capable)</SelectItem>
              <SelectItem value="gpt-4">GPT-4 (Most capable)</SelectItem>
              <SelectItem value="claude-3-opus">Claude 3 Opus (Most capable)</SelectItem>
              <SelectItem value="gemini-1.5-pro">Gemini 1.5 Pro (Latest)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label>Content Tone</Label>
          <RadioGroup
            value={workflow.aiSettings.tone}
            onValueChange={(value) => setWorkflow({
              ...workflow,
              aiSettings: { ...workflow.aiSettings, tone: value }
            })}
            className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-2"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="professional" id="professional" />
              <Label htmlFor="professional">Professional</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="casual" id="casual" />
              <Label htmlFor="casual">Casual</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="friendly" id="friendly" />
              <Label htmlFor="friendly">Friendly</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="authoritative" id="authoritative" />
              <Label htmlFor="authoritative">Authoritative</Label>
            </div>
          </RadioGroup>
        </div>

        <div>
          <Label>Content Length</Label>
          <Select
            value={workflow.aiSettings.length}
            onValueChange={(value) => setWorkflow({
              ...workflow,
              aiSettings: { ...workflow.aiSettings, length: value }
            })}
          >
            <SelectTrigger className="mt-2">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="short">Short (300-500 words)</SelectItem>
              <SelectItem value="medium">Medium (800-1200 words)</SelectItem>
              <SelectItem value="long">Long (1500-2500 words)</SelectItem>
              <SelectItem value="custom">Custom length</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label>Content Structure</Label>
          <Select
            value={workflow.aiSettings.structure}
            onValueChange={(value) => setWorkflow({
              ...workflow,
              aiSettings: { ...workflow.aiSettings, structure: value }
            })}
          >
            <SelectTrigger className="mt-2">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="intro-body-conclusion">Introduction → Body → Conclusion</SelectItem>
              <SelectItem value="problem-solution">Problem → Solution → Benefits</SelectItem>
              <SelectItem value="listicle">Listicle (numbered points)</SelectItem>
              <SelectItem value="how-to">How-to Guide</SelectItem>
              <SelectItem value="comparison">Comparison/Review</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex gap-3">
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={nextStep} className="w-full">
          Continue to Email Approval <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderEmailApproval = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <SiGmail className="h-16 w-16 mx-auto mb-4 text-red-600" />
        <h3 className="text-lg font-semibold mb-2">Email Approval Workflow</h3>
        <p className="text-muted-foreground text-sm">
          Set up content review and approval before publishing.
        </p>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-base">Enable Email Approval</CardTitle>
              <CardDescription>Send generated content for review before publishing</CardDescription>
            </div>
            <Button
              variant={workflow.emailApproval.enabled ? "default" : "outline"}
              size="sm"
              onClick={() => setWorkflow({
                ...workflow,
                emailApproval: { ...workflow.emailApproval, enabled: !workflow.emailApproval.enabled }
              })}
            >
              {workflow.emailApproval.enabled ? "Enabled" : "Disabled"}
            </Button>
          </div>
        </CardHeader>

        {workflow.emailApproval.enabled && (
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="reviewer-email">Reviewer Email</Label>
              <Input
                id="reviewer-email"
                type="email"
                placeholder="<EMAIL>"
                value={workflow.emailApproval.reviewerEmail}
                onChange={(e) => setWorkflow({
                  ...workflow,
                  emailApproval: { ...workflow.emailApproval, reviewerEmail: e.target.value }
                })}
              />
            </div>

            <div>
              <Label>Approval Timeout</Label>
              <Select
                value={workflow.emailApproval.approvalTimeout.toString()}
                onValueChange={(value) => setWorkflow({
                  ...workflow,
                  emailApproval: { ...workflow.emailApproval, approvalTimeout: parseInt(value) }
                })}
              >
                <SelectTrigger className="mt-2">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="6">6 hours</SelectItem>
                  <SelectItem value="12">12 hours</SelectItem>
                  <SelectItem value="24">24 hours</SelectItem>
                  <SelectItem value="48">48 hours</SelectItem>
                  <SelectItem value="72">72 hours</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground mt-1">
                Auto-publish if no response within this time
              </p>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="auto-publish"
                checked={workflow.emailApproval.autoPublish}
                onChange={(e) => setWorkflow({
                  ...workflow,
                  emailApproval: { ...workflow.emailApproval, autoPublish: e.target.checked }
                })}
                className="rounded"
              />
              <Label htmlFor="auto-publish" className="text-sm">
                Auto-publish after timeout if no response
              </Label>
            </div>
          </CardContent>
        )}
      </Card>

      <div className="flex gap-3">
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={nextStep} className="w-full">
          Continue to Automation Settings <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderAutomation = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Calendar className="h-16 w-16 mx-auto mb-4 text-blue-600" />
        <h3 className="text-lg font-semibold mb-2">Automation Settings</h3>
        <p className="text-muted-foreground text-sm">
          Configure when and where to publish your content.
        </p>
      </div>

      <div className="grid gap-6">
        <div>
          <Label>Publishing Schedule</Label>
          <Select
            value={workflow.automation.schedule}
            onValueChange={(value) => setWorkflow({
              ...workflow,
              automation: { ...workflow.automation, schedule: value }
            })}
          >
            <SelectTrigger className="mt-2">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">Daily</SelectItem>
              <SelectItem value="weekly">Weekly</SelectItem>
              <SelectItem value="bi-weekly">Bi-weekly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="custom">Custom</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label>Publish Time</Label>
          <Input
            type="time"
            value={workflow.automation.frequency}
            onChange={(e) => setWorkflow({
              ...workflow,
              automation: { ...workflow.automation, frequency: e.target.value }
            })}
            className="mt-2"
          />
        </div>

        <div>
          <Label>Output Location</Label>
          <Select
            value={workflow.automation.outputLocation}
            onValueChange={(value) => setWorkflow({
              ...workflow,
              automation: { ...workflow.automation, outputLocation: value }
            })}
          >
            <SelectTrigger className="mt-2">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="google-docs">Google Docs</SelectItem>
              <SelectItem value="notion">Notion</SelectItem>
              <SelectItem value="airtable">Airtable</SelectItem>
              <SelectItem value="local-file">Local File</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label>Auto-Publish To</Label>
          <div className="grid grid-cols-2 gap-3 mt-2">
            {[
              { id: 'wordpress', name: 'WordPress', icon: SiWordpress, color: 'text-blue-600' },
              { id: 'medium', name: 'Medium', icon: Globe, color: 'text-green-600' },
              { id: 'linkedin', name: 'LinkedIn', icon: Users, color: 'text-blue-700' },
              { id: 'notion', name: 'Notion', icon: SiNotion, color: 'text-gray-700' }
            ].map((platform) => (
              <Card
                key={platform.id}
                className={`cursor-pointer transition-all ${
                  workflow.automation.publishTo.includes(platform.id) ? 'ring-2 ring-primary' : ''
                }`}
                onClick={() => {
                  const publishTo = workflow.automation.publishTo.includes(platform.id)
                    ? workflow.automation.publishTo.filter(p => p !== platform.id)
                    : [...workflow.automation.publishTo, platform.id]
                  
                  setWorkflow({
                    ...workflow,
                    automation: { ...workflow.automation, publishTo }
                  })
                }}
              >
                <CardContent className="p-3">
                  <div className="flex items-center gap-2">
                    <platform.icon className={`h-5 w-5 ${platform.color}`} />
                    <span className="text-sm font-medium">{platform.name}</span>
                    {workflow.automation.publishTo.includes(platform.id) && 
                      <Check className="h-4 w-4 text-primary ml-auto" />}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>

      <div className="flex gap-3">
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={nextStep} className="w-full">
          Continue <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderActionsQuestion = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Zap className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h3 className="text-lg font-semibold mb-2">Do you want to add any actions?</h3>
        <p className="text-muted-foreground text-sm mb-6">
          Extend your automation with additional actions (optional)
        </p>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-6">
        <Button
          variant={workflow.wantsActions === true ? "default" : "outline"}
          size="lg"
          onClick={() => {
            setWorkflow({ ...workflow, wantsActions: true })
            nextStep()
          }}
          className="h-auto py-6"
        >
          <div className="flex flex-col items-center gap-2">
            <Check className="h-8 w-8" />
            <span>Yes, I want to add actions</span>
          </div>
        </Button>
        <Button
          variant={workflow.wantsActions === false ? "default" : "outline"}
          size="lg"
          onClick={() => {
            setWorkflow({ ...workflow, wantsActions: false })
            setCurrentStep('review')  // Skip directly to review
          }}
          className="h-auto py-6"
        >
          <div className="flex flex-col items-center gap-2">
            <ArrowRight className="h-8 w-8" />
            <span>No, continue without</span>
          </div>
        </Button>
      </div>

      <div>
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
      </div>
    </motion.div>
  )



  const renderActions = () => {
    const selectedActionsList = workflow.selectedActionsList || []

    const availableActionsList = [
      { id: 'discord-read', name: 'Discord Read', icon: MessageSquare, desc: 'Read messages from any Discord channel' },
      { id: 'discord-send', name: 'Discord Send', icon: MessageSquare, desc: 'Send messages to any Discord channel' },
      { id: 'gmail-read', name: 'Gmail Read', icon: Mail, desc: 'Read emails from your Gmail account' },
      { id: 'gmail-send', name: 'Gmail Send', icon: Mail, desc: 'Send emails via your Gmail account' },
      { id: 'gcalendar-read', name: 'Google Calendar Read', icon: Calendar, desc: 'Read events from any Google Calendar' },
      { id: 'gcalendar-write', name: 'Google Calendar Write', icon: Calendar, desc: 'Create events on any Google Calendar' },
      { id: 'http-request', name: 'HTTP Request', icon: Globe, desc: 'Make GET, POST, PUT, or DELETE HTTP requests' },
      { id: 'slack-read', name: 'Slack Read', icon: Hash, desc: 'Read messages from a specified Slack channel' },
      { id: 'slack-send', name: 'Slack Send', icon: Hash, desc: 'Send a message to a specified Slack channel' }
    ]

    // Filter actions based on search query
    const filteredActions = availableActionsList.filter(action => {
      const query = actionSearchQuery.toLowerCase()
      return (
        action.name.toLowerCase().includes(query) ||
        action.desc.toLowerCase().includes(query)
      )
    })

    const addAction = (action: any) => {
      const newAction = {
        id: Date.now().toString(),
        type: action.id,
        name: action.name,
        description: action.desc,
        config: {}
      }
      const updatedList = [...selectedActionsList, newAction]
      setWorkflow({ ...workflow, selectedActionsList: updatedList })
      setShowActionConfig(updatedList.length - 1)
      setWantsMoreActions(false)
    }

    const removeAction = (index: number) => {
      const updatedList = selectedActionsList.filter((_, i) => i !== index)
      setWorkflow({ ...workflow, selectedActionsList: updatedList })
    }

    // If no actions yet or wants more actions, show action selection
    if (selectedActionsList.length === 0 || wantsMoreActions) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          <div className="text-center">
            <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
            <h3 className="text-lg font-semibold mb-2">
              {selectedActionsList.length === 0 ? 'What should happen next?' : 'Add another action?'}
            </h3>
            <p className="text-muted-foreground text-sm">
              {selectedActionsList.length === 0 
                ? 'Choose an action to perform when your trigger fires'
                : 'Chain multiple actions to create powerful workflows'}
            </p>
          </div>

          {/* Search */}
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search actions..."
                value={actionSearchQuery}
                onChange={(e) => setActionSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Available Actions */}
          {filteredActions.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No actions found matching "{actionSearchQuery}"</p>
            </div>
          ) : (
            <div className="grid gap-3 max-w-2xl mx-auto">
              {filteredActions.map((action) => (
                <Card
                  key={action.id}
                  className="cursor-pointer transition-all hover:shadow-md"
                  onClick={() => addAction(action)}
                >
                  <CardHeader className="p-4">
                    <div className="flex items-center gap-3">
                      <action.icon className="h-5 w-5 text-primary" />
                      <div className="flex-1">
                        <CardTitle className="text-sm">{action.name}</CardTitle>
                        <CardDescription className="text-xs mt-1">{action.desc}</CardDescription>
                      </div>
                      <Plus className="h-5 w-5 text-muted-foreground" />
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          )}

          <div className="flex gap-3 max-w-2xl mx-auto">
            <Button onClick={prevStep} variant="outline" className="w-full">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Previous
            </Button>
            {selectedActionsList.length > 0 && (
              <Button 
                onClick={() => setWantsMoreActions(false)} 
                variant="outline"
                className="w-full"
              >
                Continue without adding
              </Button>
            )}
          </div>
        </motion.div>
      )
    }

    // Show current actions with option to add more
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <div className="text-center">
          <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
          <h3 className="text-lg font-semibold mb-2">Your Action Chain</h3>
          <p className="text-muted-foreground text-sm">
            {selectedActionsList.length} action{selectedActionsList.length !== 1 ? 's' : ''} configured
          </p>
        </div>

        {/* Action Chain Visualization */}
        <div className="max-w-2xl mx-auto space-y-3">
          {/* Trigger */}
          <div className="flex items-center gap-3 p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg border-2 border-purple-200 dark:border-purple-800">
            <SiGooglesheets className="h-5 w-5 text-purple-600" />
            <div className="flex-1">
              <p className="font-medium">Trigger: Google Sheets Update</p>
              <p className="text-xs text-muted-foreground">When keywords are added to your spreadsheet</p>
            </div>
          </div>

          {/* Arrow */}
          <div className="flex justify-center">
            <ArrowRight className="h-4 w-4 text-muted-foreground" />
          </div>

          {/* Actions */}
          {selectedActionsList.map((action, index) => (
            <div key={action.id}>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center gap-3 p-4 bg-muted/30 rounded-lg border"
              >
                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-xs font-medium">
                  {index + 1}
                </div>
                <div className="flex-1">
                  <p className="font-medium">{action.name}</p>
                  <p className="text-xs text-muted-foreground">{action.description}</p>
                </div>
                <div className="flex gap-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setShowActionConfig(index)}
                    className="h-8 px-2"
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => removeAction(index)}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </motion.div>
              {index < selectedActionsList.length - 1 && (
                <div className="flex justify-center my-2">
                  <ArrowRight className="h-4 w-4 text-muted-foreground" />
                </div>
              )}
            </div>
          ))}

          {/* Add Another Action Button */}
          <Button
            variant="outline"
            onClick={() => setWantsMoreActions(true)}
            className="w-full"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Another Action
          </Button>
        </div>

        <div className="flex gap-3 max-w-2xl mx-auto">
          <Button onClick={prevStep} variant="outline" className="w-full">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button 
            onClick={nextStep} 
            className="w-full"
            disabled={selectedActionsList.length === 0}
          >
            Continue <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>

        {/* Action Configuration Modal */}
        {showActionConfig !== null && selectedActionsList[showActionConfig] && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowActionConfig(null)}
          >
            <motion.div
              initial={{ scale: 0.95 }}
              animate={{ scale: 1 }}
              className="bg-card p-6 rounded-lg shadow-lg max-w-md w-full max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">
                Configure {selectedActionsList[showActionConfig].name}
              </h3>
              
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Configuration options will appear here based on the action type.
                </p>
                <div className="flex justify-end gap-2 mt-6">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowActionConfig(null)}
                  >
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => setShowActionConfig(null)}
                  >
                    Save Configuration
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </motion.div>
    )
  }

  const renderTesting = () => {
    const runTest = async () => {
      setIsRunningTest(true)
      
      // Simulate test execution for blog workflow
      const steps = [
        { step: 'Connect to Google Sheets', delay: 500 },
        { step: 'Read keyword data', delay: 600 },
        { step: 'Generate AI content', delay: 1200 },
        { step: 'Send for email approval', delay: 400 },
        { step: 'Publish to platforms', delay: 800 }
      ]

      const execution: typeof testResults.execution = []
      
      for (let i = 0; i < steps.length; i++) {
        execution.push({ step: steps[i].step, status: 'running' as const })
        setTestResults({
          execution: [...execution],
          validation: [],
          performance: []
        })
        
        await new Promise(resolve => setTimeout(resolve, steps[i].delay))
        
        execution[i] = { 
          step: steps[i].step, 
          status: 'success' as const, 
          time: steps[i].delay 
        }
        setTestResults({
          execution: [...execution],
          validation: [],
          performance: []
        })
      }

      // Add validation results
      const validation = [
        { passed: true, message: 'Google Sheets connection verified' },
        { passed: workflow.googleSheets.spreadsheetId !== '', message: 'Spreadsheet ID configured' },
        { passed: workflow.emailApproval.reviewerEmail !== '' || !workflow.emailApproval.enabled, message: 'Email approval settings valid' },
        { passed: true, message: 'AI model configuration valid' },
        { passed: workflow.automation.publishTo.length > 0, message: 'Publishing destinations selected' }
      ]

      // Add performance metrics
      const performance = [
        { metric: 'Keywords per run', value: '10-50', status: 'good' as const },
        { metric: 'AI generation time', value: '15-30 seconds', status: 'good' as const },
        { metric: 'Total execution time', value: '3-5 minutes', status: 'good' as const },
        { metric: 'API usage', value: `${workflow.selectedActionsList.length + 3} calls`, status: workflow.selectedActionsList.length > 5 ? 'warning' as const : 'good' as const }
      ]

      setTestResults({
        execution,
        validation,
        performance
      })
      
      setIsRunningTest(false)
    }

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <Card>
          <CardHeader>
            <CardTitle>Test Your Blog Workflow</CardTitle>
            <CardDescription>
              Run a test to ensure your blog automation is configured correctly
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {!testResults ? (
              <div className="text-center py-8">
                <Play className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground mb-4">
                  Test your workflow to see how it will generate blog content
                </p>
                <Button 
                  onClick={runTest} 
                  disabled={isRunningTest}
                  className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
                >
                  {isRunningTest ? (
                    <>
                      <Activity className="mr-2 h-4 w-4 animate-pulse" />
                      Running Test...
                    </>
                  ) : (
                    <>
                      <Play className="mr-2 h-4 w-4" />
                      Run Test
                    </>
                  )}
                </Button>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Execution Steps */}
                <div>
                  <h4 className="text-sm font-medium mb-3">Execution Preview</h4>
                  <div className="space-y-2">
                    {testResults.execution.map((step, index) => (
                      <div key={index} className="flex items-center gap-3">
                        {step.status === 'running' ? (
                          <Activity className="h-4 w-4 text-blue-600 animate-pulse" />
                        ) : step.status === 'success' ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : step.status === 'error' ? (
                          <AlertCircle className="h-4 w-4 text-red-600" />
                        ) : (
                          <div className="h-4 w-4 rounded-full border-2 border-muted" />
                        )}
                        <span className="text-sm flex-1">{step.step}</span>
                        {step.time && (
                          <span className="text-xs text-muted-foreground">{step.time}ms</span>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Validation Results */}
                <div>
                  <h4 className="text-sm font-medium mb-3">Validation Results</h4>
                  <div className="space-y-2">
                    {testResults.validation.map((result, index) => (
                      <div key={index} className="flex items-center gap-3">
                        {result.passed ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : (
                          <AlertCircle className="h-4 w-4 text-amber-600" />
                        )}
                        <span className="text-sm">{result.message}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Performance Impact */}
                <div>
                  <h4 className="text-sm font-medium mb-3">Performance Estimates</h4>
                  <div className="grid grid-cols-2 gap-3">
                    {testResults.performance.map((metric, index) => (
                      <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                        <span className="text-sm">{metric.metric}</span>
                        <Badge 
                          variant={metric.status === 'good' ? 'default' : metric.status === 'warning' ? 'secondary' : 'destructive'}
                        >
                          {metric.value}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <div className="flex items-center gap-2 text-sm text-green-600">
                    <CheckCircle className="h-4 w-4" />
                    <span className="font-medium">All tests passed! Your blog workflow is ready to automate.</span>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <div className="flex gap-3">
          <Button onClick={prevStep} variant="outline" className="w-full">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button 
            onClick={nextStep} 
            className="w-full bg-gradient-to-r from-green-600 to-blue-600"
            disabled={!testResults}
          >
            Continue to Review <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </motion.div>
    )
  }

  const renderReview = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Settings className="h-16 w-16 mx-auto mb-4 text-gray-600" />
        <h3 className="text-lg font-semibold mb-2">Review Your Configuration</h3>
        <p className="text-muted-foreground text-sm">
          Confirm all settings before creating your automation.
        </p>
      </div>

      <div className="space-y-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <SiGooglesheets className="h-4 w-4 text-green-600" />
              Google Sheets
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0 text-sm text-muted-foreground">
            Sheet: {workflow.googleSheets.sheetName} | Column: {workflow.googleSheets.keywordColumn}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Brain className="h-4 w-4 text-purple-600" />
              AI Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0 text-sm text-muted-foreground">
            {workflow.aiSettings.model} | {workflow.aiSettings.tone} tone | {workflow.aiSettings.length} length
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <SiGmail className="h-4 w-4 text-red-600" />
              Email Approval
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0 text-sm text-muted-foreground">
            {workflow.emailApproval.enabled ? 
              `Enabled | ${workflow.emailApproval.reviewerEmail} | ${workflow.emailApproval.approvalTimeout}h timeout` :
              'Disabled'
            }
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Calendar className="h-4 w-4 text-blue-600" />
              Automation
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0 text-sm text-muted-foreground">
            {workflow.automation.schedule} at {workflow.automation.frequency} | 
            Publish to: {workflow.automation.publishTo.join(', ') || 'None'}
          </CardContent>
        </Card>



        {workflow.selectedActionsList.length > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <Zap className="h-4 w-4 text-purple-600" />
                Selected Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0 text-sm text-muted-foreground">
              {workflow.selectedActionsList.length} action{workflow.selectedActionsList.length !== 1 ? 's' : ''} selected
            </CardContent>
          </Card>
        )}
      </div>

      <div className="flex gap-3">
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={nextStep} className="w-full bg-gradient-to-r from-green-600 to-blue-600">
          Create Automation <Zap className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderComplete = () => (
    <>
      {showCelebration && (
        <EmojiCelebration
          onComplete={() => setShowCelebration(false)}
          duration={3000}
        />
      )}
      
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="text-center space-y-6"
      >
        <div className="w-20 h-20 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto">
          <Check className="h-10 w-10 text-green-600 dark:text-green-400" />
        </div>
        <div>
          <h2 className="text-3xl font-bold mb-2">Automation Created!</h2>
          <p className="text-muted-foreground text-lg">
            Your "{workflow.name}" automation is now ready and will start generating content automatically.
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3 justify-center mt-8">
          <Button 
            onClick={() => setLocation('/dashboard/automations')}
            size="lg"
            className="bg-primary hover:bg-primary/90"
          >
            View All Automations
          </Button>
          <Button 
            onClick={() => setLocation('/dashboard/browse-templates')}
            variant="outline"
            size="lg"
          >
            Browse More Templates
          </Button>
        </div>
      </motion.div>
    </>
  )

  const renderStepContent = () => {
    switch (currentStep) {
      case 'intro': return renderIntro()
      case 'naming': return renderNaming()
      case 'sheets': return renderSheets()
      case 'ai-settings': return renderAISettings()
      case 'email-approval': return renderEmailApproval()
      case 'automation': return renderAutomation()
      case 'actions-question': return renderActionsQuestion()
      case 'actions': return renderActions()
      case 'testing': return renderTesting()
      case 'review': return renderReview()
      case 'complete': return renderComplete()
      default: return null
    }
  }

  const CurrentIcon = stepConfig[currentStep].icon

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => setLocation('/dashboard/browse-templates')}
          className="mb-4"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back to Templates
        </Button>

        {currentStep !== 'complete' && (
          <>
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <CurrentIcon className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">{stepConfig[currentStep].title}</h1>
                <p className="text-muted-foreground text-sm">{stepConfig[currentStep].subtitle}</p>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex justify-between text-xs text-muted-foreground mb-2">
                <span>Step {Object.keys(stepConfig).indexOf(currentStep) + 1} of 9</span>
                <span>{stepConfig[currentStep].progress}% Complete</span>
              </div>
              <Progress value={stepConfig[currentStep].progress} className="h-2" />
            </div>
          </>
        )}
      </div>

      {/* Content */}
      <Card className="p-6">
        {renderStepContent()}
      </Card>
    </div>
  )
}