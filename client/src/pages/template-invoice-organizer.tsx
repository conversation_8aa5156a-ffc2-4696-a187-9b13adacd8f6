import { useState } from 'react'
import { motion } from 'framer-motion'
import { useLocation } from 'wouter'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Checkbox } from "@/components/ui/checkbox"
import { Slider } from "@/components/ui/slider"
import { 
  FileText, Mail, HardDrive, Calendar, DollarSign, Table, Settings, Check, ArrowRight, ChevronLeft,
  X, Eye, Plus, MessageSquare, Hash, Globe, Search, Zap, Filter, Folder<PERSON><PERSON>
} from 'lucide-react'
import { SiGmail, SiGoogledrive, SiGooglesheets } from 'react-icons/si'
import { ActionsSelector } from '@/components/actions-selector'
import { ToolsSelector } from '@/components/tools-selector'

type StepType = 'intro' | 'naming' | 'source-config' | 'extraction-settings' | 'sheets-config' | 'organization-rules' | 'actions-question' | 'actions' | 'review' | 'complete'

interface InvoiceOrganizerConfig {
  name: string
  sourceConfig: {
    type: 'gmail' | 'drive' | 'both'
    googleAuth: {
      isAuthenticated: boolean
      userEmail: string
      userName: string
      accountId: string
    }
    gmail: {
      labels: string[]
      senders: string[]
      subjectFilters: string[]
      attachmentOnly: boolean
    }
    drive: {
      folders: string[]
      filePattern: string
      scanFrequency: 'realtime' | 'hourly' | 'daily'
    }
  }
  extractionSettings: {
    aiProvider: 'openai' | 'anthropic' | 'google'
    model: string
    fieldsToExtract: {
      invoiceNumber: boolean
      date: boolean
      amount: boolean
      vendor: boolean
      taxAmount: boolean
      items: boolean
      dueDate: boolean
      currency: boolean
    }
    customFields: string[]
    confidence: number
  }
  sheetsConfig: {
    spreadsheetId: string
    sheetName: string
    columnMapping: {
      invoiceNumber: string
      date: string
      amount: string
      vendor: string
      taxAmount: string
      dueDate: string
      currency: string
      filePath: string
      processedDate: string
    }
    createIfMissing: boolean
  }
  organizationRules: {
    sortBy: 'date' | 'vendor' | 'amount' | 'custom'
    groupBy: 'month' | 'quarter' | 'year' | 'vendor'
    folderStructure: 'YYYY/MM' | 'YYYY/MM/DD' | 'Vendor/YYYY' | 'custom'
    duplicateHandling: 'skip' | 'overwrite' | 'rename'
    archiveOriginals: boolean
  }
  selectedActionsList: Array<{
    id: string
    type: string
    name: string
    description: string
    config: Record<string, any>
  }>
  wantsActions: boolean | null
}

const defaultConfig: InvoiceOrganizerConfig = {
  name: '',
  sourceConfig: {
    type: 'gmail',
    googleAuth: {
      isAuthenticated: false,
      userEmail: '',
      userName: '',
      accountId: ''
    },
    gmail: {
      labels: ['INBOX'],
      senders: [],
      subjectFilters: ['invoice', 'receipt', 'bill'],
      attachmentOnly: true
    },
    drive: {
      folders: [],
      filePattern: '*.pdf',
      scanFrequency: 'hourly'
    }
  },
  extractionSettings: {
    aiProvider: 'openai',
    model: 'gpt-4o',
    fieldsToExtract: {
      invoiceNumber: true,
      date: true,
      amount: true,
      vendor: true,
      taxAmount: true,
      items: false,
      dueDate: true,
      currency: true
    },
    customFields: [],
    confidence: 0.8
  },
  sheetsConfig: {
    spreadsheetId: '',
    sheetName: 'Invoices',
    columnMapping: {
      invoiceNumber: 'A',
      date: 'B',
      amount: 'C',
      vendor: 'D',
      taxAmount: 'E',
      dueDate: 'F',
      currency: 'G',
      filePath: 'H',
      processedDate: 'I'
    },
    createIfMissing: true
  },
  organizationRules: {
    sortBy: 'date',
    groupBy: 'month',
    folderStructure: 'YYYY/MM',
    duplicateHandling: 'skip',
    archiveOriginals: true
  },
  selectedActionsList: [],
  wantsActions: null
}

const steps: { id: StepType; title: string; icon: React.ComponentType<{ className?: string }> }[] = [
  { id: 'intro', title: 'Introduction', icon: FileText },
  { id: 'naming', title: 'Name Your Automation', icon: FileText },
  { id: 'source-config', title: 'Configure Source', icon: Mail },
  { id: 'extraction-settings', title: 'Extraction Settings', icon: Filter },
  { id: 'sheets-config', title: 'Google Sheets Setup', icon: Table },
  { id: 'organization-rules', title: 'Organization Rules', icon: FolderOpen },
  { id: 'actions-question', title: 'Actions', icon: Zap },
  { id: 'actions', title: 'Configure Actions', icon: Settings },
  { id: 'review', title: 'Review', icon: Eye },
  { id: 'complete', title: 'Complete', icon: Check }
]

export default function InvoiceOrganizerTemplate() {
  const [, setLocation] = useLocation()
  const [config, setConfig] = useState<InvoiceOrganizerConfig>(defaultConfig)
  const [currentStep, setCurrentStep] = useState<StepType>('intro')
  const [showActionConfig, setShowActionConfig] = useState<number | null>(null)
  const [selectedTools, setSelectedTools] = useState<string[]>([])
  const [selectedActions, setSelectedActions] = useState<string[]>([])
  const [wantsMoreActions, setWantsMoreActions] = useState(false)
  const [actionSearchQuery, setActionSearchQuery] = useState('')

  const currentStepIndex = steps.findIndex(s => s.id === currentStep)
  const progress = ((currentStepIndex + 1) / steps.length) * 100

  const nextStep = () => {
    const currentIndex = steps.findIndex(s => s.id === currentStep)
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1].id)
    }
  }

  const prevStep = () => {
    const currentIndex = steps.findIndex(s => s.id === currentStep)
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1].id)
    }
  }

  const renderIntro = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center mx-auto">
        <FileText className="h-10 w-10 text-white" />
      </div>
      <div>
        <h2 className="text-2xl font-bold mb-2">Invoice PDF Organizer</h2>
        <p className="text-muted-foreground max-w-md mx-auto">
          This template creates an automated workflow that monitors Gmail and Google Drive for invoice PDFs, 
          extracts key data using AI, logs information to Google Sheets, and organizes files by month or vendor.
        </p>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Mail className="h-8 w-8 mx-auto mb-2 text-red-600" />
          <p className="text-xs font-medium">Gmail Monitor</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <HardDrive className="h-8 w-8 mx-auto mb-2 text-blue-600" />
          <p className="text-xs font-medium">Drive Sync</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Filter className="h-8 w-8 mx-auto mb-2 text-purple-600" />
          <p className="text-xs font-medium">AI Extraction</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Table className="h-8 w-8 mx-auto mb-2 text-green-600" />
          <p className="text-xs font-medium">Sheets Logging</p>
        </div>
      </div>

      <Button 
        onClick={nextStep} 
        size="lg"
        className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
      >
        Start Setup
        <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
    </motion.div>
  )

  const renderNaming = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <FileText className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h3 className="text-lg font-semibold mb-2">Name Your Automation</h3>
        <p className="text-muted-foreground text-sm">
          Give your invoice organizer a memorable name
        </p>
      </div>

      <div className="max-w-md mx-auto">
        <Label htmlFor="automation-name">Automation Name *</Label>
        <Input
          id="automation-name"
          placeholder="e.g., Monthly Invoice Processor"
          value={config.name}
          onChange={(e) => setConfig({ ...config, name: e.target.value })}
          className="mt-2"
        />
        <p className="text-xs text-muted-foreground mt-2">
          This name will help you identify your automation later
        </p>
      </div>

      <div className="flex gap-3 max-w-md mx-auto">
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          disabled={!config.name.trim()}
          className="w-full"
        >
          Continue
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderSourceConfig = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Mail className="h-16 w-16 mx-auto mb-4 text-blue-600" />
        <h3 className="text-lg font-semibold mb-2">Configure Invoice Source</h3>
        <p className="text-muted-foreground text-sm">
          Choose where to monitor for invoice PDFs
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Source Type</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <RadioGroup
            value={config.sourceConfig.type}
            onValueChange={(value: 'gmail' | 'drive' | 'both') => 
              setConfig({
                ...config,
                sourceConfig: { ...config.sourceConfig, type: value }
              })
            }
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="gmail" id="gmail" />
              <Label htmlFor="gmail" className="font-normal cursor-pointer">
                Gmail (Email attachments)
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="drive" id="drive" />
              <Label htmlFor="drive" className="font-normal cursor-pointer">
                Google Drive (PDF files)
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="both" id="both" />
              <Label htmlFor="both" className="font-normal cursor-pointer">
                Both Gmail and Google Drive
              </Label>
            </div>
          </RadioGroup>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Authentication</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label>Authentication Type *</Label>
            <RadioGroup value="google" className="mt-2">
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="google" id="google-auth" checked disabled />
                <Label htmlFor="google-auth" className="font-normal">
                  Google Sign-In (Required)
                </Label>
              </div>
            </RadioGroup>
          </div>

          {!config.sourceConfig.googleAuth.isAuthenticated ? (
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Note: Select existing Google account from below or Signin with a different account
              </p>
              <Button 
                onClick={() => setConfig({
                  ...config,
                  sourceConfig: {
                    ...config.sourceConfig,
                    googleAuth: {
                      isAuthenticated: true,
                      userEmail: '<EMAIL>',
                      userName: 'John Doe',
                      accountId: '*********'
                    }
                  }
                })}
                className="w-full"
              >
                <SiGmail className="mr-2 h-4 w-4" />
                Sign in with Google
              </Button>
            </div>
          ) : (
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <div>
                  <p className="text-sm font-medium">Google account: {config.sourceConfig.googleAuth.userName} ({config.sourceConfig.googleAuth.userEmail})</p>
                </div>
                <Badge variant="default" className="bg-green-600">
                  Connected
                </Badge>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {(config.sourceConfig.type === 'gmail' || config.sourceConfig.type === 'both') && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Gmail Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="labels">Labels to Monitor</Label>
              <Input
                id="labels"
                placeholder="INBOX, Invoices, Receipts"
                value={config.sourceConfig.gmail.labels.join(', ')}
                onChange={(e) => setConfig({
                  ...config,
                  sourceConfig: {
                    ...config.sourceConfig,
                    gmail: {
                      ...config.sourceConfig.gmail,
                      labels: e.target.value.split(',').map(l => l.trim()).filter(Boolean)
                    }
                  }
                })}
                className="mt-2"
              />
            </div>

            <div>
              <Label htmlFor="senders">Specific Senders (optional)</Label>
              <Textarea
                id="senders"
                placeholder="<EMAIL>&#10;<EMAIL>"
                value={config.sourceConfig.gmail.senders.join('\n')}
                onChange={(e) => setConfig({
                  ...config,
                  sourceConfig: {
                    ...config.sourceConfig,
                    gmail: {
                      ...config.sourceConfig.gmail,
                      senders: e.target.value.split('\n').filter(Boolean)
                    }
                  }
                })}
                className="mt-2"
              />
            </div>

            <div>
              <Label htmlFor="subject-filters">Subject Keywords</Label>
              <Input
                id="subject-filters"
                placeholder="invoice, receipt, bill, payment"
                value={config.sourceConfig.gmail.subjectFilters.join(', ')}
                onChange={(e) => setConfig({
                  ...config,
                  sourceConfig: {
                    ...config.sourceConfig,
                    gmail: {
                      ...config.sourceConfig.gmail,
                      subjectFilters: e.target.value.split(',').map(s => s.trim()).filter(Boolean)
                    }
                  }
                })}
                className="mt-2"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="attachment-only"
                checked={config.sourceConfig.gmail.attachmentOnly}
                onCheckedChange={(checked) => setConfig({
                  ...config,
                  sourceConfig: {
                    ...config.sourceConfig,
                    gmail: {
                      ...config.sourceConfig.gmail,
                      attachmentOnly: checked as boolean
                    }
                  }
                })}
              />
              <Label htmlFor="attachment-only" className="font-normal cursor-pointer">
                Only process emails with PDF attachments
              </Label>
            </div>
          </CardContent>
        </Card>
      )}

      {(config.sourceConfig.type === 'drive' || config.sourceConfig.type === 'both') && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Google Drive Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="folders">Folders to Monitor</Label>
              <Textarea
                id="folders"
                placeholder="/Invoices&#10;/Accounting/2024&#10;/Receipts"
                value={config.sourceConfig.drive.folders.join('\n')}
                onChange={(e) => setConfig({
                  ...config,
                  sourceConfig: {
                    ...config.sourceConfig,
                    drive: {
                      ...config.sourceConfig.drive,
                      folders: e.target.value.split('\n').filter(Boolean)
                    }
                  }
                })}
                className="mt-2"
              />
            </div>

            <div>
              <Label htmlFor="file-pattern">File Pattern</Label>
              <Input
                id="file-pattern"
                placeholder="*.pdf"
                value={config.sourceConfig.drive.filePattern}
                onChange={(e) => setConfig({
                  ...config,
                  sourceConfig: {
                    ...config.sourceConfig,
                    drive: {
                      ...config.sourceConfig.drive,
                      filePattern: e.target.value
                    }
                  }
                })}
                className="mt-2"
              />
            </div>

            <div>
              <Label>Scan Frequency</Label>
              <Select
                value={config.sourceConfig.drive.scanFrequency}
                onValueChange={(value: 'realtime' | 'hourly' | 'daily') => setConfig({
                  ...config,
                  sourceConfig: {
                    ...config.sourceConfig,
                    drive: {
                      ...config.sourceConfig.drive,
                      scanFrequency: value
                    }
                  }
                })}
              >
                <SelectTrigger className="mt-2">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="realtime">Real-time</SelectItem>
                  <SelectItem value="hourly">Every hour</SelectItem>
                  <SelectItem value="daily">Once daily</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="flex gap-3">
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          disabled={!config.sourceConfig.googleAuth.isAuthenticated}
          className="w-full"
        >
          Continue
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderExtractionSettings = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Filter className="h-16 w-16 mx-auto mb-4 text-orange-600" />
        <h3 className="text-lg font-semibold mb-2">Data Extraction Settings</h3>
        <p className="text-muted-foreground text-sm">
          Configure AI to extract data from invoice PDFs
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">AI Provider</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <RadioGroup
            value={config.extractionSettings.aiProvider}
            onValueChange={(value: 'openai' | 'anthropic' | 'google') => {
              let model = ''
              if (value === 'openai') model = 'gpt-4o'
              else if (value === 'anthropic') model = 'claude-3-opus-20240229'
              else if (value === 'google') model = 'gemini-1.5-pro'
              
              setConfig({
                ...config,
                extractionSettings: {
                  ...config.extractionSettings,
                  aiProvider: value,
                  model
                }
              })
            }}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="openai" id="openai" />
              <Label htmlFor="openai" className="font-normal cursor-pointer">
                OpenAI (GPT-4o)
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="anthropic" id="anthropic" />
              <Label htmlFor="anthropic" className="font-normal cursor-pointer">
                Anthropic (Claude 3)
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="google" id="google" />
              <Label htmlFor="google" className="font-normal cursor-pointer">
                Google (Gemini 1.5 Pro)
              </Label>
            </div>
          </RadioGroup>

          <div>
            <Label>Model</Label>
            <Select
              value={config.extractionSettings.model}
              onValueChange={(value) => setConfig({
                ...config,
                extractionSettings: {
                  ...config.extractionSettings,
                  model: value
                }
              })}
            >
              <SelectTrigger className="mt-2">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {config.extractionSettings.aiProvider === 'openai' && (
                  <>
                    <SelectItem value="gpt-4o">GPT-4o (Recommended)</SelectItem>
                    <SelectItem value="gpt-4">GPT-4</SelectItem>
                    <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                  </>
                )}
                {config.extractionSettings.aiProvider === 'anthropic' && (
                  <>
                    <SelectItem value="claude-3-opus-20240229">Claude 3 Opus</SelectItem>
                    <SelectItem value="claude-3-sonnet-20240229">Claude 3 Sonnet</SelectItem>
                    <SelectItem value="claude-3-haiku-20240307">Claude 3 Haiku</SelectItem>
                  </>
                )}
                {config.extractionSettings.aiProvider === 'google' && (
                  <>
                    <SelectItem value="gemini-1.5-pro">Gemini 1.5 Pro</SelectItem>
                    <SelectItem value="gemini-1.0-pro">Gemini 1.0 Pro</SelectItem>
                  </>
                )}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Fields to Extract</CardTitle>
          <CardDescription>Select which data to extract from invoices</CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          {Object.entries({
            invoiceNumber: 'Invoice Number',
            date: 'Invoice Date',
            amount: 'Total Amount',
            vendor: 'Vendor Name',
            taxAmount: 'Tax Amount',
            dueDate: 'Due Date',
            currency: 'Currency',
            items: 'Line Items (Advanced)'
          }).map(([key, label]) => (
            <div key={key} className="flex items-center space-x-2">
              <Checkbox
                id={key}
                checked={config.extractionSettings.fieldsToExtract[key as keyof typeof config.extractionSettings.fieldsToExtract]}
                onCheckedChange={(checked) => setConfig({
                  ...config,
                  extractionSettings: {
                    ...config.extractionSettings,
                    fieldsToExtract: {
                      ...config.extractionSettings.fieldsToExtract,
                      [key]: checked as boolean
                    }
                  }
                })}
              />
              <Label htmlFor={key} className="font-normal cursor-pointer">
                {label}
              </Label>
            </div>
          ))}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Advanced Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="custom-fields">Custom Fields (optional)</Label>
            <Textarea
              id="custom-fields"
              placeholder="Purchase Order Number&#10;Department&#10;Cost Center"
              value={config.extractionSettings.customFields.join('\n')}
              onChange={(e) => setConfig({
                ...config,
                extractionSettings: {
                  ...config.extractionSettings,
                  customFields: e.target.value.split('\n').filter(Boolean)
                }
              })}
              className="mt-2"
            />
            <p className="text-xs text-muted-foreground mt-1">
              Add any additional fields to extract, one per line
            </p>
          </div>

          <div>
            <div className="flex items-center justify-between mb-2">
              <Label>Confidence Threshold</Label>
              <span className="text-sm text-muted-foreground">
                {Math.round(config.extractionSettings.confidence * 100)}%
              </span>
            </div>
            <Slider
              value={[config.extractionSettings.confidence]}
              onValueChange={([value]) => setConfig({
                ...config,
                extractionSettings: {
                  ...config.extractionSettings,
                  confidence: value
                }
              })}
              min={0.5}
              max={1}
              step={0.05}
              className="mt-2"
            />
            <p className="text-xs text-muted-foreground mt-1">
              Only extract data when AI confidence is above this threshold
            </p>
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={nextStep} className="w-full">
          Continue
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderSheetsConfig = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Table className="h-16 w-16 mx-auto mb-4 text-green-600" />
        <h3 className="text-lg font-semibold mb-2">Google Sheets Setup</h3>
        <p className="text-muted-foreground text-sm">
          Configure where to log extracted invoice data
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Spreadsheet Selection</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label>File</Label>
            <p className="text-sm text-muted-foreground mb-2">
              There are no files, please refresh
            </p>
            <Select
              value={config.sheetsConfig.spreadsheetId}
              onValueChange={(value) => setConfig({
                ...config,
                sheetsConfig: { ...config.sheetsConfig, spreadsheetId: value }
              })}
            >
              <SelectTrigger className="mt-2">
                <SelectValue placeholder="Select a spreadsheet" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="new">Create new spreadsheet</SelectItem>
                <SelectItem value="*********0">Invoice Tracker 2024</SelectItem>
                <SelectItem value="**********">Accounting Master Sheet</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="sheet-name">Sheet Name</Label>
            <Input
              id="sheet-name"
              placeholder="Invoices"
              value={config.sheetsConfig.sheetName}
              onChange={(e) => setConfig({
                ...config,
                sheetsConfig: { ...config.sheetsConfig, sheetName: e.target.value }
              })}
              className="mt-2"
            />
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="create-missing"
              checked={config.sheetsConfig.createIfMissing}
              onCheckedChange={(checked) => setConfig({
                ...config,
                sheetsConfig: { ...config.sheetsConfig, createIfMissing: checked as boolean }
              })}
            />
            <Label htmlFor="create-missing" className="font-normal cursor-pointer">
              Create sheet if it doesn't exist
            </Label>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Column Mapping</CardTitle>
          <CardDescription>Map invoice fields to spreadsheet columns</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            {Object.entries({
              invoiceNumber: 'Invoice Number',
              date: 'Date',
              amount: 'Amount',
              vendor: 'Vendor',
              taxAmount: 'Tax Amount',
              dueDate: 'Due Date',
              currency: 'Currency',
              filePath: 'File Path',
              processedDate: 'Processed Date'
            }).map(([key, label]) => (
              <div key={key}>
                <Label htmlFor={`col-${key}`} className="text-sm">{label}</Label>
                <Input
                  id={`col-${key}`}
                  placeholder="A"
                  value={config.sheetsConfig.columnMapping[key as keyof typeof config.sheetsConfig.columnMapping]}
                  onChange={(e) => setConfig({
                    ...config,
                    sheetsConfig: {
                      ...config.sheetsConfig,
                      columnMapping: {
                        ...config.sheetsConfig.columnMapping,
                        [key]: e.target.value
                      }
                    }
                  })}
                  className="mt-1"
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={nextStep} className="w-full">
          Continue
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderOrganizationRules = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <FolderOpen className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h3 className="text-lg font-semibold mb-2">Organization Rules</h3>
        <p className="text-muted-foreground text-sm">
          Define how to organize and sort your invoices
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Sorting & Grouping</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label>Sort By</Label>
            <Select
              value={config.organizationRules.sortBy}
              onValueChange={(value: 'date' | 'vendor' | 'amount' | 'custom') => setConfig({
                ...config,
                organizationRules: { ...config.organizationRules, sortBy: value }
              })}
            >
              <SelectTrigger className="mt-2">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date">Invoice Date</SelectItem>
                <SelectItem value="vendor">Vendor Name</SelectItem>
                <SelectItem value="amount">Amount</SelectItem>
                <SelectItem value="custom">Custom Field</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label>Group By</Label>
            <Select
              value={config.organizationRules.groupBy}
              onValueChange={(value: 'month' | 'quarter' | 'year' | 'vendor') => setConfig({
                ...config,
                organizationRules: { ...config.organizationRules, groupBy: value }
              })}
            >
              <SelectTrigger className="mt-2">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="month">Month</SelectItem>
                <SelectItem value="quarter">Quarter</SelectItem>
                <SelectItem value="year">Year</SelectItem>
                <SelectItem value="vendor">Vendor</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">File Organization</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label>Folder Structure</Label>
            <Select
              value={config.organizationRules.folderStructure}
              onValueChange={(value: 'YYYY/MM' | 'YYYY/MM/DD' | 'Vendor/YYYY' | 'custom') => setConfig({
                ...config,
                organizationRules: { ...config.organizationRules, folderStructure: value }
              })}
            >
              <SelectTrigger className="mt-2">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="YYYY/MM">Year/Month (2024/03)</SelectItem>
                <SelectItem value="YYYY/MM/DD">Year/Month/Day (2024/03/15)</SelectItem>
                <SelectItem value="Vendor/YYYY">Vendor/Year (Acme Corp/2024)</SelectItem>
                <SelectItem value="custom">Custom Pattern</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label>Duplicate Handling</Label>
            <RadioGroup
              value={config.organizationRules.duplicateHandling}
              onValueChange={(value: 'skip' | 'overwrite' | 'rename') => setConfig({
                ...config,
                organizationRules: { ...config.organizationRules, duplicateHandling: value }
              })}
              className="mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="skip" id="skip" />
                <Label htmlFor="skip" className="font-normal">Skip duplicates</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="overwrite" id="overwrite" />
                <Label htmlFor="overwrite" className="font-normal">Overwrite existing</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="rename" id="rename" />
                <Label htmlFor="rename" className="font-normal">Rename with suffix</Label>
              </div>
            </RadioGroup>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="archive"
              checked={config.organizationRules.archiveOriginals}
              onCheckedChange={(checked) => setConfig({
                ...config,
                organizationRules: { ...config.organizationRules, archiveOriginals: checked as boolean }
              })}
            />
            <Label htmlFor="archive" className="font-normal cursor-pointer">
              Archive original files after processing
            </Label>
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={nextStep} className="w-full">
          Continue
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderActionsQuestion = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Zap className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h3 className="text-lg font-semibold mb-2">Do you want to add any actions?</h3>
        <p className="text-muted-foreground text-sm mb-6">
          Extend your automation with additional actions (optional)
        </p>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-6">
        <Button
          variant={config.wantsActions === true ? "default" : "outline"}
          size="lg"
          onClick={() => {
            setConfig({ ...config, wantsActions: true })
            nextStep()
          }}
          className="h-auto py-6"
        >
          <div className="flex flex-col items-center gap-2">
            <Check className="h-8 w-8" />
            <span>Yes, I want to add actions</span>
          </div>
        </Button>
        <Button
          variant={config.wantsActions === false ? "default" : "outline"}
          size="lg"
          onClick={() => {
            setConfig({ ...config, wantsActions: false })
            setCurrentStep('review')
          }}
          className="h-auto py-6"
        >
          <div className="flex flex-col items-center gap-2">
            <ArrowRight className="h-8 w-8" />
            <span>No, continue without</span>
          </div>
        </Button>
      </div>

      <div>
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
      </div>
    </motion.div>
  )

  const renderActions = () => {
    const selectedActionsList = config.selectedActionsList || []

    const availableActionsList = [
      { id: 'discord-read', name: 'Discord Read', icon: MessageSquare, desc: 'Read messages from any Discord channel' },
      { id: 'discord-send', name: 'Discord Send', icon: MessageSquare, desc: 'Send messages to any Discord channel' },
      { id: 'gmail-read', name: 'Gmail Read', icon: Mail, desc: 'Read emails from your Gmail account' },
      { id: 'gmail-send', name: 'Gmail Send', icon: Mail, desc: 'Send emails via your Gmail account' },
      { id: 'gcalendar-read', name: 'Google Calendar Read', icon: Calendar, desc: 'Read events from any Google Calendar' },
      { id: 'gcalendar-write', name: 'Google Calendar Write', icon: Calendar, desc: 'Create events on any Google Calendar' },
      { id: 'http-request', name: 'HTTP Request', icon: Globe, desc: 'Make GET, POST, PUT, or DELETE HTTP requests' },
      { id: 'slack-read', name: 'Slack Read', icon: Hash, desc: 'Read messages from a specified Slack channel' },
      { id: 'slack-send', name: 'Slack Send', icon: Hash, desc: 'Send a message to a specified Slack channel' }
    ]

    // Filter actions based on search query
    const filteredActions = availableActionsList.filter(action => {
      const query = actionSearchQuery.toLowerCase()
      return (
        action.name.toLowerCase().includes(query) ||
        action.desc.toLowerCase().includes(query)
      )
    })

    const addAction = (action: any) => {
      const newAction = {
        id: Date.now().toString(),
        type: action.id,
        name: action.name,
        description: action.desc,
        config: {}
      }
      const updatedList = [...selectedActionsList, newAction]
      setConfig({ ...config, selectedActionsList: updatedList })
      setShowActionConfig(updatedList.length - 1)
      setWantsMoreActions(false)
    }

    const removeAction = (index: number) => {
      const updatedList = selectedActionsList.filter((_, i) => i !== index)
      setConfig({ ...config, selectedActionsList: updatedList })
    }

    // If no actions yet or wants more actions, show action selection
    if (selectedActionsList.length === 0 || wantsMoreActions) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          <div className="text-center">
            <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
            <h3 className="text-lg font-semibold mb-2">
              {selectedActionsList.length === 0 ? 'What should happen next?' : 'Add another action?'}
            </h3>
            <p className="text-muted-foreground text-sm">
              {selectedActionsList.length === 0 
                ? 'Choose an action to perform when your trigger fires'
                : 'Chain multiple actions to create powerful workflows'}
            </p>
          </div>

          {/* Search */}
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search actions..."
                value={actionSearchQuery}
                onChange={(e) => setActionSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Available Actions */}
          {filteredActions.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No actions found matching "{actionSearchQuery}"</p>
            </div>
          ) : (
            <div className="grid gap-3 max-w-2xl mx-auto">
              {filteredActions.map((action) => (
                <Card
                  key={action.id}
                  className="cursor-pointer transition-all hover:shadow-md"
                  onClick={() => addAction(action)}
                >
                  <CardHeader className="p-4">
                    <div className="flex items-center gap-3">
                      <action.icon className="h-5 w-5 text-primary" />
                      <div className="flex-1">
                        <CardTitle className="text-sm">{action.name}</CardTitle>
                        <CardDescription className="text-xs mt-1">{action.desc}</CardDescription>
                      </div>
                      <Plus className="h-5 w-5 text-muted-foreground" />
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          )}

          <div className="flex gap-3 max-w-2xl mx-auto">
            <Button onClick={prevStep} variant="outline" className="w-full">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Previous
            </Button>
            {selectedActionsList.length > 0 && (
              <Button 
                onClick={() => setWantsMoreActions(false)} 
                variant="outline"
                className="w-full"
              >
                Continue without adding
              </Button>
            )}
          </div>
        </motion.div>
      )
    }

    // Show current actions with option to add more
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <div className="text-center">
          <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
          <h3 className="text-lg font-semibold mb-2">Your Action Chain</h3>
          <p className="text-muted-foreground text-sm">
            {selectedActionsList.length} action{selectedActionsList.length !== 1 ? 's' : ''} configured
          </p>
        </div>

        {/* Action Chain Visualization */}
        <div className="max-w-2xl mx-auto space-y-3">
          {/* Trigger */}
          <div className="flex items-center gap-3 p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg border-2 border-purple-200 dark:border-purple-800">
            <FileText className="h-5 w-5 text-purple-600" />
            <div className="flex-1">
              <p className="font-medium">Trigger: Invoice Detection</p>
              <p className="text-xs text-muted-foreground">
                Monitors {config.sourceConfig.type === 'both' ? 'Gmail & Drive' : config.sourceConfig.type} for PDF invoices
              </p>
            </div>
          </div>

          {/* Arrow */}
          <div className="flex justify-center">
            <ArrowRight className="h-4 w-4 text-muted-foreground" />
          </div>

          {/* Actions */}
          {selectedActionsList.map((action, index) => (
            <div key={action.id}>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center gap-3 p-4 bg-muted/30 rounded-lg border"
              >
                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-xs font-medium">
                  {index + 1}
                </div>
                <div className="flex-1">
                  <p className="font-medium">{action.name}</p>
                  <p className="text-xs text-muted-foreground">{action.description}</p>
                </div>
                <div className="flex gap-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setShowActionConfig(index)}
                    className="h-8 px-2"
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => removeAction(index)}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </motion.div>
              {index < selectedActionsList.length - 1 && (
                <div className="flex justify-center my-2">
                  <ArrowRight className="h-4 w-4 text-muted-foreground" />
                </div>
              )}
            </div>
          ))}

          {/* Add Another Action Button */}
          <Button
            variant="outline"
            onClick={() => setWantsMoreActions(true)}
            className="w-full"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Another Action
          </Button>
        </div>

        <div className="flex gap-3 max-w-2xl mx-auto">
          <Button onClick={prevStep} variant="outline" className="w-full">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button 
            onClick={nextStep} 
            className="w-full"
            disabled={selectedActionsList.length === 0}
          >
            Continue <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>

        {/* Action Configuration Modal */}
        {showActionConfig !== null && selectedActionsList[showActionConfig] && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowActionConfig(null)}
          >
            <motion.div
              initial={{ scale: 0.95 }}
              animate={{ scale: 1 }}
              className="bg-card p-6 rounded-lg shadow-lg max-w-md w-full max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">
                Configure {selectedActionsList[showActionConfig].name}
              </h3>
              
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Configuration options will appear here based on the action type.
                </p>
                <div className="flex justify-end gap-2 mt-6">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowActionConfig(null)}
                  >
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => setShowActionConfig(null)}
                  >
                    Save Configuration
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </motion.div>
    )
  }

  const renderReview = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Eye className="h-16 w-16 mx-auto mb-4 text-indigo-600" />
        <h3 className="text-lg font-semibold mb-2">Review Your Configuration</h3>
        <p className="text-muted-foreground text-sm">
          Check your settings before creating the automation
        </p>
      </div>

      <div className="space-y-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Automation Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Name:</span>
              <span className="font-medium">{config.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Source:</span>
              <span className="font-medium capitalize">{config.sourceConfig.type}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Google Account:</span>
              <span className="font-medium">{config.sourceConfig.googleAuth.userEmail}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Extraction Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">AI Provider:</span>
              <span className="font-medium capitalize">{config.extractionSettings.aiProvider}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Model:</span>
              <span className="font-medium">{config.extractionSettings.model}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Fields:</span>
              <span className="font-medium">
                {Object.entries(config.extractionSettings.fieldsToExtract)
                  .filter(([_, enabled]) => enabled)
                  .length} selected
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Organization</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Group By:</span>
              <span className="font-medium capitalize">{config.organizationRules.groupBy}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Folder Structure:</span>
              <span className="font-medium">{config.organizationRules.folderStructure}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Actions:</span>
              <span className="font-medium">
                {config.wantsActions ? `${selectedActions.length} configured` : 'None'}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex gap-3">
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={nextStep} className="w-full">
          Create Automation
          <Check className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderComplete = () => (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto">
        <Check className="h-10 w-10 text-green-600" />
      </div>
      
      <div>
        <h1 className="text-2xl font-bold mb-2">Automation Created!</h1>
        <p className="text-muted-foreground">
          Your invoice organizer has been set up successfully
        </p>
      </div>

      <Card className="max-w-md mx-auto">
        <CardContent className="pt-6">
          <div className="space-y-3 text-sm">
            <div className="flex items-center gap-2">
              <Check className="h-4 w-4 text-green-600" />
              <span>Monitoring {config.sourceConfig.type} for invoices</span>
            </div>
            <div className="flex items-center gap-2">
              <Check className="h-4 w-4 text-green-600" />
              <span>AI extraction configured</span>
            </div>
            <div className="flex items-center gap-2">
              <Check className="h-4 w-4 text-green-600" />
              <span>Google Sheets logging enabled</span>
            </div>
            <div className="flex items-center gap-2">
              <Check className="h-4 w-4 text-green-600" />
              <span>Organization rules applied</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3 max-w-md mx-auto">
        <Button onClick={() => setLocation('/dashboard/automations')} className="w-full">
          View My Automations
        </Button>
        <Button onClick={() => setLocation('/dashboard/browse-templates')} variant="outline" className="w-full">
          Back to Templates
        </Button>
      </div>
    </motion.div>
  )

  const renderStep = () => {
    switch (currentStep) {
      case 'intro':
        return renderIntro()
      case 'naming':
        return renderNaming()
      case 'source-config':
        return renderSourceConfig()
      case 'extraction-settings':
        return renderExtractionSettings()
      case 'sheets-config':
        return renderSheetsConfig()
      case 'organization-rules':
        return renderOrganizationRules()
      case 'actions-question':
        return renderActionsQuestion()
      case 'actions':
        return renderActions()
      case 'review':
        return renderReview()
      case 'complete':
        return renderComplete()
      default:
        return null
    }
  }

  const getCurrentStepConfig = () => {
    const step = steps.find(s => s.id === currentStep)
    return step || steps[0]
  }

  const stepConfig = getCurrentStepConfig()

  const CurrentIcon = stepConfig.icon

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => setLocation('/dashboard/browse-templates')}
          className="mb-4"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back to Templates
        </Button>

        {currentStep !== 'complete' && (
          <>
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <CurrentIcon className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">{stepConfig.title}</h1>
                <p className="text-muted-foreground text-sm">
                  Configure your invoice organizer settings
                </p>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex justify-between text-xs text-muted-foreground mb-2">
                <span>Step {currentStepIndex + 1} of {steps.length - 2}</span>
                <span>{Math.round(progress)}% Complete</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          </>
        )}
      </div>

      {/* Content */}
      {currentStep !== 'intro' && currentStep !== 'complete' && (
        <Card>
          <CardContent className="pt-6">
            {renderStep()}
          </CardContent>
        </Card>
      )}
      
      {(currentStep === 'intro' || currentStep === 'complete') && renderStep()}
    </div>
  )
}