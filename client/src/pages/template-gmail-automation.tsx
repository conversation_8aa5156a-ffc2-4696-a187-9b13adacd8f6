import { useState } from 'react'
import { motion } from 'framer-motion'
import { useLocation } from 'wouter'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Checkbox } from "@/components/ui/checkbox"
import { Switch } from "@/components/ui/switch"
import { 
  Mail, Shield, Brain, Clock, Settings, Bell, Check, ArrowRight, ChevronLeft,
  X, Eye, Plus, Calendar, MessageSquare, Hash, Globe, Filter, Flag, Users, Archive, Search
} from 'lucide-react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ai, Si<PERSON>nthropic, <PERSON><PERSON><PERSON><PERSON> } from 'react-icons/si'

type StepType = 'intro' | 'naming' | 'gmail-connection' | 'api-integration' | 'polling-settings' | 'analysis-settings' | 'approval-settings' | 'notification-settings' | 'actions-question' | 'actions' | 'review' | 'complete'

interface GmailAutomationConfig {
  name: string
  googleAuth: {
    isAuthenticated: boolean
    userEmail: string
    userName: string
    accountId: string
  }
  gmailConnection: {
    email: string
    scopes: string[]
  }
  apiIntegration: {
    provider: 'openai' | 'anthropic' | 'google'
    model: string
    isConfigured: boolean
  }
  pollingSettings: {
    frequency: number
    timeUnit: 'minutes' | 'hours'
    filters: {
      labels: string[]
      senders: string[]
      keywords: string[]
      unreadOnly: boolean
    }
  }
  analysisSettings: {
    actions: {
      autoSort: boolean
      flagImportant: boolean
      sendAlerts: boolean
      routeToHuman: boolean
      autoArchive: boolean
      autoReply: boolean
    }
    alertThresholds: {
      urgentKeywords: string[]
      vipSenders: string[]
    }
  }
  approvalSettings: {
    requireApproval: boolean
    approvers: string[]
    timeoutHours: number
    defaultAction: 'archive' | 'flag' | 'route' | 'none'
  }
  notificationSettings: {
    channels: {
      email: boolean
      slack: boolean
      webhook: boolean
    }
    recipients: string[]
    slackWebhook: string
    customWebhook: string
  }
  selectedActionsList: Array<{
    id: string
    type: string
    name: string
    description: string
    config: Record<string, any>
  }>
  wantsActions: boolean | null
}

export default function GmailEmailAutomationTemplate() {
  const [, setLocation] = useLocation()
  const [currentStep, setCurrentStep] = useState<StepType>('intro')
  const [showActionConfig, setShowActionConfig] = useState<number | null>(null)
  const [config, setConfig] = useState<GmailAutomationConfig>({
    name: '',
    googleAuth: {
      isAuthenticated: false,
      userEmail: '',
      userName: '',
      accountId: ''
    },
    gmailConnection: {
      email: '',
      scopes: ['gmail.readonly', 'gmail.modify']
    },
    apiIntegration: {
      provider: 'openai',
      model: 'gpt-4o',
      isConfigured: false
    },
    pollingSettings: {
      frequency: 5,
      timeUnit: 'minutes',
      filters: {
        labels: [],
        senders: [],
        keywords: [],
        unreadOnly: true
      }
    },
    analysisSettings: {
      actions: {
        autoSort: true,
        flagImportant: true,
        sendAlerts: true,
        routeToHuman: false,
        autoArchive: false,
        autoReply: false
      },
      alertThresholds: {
        urgentKeywords: ['urgent', 'asap', 'emergency', 'critical'],
        vipSenders: []
      }
    },
    approvalSettings: {
      requireApproval: false,
      approvers: [],
      timeoutHours: 24,
      defaultAction: 'flag'
    },
    notificationSettings: {
      channels: {
        email: true,
        slack: false,
        webhook: false
      },
      recipients: [],
      slackWebhook: '',
      customWebhook: ''
    },
    selectedActionsList: [],
    wantsActions: null
  })

  const stepConfig = {
    intro: { 
      title: 'Gmail Email Automation', 
      subtitle: 'Intelligently monitor and automate your Gmail inbox',
      icon: Mail,
      progress: 0
    },
    naming: {
      title: 'Name Your Automation',
      subtitle: 'Give your automation a memorable name',
      icon: Settings,
      progress: 8
    },
    'gmail-connection': { 
      title: 'Connect Gmail', 
      subtitle: 'Authenticate with your Gmail account',
      icon: SiGmail,
      progress: 16
    },
    'api-integration': { 
      title: 'AI Integration', 
      subtitle: 'Configure AI for email analysis',
      icon: Brain,
      progress: 24
    },
    'polling-settings': { 
      title: 'Polling Settings', 
      subtitle: 'Define how often to check for new emails',
      icon: Clock,
      progress: 32
    },
    'analysis-settings': { 
      title: 'Analysis Settings', 
      subtitle: 'Configure AI analysis actions',
      icon: Filter,
      progress: 40
    },
    'approval-settings': { 
      title: 'Human Approval', 
      subtitle: 'Set up approval workflow',
      icon: Users,
      progress: 48
    },
    'notification-settings': { 
      title: 'Notifications', 
      subtitle: 'Configure alert channels',
      icon: Bell,
      progress: 56
    },
    'actions-question': { 
      title: 'Additional Actions', 
      subtitle: 'Add more capabilities to your automation',
      icon: Plus,
      progress: 64
    },
    'actions': { 
      title: 'Configure Actions', 
      subtitle: 'Set up your selected actions',
      icon: Settings,
      progress: 72
    },
    review: { 
      title: 'Review Configuration', 
      subtitle: 'Confirm your automation settings',
      icon: Eye,
      progress: 90
    },
    complete: { 
      title: 'Complete', 
      subtitle: 'Your automation is ready',
      icon: Check,
      progress: 100
    }
  }

  const nextStep = () => {
    const steps: StepType[] = ['intro', 'naming', 'gmail-connection', 'api-integration', 'polling-settings', 'analysis-settings', 'approval-settings', 'notification-settings', 'actions-question', 'actions', 'review', 'complete']
    const currentIndex = steps.indexOf(currentStep)
    
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1])
    }
  }

  const prevStep = () => {
    const steps: StepType[] = ['intro', 'naming', 'gmail-connection', 'api-integration', 'polling-settings', 'analysis-settings', 'approval-settings', 'notification-settings', 'actions-question', 'actions', 'review', 'complete']
    const currentIndex = steps.indexOf(currentStep)
    
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1])
    }
  }

  const renderIntro = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-gradient-to-br from-red-500 to-pink-600 rounded-full flex items-center justify-center mx-auto">
        <Mail className="h-10 w-10 text-white" />
      </div>
      <div>
        <h2 className="text-2xl font-bold mb-2">Gmail Email Automation</h2>
        <p className="text-muted-foreground max-w-md mx-auto">
          This template creates an intelligent email monitoring system that automatically
          analyzes incoming Gmail messages, sorts them based on importance, sends alerts
          for urgent items, and routes complex requests for human review.
        </p>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <SiGmail className="h-8 w-8 mx-auto mb-2 text-red-600" />
          <p className="text-xs font-medium">Gmail Monitor</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Brain className="h-8 w-8 mx-auto mb-2 text-purple-600" />
          <p className="text-xs font-medium">AI Analysis</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Filter className="h-8 w-8 mx-auto mb-2 text-blue-600" />
          <p className="text-xs font-medium">Smart Sorting</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Bell className="h-8 w-8 mx-auto mb-2 text-orange-600" />
          <p className="text-xs font-medium">Instant Alerts</p>
        </div>
      </div>

      <Button 
        onClick={nextStep} 
        size="lg"
        className="bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700"
      >
        Start Setup
        <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
    </motion.div>
  )

  const renderNaming = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Settings className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h2 className="text-xl font-semibold mb-2">Name Your Automation</h2>
        <p className="text-muted-foreground">
          Give your automation a memorable name that describes what it does
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="automation-name">Automation Name *</Label>
          <Input
            id="automation-name"
            placeholder="e.g., Customer Support Email Triage, VIP Client Monitor"
            value={config.name}
            onChange={(e) => setConfig({ ...config, name: e.target.value })}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && config.name) {
                nextStep()
              }
            }}
            className="mt-2"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Choose a descriptive name you'll easily recognize
          </p>
        </div>
      </div>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          disabled={!config.name}
          className="w-full"
        >
          Continue to Gmail Setup
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderGmailConnection = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <SiGmail className="h-16 w-16 mx-auto mb-4 text-red-600" />
        <h3 className="text-lg font-semibold mb-2">Connect Your Gmail</h3>
        <p className="text-muted-foreground text-sm">
          Authenticate to monitor and manage your Gmail inbox
        </p>
      </div>

      <Card>
        <CardContent className="p-6 space-y-4">
          <div>
            <Label className="text-sm font-medium">Authentication Type <span className="text-red-500">*</span></Label>
            <div className="mt-2">
              <div className="flex items-center space-x-2">
                <input type="radio" id="google-signin" checked readOnly className="text-blue-600" />
                <Label htmlFor="google-signin" className="text-sm">Google Sign-In</Label>
              </div>
            </div>
          </div>

          <div className="text-xs text-muted-foreground">
            <strong>Note:</strong> Select existing Google account from below or Signin with a different account.
          </div>

          {!config.googleAuth.isAuthenticated ? (
            <Button
              onClick={() => setConfig({
                ...config,
                googleAuth: {
                  isAuthenticated: true,
                  userEmail: '<EMAIL>',
                  userName: 'John Doe',
                  accountId: 'gauth-12345'
                }
              })}
              className="w-full"
            >
              <SiGmail className="mr-2 h-4 w-4" />
              Sign in with Google
            </Button>
          ) : (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Badge variant="default" className="bg-green-600">Connected</Badge>
                <span className="text-sm font-medium">Google</span>
              </div>
              
              <div className="space-y-2">
                <Label className="text-sm font-medium">Google account</Label>
                <div className="p-3 border rounded-md bg-muted/50">
                  <div className="text-sm font-medium">{config.googleAuth.userName}</div>
                  <div className="text-sm text-muted-foreground">({config.googleAuth.userEmail})</div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          disabled={!config.googleAuth.isAuthenticated}
          className="w-full"
        >
          Continue to AI Setup
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderApiIntegration = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Brain className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h3 className="text-lg font-semibold mb-2">Configure AI Integration</h3>
        <p className="text-muted-foreground text-sm">
          Choose your AI provider for intelligent email analysis
        </p>
      </div>

      <Card>
        <CardContent className="p-6 space-y-4">
          <div>
            <Label>AI Provider</Label>
            <RadioGroup
              value={config.apiIntegration.provider}
              onValueChange={(value) => setConfig({
                ...config,
                apiIntegration: { ...config.apiIntegration, provider: value as any }
              })}
              className="mt-2"
            >
              <div className="flex items-center space-x-2 p-3 border rounded-lg">
                <RadioGroupItem value="openai" id="openai" />
                <Label htmlFor="openai" className="flex items-center gap-2 cursor-pointer">
                  <SiOpenai className="h-4 w-4" />
                  OpenAI
                </Label>
              </div>
              <div className="flex items-center space-x-2 p-3 border rounded-lg">
                <RadioGroupItem value="anthropic" id="anthropic" />
                <Label htmlFor="anthropic" className="flex items-center gap-2 cursor-pointer">
                  <SiAnthropic className="h-4 w-4" />
                  Anthropic
                </Label>
              </div>
              <div className="flex items-center space-x-2 p-3 border rounded-lg">
                <RadioGroupItem value="google" id="google" />
                <Label htmlFor="google" className="flex items-center gap-2 cursor-pointer">
                  <SiGoogle className="h-4 w-4" />
                  Google AI
                </Label>
              </div>
            </RadioGroup>
          </div>

          <div>
            <Label>Model Selection</Label>
            <Select
              value={config.apiIntegration.model}
              onValueChange={(value) => setConfig({
                ...config,
                apiIntegration: { ...config.apiIntegration, model: value }
              })}
            >
              <SelectTrigger className="mt-2">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {config.apiIntegration.provider === 'openai' && (
                  <>
                    <SelectItem value="gpt-4o">GPT-4o (Recommended)</SelectItem>
                    <SelectItem value="gpt-4">GPT-4</SelectItem>
                    <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                  </>
                )}
                {config.apiIntegration.provider === 'anthropic' && (
                  <>
                    <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
                    <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                    <SelectItem value="claude-3-haiku">Claude 3 Haiku</SelectItem>
                  </>
                )}
                {config.apiIntegration.provider === 'google' && (
                  <>
                    <SelectItem value="gemini-1.5-pro">Gemini 1.5 Pro</SelectItem>
                    <SelectItem value="gemini-1.0-pro">Gemini 1.0 Pro</SelectItem>
                  </>
                )}
              </SelectContent>
            </Select>
          </div>

          <Button
            className="w-full"
            onClick={() => setConfig({
              ...config,
              apiIntegration: { ...config.apiIntegration, isConfigured: true }
            })}
          >
            Verify API Configuration
          </Button>

          {config.apiIntegration.isConfigured && (
            <div className="flex items-center gap-2 text-green-600">
              <Check className="h-4 w-4" />
              <span className="text-sm">AI integration configured successfully</span>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          disabled={!config.apiIntegration.isConfigured}
          className="w-full"
        >
          Continue to Polling Setup
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderPollingSettings = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Clock className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h3 className="text-lg font-semibold mb-2">Polling Settings</h3>
        <p className="text-muted-foreground text-sm">
          Configure how often to check for new emails
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Check Frequency</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="frequency">Frequency</Label>
              <Input
                id="frequency"
                type="number"
                min="1"
                value={config.pollingSettings.frequency}
                onChange={(e) => setConfig({
                  ...config,
                  pollingSettings: { 
                    ...config.pollingSettings, 
                    frequency: parseInt(e.target.value) 
                  }
                })}
                className="mt-2"
              />
            </div>
            <div>
              <Label htmlFor="timeUnit">Time Unit</Label>
              <Select
                value={config.pollingSettings.timeUnit}
                onValueChange={(value) => setConfig({
                  ...config,
                  pollingSettings: { 
                    ...config.pollingSettings, 
                    timeUnit: value as any 
                  }
                })}
              >
                <SelectTrigger id="timeUnit" className="mt-2">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="minutes">Minutes</SelectItem>
                  <SelectItem value="hours">Hours</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Email Filters</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="unreadOnly"
              checked={config.pollingSettings.filters.unreadOnly}
              onCheckedChange={(checked) => setConfig({
                ...config,
                pollingSettings: {
                  ...config.pollingSettings,
                  filters: {
                    ...config.pollingSettings.filters,
                    unreadOnly: checked as boolean
                  }
                }
              })}
            />
            <Label htmlFor="unreadOnly" className="text-sm font-normal cursor-pointer">
              Only process unread emails
            </Label>
          </div>

          <div>
            <Label htmlFor="labels">Gmail Labels (comma-separated)</Label>
            <Input
              id="labels"
              placeholder="e.g., INBOX, IMPORTANT"
              value={config.pollingSettings.filters.labels.join(', ')}
              onChange={(e) => setConfig({
                ...config,
                pollingSettings: {
                  ...config.pollingSettings,
                  filters: {
                    ...config.pollingSettings.filters,
                    labels: e.target.value.split(',').map(l => l.trim()).filter(Boolean)
                  }
                }
              })}
              className="mt-2"
            />
          </div>

          <div>
            <Label htmlFor="senders">Specific Senders (comma-separated)</Label>
            <Input
              id="senders"
              placeholder="e.g., <EMAIL>, <EMAIL>"
              value={config.pollingSettings.filters.senders.join(', ')}
              onChange={(e) => setConfig({
                ...config,
                pollingSettings: {
                  ...config.pollingSettings,
                  filters: {
                    ...config.pollingSettings.filters,
                    senders: e.target.value.split(',').map(s => s.trim()).filter(Boolean)
                  }
                }
              })}
              className="mt-2"
            />
            <p className="text-xs text-muted-foreground mt-1">
              Leave empty to monitor all senders
            </p>
          </div>

          <div>
            <Label htmlFor="keywords">Keywords to Monitor (comma-separated)</Label>
            <Input
              id="keywords"
              placeholder="e.g., urgent, invoice, payment"
              value={config.pollingSettings.filters.keywords.join(', ')}
              onChange={(e) => setConfig({
                ...config,
                pollingSettings: {
                  ...config.pollingSettings,
                  filters: {
                    ...config.pollingSettings.filters,
                    keywords: e.target.value.split(',').map(k => k.trim()).filter(Boolean)
                  }
                }
              })}
              className="mt-2"
            />
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          className="w-full"
        >
          Continue to Analysis Settings
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderAnalysisSettings = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Filter className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h3 className="text-lg font-semibold mb-2">Analysis Settings</h3>
        <p className="text-muted-foreground text-sm">
          Configure what the AI should do with analyzed emails
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Automated Actions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {Object.entries({
            autoSort: 'Automatically sort emails into labels',
            flagImportant: 'Flag important emails',
            sendAlerts: 'Send alerts for urgent emails',
            routeToHuman: 'Route complex requests to human review',
            autoArchive: 'Auto-archive processed emails',
            autoReply: 'Send automated replies'
          }).map(([key, label]) => (
            <div key={key} className="flex items-center justify-between">
              <Label htmlFor={key} className="text-sm font-normal cursor-pointer">
                {label}
              </Label>
              <Switch
                id={key}
                checked={config.analysisSettings.actions[key as keyof typeof config.analysisSettings.actions]}
                onCheckedChange={(checked) => setConfig({
                  ...config,
                  analysisSettings: {
                    ...config.analysisSettings,
                    actions: {
                      ...config.analysisSettings.actions,
                      [key]: checked
                    }
                  }
                })}
              />
            </div>
          ))}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Alert Thresholds</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="urgentKeywords">Urgent Keywords (comma-separated)</Label>
            <Input
              id="urgentKeywords"
              placeholder="e.g., urgent, asap, emergency, critical"
              value={config.analysisSettings.alertThresholds.urgentKeywords.join(', ')}
              onChange={(e) => setConfig({
                ...config,
                analysisSettings: {
                  ...config.analysisSettings,
                  alertThresholds: {
                    ...config.analysisSettings.alertThresholds,
                    urgentKeywords: e.target.value.split(',').map(k => k.trim()).filter(Boolean)
                  }
                }
              })}
              className="mt-2"
            />
          </div>

          <div>
            <Label htmlFor="vipSenders">VIP Senders (comma-separated)</Label>
            <Input
              id="vipSenders"
              placeholder="e.g., <EMAIL>, <EMAIL>"
              value={config.analysisSettings.alertThresholds.vipSenders.join(', ')}
              onChange={(e) => setConfig({
                ...config,
                analysisSettings: {
                  ...config.analysisSettings,
                  alertThresholds: {
                    ...config.analysisSettings.alertThresholds,
                    vipSenders: e.target.value.split(',').map(s => s.trim()).filter(Boolean)
                  }
                }
              })}
              className="mt-2"
            />
            <p className="text-xs text-muted-foreground mt-1">
              Emails from these senders will always trigger alerts
            </p>
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          className="w-full"
        >
          Continue to Approval Settings
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderApprovalSettings = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Users className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h3 className="text-lg font-semibold mb-2">Human Approval Settings</h3>
        <p className="text-muted-foreground text-sm">
          Configure when and how to involve human review
        </p>
      </div>

      <Card>
        <CardContent className="p-6 space-y-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="requireApproval" className="text-sm font-normal cursor-pointer">
              Require human approval for certain actions
            </Label>
            <Switch
              id="requireApproval"
              checked={config.approvalSettings.requireApproval}
              onCheckedChange={(checked) => setConfig({
                ...config,
                approvalSettings: {
                  ...config.approvalSettings,
                  requireApproval: checked
                }
              })}
            />
          </div>

          {config.approvalSettings.requireApproval && (
            <>
              <div>
                <Label htmlFor="approvers">Approver Emails (comma-separated)</Label>
                <Input
                  id="approvers"
                  placeholder="e.g., <EMAIL>, <EMAIL>"
                  value={config.approvalSettings.approvers.join(', ')}
                  onChange={(e) => setConfig({
                    ...config,
                    approvalSettings: {
                      ...config.approvalSettings,
                      approvers: e.target.value.split(',').map(a => a.trim()).filter(Boolean)
                    }
                  })}
                  className="mt-2"
                />
              </div>

              <div>
                <Label htmlFor="timeout">Approval Timeout (hours)</Label>
                <Input
                  id="timeout"
                  type="number"
                  min="1"
                  value={config.approvalSettings.timeoutHours}
                  onChange={(e) => setConfig({
                    ...config,
                    approvalSettings: {
                      ...config.approvalSettings,
                      timeoutHours: parseInt(e.target.value)
                    }
                  })}
                  className="mt-2"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Default action if no response within timeout
                </p>
              </div>

              <div>
                <Label>Default Action on Timeout</Label>
                <Select
                  value={config.approvalSettings.defaultAction}
                  onValueChange={(value) => setConfig({
                    ...config,
                    approvalSettings: {
                      ...config.approvalSettings,
                      defaultAction: value as any
                    }
                  })}
                >
                  <SelectTrigger className="mt-2">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="archive">Archive Email</SelectItem>
                    <SelectItem value="flag">Flag for Review</SelectItem>
                    <SelectItem value="route">Route to Support</SelectItem>
                    <SelectItem value="none">No Action</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          className="w-full"
        >
          Continue to Notifications
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderNotificationSettings = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Bell className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h3 className="text-lg font-semibold mb-2">Notification Settings</h3>
        <p className="text-muted-foreground text-sm">
          Configure how to receive alerts and notifications
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Notification Channels</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="emailNotif" className="text-sm font-normal cursor-pointer">
                Email Notifications
              </Label>
              <Switch
                id="emailNotif"
                checked={config.notificationSettings.channels.email}
                onCheckedChange={(checked) => setConfig({
                  ...config,
                  notificationSettings: {
                    ...config.notificationSettings,
                    channels: {
                      ...config.notificationSettings.channels,
                      email: checked
                    }
                  }
                })}
              />
            </div>

            {config.notificationSettings.channels.email && (
              <div className="ml-6">
                <Label htmlFor="recipients">Recipient Emails (comma-separated)</Label>
                <Input
                  id="recipients"
                  placeholder="e.g., <EMAIL>, <EMAIL>"
                  value={config.notificationSettings.recipients.join(', ')}
                  onChange={(e) => setConfig({
                    ...config,
                    notificationSettings: {
                      ...config.notificationSettings,
                      recipients: e.target.value.split(',').map(r => r.trim()).filter(Boolean)
                    }
                  })}
                  className="mt-2"
                />
              </div>
            )}

            <div className="flex items-center justify-between">
              <Label htmlFor="slackNotif" className="text-sm font-normal cursor-pointer">
                Slack Notifications
              </Label>
              <Switch
                id="slackNotif"
                checked={config.notificationSettings.channels.slack}
                onCheckedChange={(checked) => setConfig({
                  ...config,
                  notificationSettings: {
                    ...config.notificationSettings,
                    channels: {
                      ...config.notificationSettings.channels,
                      slack: checked
                    }
                  }
                })}
              />
            </div>

            {config.notificationSettings.channels.slack && (
              <div className="ml-6">
                <Label htmlFor="slackWebhook">Slack Webhook URL</Label>
                <Input
                  id="slackWebhook"
                  placeholder="https://hooks.slack.com/services/..."
                  value={config.notificationSettings.slackWebhook}
                  onChange={(e) => setConfig({
                    ...config,
                    notificationSettings: {
                      ...config.notificationSettings,
                      slackWebhook: e.target.value
                    }
                  })}
                  className="mt-2"
                />
              </div>
            )}

            <div className="flex items-center justify-between">
              <Label htmlFor="webhookNotif" className="text-sm font-normal cursor-pointer">
                Custom Webhook
              </Label>
              <Switch
                id="webhookNotif"
                checked={config.notificationSettings.channels.webhook}
                onCheckedChange={(checked) => setConfig({
                  ...config,
                  notificationSettings: {
                    ...config.notificationSettings,
                    channels: {
                      ...config.notificationSettings.channels,
                      webhook: checked
                    }
                  }
                })}
              />
            </div>

            {config.notificationSettings.channels.webhook && (
              <div className="ml-6">
                <Label htmlFor="customWebhook">Webhook URL</Label>
                <Input
                  id="customWebhook"
                  placeholder="https://your-webhook-endpoint.com"
                  value={config.notificationSettings.customWebhook}
                  onChange={(e) => setConfig({
                    ...config,
                    notificationSettings: {
                      ...config.notificationSettings,
                      customWebhook: e.target.value
                    }
                  })}
                  className="mt-2"
                />
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          className="w-full"
        >
          Continue
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderActionsQuestion = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Plus className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h3 className="text-lg font-semibold mb-2">Do you want to add any actions?</h3>
        <p className="text-muted-foreground text-sm">
          Actions let you extend your automation with additional capabilities
        </p>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <Button
          variant={config.wantsActions === true ? "default" : "outline"}
          size="lg"
          onClick={() => {
            setConfig({ ...config, wantsActions: true })
            nextStep()
          }}
          className="h-auto py-6"
        >
          <div className="flex flex-col items-center gap-2">
            <Check className="h-8 w-8" />
            <span>Yes, I want to add actions</span>
          </div>
        </Button>
        <Button
          variant={config.wantsActions === false ? "default" : "outline"}
          size="lg"
          onClick={() => {
            setConfig({ ...config, wantsActions: false })
            setCurrentStep('review')  // Skip directly to review
          }}
          className="h-auto py-6"
        >
          <div className="flex flex-col items-center gap-2">
            <ArrowRight className="h-8 w-8" />
            <span>No, continue without</span>
          </div>
        </Button>
      </div>

      <div>
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
      </div>
    </motion.div>
  )

  const renderActions = () => {
    const selectedActionsList = config.selectedActionsList || []
    const [wantsMoreActions, setWantsMoreActions] = useState(false)
    const [actionSearchQuery, setActionSearchQuery] = useState('')

    const availableActionsList = [
      { id: 'discord-read', name: 'Discord Read', icon: MessageSquare, desc: 'Read messages from any Discord channel' },
      { id: 'discord-send', name: 'Discord Send', icon: MessageSquare, desc: 'Send messages to any Discord channel' },
      { id: 'gmail-read', name: 'Gmail Read', icon: Mail, desc: 'Read emails from your Gmail account' },
      { id: 'gmail-send', name: 'Gmail Send', icon: Mail, desc: 'Send emails via your Gmail account' },
      { id: 'gcalendar-read', name: 'Google Calendar Read', icon: Calendar, desc: 'Read events from any Google Calendar' },
      { id: 'gcalendar-write', name: 'Google Calendar Write', icon: Calendar, desc: 'Create events on any Google Calendar' },
      { id: 'http-request', name: 'HTTP Request', icon: Globe, desc: 'Make GET, POST, PUT, or DELETE HTTP requests' },
      { id: 'slack-read', name: 'Slack Read', icon: Hash, desc: 'Read messages from a specified Slack channel' },
      { id: 'slack-send', name: 'Slack Send', icon: Hash, desc: 'Send a message to a specified Slack channel' }
    ]

    // Filter actions based on search query
    const filteredActions = availableActionsList.filter(action => {
      const query = actionSearchQuery.toLowerCase()
      return (
        action.name.toLowerCase().includes(query) ||
        action.desc.toLowerCase().includes(query)
      )
    })

    const addAction = (action: any) => {
      const newAction = {
        id: Date.now().toString(),
        type: action.id,
        name: action.name,
        description: action.desc,
        config: {}
      }
      const updatedList = [...selectedActionsList, newAction]
      setConfig({ ...config, selectedActionsList: updatedList })
      setShowActionConfig(updatedList.length - 1)
      setWantsMoreActions(false)
    }

    const removeAction = (index: number) => {
      const updatedList = selectedActionsList.filter((_, i) => i !== index)
      setConfig({ ...config, selectedActionsList: updatedList })
    }

    // If no actions yet or wants more actions, show action selection
    if (selectedActionsList.length === 0 || wantsMoreActions) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          <div className="text-center">
            <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
            <h3 className="text-lg font-semibold mb-2">
              {selectedActionsList.length === 0 ? 'What should happen next?' : 'Add another action?'}
            </h3>
            <p className="text-muted-foreground text-sm">
              {selectedActionsList.length === 0 
                ? 'Choose an action to perform when your trigger fires'
                : 'Chain multiple actions to create powerful workflows'}
            </p>
          </div>

          {/* Search */}
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search actions..."
                value={actionSearchQuery}
                onChange={(e) => setActionSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Available Actions */}
          {filteredActions.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No actions found matching "{actionSearchQuery}"</p>
            </div>
          ) : (
            <div className="grid gap-3 max-w-2xl mx-auto">
              {filteredActions.map((action) => (
                <Card
                  key={action.id}
                  className="cursor-pointer transition-all hover:shadow-md"
                  onClick={() => addAction(action)}
                >
                  <CardHeader className="p-4">
                    <div className="flex items-center gap-3">
                      <action.icon className="h-5 w-5 text-primary" />
                      <div className="flex-1">
                        <CardTitle className="text-sm">{action.name}</CardTitle>
                        <CardDescription className="text-xs mt-1">{action.desc}</CardDescription>
                      </div>
                      <Plus className="h-5 w-5 text-muted-foreground" />
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          )}

          <div className="flex gap-3 max-w-2xl mx-auto">
            <Button onClick={prevStep} variant="outline" className="w-full">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Previous
            </Button>
            {selectedActionsList.length > 0 && (
              <Button 
                onClick={() => setWantsMoreActions(false)} 
                variant="outline"
                className="w-full"
              >
                Continue without adding
              </Button>
            )}
          </div>
        </motion.div>
      )
    }

    // Show current actions with option to add more
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <div className="text-center">
          <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
          <h3 className="text-lg font-semibold mb-2">Your Action Chain</h3>
          <p className="text-muted-foreground text-sm">
            {selectedActionsList.length} action{selectedActionsList.length !== 1 ? 's' : ''} configured
          </p>
        </div>

        {/* Action Chain Visualization */}
        <div className="max-w-2xl mx-auto space-y-3">
          {/* Trigger */}
          <div className="flex items-center gap-3 p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg border-2 border-purple-200 dark:border-purple-800">
            <Mail className="h-5 w-5 text-purple-600" />
            <div className="flex-1">
              <p className="font-medium">Trigger: Gmail Email Check</p>
              <p className="text-xs text-muted-foreground">Every {config.pollingSettings.frequency} {config.pollingSettings.timeUnit}</p>
            </div>
          </div>

          {/* Arrow */}
          <div className="flex justify-center">
            <ArrowRight className="h-4 w-4 text-muted-foreground" />
          </div>

          {/* Actions */}
          {selectedActionsList.map((action, index) => (
            <div key={action.id}>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center gap-3 p-4 bg-muted/30 rounded-lg border"
              >
                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-xs font-medium">
                  {index + 1}
                </div>
                <div className="flex-1">
                  <p className="font-medium">{action.name}</p>
                  <p className="text-xs text-muted-foreground">{action.description}</p>
                </div>
                <div className="flex gap-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setShowActionConfig(index)}
                    className="h-8 px-2"
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => removeAction(index)}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </motion.div>
              {index < selectedActionsList.length - 1 && (
                <div className="flex justify-center my-2">
                  <ArrowRight className="h-4 w-4 text-muted-foreground" />
                </div>
              )}
            </div>
          ))}

          {/* Add Another Action Button */}
          <Button
            variant="outline"
            onClick={() => setWantsMoreActions(true)}
            className="w-full"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Another Action
          </Button>
        </div>

        <div className="flex gap-3 max-w-2xl mx-auto">
          <Button onClick={prevStep} variant="outline" className="w-full">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button 
            onClick={nextStep} 
            className="w-full"
            disabled={selectedActionsList.length === 0}
          >
            Continue <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>

        {/* Action Configuration Modal */}
        {showActionConfig !== null && selectedActionsList[showActionConfig] && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowActionConfig(null)}
          >
            <motion.div
              initial={{ scale: 0.95 }}
              animate={{ scale: 1 }}
              className="bg-card p-6 rounded-lg shadow-lg max-w-md w-full max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">
                Configure {selectedActionsList[showActionConfig].name}
              </h3>
              
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Configuration options will appear here based on the action type.
                </p>
                <div className="flex justify-end gap-2 mt-6">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowActionConfig(null)}
                  >
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => setShowActionConfig(null)}
                  >
                    Save Configuration
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </motion.div>
    )
  }

  const renderReview = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Eye className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h3 className="text-lg font-semibold mb-2">Review Your Configuration</h3>
        <p className="text-muted-foreground text-sm">
          Make sure everything looks correct before creating your automation
        </p>
      </div>

      <div className="space-y-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Automation Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Name</span>
              <span className="text-sm font-medium">{config.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Gmail Account</span>
              <span className="text-sm font-medium">{config.googleAuth.userEmail}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Polling Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Check Frequency</span>
              <span className="text-sm font-medium">Every {config.pollingSettings.frequency} {config.pollingSettings.timeUnit}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Unread Only</span>
              <span className="text-sm font-medium">{config.pollingSettings.filters.unreadOnly ? 'Yes' : 'No'}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">AI Analysis</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Provider</span>
              <span className="text-sm font-medium capitalize">{config.apiIntegration.provider}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Model</span>
              <span className="text-sm font-medium">{config.apiIntegration.model}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Automated Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-1">
              {Object.entries(config.analysisSettings.actions)
                .filter(([_, enabled]) => enabled)
                .map(([key]) => (
                  <div key={key} className="text-sm">
                    • {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>

        {config.selectedActionsList.length > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Additional Actions ({config.selectedActionsList.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {config.selectedActionsList.map((action) => (
                  <div key={action.id} className="text-sm">
                    • {action.name}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          className="w-full bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700"
        >
          Create Automation
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderComplete = () => (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto">
        <Check className="h-10 w-10 text-green-600 dark:text-green-400" />
      </div>
      <div>
        <h2 className="text-3xl font-bold mb-2">Automation Created!</h2>
        <p className="text-muted-foreground text-lg">
          Your "{config.name}" automation is now ready and will start monitoring your Gmail automatically.
        </p>
      </div>
      
      <div className="flex flex-col sm:flex-row gap-3 justify-center mt-8">
        <Button 
          onClick={() => setLocation('/dashboard/automations')}
          size="lg"
          className="bg-primary hover:bg-primary/90"
        >
          View All Automations
        </Button>
        <Button 
          onClick={() => setLocation('/dashboard/browse-templates')}
          variant="outline"
          size="lg"
        >
          Browse More Templates
        </Button>
      </div>
    </motion.div>
  )

  const renderStepContent = () => {
    switch (currentStep) {
      case 'intro': return renderIntro()
      case 'naming': return renderNaming()
      case 'gmail-connection': return renderGmailConnection()
      case 'api-integration': return renderApiIntegration()
      case 'polling-settings': return renderPollingSettings()
      case 'analysis-settings': return renderAnalysisSettings()
      case 'approval-settings': return renderApprovalSettings()
      case 'notification-settings': return renderNotificationSettings()
      case 'actions-question': return renderActionsQuestion()
      case 'actions': return renderActions()
      case 'review': return renderReview()
      case 'complete': return renderComplete()
      default: return null
    }
  }

  const CurrentIcon = stepConfig[currentStep].icon

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => setLocation('/dashboard/browse-templates')}
          className="mb-4"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back to Templates
        </Button>

        {currentStep !== 'complete' && (
          <>
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <CurrentIcon className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">{stepConfig[currentStep].title}</h1>
                <p className="text-muted-foreground text-sm">{stepConfig[currentStep].subtitle}</p>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex justify-between text-xs text-muted-foreground mb-2">
                <span>Step {Object.keys(stepConfig).indexOf(currentStep) + 1} of 11</span>
                <span>{stepConfig[currentStep].progress}% Complete</span>
              </div>
              <Progress value={stepConfig[currentStep].progress} className="h-2" />
            </div>
          </>
        )}
      </div>

      {/* Content */}
      <Card className="p-6">
        {renderStepContent()}
      </Card>
    </div>
  )
}