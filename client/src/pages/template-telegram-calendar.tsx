import { useState } from 'react'
import { useLocation } from 'wouter'
import { motion } from 'framer-motion'
import { 
  Calendar, 
  Settings, 
  Check, 
  ChevronLeft, 
  ChevronRight, 
  Mail, 
  Zap, 
  MessageSquare, 
  Clock, 
  MapPin, 
  Users,
  Bell,
  Globe,
  Hash,
  Search,
  ArrowLeft,
  ArrowRight,
  Brain
} from 'lucide-react'
import { SiTelegram, SiGooglecalendar, SiOpenai, SiGoogle } from 'react-icons/si'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Progress } from '@/components/ui/progress'
import { ActionsSelector } from '@/components/actions-selector'
import { CustomActionsEditor, CustomAction } from '@/components/custom-actions-editor'
import { EmojiCelebration } from '@/components/emoji-celebration'

type StepType = 'intro' | 'naming' | 'telegram-config' | 'google-auth' | 'calendar-config' | 'ai-settings' | 'actions-question' | 'actions' | 'review' | 'complete'

interface WorkflowConfig {
  name: string
  telegramConfig: {
    botToken: string
    chatId: string
    isAuthenticated: boolean
    messageFilters: {
      keywords: string[]
      users: string[]
      messageTypes: string[]
      timePattern: string
    }
    eventDetection: {
      enabled: boolean
      aiParsing: boolean
      patterns: string[]
    }
  }
  googleAuth: {
    isAuthenticated: boolean
    userEmail: string
    userName: string
    accountId: string
  }
  calendarConfig: {
    calendarId: string
    calendarName: string
    defaultDuration: number
    timezone: string
    eventDefaults: {
      visibility: string
      reminders: boolean
      reminderMinutes: number[]
      attendeeNotifications: boolean
    }
    conflictHandling: {
      checkConflicts: boolean
      skipConflicts: boolean
      notifyConflicts: boolean
    }
  }
  aiSettings: {
    provider: string
    model: string
    parseAccuracy: string
    extractFields: string[]
    customInstructions: string
    confidence: number
    fallbackHandling: string
  }
  selectedActionsList: Array<{
    id: string
    type: string
    name: string
    description: string
    config: Record<string, any>
  }>
  wantsActions: boolean | null
}

export default function TelegramCalendarTemplate() {
  const [, setLocation] = useLocation()
  const [currentStep, setCurrentStep] = useState<StepType>('intro')
  const [workflow, setWorkflow] = useState<WorkflowConfig>({
    name: '',
    telegramConfig: {
      botToken: '',
      chatId: '',
      isAuthenticated: false,
      messageFilters: {
        keywords: [],
        users: [],
        messageTypes: ['text', 'photo', 'document'],
        timePattern: 'realtime'
      },
      eventDetection: {
        enabled: true,
        aiParsing: true,
        patterns: ['meeting', 'appointment', 'call', 'deadline', 'reminder']
      }
    },
    googleAuth: {
      isAuthenticated: false,
      userEmail: '',
      userName: '',
      accountId: ''
    },
    calendarConfig: {
      calendarId: 'primary',
      calendarName: 'Primary Calendar',
      defaultDuration: 60,
      timezone: 'UTC',
      eventDefaults: {
        visibility: 'private',
        reminders: true,
        reminderMinutes: [10, 30],
        attendeeNotifications: true
      },
      conflictHandling: {
        checkConflicts: true,
        skipConflicts: false,
        notifyConflicts: true
      }
    },
    aiSettings: {
      provider: 'openai',
      model: 'gpt-4o',
      parseAccuracy: 'balanced',
      extractFields: ['title', 'date', 'time', 'duration', 'location', 'description'],
      customInstructions: '',
      confidence: 0.8,
      fallbackHandling: 'notify'
    },
    selectedActionsList: [],
    wantsActions: null
  })

  const [selectedActions, setSelectedActions] = useState<string[]>([])
  const [wantsMoreActions, setWantsMoreActions] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [actionConfigOpen, setActionConfigOpen] = useState<string | null>(null)
  const [tempActionConfig, setTempActionConfig] = useState<Record<string, any>>({})
  const [showActionConfig, setShowActionConfig] = useState<number | null>(null)
  const [showCelebration, setShowCelebration] = useState(false)
  
  const [customActions, setCustomActions] = useState<CustomAction[]>([])
  const [customActionEditId, setCustomActionEditId] = useState<string | null>(null)

  const steps: { id: StepType; title: string; icon: React.ComponentType<{ className?: string }> }[] = [
    { id: 'intro', title: 'Introduction', icon: MessageSquare },
    { id: 'naming', title: 'Name Your Automation', icon: Settings },
    { id: 'telegram-config', title: 'Telegram Bot Setup', icon: SiTelegram },
    { id: 'google-auth', title: 'Google Authentication', icon: SiGooglecalendar },
    { id: 'calendar-config', title: 'Calendar Configuration', icon: Calendar },
    { id: 'ai-settings', title: 'AI Parsing Settings', icon: SiOpenai },
    { id: 'actions-question', title: 'Additional Actions', icon: Zap },
    { id: 'actions', title: 'Configure Actions', icon: Zap },
    { id: 'review', title: 'Review', icon: Settings },
    { id: 'complete', title: 'Complete', icon: Check }
  ]

  const nextStep = () => {
    const currentIndex = steps.findIndex(s => s.id === currentStep)
    if (currentIndex < steps.length - 1) {
      const nextStepId = steps[currentIndex + 1].id
      
      // Special handling for actions-question step
      if (currentStep === 'actions-question') {
        if (workflow.wantsActions === false) {
          // Skip actions step and go to review
          setCurrentStep('review')
        } else {
          // Go to actions step
          setCurrentStep('actions')
        }
      } else if (currentStep === 'review') {
        setShowCelebration(true)
        setCurrentStep(nextStepId)
      } else {
        setCurrentStep(nextStepId)
      }
    }
  }

  const previousStep = () => {
    const currentIndex = steps.findIndex(s => s.id === currentStep)
    if (currentIndex > 0) {
      // Special handling for review step
      if (currentStep === 'review') {
        if (workflow.wantsActions === false) {
          // Skip actions step and go to actions-question
          setCurrentStep('actions-question')
        } else {
          // Go to actions step
          setCurrentStep('actions')
        }
      } else {
        const prevStepId = steps[currentIndex - 1].id
        setCurrentStep(prevStepId)
      }
    }
  }

  const handleActionsChange = (actions: string[]) => {
    setSelectedActions(actions)
    
    // Convert to the format expected by workflow
    const actionsList = actions.map(actionId => {
      const action = {
        id: actionId,
        type: actionId,
        name: actionId.charAt(0).toUpperCase() + actionId.slice(1),
        description: `Configure ${actionId} settings`,
        config: {}
      }
      return action
    })
    
    setWorkflow(prev => ({
      ...prev,
      selectedActionsList: actionsList
    }))
  }

  const addAction = (actionType: string) => {
    const newAction = {
      id: Date.now().toString(),
      type: actionType,
      name: actionType.charAt(0).toUpperCase() + actionType.slice(1),
      description: `Configure ${actionType} settings`,
      config: {}
    }
    
    setWorkflow(prev => ({
      ...prev,
      selectedActionsList: [...prev.selectedActionsList, newAction]
    }))
  }

  const removeAction = (index: number) => {
    setWorkflow(prev => ({
      ...prev,
      selectedActionsList: prev.selectedActionsList.filter((_, i) => i !== index)
    }))
  }

  const configureAction = (index: number, config: Record<string, any>) => {
    setWorkflow(prev => ({
      ...prev,
      selectedActionsList: prev.selectedActionsList.map((action, i) => 
        i === index ? { ...action, config } : action
      )
    }))
  }

  const renderIntroStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center mx-auto">
        <MessageSquare className="h-10 w-10 text-white" />
      </div>
      <div>
        <h2 className="text-2xl font-bold mb-2">Telegram to Calendar Automation</h2>
        <p className="text-muted-foreground max-w-md mx-auto">
          This template creates an automated workflow that monitors Telegram messages, 
          uses AI to extract event details, and automatically creates Google Calendar events 
          with smart scheduling and conflict detection.
        </p>
      </div>
      
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <SiTelegram className="h-8 w-8 mx-auto mb-2 text-blue-500" />
          <p className="text-xs font-medium">Telegram Bot</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Brain className="h-8 w-8 mx-auto mb-2 text-purple-600" />
          <p className="text-xs font-medium">AI Parsing</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <SiGooglecalendar className="h-8 w-8 mx-auto mb-2 text-green-600" />
          <p className="text-xs font-medium">Google Calendar</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Calendar className="h-8 w-8 mx-auto mb-2 text-orange-600" />
          <p className="text-xs font-medium">Auto Events</p>
        </div>
      </div>

      <Button 
        onClick={nextStep} 
        size="lg"
        className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 min-h-[48px]"
      >
        Start Setup
        <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
    </motion.div>
  )

  const renderNamingStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Settings className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h2 className="text-xl font-semibold mb-2">Name Your Automation</h2>
        <p className="text-muted-foreground">
          Give your automation a memorable name that describes what it does
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="automation-name">Automation Name *</Label>
          <Input
            id="automation-name"
            placeholder="e.g., Team Meeting Scheduler, Personal Event Creator"
            value={workflow.name}
            onChange={(e) => setWorkflow(prev => ({ ...prev, name: e.target.value }))}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && workflow.name) {
                nextStep()
              }
            }}
            className="mt-2"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Choose a descriptive name you'll easily recognize
          </p>
        </div>
      </div>

      <div className="flex gap-3">
        <Button 
          onClick={previousStep}
          variant="outline"
          size="lg"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          disabled={!workflow.name}
          size="lg"
          className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 flex-1"
        >
          Continue
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderTelegramConfig = () => (
    <Card>
      <CardHeader>
        <CardTitle>Telegram Bot Configuration</CardTitle>
        <CardDescription>
          Set up your Telegram bot to monitor messages for event information
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="bot-token">Bot Token *</Label>
            <Input
              id="bot-token"
              type="password"
              placeholder="Your Telegram bot token"
              value={workflow.telegramConfig.botToken}
              onChange={(e) => setWorkflow(prev => ({ 
                ...prev, 
                telegramConfig: { ...prev.telegramConfig, botToken: e.target.value }
              }))}
            />
            <p className="text-sm text-muted-foreground">
              Get your bot token from @BotFather on Telegram
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="chat-id">Chat ID *</Label>
            <Input
              id="chat-id"
              placeholder="Chat ID or username"
              value={workflow.telegramConfig.chatId}
              onChange={(e) => setWorkflow(prev => ({ 
                ...prev, 
                telegramConfig: { ...prev.telegramConfig, chatId: e.target.value }
              }))}
            />
            <p className="text-sm text-muted-foreground">
              The chat ID or username to monitor for messages
            </p>
          </div>
        </div>

        <Separator />

        <div className="space-y-4">
          <h3 className="font-semibold">Message Filters</h3>
          
          <div className="space-y-2">
            <Label>Keywords (optional)</Label>
            <Input
              placeholder="meeting, appointment, call (comma-separated)"
              value={workflow.telegramConfig.messageFilters.keywords.join(', ')}
              onChange={(e) => setWorkflow(prev => ({ 
                ...prev, 
                telegramConfig: { 
                  ...prev.telegramConfig, 
                  messageFilters: { 
                    ...prev.telegramConfig.messageFilters, 
                    keywords: e.target.value.split(',').map(k => k.trim()).filter(k => k)
                  }
                }
              }))}
            />
          </div>

          <div className="space-y-2">
            <Label>Message Types</Label>
            <div className="space-y-2">
              {['text', 'photo', 'document', 'audio', 'video'].map(type => (
                <div key={type} className="flex items-center space-x-2">
                  <Checkbox
                    id={type}
                    checked={workflow.telegramConfig.messageFilters.messageTypes.includes(type)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setWorkflow(prev => ({ 
                          ...prev, 
                          telegramConfig: { 
                            ...prev.telegramConfig, 
                            messageFilters: { 
                              ...prev.telegramConfig.messageFilters, 
                              messageTypes: [...prev.telegramConfig.messageFilters.messageTypes, type]
                            }
                          }
                        }))
                      } else {
                        setWorkflow(prev => ({ 
                          ...prev, 
                          telegramConfig: { 
                            ...prev.telegramConfig, 
                            messageFilters: { 
                              ...prev.telegramConfig.messageFilters, 
                              messageTypes: prev.telegramConfig.messageFilters.messageTypes.filter(t => t !== type)
                            }
                          }
                        }))
                      }
                    }}
                  />
                  <Label htmlFor={type} className="capitalize">{type} messages</Label>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="flex justify-between pt-4">
          <Button variant="outline" onClick={previousStep}>
            <ChevronLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>
          <Button 
            onClick={nextStep}
            disabled={!workflow.telegramConfig.botToken || !workflow.telegramConfig.chatId}
            className="bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white"
          >
            Continue
            <ChevronRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </CardContent>
    </Card>
  )

  const renderGoogleAuth = () => (
    <Card>
      <CardHeader>
        <CardTitle>Google Authentication</CardTitle>
        <CardDescription>
          Connect your Google account to access Calendar
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <Label>Authentication Type *</Label>
          <RadioGroup value="google" className="space-y-2">
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="google" id="google" />
              <Label htmlFor="google">Google Sign-In</Label>
            </div>
          </RadioGroup>
        </div>

        <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg">
          <p className="text-sm text-blue-800 dark:text-blue-200">
            <strong>Note:</strong> Select existing Google account from below or Sign in with a different account
          </p>
        </div>

        {!workflow.googleAuth.isAuthenticated ? (
          <div className="space-y-4">
            <Button 
              onClick={() => {
                setWorkflow(prev => ({
                  ...prev,
                  googleAuth: {
                    ...prev.googleAuth,
                    isAuthenticated: true,
                    userEmail: '<EMAIL>',
                    userName: 'John Doe',
                    accountId: 'google-123'
                  }
                }))
              }}
              className="w-full bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white"
            >
              <SiGooglecalendar className="h-4 w-4 mr-2" />
              Sign in with Google
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-950/20 rounded-lg">
              <div className="flex items-center gap-3">
                <SiGooglecalendar className="h-6 w-6 text-green-500" />
                <div>
                  <p className="font-medium text-green-900 dark:text-green-100">
                    Google account: {workflow.googleAuth.userName} ({workflow.googleAuth.userEmail})
                  </p>
                  <Badge className="mt-1 bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                    Connected
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="flex justify-between pt-4">
          <Button variant="outline" onClick={previousStep}>
            <ChevronLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>
          <Button 
            onClick={nextStep}
            disabled={!workflow.googleAuth.isAuthenticated}
            className="bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white"
          >
            Continue
            <ChevronRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </CardContent>
    </Card>
  )

  const renderCalendarConfig = () => (
    <Card>
      <CardHeader>
        <CardTitle>Calendar Configuration</CardTitle>
        <CardDescription>
          Configure how events are created in your Google Calendar
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>Target Calendar</Label>
            <Select 
              value={workflow.calendarConfig.calendarId} 
              onValueChange={(value) => setWorkflow(prev => ({ 
                ...prev, 
                calendarConfig: { ...prev.calendarConfig, calendarId: value }
              }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select calendar" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="primary">Primary Calendar</SelectItem>
                <SelectItem value="work">Work Calendar</SelectItem>
                <SelectItem value="personal">Personal Calendar</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Default Event Duration (minutes)</Label>
            <Select 
              value={workflow.calendarConfig.defaultDuration.toString()} 
              onValueChange={(value) => setWorkflow(prev => ({ 
                ...prev, 
                calendarConfig: { ...prev.calendarConfig, defaultDuration: parseInt(value) }
              }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select duration" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="30">30 minutes</SelectItem>
                <SelectItem value="60">1 hour</SelectItem>
                <SelectItem value="90">1.5 hours</SelectItem>
                <SelectItem value="120">2 hours</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Timezone</Label>
            <Select 
              value={workflow.calendarConfig.timezone} 
              onValueChange={(value) => setWorkflow(prev => ({ 
                ...prev, 
                calendarConfig: { ...prev.calendarConfig, timezone: value }
              }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select timezone" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="UTC">UTC</SelectItem>
                <SelectItem value="America/New_York">Eastern Time</SelectItem>
                <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                <SelectItem value="Europe/London">London</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Separator />

        <div className="space-y-4">
          <h3 className="font-semibold">Event Defaults</h3>
          
          <div className="space-y-2">
            <Label>Event Visibility</Label>
            <RadioGroup 
              value={workflow.calendarConfig.eventDefaults.visibility} 
              onValueChange={(value) => setWorkflow(prev => ({ 
                ...prev, 
                calendarConfig: { 
                  ...prev.calendarConfig, 
                  eventDefaults: { ...prev.calendarConfig.eventDefaults, visibility: value }
                }
              }))}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="private" id="private" />
                <Label htmlFor="private">Private</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="public" id="public" />
                <Label htmlFor="public">Public</Label>
              </div>
            </RadioGroup>
          </div>

          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="reminders"
                checked={workflow.calendarConfig.eventDefaults.reminders}
                onCheckedChange={(checked) => setWorkflow(prev => ({ 
                  ...prev, 
                  calendarConfig: { 
                    ...prev.calendarConfig, 
                    eventDefaults: { ...prev.calendarConfig.eventDefaults, reminders: checked as boolean }
                  }
                }))}
              />
              <Label htmlFor="reminders">Enable reminders</Label>
            </div>
            
            {workflow.calendarConfig.eventDefaults.reminders && (
              <div className="ml-6 space-y-2">
                <Label>Reminder times (minutes before event)</Label>
                <Input
                  placeholder="10, 30, 60 (comma-separated)"
                  value={workflow.calendarConfig.eventDefaults.reminderMinutes.join(', ')}
                  onChange={(e) => setWorkflow(prev => ({ 
                    ...prev, 
                    calendarConfig: { 
                      ...prev.calendarConfig, 
                      eventDefaults: { 
                        ...prev.calendarConfig.eventDefaults, 
                        reminderMinutes: e.target.value.split(',').map(m => parseInt(m.trim())).filter(m => !isNaN(m))
                      }
                    }
                  }))}
                />
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-between pt-4">
          <Button variant="outline" onClick={previousStep}>
            <ChevronLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>
          <Button 
            onClick={nextStep}
            className="bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white"
          >
            Continue
            <ChevronRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </CardContent>
    </Card>
  )

  const renderAiSettings = () => (
    <Card>
      <CardHeader>
        <CardTitle>AI Parsing Settings</CardTitle>
        <CardDescription>
          Configure how AI extracts event information from messages
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>AI Provider</Label>
            <Select 
              value={workflow.aiSettings.provider} 
              onValueChange={(value) => setWorkflow(prev => ({ 
                ...prev, 
                aiSettings: { ...prev.aiSettings, provider: value }
              }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select AI provider" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="openai">OpenAI</SelectItem>
                <SelectItem value="anthropic">Anthropic</SelectItem>
                <SelectItem value="google">Google</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Model</Label>
            <Select 
              value={workflow.aiSettings.model} 
              onValueChange={(value) => setWorkflow(prev => ({ 
                ...prev, 
                aiSettings: { ...prev.aiSettings, model: value }
              }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select model" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="gpt-4o">GPT-4o</SelectItem>
                <SelectItem value="gpt-4">GPT-4</SelectItem>
                <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
                <SelectItem value="gemini-pro">Gemini Pro</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Parse Accuracy</Label>
            <RadioGroup 
              value={workflow.aiSettings.parseAccuracy} 
              onValueChange={(value) => setWorkflow(prev => ({ 
                ...prev, 
                aiSettings: { ...prev.aiSettings, parseAccuracy: value }
              }))}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="fast" id="fast" />
                <Label htmlFor="fast">Fast (less accurate)</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="balanced" id="balanced" />
                <Label htmlFor="balanced">Balanced</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="precise" id="precise" />
                <Label htmlFor="precise">Precise (slower)</Label>
              </div>
            </RadioGroup>
          </div>
        </div>

        <Separator />

        <div className="space-y-4">
          <h3 className="font-semibold">Extract Fields</h3>
          <div className="space-y-2">
            {['title', 'date', 'time', 'duration', 'location', 'description', 'attendees'].map(field => (
              <div key={field} className="flex items-center space-x-2">
                <Checkbox
                  id={field}
                  checked={workflow.aiSettings.extractFields.includes(field)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setWorkflow(prev => ({ 
                        ...prev, 
                        aiSettings: { 
                          ...prev.aiSettings, 
                          extractFields: [...prev.aiSettings.extractFields, field]
                        }
                      }))
                    } else {
                      setWorkflow(prev => ({ 
                        ...prev, 
                        aiSettings: { 
                          ...prev.aiSettings, 
                          extractFields: prev.aiSettings.extractFields.filter(f => f !== field)
                        }
                      }))
                    }
                  }}
                />
                <Label htmlFor={field} className="capitalize">{field}</Label>
              </div>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <Label>Custom Instructions (optional)</Label>
          <Textarea
            placeholder="Additional instructions for AI parsing..."
            value={workflow.aiSettings.customInstructions}
            onChange={(e) => setWorkflow(prev => ({ 
              ...prev, 
              aiSettings: { ...prev.aiSettings, customInstructions: e.target.value }
            }))}
            rows={3}
          />
        </div>

        <div className="flex justify-between pt-4">
          <Button variant="outline" onClick={previousStep}>
            <ChevronLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>
          <Button 
            onClick={nextStep}
            className="bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white"
          >
            Continue
            <ChevronRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </CardContent>
    </Card>
  )

  const renderActionsQuestion = () => (
    <Card>
      <CardHeader>
        <CardTitle>Additional Actions</CardTitle>
        <CardDescription>
          Do you want to add any additional actions to this automation?
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="text-center space-y-4">
          <div className="flex justify-center space-x-4">
            <Button
              onClick={() => {
                setWorkflow(prev => ({ ...prev, wantsActions: true }))
                nextStep()
              }}
              className="bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white"
            >
              Yes, add actions
            </Button>
            <Button
              onClick={() => {
                setWorkflow(prev => ({ ...prev, wantsActions: false }))
                nextStep()
              }}
              variant="outline"
            >
              No, continue
            </Button>
          </div>
        </div>

        <div className="flex justify-between pt-4">
          <Button variant="outline" onClick={previousStep}>
            <ChevronLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>
        </div>
      </CardContent>
    </Card>
  )

  const renderActions = () => {
    const selectedActionsList = workflow.selectedActionsList || []

    const availableActionsList = [
      { id: 'discord-read', name: 'Discord Read', icon: MessageSquare, desc: 'Read messages from any Discord channel' },
      { id: 'discord-send', name: 'Discord Send', icon: MessageSquare, desc: 'Send messages to any Discord channel' },
      { id: 'gmail-read', name: 'Gmail Read', icon: Mail, desc: 'Read emails from your Gmail account' },
      { id: 'gmail-send', name: 'Gmail Send', icon: Mail, desc: 'Send emails via your Gmail account' },
      { id: 'gcalendar-read', name: 'Google Calendar Read', icon: Calendar, desc: 'Read events from any Google Calendar' },
      { id: 'gcalendar-write', name: 'Google Calendar Write', icon: Calendar, desc: 'Create events on any Google Calendar' },
      { id: 'http-request', name: 'HTTP Request', icon: Globe, desc: 'Make GET, POST, PUT, or DELETE HTTP requests' },
      { id: 'slack-read', name: 'Slack Read', icon: Hash, desc: 'Read messages from a specified Slack channel' },
      { id: 'slack-send', name: 'Slack Send', icon: Hash, desc: 'Send a message to a specified Slack channel' }
    ]

    const filteredActions = availableActionsList.filter(action => {
      const query = searchQuery.toLowerCase()
      return (
        action.name.toLowerCase().includes(query) ||
        action.desc.toLowerCase().includes(query)
      )
    })

    const addAction = (action: any) => {
      const newAction = {
        id: Date.now().toString(),
        type: action.id,
        name: action.name,
        description: action.desc,
        config: {}
      }
      const updatedList = [...selectedActionsList, newAction]
      setWorkflow({ ...workflow, selectedActionsList: updatedList })
      setShowActionConfig(updatedList.length - 1)
      setWantsMoreActions(false)
    }

    const removeAction = (index: number) => {
      const updatedList = selectedActionsList.filter((_, i) => i !== index)
      setWorkflow({ ...workflow, selectedActionsList: updatedList })
    }

    // If no actions yet or wants more actions, show action selection
    if (selectedActionsList.length === 0 || wantsMoreActions) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          <div className="text-center">
            <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
            <h3 className="text-lg font-semibold mb-2">
              {selectedActionsList.length === 0 ? 'What should happen next?' : 'Add another action?'}
            </h3>
            <p className="text-muted-foreground text-sm">
              {selectedActionsList.length === 0 
                ? 'Choose an action to perform when your trigger fires'
                : 'Chain multiple actions to create powerful workflows'}
            </p>
          </div>

          {/* Search */}
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search actions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Action Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-4xl mx-auto">
            {filteredActions.map((action) => (
              <Card 
                key={action.id} 
                className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => addAction(action)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                      <action.icon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-sm">{action.name}</h4>
                      <p className="text-xs text-muted-foreground">{action.desc}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredActions.length === 0 && (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No actions found matching your search</p>
            </div>
          )}

          <div className="flex justify-between pt-4">
            <Button variant="outline" onClick={previousStep}>
              <ChevronLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>
            <Button 
              onClick={nextStep}
              className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
            >
              Continue
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </motion.div>
      )
    }

    // Show selected actions
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <div className="text-center">
          <Settings className="h-16 w-16 mx-auto mb-4 text-green-600" />
          <h3 className="text-lg font-semibold mb-2">Actions configured</h3>
          <p className="text-muted-foreground text-sm">
            Your workflow will perform these actions in sequence
          </p>
        </div>

        {/* Action Chain Visualization */}
        <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg">
          <h3 className="font-semibold mb-3">Action Chain</h3>
          <div className="flex items-center space-x-2 overflow-x-auto pb-2">
            <div className="flex items-center space-x-2 bg-blue-100 dark:bg-blue-900 px-3 py-1 rounded-full whitespace-nowrap">
              <MessageSquare className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800 dark:text-blue-200">Telegram Message</span>
            </div>
            <ChevronRight className="h-4 w-4 text-gray-400" />
            <div className="flex items-center space-x-2 bg-green-100 dark:bg-green-900 px-3 py-1 rounded-full whitespace-nowrap">
              <Calendar className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-800 dark:text-green-200">Calendar Event</span>
            </div>
            {selectedActionsList.map((action, index) => (
              <div key={index} className="flex items-center space-x-2">
                <ChevronRight className="h-4 w-4 text-gray-400" />
                <div className="flex items-center space-x-2 bg-purple-100 dark:bg-purple-900 px-3 py-1 rounded-full whitespace-nowrap">
                  <span className="text-sm font-medium text-purple-800 dark:text-purple-200">{index + 1}. {action.name}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeAction(index)}
                    className="h-4 w-4 p-0 hover:bg-purple-200 dark:hover:bg-purple-800"
                  >
                    ×
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Selected Actions */}
        <div className="space-y-3">
          <h3 className="font-semibold">Selected Actions</h3>
          {selectedActionsList.map((action, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-900 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center">
                  <span className="text-sm font-semibold text-purple-600 dark:text-purple-400">{index + 1}</span>
                </div>
                <div>
                  <div className="font-medium">{action.name}</div>
                  <div className="text-sm text-muted-foreground">{action.description}</div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowActionConfig(index)}
                >
                  <Settings className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeAction(index)}
                >
                  ×
                </Button>
              </div>
            </div>
          ))}
        </div>

        {/* Add another action */}
        <Button 
          onClick={() => setWantsMoreActions(true)}
          variant="outline"
          className="w-full"
        >
          <Zap className="h-4 w-4 mr-2" />
          Add Another Action
        </Button>

        <div className="flex justify-between pt-4">
          <Button variant="outline" onClick={previousStep}>
            <ChevronLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>
          <Button 
            onClick={nextStep}
            className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
          >
            Continue
            <ChevronRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </motion.div>
    )
  }

  const renderReview = () => (
    <Card>
      <CardHeader>
        <CardTitle>Review Your Automation</CardTitle>
        <CardDescription>
          Please review your automation settings before creating
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
            <h3 className="font-semibold mb-2">Automation Name</h3>
            <p>{workflow.name}</p>
          </div>

          <div className="p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
            <h3 className="font-semibold mb-2">Telegram Configuration</h3>
            <p>Bot Token: {workflow.telegramConfig.botToken ? '***configured***' : 'Not configured'}</p>
            <p>Chat ID: {workflow.telegramConfig.chatId}</p>
            <p>Message Types: {workflow.telegramConfig.messageFilters.messageTypes.join(', ')}</p>
          </div>

          <div className="p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
            <h3 className="font-semibold mb-2">Google Calendar</h3>
            <p>Account: {workflow.googleAuth.userName} ({workflow.googleAuth.userEmail})</p>
            <p>Target Calendar: {workflow.calendarConfig.calendarName}</p>
            <p>Default Duration: {workflow.calendarConfig.defaultDuration} minutes</p>
          </div>

          <div className="p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
            <h3 className="font-semibold mb-2">AI Settings</h3>
            <p>Provider: {workflow.aiSettings.provider}</p>
            <p>Model: {workflow.aiSettings.model}</p>
            <p>Parse Accuracy: {workflow.aiSettings.parseAccuracy}</p>
          </div>

          {workflow.selectedActionsList.length > 0 && (
            <div className="p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
              <h3 className="font-semibold mb-2">Additional Actions</h3>
              <ul className="space-y-1">
                {workflow.selectedActionsList.map((action, index) => (
                  <li key={index}>• {action.name}</li>
                ))}
              </ul>
            </div>
          )}
        </div>

        <div className="flex justify-between pt-4">
          <Button variant="outline" onClick={previousStep}>
            <ChevronLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>
          <Button 
            onClick={nextStep}
            className="bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white"
          >
            Create Automation
            <ChevronRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </CardContent>
    </Card>
  )

  const renderComplete = () => (
    <>
      {showCelebration && (
        <EmojiCelebration
          onComplete={() => setShowCelebration(false)}
          duration={3000}
        />
      )}
      
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="text-center space-y-6"
      >
        <div className="w-20 h-20 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto">
          <Check className="h-10 w-10 text-green-600 dark:text-green-400" />
        </div>
        <div>
          <h2 className="text-3xl font-bold mb-2">Automation Created!</h2>
          <p className="text-muted-foreground text-lg">
            Your "{workflow.name}" automation is now active and will monitor your Telegram chat for event information.
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3 justify-center mt-8">
          <Button 
            onClick={() => setLocation('/dashboard/automations')}
            size="lg"
            className="bg-primary hover:bg-primary/90"
          >
            View All Automations
          </Button>
          <Button 
            onClick={() => setLocation('/dashboard/browse-templates')}
            variant="outline"
            size="lg"
          >
            Browse More Templates
          </Button>
        </div>
      </motion.div>
    </>
  )

  const getCurrentStepNumber = () => {
    return steps.findIndex(step => step.id === currentStep) + 1
  }

  const getProgress = () => {
    const currentIndex = steps.findIndex(step => step.id === currentStep)
    return ((currentIndex + 1) / steps.length) * 100
  }

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'intro':
        return renderIntroStep()
      case 'naming':
        return renderNamingStep()
      case 'telegram-config':
        return renderTelegramConfig()
      case 'google-auth':
        return renderGoogleAuth()
      case 'calendar-config':
        return renderCalendarConfig()
      case 'ai-settings':
        return renderAiSettings()
      case 'actions-question':
        return renderActionsQuestion()
      case 'actions':
        return renderActions()
      case 'review':
        return renderReview()
      case 'complete':
        return renderComplete()
      default:
        return renderIntroStep()
    }
  }

  const getCurrentStepConfig = () => {
    const stepConfig = {
      'intro': { title: 'Welcome', subtitle: 'Let\'s get started', icon: MessageSquare, progress: 0 },
      'naming': { title: 'Name Your Automation', subtitle: 'Choose a memorable name', icon: Settings, progress: 12 },
      'telegram-config': { title: 'Telegram Configuration', subtitle: 'Set up your bot', icon: MessageSquare, progress: 25 },
      'google-auth': { title: 'Google Authentication', subtitle: 'Connect your Google account', icon: SiGoogle, progress: 37 },
      'calendar-config': { title: 'Calendar Setup', subtitle: 'Configure your calendar', icon: Calendar, progress: 50 },
      'ai-settings': { title: 'AI Settings', subtitle: 'Configure AI processing', icon: Brain, progress: 62 },
      'actions-question': { title: 'Additional Actions', subtitle: 'Extend your automation', icon: Settings, progress: 75 },
      'actions': { title: 'Configure Actions', subtitle: 'Set up additional actions', icon: Settings, progress: 87 },
      'review': { title: 'Review & Create', subtitle: 'Review your automation', icon: Check, progress: 100 },
      'complete': { title: 'Complete', subtitle: 'Automation created', icon: Check, progress: 100 }
    }
    return stepConfig[currentStep] || stepConfig['intro']
  }

  const CurrentIcon = getCurrentStepConfig().icon

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => setLocation('/dashboard/browse-templates')}
          className="mb-4"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back to Templates
        </Button>

        {currentStep !== 'complete' && (
          <>
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <CurrentIcon className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">{getCurrentStepConfig().title}</h1>
                <p className="text-muted-foreground text-sm">{getCurrentStepConfig().subtitle}</p>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex justify-between text-xs text-muted-foreground mb-2">
                <span>Step {getCurrentStepNumber()} of {steps.length}</span>
                <span>{getCurrentStepConfig().progress}% Complete</span>
              </div>
              <Progress value={getCurrentStepConfig().progress} className="h-2" />
            </div>
          </>
        )}
      </div>

      {/* Content */}
      <Card className="p-6">
        {renderCurrentStep()}
      </Card>
    </div>
  )
}