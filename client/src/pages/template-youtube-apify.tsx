import { useState } from 'react'
import { motion } from 'framer-motion'
import { useLocation } from 'wouter'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  Video, Key, Mic, FileText, Settings, Check, ArrowRight, ChevronLeft,
  X, Eye, EyeOff, Plus, Download, Mail, Calendar, MessageSquare, Hash, Globe, Search
} from 'lucide-react'
import { SiYoutube, SiGooglesheets } from 'react-icons/si'

type StepType = 'intro' | 'naming' | 'youtube-apify' | 'transcription-config' | 'output-delivery' | 'actions-question' | 'actions' | 'review' | 'complete'

interface YouTubeApifyConfig {
  name: string
  youtubeApify: {
    youtubeUrl: string
    apiKey: string
    isConfigured: boolean
  }
  transcriptionConfig: {
    service: 'whisper' | 'assemblyai' | 'rev'
    generateSummary: boolean
    summaryProvider: 'openai' | 'anthropic' | 'google'
    summaryModel: string
    language: string
    includeTimestamps: boolean
  }
  outputDelivery: {
    format: 'text' | 'srt' | 'vtt' | 'json'
    deliveryMethod: 'download' | 'email' | 'webhook' | 'googlesheets'
    email: string
    webhookUrl: string
    googleAuth: {
      isAuthenticated: boolean
      userEmail: string
      userName: string
      accountId: string
    }
  }
  selectedActionsList: Array<{
    id: string
    type: string
    name: string
    description: string
    config: Record<string, any>
  }>
  wantsActions: boolean | null
}

export default function YouTubeVideoTranscriptionTemplate() {
  const [, setLocation] = useLocation()
  const [currentStep, setCurrentStep] = useState<StepType>('intro')
  const [showApiKey, setShowApiKey] = useState(false)
  const [showActionConfig, setShowActionConfig] = useState<number | null>(null)
  const [actionSearchQuery, setActionSearchQuery] = useState('')
  const [config, setConfig] = useState<YouTubeApifyConfig>({
    name: '',
    youtubeApify: {
      youtubeUrl: '',
      apiKey: '',
      isConfigured: false
    },
    transcriptionConfig: {
      service: 'whisper',
      generateSummary: true,
      summaryProvider: 'openai',
      summaryModel: 'gpt-4o',
      language: 'en',
      includeTimestamps: true
    },
    outputDelivery: {
      format: 'text',
      deliveryMethod: 'download',
      email: '',
      webhookUrl: '',
      googleAuth: {
        isAuthenticated: false,
        userEmail: '',
        userName: '',
        accountId: ''
      }
    },
    selectedActionsList: [],
    wantsActions: null
  })

  const stepConfig = {
    intro: { 
      title: 'YouTube Video Transcription', 
      subtitle: 'Extract and summarize video content automatically',
      icon: Video,
      progress: 0
    },
    naming: {
      title: 'Name Your Automation',
      subtitle: 'Give your automation a memorable name',
      icon: Settings,
      progress: 15
    },
    'youtube-apify': { 
      title: 'YouTube & Apify Setup', 
      subtitle: 'Configure video input and API access',
      icon: SiYoutube,
      progress: 30
    },
    'transcription-config': { 
      title: 'Transcription Configuration', 
      subtitle: 'Set up transcription and summary settings',
      icon: Mic,
      progress: 45
    },
    'output-delivery': { 
      title: 'Content Output & Delivery', 
      subtitle: 'Choose how to receive your transcriptions',
      icon: FileText,
      progress: 60
    },
    'actions-question': { 
      title: 'Additional Actions', 
      subtitle: 'Add more capabilities to your automation',
      icon: Plus,
      progress: 75
    },
    'actions': { 
      title: 'Configure Actions', 
      subtitle: 'Set up your selected actions',
      icon: Settings,
      progress: 85
    },
    review: { 
      title: 'Review Configuration', 
      subtitle: 'Confirm your automation settings',
      icon: Eye,
      progress: 95
    },
    complete: { 
      title: 'Complete', 
      subtitle: 'Your automation is ready',
      icon: Check,
      progress: 100
    }
  }

  const nextStep = () => {
    const steps: StepType[] = ['intro', 'naming', 'youtube-apify', 'transcription-config', 'output-delivery', 'actions-question', 'actions', 'review', 'complete']
    const currentIndex = steps.indexOf(currentStep)
    
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1])
    }
  }

  const prevStep = () => {
    const steps: StepType[] = ['intro', 'naming', 'youtube-apify', 'transcription-config', 'output-delivery', 'actions-question', 'actions', 'review', 'complete']
    const currentIndex = steps.indexOf(currentStep)
    
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1])
    }
  }

  const renderIntro = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-gradient-to-br from-red-500 to-orange-600 rounded-full flex items-center justify-center mx-auto">
        <Video className="h-10 w-10 text-white" />
      </div>
      <div>
        <h2 className="text-2xl font-bold mb-2">YouTube Video Transcription & Summary</h2>
        <p className="text-muted-foreground max-w-md mx-auto">
          This template extracts audio from YouTube videos using Apify, generates accurate
          transcriptions with your choice of service, and creates intelligent summaries
          using AI for easy content repurposing and accessibility.
        </p>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <SiYoutube className="h-8 w-8 mx-auto mb-2 text-red-600" />
          <p className="text-xs font-medium">YouTube Input</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Key className="h-8 w-8 mx-auto mb-2 text-green-600" />
          <p className="text-xs font-medium">Apify Extract</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Mic className="h-8 w-8 mx-auto mb-2 text-purple-600" />
          <p className="text-xs font-medium">AI Transcription</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <FileText className="h-8 w-8 mx-auto mb-2 text-blue-600" />
          <p className="text-xs font-medium">Smart Summary</p>
        </div>
      </div>

      <Button 
        onClick={nextStep} 
        size="lg"
        className="bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700"
      >
        Start Setup
        <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
    </motion.div>
  )

  const renderNaming = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Settings className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h2 className="text-xl font-semibold mb-2">Name Your Automation</h2>
        <p className="text-muted-foreground">
          Give your automation a memorable name that describes what it does
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="automation-name">Automation Name *</Label>
          <Input
            id="automation-name"
            placeholder="e.g., Podcast Transcriber, Video Content Extractor"
            value={config.name}
            onChange={(e) => setConfig({ ...config, name: e.target.value })}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && config.name) {
                nextStep()
              }
            }}
            className="mt-2"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Choose a descriptive name you'll easily recognize
          </p>
        </div>
      </div>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          disabled={!config.name}
          className="w-full"
        >
          Continue to YouTube Setup
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderYouTubeApify = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <SiYoutube className="h-16 w-16 mx-auto mb-4 text-red-600" />
        <h3 className="text-lg font-semibold mb-2">YouTube & Apify Configuration</h3>
        <p className="text-muted-foreground text-sm">
          Set up your YouTube video source and Apify API
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">YouTube Video</CardTitle>
        </CardHeader>
        <CardContent>
          <div>
            <Label htmlFor="youtube-url">YouTube Video URL *</Label>
            <Input
              id="youtube-url"
              type="url"
              placeholder="https://www.youtube.com/watch?v=..."
              value={config.youtubeApify.youtubeUrl}
              onChange={(e) => setConfig({
                ...config,
                youtubeApify: { ...config.youtubeApify, youtubeUrl: e.target.value }
              })}
              className="mt-2"
            />
            <p className="text-xs text-muted-foreground mt-1">
              Enter the full YouTube video URL or video ID
            </p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Key className="h-4 w-4" />
            Apify API Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="api-key">Apify API Token</Label>
            <div className="relative mt-2">
              <Input
                id="api-key"
                type={showApiKey ? "text" : "password"}
                placeholder="Enter your Apify API token"
                value={config.youtubeApify.apiKey}
                onChange={(e) => setConfig({
                  ...config,
                  youtubeApify: { ...config.youtubeApify, apiKey: e.target.value }
                })}
              />
              <Button
                size="sm"
                variant="ghost"
                className="absolute right-2 top-1/2 -translate-y-1/2"
                onClick={() => setShowApiKey(!showApiKey)}
              >
                {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Get your API token from <a href="https://apify.com" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">apify.com</a>
            </p>
          </div>

          {config.youtubeApify.apiKey && (
            <Button
              className="w-full"
              onClick={() => setConfig({
                ...config,
                youtubeApify: { ...config.youtubeApify, isConfigured: true }
              })}
            >
              Verify API Configuration
            </Button>
          )}

          {config.youtubeApify.isConfigured && (
            <div className="flex items-center gap-2 text-green-600">
              <Check className="h-4 w-4" />
              <span className="text-sm">API configuration verified successfully</span>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          disabled={!config.youtubeApify.youtubeUrl || !config.youtubeApify.isConfigured}
          className="w-full"
        >
          Continue to Transcription Setup
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderTranscriptionConfig = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Mic className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h3 className="text-lg font-semibold mb-2">Transcription Configuration</h3>
        <p className="text-muted-foreground text-sm">
          Choose your transcription service and settings
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Transcription Service</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <RadioGroup
            value={config.transcriptionConfig.service}
            onValueChange={(value) => setConfig({
              ...config,
              transcriptionConfig: { 
                ...config.transcriptionConfig, 
                service: value as any 
              }
            })}
          >
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <RadioGroupItem value="whisper" id="whisper" />
              <Label htmlFor="whisper" className="cursor-pointer">
                <div>
                  <div className="font-medium">OpenAI Whisper</div>
                  <div className="text-xs text-muted-foreground">High accuracy, multiple languages</div>
                </div>
              </Label>
            </div>
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <RadioGroupItem value="assemblyai" id="assemblyai" />
              <Label htmlFor="assemblyai" className="cursor-pointer">
                <div>
                  <div className="font-medium">AssemblyAI</div>
                  <div className="text-xs text-muted-foreground">Advanced features, speaker detection</div>
                </div>
              </Label>
            </div>
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <RadioGroupItem value="rev" id="rev" />
              <Label htmlFor="rev" className="cursor-pointer">
                <div>
                  <div className="font-medium">Rev.ai</div>
                  <div className="text-xs text-muted-foreground">Professional grade, custom vocabulary</div>
                </div>
              </Label>
            </div>
          </RadioGroup>

          <div>
            <Label htmlFor="language">Transcription Language</Label>
            <Select
              value={config.transcriptionConfig.language}
              onValueChange={(value) => setConfig({
                ...config,
                transcriptionConfig: { 
                  ...config.transcriptionConfig, 
                  language: value 
                }
              })}
            >
              <SelectTrigger id="language" className="mt-2">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">English</SelectItem>
                <SelectItem value="es">Spanish</SelectItem>
                <SelectItem value="fr">French</SelectItem>
                <SelectItem value="de">German</SelectItem>
                <SelectItem value="ja">Japanese</SelectItem>
                <SelectItem value="ko">Korean</SelectItem>
                <SelectItem value="zh">Chinese</SelectItem>
                <SelectItem value="auto">Auto-detect</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="timestamps"
              checked={config.transcriptionConfig.includeTimestamps}
              onCheckedChange={(checked) => setConfig({
                ...config,
                transcriptionConfig: {
                  ...config.transcriptionConfig,
                  includeTimestamps: checked as boolean
                }
              })}
            />
            <Label htmlFor="timestamps" className="text-sm font-normal cursor-pointer">
              Include timestamps in transcription
            </Label>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">AI Summary Generation</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="summary"
              checked={config.transcriptionConfig.generateSummary}
              onCheckedChange={(checked) => setConfig({
                ...config,
                transcriptionConfig: {
                  ...config.transcriptionConfig,
                  generateSummary: checked as boolean
                }
              })}
            />
            <Label htmlFor="summary" className="text-sm font-normal cursor-pointer">
              Generate AI-powered summary
            </Label>
          </div>

          {config.transcriptionConfig.generateSummary && (
            <>
              <div>
                <Label>AI Provider</Label>
                <Select
                  value={config.transcriptionConfig.summaryProvider}
                  onValueChange={(value) => setConfig({
                    ...config,
                    transcriptionConfig: { 
                      ...config.transcriptionConfig, 
                      summaryProvider: value as any 
                    }
                  })}
                >
                  <SelectTrigger className="mt-2">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="openai">OpenAI</SelectItem>
                    <SelectItem value="anthropic">Anthropic</SelectItem>
                    <SelectItem value="google">Google AI</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Model</Label>
                <Select
                  value={config.transcriptionConfig.summaryModel}
                  onValueChange={(value) => setConfig({
                    ...config,
                    transcriptionConfig: { 
                      ...config.transcriptionConfig, 
                      summaryModel: value 
                    }
                  })}
                >
                  <SelectTrigger className="mt-2">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {config.transcriptionConfig.summaryProvider === 'openai' && (
                      <>
                        <SelectItem value="gpt-4o">GPT-4o (Recommended)</SelectItem>
                        <SelectItem value="gpt-4">GPT-4</SelectItem>
                        <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                      </>
                    )}
                    {config.transcriptionConfig.summaryProvider === 'anthropic' && (
                      <>
                        <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
                        <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                        <SelectItem value="claude-3-haiku">Claude 3 Haiku</SelectItem>
                      </>
                    )}
                    {config.transcriptionConfig.summaryProvider === 'google' && (
                      <>
                        <SelectItem value="gemini-1.5-pro">Gemini 1.5 Pro</SelectItem>
                        <SelectItem value="gemini-1.0-pro">Gemini 1.0 Pro</SelectItem>
                      </>
                    )}
                  </SelectContent>
                </Select>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          className="w-full"
        >
          Continue to Output Settings
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderOutputDelivery = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <FileText className="h-16 w-16 mx-auto mb-4 text-blue-600" />
        <h3 className="text-lg font-semibold mb-2">Content Output & Delivery</h3>
        <p className="text-muted-foreground text-sm">
          Choose format and delivery method for your transcription
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Output Format</CardTitle>
        </CardHeader>
        <CardContent>
          <RadioGroup
            value={config.outputDelivery.format}
            onValueChange={(value) => setConfig({
              ...config,
              outputDelivery: { 
                ...config.outputDelivery, 
                format: value as any 
              }
            })}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="text" id="text" />
              <Label htmlFor="text" className="font-normal">Plain Text</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="srt" id="srt" />
              <Label htmlFor="srt" className="font-normal">SRT (SubRip Subtitle)</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="vtt" id="vtt" />
              <Label htmlFor="vtt" className="font-normal">VTT (WebVTT)</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="json" id="json" />
              <Label htmlFor="json" className="font-normal">JSON (Structured Data)</Label>
            </div>
          </RadioGroup>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Delivery Method</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <RadioGroup
            value={config.outputDelivery.deliveryMethod}
            onValueChange={(value) => setConfig({
              ...config,
              outputDelivery: { 
                ...config.outputDelivery, 
                deliveryMethod: value as any 
              }
            })}
          >
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <RadioGroupItem value="download" id="download" />
              <Label htmlFor="download" className="cursor-pointer flex items-center gap-2">
                <Download className="h-4 w-4" />
                Direct Download
              </Label>
            </div>
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <RadioGroupItem value="email" id="email" />
              <Label htmlFor="email" className="cursor-pointer flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Email Delivery
              </Label>
            </div>
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <RadioGroupItem value="webhook" id="webhook" />
              <Label htmlFor="webhook" className="cursor-pointer flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Webhook
              </Label>
            </div>
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <RadioGroupItem value="googlesheets" id="googlesheets" />
              <Label htmlFor="googlesheets" className="cursor-pointer flex items-center gap-2">
                <SiGooglesheets className="h-4 w-4" />
                Google Sheets
              </Label>
            </div>
          </RadioGroup>

          {config.outputDelivery.deliveryMethod === 'email' && (
            <div>
              <Label htmlFor="email-address">Email Address</Label>
              <Input
                id="email-address"
                type="email"
                placeholder="<EMAIL>"
                value={config.outputDelivery.email}
                onChange={(e) => setConfig({
                  ...config,
                  outputDelivery: { 
                    ...config.outputDelivery, 
                    email: e.target.value 
                  }
                })}
                className="mt-2"
              />
            </div>
          )}

          {config.outputDelivery.deliveryMethod === 'webhook' && (
            <div>
              <Label htmlFor="webhook-url">Webhook URL</Label>
              <Input
                id="webhook-url"
                type="url"
                placeholder="https://your-webhook-endpoint.com"
                value={config.outputDelivery.webhookUrl}
                onChange={(e) => setConfig({
                  ...config,
                  outputDelivery: { 
                    ...config.outputDelivery, 
                    webhookUrl: e.target.value 
                  }
                })}
                className="mt-2"
              />
            </div>
          )}

          {config.outputDelivery.deliveryMethod === 'googlesheets' && (
            <div className="space-y-4 pt-2">
              <div>
                <Label className="text-sm font-medium">Authentication Type <span className="text-red-500">*</span></Label>
                <div className="mt-2">
                  <div className="flex items-center space-x-2">
                    <input type="radio" id="google-signin" checked readOnly className="text-blue-600" />
                    <Label htmlFor="google-signin" className="text-sm">Google Sign-In</Label>
                  </div>
                </div>
              </div>

              <div className="text-xs text-muted-foreground">
                <strong>Note:</strong> Select existing Google account from below or Signin with a different account.
              </div>

              {!config.outputDelivery.googleAuth.isAuthenticated ? (
                <Button
                  onClick={() => setConfig({
                    ...config,
                    outputDelivery: {
                      ...config.outputDelivery,
                      googleAuth: {
                        isAuthenticated: true,
                        userEmail: '<EMAIL>',
                        userName: 'John Doe',
                        accountId: 'gauth-12345'
                      }
                    }
                  })}
                  className="w-full"
                >
                  <SiGooglesheets className="mr-2 h-4 w-4" />
                  Sign in with Google
                </Button>
              ) : (
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Badge variant="default" className="bg-green-600">Connected</Badge>
                    <span className="text-sm font-medium">Google</span>
                  </div>
                  
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Google account</Label>
                    <div className="p-3 border rounded-md bg-muted/50">
                      <div className="text-sm font-medium">{config.outputDelivery.googleAuth.userName}</div>
                      <div className="text-sm text-muted-foreground">({config.outputDelivery.googleAuth.userEmail})</div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          className="w-full"
        >
          Continue
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderActionsQuestion = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Plus className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h3 className="text-lg font-semibold mb-2">Do you want to add any actions?</h3>
        <p className="text-muted-foreground text-sm">
          Actions let you extend your automation with additional capabilities
        </p>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <Button
          variant={config.wantsActions === true ? "default" : "outline"}
          size="lg"
          onClick={() => {
            setConfig({ ...config, wantsActions: true })
            nextStep()
          }}
          className="h-auto py-6"
        >
          <div className="flex flex-col items-center gap-2">
            <Check className="h-8 w-8" />
            <span>Yes, I want to add actions</span>
          </div>
        </Button>
        <Button
          variant={config.wantsActions === false ? "default" : "outline"}
          size="lg"
          onClick={() => {
            setConfig({ ...config, wantsActions: false })
            setCurrentStep('review')  // Skip directly to review
          }}
          className="h-auto py-6"
        >
          <div className="flex flex-col items-center gap-2">
            <ArrowRight className="h-8 w-8" />
            <span>No, continue without</span>
          </div>
        </Button>
      </div>

      <div>
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
      </div>
    </motion.div>
  )

  const renderActions = () => {
    const selectedActionsList = config.selectedActionsList || []

    const availableActionsList = [
      { id: 'discord-read', name: 'Discord Read', icon: MessageSquare, desc: 'Read messages from any Discord channel' },
      { id: 'discord-send', name: 'Discord Send', icon: MessageSquare, desc: 'Send messages to any Discord channel' },
      { id: 'gmail-read', name: 'Gmail Read', icon: Mail, desc: 'Read emails from your Gmail account' },
      { id: 'gmail-send', name: 'Gmail Send', icon: Mail, desc: 'Send emails via your Gmail account' },
      { id: 'gcalendar-read', name: 'Google Calendar Read', icon: Calendar, desc: 'Read events from any Google Calendar' },
      { id: 'gcalendar-write', name: 'Google Calendar Write', icon: Calendar, desc: 'Create events on any Google Calendar' },
      { id: 'http-request', name: 'HTTP Request', icon: Globe, desc: 'Make GET, POST, PUT, or DELETE HTTP requests' },
      { id: 'slack-read', name: 'Slack Read', icon: Hash, desc: 'Read messages from a specified Slack channel' },
      { id: 'slack-send', name: 'Slack Send', icon: Hash, desc: 'Send a message to a specified Slack channel' }
    ]

    const addAction = (action: any) => {
      const newAction = {
        id: Date.now().toString(),
        type: action.id,
        name: action.name,
        description: action.desc,
        config: {}
      }
      const updatedList = [...selectedActionsList, newAction]
      setConfig({ ...config, selectedActionsList: updatedList })
      setShowActionConfig(updatedList.length - 1)
    }

    const removeAction = (index: number) => {
      const updatedList = selectedActionsList.filter((_, i) => i !== index)
      setConfig({ ...config, selectedActionsList: updatedList })
    }

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <div className="text-center">
          <Plus className="h-16 w-16 mx-auto mb-4 text-purple-600" />
          <h3 className="text-lg font-semibold mb-2">What actions should happen?</h3>
          <p className="text-muted-foreground text-sm">
            Define the steps your automation will take
          </p>
        </div>

        {/* Selected Actions */}
        {selectedActionsList.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base">⚙️ Actions ({selectedActionsList.length})</Label>
            </div>
            <div className="space-y-2">
              {selectedActionsList.map((action, index) => {
                const actionInfo = availableActionsList.find(a => a.id === action.type)
                const Icon = actionInfo?.icon || Settings
                return (
                  <motion.div
                    key={action.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="flex items-center gap-3 p-4 bg-muted/30 rounded-lg border"
                  >
                    <Icon className="h-5 w-5 text-primary" />
                    <div className="flex-1">
                      <p className="font-medium">{action.name}</p>
                      <p className="text-xs text-muted-foreground">{action.description}</p>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setShowActionConfig(index)}
                        className="h-8 px-2"
                      >
                        <Settings className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => removeAction(index)}
                        className="h-8 w-8 p-0"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </motion.div>
                )
              })}
            </div>
          </div>
        )}

        {/* Available Actions */}
        <div>
          <Label className="text-base mb-3 block">Available Actions</Label>
          <div className="grid gap-3">
            {availableActionsList.map((action) => (
              <Card
                key={action.id}
                className="cursor-pointer transition-all hover:shadow-md"
                onClick={() => addAction(action)}
              >
                <CardHeader className="p-4">
                  <div className="flex items-center gap-3">
                    <action.icon className="h-5 w-5 text-primary" />
                    <div className="flex-1">
                      <CardTitle className="text-sm">{action.name}</CardTitle>
                      <CardDescription className="text-xs mt-1">{action.desc}</CardDescription>
                    </div>
                    <Plus className="h-4 w-4 text-muted-foreground" />
                  </div>
                </CardHeader>
              </Card>
            ))}
          </div>
        </div>

        <div className="flex gap-3">
          <Button 
            onClick={prevStep}
            variant="outline"
            className="w-full"
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button 
            onClick={nextStep}
            className="w-full"
          >
            Continue to Review
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </motion.div>
    )
  }

  const renderReview = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Eye className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h3 className="text-lg font-semibold mb-2">Review Your Configuration</h3>
        <p className="text-muted-foreground text-sm">
          Make sure everything looks correct before creating your automation
        </p>
      </div>

      <div className="space-y-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Automation Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Name</span>
              <span className="text-sm font-medium">{config.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">YouTube URL</span>
              <span className="text-sm font-medium truncate max-w-[200px]">{config.youtubeApify.youtubeUrl}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Transcription Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Service</span>
              <span className="text-sm font-medium capitalize">{config.transcriptionConfig.service}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Language</span>
              <span className="text-sm font-medium">{config.transcriptionConfig.language === 'auto' ? 'Auto-detect' : config.transcriptionConfig.language.toUpperCase()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Include Timestamps</span>
              <span className="text-sm font-medium">{config.transcriptionConfig.includeTimestamps ? 'Yes' : 'No'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">AI Summary</span>
              <span className="text-sm font-medium">{config.transcriptionConfig.generateSummary ? 'Yes' : 'No'}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Output Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Format</span>
              <span className="text-sm font-medium uppercase">{config.outputDelivery.format}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Delivery Method</span>
              <span className="text-sm font-medium capitalize">{config.outputDelivery.deliveryMethod.replace('googlesheets', 'Google Sheets')}</span>
            </div>
          </CardContent>
        </Card>

        {config.selectedActionsList.length > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Actions ({config.selectedActionsList.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {config.selectedActionsList.map((action) => (
                  <div key={action.id} className="text-sm">
                    • {action.name}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          className="w-full bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700"
        >
          Create Automation
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderComplete = () => (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto">
        <Check className="h-10 w-10 text-green-600 dark:text-green-400" />
      </div>
      <div>
        <h2 className="text-3xl font-bold mb-2">Automation Created!</h2>
        <p className="text-muted-foreground text-lg">
          Your "{config.name}" automation is now ready and will start processing videos automatically.
        </p>
      </div>
      
      <div className="flex flex-col sm:flex-row gap-3 justify-center mt-8">
        <Button 
          onClick={() => setLocation('/dashboard/automations')}
          size="lg"
          className="bg-primary hover:bg-primary/90"
        >
          View All Automations
        </Button>
        <Button 
          onClick={() => setLocation('/dashboard/browse-templates')}
          variant="outline"
          size="lg"
        >
          Browse More Templates
        </Button>
      </div>
    </motion.div>
  )

  const renderStepContent = () => {
    switch (currentStep) {
      case 'intro': return renderIntro()
      case 'naming': return renderNaming()
      case 'youtube-apify': return renderYouTubeApify()
      case 'transcription-config': return renderTranscriptionConfig()
      case 'output-delivery': return renderOutputDelivery()
      case 'actions-question': return renderActionsQuestion()
      case 'actions': return renderActions()
      case 'review': return renderReview()
      case 'complete': return renderComplete()
      default: return null
    }
  }

  const CurrentIcon = stepConfig[currentStep].icon

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => setLocation('/dashboard/browse-templates')}
          className="mb-4"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back to Templates
        </Button>

        {currentStep !== 'complete' && (
          <>
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <CurrentIcon className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">{stepConfig[currentStep].title}</h1>
                <p className="text-muted-foreground text-sm">{stepConfig[currentStep].subtitle}</p>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex justify-between text-xs text-muted-foreground mb-2">
                <span>Step {Object.keys(stepConfig).indexOf(currentStep) + 1} of 8</span>
                <span>{stepConfig[currentStep].progress}% Complete</span>
              </div>
              <Progress value={stepConfig[currentStep].progress} className="h-2" />
            </div>
          </>
        )}
      </div>

      {/* Content */}
      <Card className="p-6">
        {renderStepContent()}
      </Card>
    </div>
  )
}