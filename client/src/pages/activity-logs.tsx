import { useState } from 'react'
import { format } from 'date-fns'
import { 
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw,
  Download,
  Filter,
  Eye
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { useSettings } from '@/contexts/SettingsContext'

interface Log {
  id: string
  workflow: string
  status: 'success' | 'failed' | 'running'
  trigger: string
  timestamp: Date
  executionTime: number
  error?: string
  details?: any
}

// Mock data
const mockLogs: Log[] = [
  {
    id: '1',
    workflow: 'Slack → Gmail Auto-Reply',
    status: 'success',
    trigger: 'Webhook',
    timestamp: new Date(Date.now() - 300000),
    executionTime: 2.3,
  },
  {
    id: '2',
    workflow: 'Lead Capture to Airtable',
    status: 'failed',
    trigger: 'Form Submit',
    timestamp: new Date(Date.now() - 900000),
    executionTime: 0.8,
    error: 'Authentication failed: Invalid API key',
  },
  {
    id: '3',
    workflow: 'Invoice Generation',
    status: 'success',
    trigger: 'Schedule',
    timestamp: new Date(Date.now() - 1800000),
    executionTime: 5.1,
  },
  {
    id: '4',
    workflow: 'Daily Report Email',
    status: 'running',
    trigger: 'Schedule',
    timestamp: new Date(Date.now() - 60000),
    executionTime: 0,
  },
  {
    id: '5',
    workflow: 'Slack to Sheets Logger',
    status: 'success',
    trigger: 'Slack',
    timestamp: new Date(Date.now() - 3600000),
    executionTime: 1.2,
  },
]

export default function ActivityLogs() {
  const [logs] = useState(mockLogs)
  const [statusFilter, setStatusFilter] = useState('all')
  const [workflowFilter, setWorkflowFilter] = useState('all')
  const [dateFilter, setDateFilter] = useState('')
  const [selectedLog, setSelectedLog] = useState<Log | null>(null)
  const { settings } = useSettings()

  const uniqueWorkflows = ['all', ...new Set(logs.map(log => log.workflow))]

  const filteredLogs = logs.filter(log => {
    const matchesStatus = statusFilter === 'all' || log.status === statusFilter
    const matchesWorkflow = workflowFilter === 'all' || log.workflow === workflowFilter
    let matchesDate = true
    
    if (dateFilter) {
      const logDate = format(log.timestamp, 'yyyy-MM-dd')
      matchesDate = logDate === dateFilter
    }
    
    return matchesStatus && matchesWorkflow && matchesDate
  })

  const getStatusIcon = (status: Log['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'running':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />
    }
  }

  const getStatusBadge = (status: Log['status']) => {
    switch (status) {
      case 'success':
        return <Badge className="bg-green-100 text-green-800">Success</Badge>
      case 'failed':
        return <Badge className="bg-red-100 text-red-800">Failed</Badge>
      case 'running':
        return <Badge className="bg-blue-100 text-blue-800">Running</Badge>
    }
  }

  const formatTime = (date: Date) => {
    if (settings.timeFormat === '24h') {
      return format(date, 'HH:mm:ss')
    }
    return format(date, 'hh:mm:ss a')
  }

  const formatDate = (date: Date) => {
    if (settings.dateFormat === 'DD/MM/YYYY') {
      return format(date, 'dd/MM/yyyy')
    }
    return format(date, 'MM/dd/yyyy')
  }

  const handleRefresh = () => {
    // Refresh logs
    window.location.reload()
  }

  const handleExport = () => {
    // Export logs as CSV
    const csv = [
      ['Workflow', 'Status', 'Trigger', 'Timestamp', 'Execution Time', 'Error'],
      ...filteredLogs.map(log => [
        log.workflow,
        log.status,
        log.trigger,
        log.timestamp.toISOString(),
        log.executionTime.toString(),
        log.error || ''
      ])
    ].map(row => row.join(',')).join('\n')

    const blob = new Blob([csv], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `activity-logs-${format(new Date(), 'yyyy-MM-dd')}.csv`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Activity Logs</h1>
        <p className="text-muted-foreground">
          Monitor all automation executions and debug issues
        </p>
      </div>

      {/* Stats */}
      <div className="grid gap-4 grid-cols-2 lg:grid-cols-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Executions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">{logs.length}</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Success Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold text-green-600">
              {Math.round((logs.filter(l => l.status === 'success').length / logs.filter(l => l.status !== 'running').length) * 100)}%
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Failed
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold text-red-600">
              {logs.filter(l => l.status === 'failed').length}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Avg. Execution Time
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">
              {(logs.filter(l => l.executionTime > 0).reduce((sum, l) => sum + l.executionTime, 0) / logs.filter(l => l.executionTime > 0).length).toFixed(1)}s
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col lg:flex-row gap-4 mb-6">
        <div className="flex flex-col sm:flex-row flex-1 gap-4">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder="All Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="success">Success</SelectItem>
              <SelectItem value="failed">Failed</SelectItem>
              <SelectItem value="running">Running</SelectItem>
            </SelectContent>
          </Select>

          <Select value={workflowFilter} onValueChange={setWorkflowFilter}>
            <SelectTrigger className="w-full sm:w-64">
              <SelectValue placeholder="All Workflows" />
            </SelectTrigger>
            <SelectContent>
              {uniqueWorkflows.map(workflow => (
                <SelectItem key={workflow} value={workflow}>
                  {workflow === 'all' ? 'All Workflows' : workflow}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <div className="relative">
            <Calendar className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              type="date"
              className="pl-10"
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
            />
          </div>
        </div>

        <div className="flex gap-2 w-full sm:w-auto">
          <Button variant="outline" onClick={handleRefresh} className="flex-1 sm:flex-initial">
            <RefreshCw className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Refresh</span>
          </Button>
          <Button variant="outline" onClick={handleExport} className="flex-1 sm:flex-initial">
            <Download className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Export</span>
          </Button>
        </div>
      </div>

      {/* Logs Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Workflow</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Timestamp</TableHead>
                  <TableHead>Trigger</TableHead>
                  <TableHead>Execution Time</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredLogs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell className="font-medium">
                      {log.workflow}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(log.status)}
                        {getStatusBadge(log.status)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>{formatDate(log.timestamp)}</div>
                        <div className="text-muted-foreground">{formatTime(log.timestamp)}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{log.trigger}</Badge>
                    </TableCell>
                    <TableCell>
                      {log.status === 'running' ? (
                        <span className="text-muted-foreground">-</span>
                      ) : (
                        `${log.executionTime}s`
                      )}
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSelectedLog(log)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Log Details Dialog */}
      <Dialog open={!!selectedLog} onOpenChange={() => setSelectedLog(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{selectedLog?.workflow}</DialogTitle>
            <DialogDescription>
              Execution details for {selectedLog && formatDate(selectedLog.timestamp)} at {selectedLog && formatTime(selectedLog.timestamp)}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Status</p>
                <div className="flex items-center gap-2 mt-1">
                  {selectedLog && getStatusIcon(selectedLog.status)}
                  {selectedLog && getStatusBadge(selectedLog.status)}
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Trigger</p>
                <Badge variant="outline" className="mt-1">{selectedLog?.trigger}</Badge>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Execution Time</p>
                <p className="mt-1">{selectedLog?.executionTime}s</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Log ID</p>
                <p className="mt-1 font-mono text-sm">{selectedLog?.id}</p>
              </div>
            </div>
            
            {selectedLog?.error && (
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-2">Error Details</p>
                <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                  <p className="text-sm text-red-800 dark:text-red-200">
                    {selectedLog.error}
                  </p>
                </div>
              </div>
            )}

            {selectedLog?.details && (
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-2">Execution Details</p>
                <pre className="p-4 bg-muted rounded-lg overflow-x-auto">
                  <code className="text-sm">
                    {JSON.stringify(selectedLog.details, null, 2)}
                  </code>
                </pre>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}