import { useEffect } from 'react';
import { useLocation } from 'wouter';

export default function OAuthSuccess() {
  const [, setLocation] = useLocation();

  useEffect(() => {
    // Extract URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const automationId = urlParams.get('automation_id');
    const error = urlParams.get('error');

    if (error) {
      // Send error to parent window
      if (window.opener) {
        window.opener.postMessage({
          type: 'oauth-error',
          error: decodeURIComponent(error),
          automationId
        }, window.location.origin);
        window.close();
      } else {
        // If not in popup, redirect to dashboard with error
        setLocation(`/dashboard/automations?error=${error}`);
      }
      return;
    }

    if (automationId) {
      // Fetch the updated automation to get Google account details
      fetch(`/api/automations/${automationId}`)
        .then(response => response.json())
        .then(automation => {
          const config = automation.trigger?.config;
          
          if (window.opener) {
            // Send success data to parent window
            window.opener.postMessage({
              type: 'oauth-success',
              automationId,
              googleAccountId: config?.googleAccountId,
              googleAccountName: config?.googleAccountName,
              googleAccountEmail: config?.googleAccountEmail
            }, window.location.origin);
            window.close();
          } else {
            // If not in popup, redirect to automation edit page
            setLocation(`/dashboard/automations/${automationId}/edit?oauth_success=true`);
          }
        })
        .catch(error => {
          console.error('Failed to fetch automation:', error);
          if (window.opener) {
            window.opener.postMessage({
              type: 'oauth-error',
              error: 'Failed to fetch automation details',
              automationId
            }, window.location.origin);
            window.close();
          } else {
            setLocation(`/dashboard/automations?error=oauth_failed`);
          }
        });
    } else {
      // No automation ID, just close or redirect
      if (window.opener) {
        window.close();
      } else {
        setLocation('/dashboard/automations');
      }
    }
  }, [setLocation]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <h2 className="text-lg font-semibold mb-2">Completing Authentication</h2>
        <p className="text-muted-foreground">Please wait while we finish setting up your Google connection...</p>
      </div>
    </div>
  );
}