import { useState } from 'react'
import { motion } from 'framer-motion'
import { useLocation } from 'wouter'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Checkbox } from "@/components/ui/checkbox"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { 
  Calendar, Mail, Clock, Brain, Send, Settings, Check, ArrowRight, ChevronLeft,
  X, Eye, Plus, MessageSquare, Hash, Globe, Bell, Search
} from 'lucide-react'
import { SiGmail, SiGooglecalendar, Si<PERSON>penai, SiAnthropic, SiGoogle } from 'react-icons/si'

type StepType = 'intro' | 'naming' | 'google-auth' | 'calendar-config' | 'email-config' | 'ai-settings' | 'delivery-settings' | 'actions-question' | 'actions' | 'review' | 'complete'

interface DailyDigestConfig {
  name: string
  googleAuth: {
    isAuthenticated: boolean
    userEmail: string
    userName: string
    accountId: string
  }
  calendarConfig: {
    calendars: string[]
    includeAllDay: boolean
    includeDeclined: boolean
    timeRange: {
      start: string
      end: string
    }
    eventDetails: {
      title: boolean
      time: boolean
      location: boolean
      attendees: boolean
      description: boolean
    }
  }
  emailConfig: {
    filters: {
      unreadOnly: boolean
      labels: string[]
      senders: string[]
      keywords: string[]
      timeRange: number // hours to look back
    }
    summaryDepth: 'brief' | 'detailed' | 'comprehensive'
    includeAttachments: boolean
  }
  aiSettings: {
    provider: 'openai' | 'anthropic' | 'google'
    model: string
    isConfigured: boolean
    summaryStyle: 'professional' | 'casual' | 'executive'
    language: string
    tone: 'informative' | 'friendly' | 'formal'
  }
  deliverySettings: {
    schedule: {
      time: string
      timezone: string
      weekdays: string[]
    }
    email: {
      recipient: string
      subject: string
      format: 'html' | 'text'
      includeActions: boolean
    }
    backup: {
      enabled: boolean
      slackChannel: string
      webhookUrl: string
    }
  }
  selectedActionsList: Array<{
    id: string
    type: string
    name: string
    description: string
    config: Record<string, any>
  }>
  wantsActions: boolean | null
}

export default function DailyDigestTemplate() {
  const [, setLocation] = useLocation()
  const [currentStep, setCurrentStep] = useState<StepType>('intro')
  const [showActionConfig, setShowActionConfig] = useState<number | null>(null)
  const [actionSearchQuery, setActionSearchQuery] = useState('')
  const [config, setConfig] = useState<DailyDigestConfig>({
    name: '',
    googleAuth: {
      isAuthenticated: false,
      userEmail: '',
      userName: '',
      accountId: ''
    },
    calendarConfig: {
      calendars: [],
      includeAllDay: true,
      includeDeclined: false,
      timeRange: {
        start: '00:00',
        end: '23:59'
      },
      eventDetails: {
        title: true,
        time: true,
        location: true,
        attendees: false,
        description: false
      }
    },
    emailConfig: {
      filters: {
        unreadOnly: true,
        labels: [],
        senders: [],
        keywords: [],
        timeRange: 24
      },
      summaryDepth: 'brief',
      includeAttachments: false
    },
    aiSettings: {
      provider: 'openai',
      model: 'gpt-4o',
      isConfigured: false,
      summaryStyle: 'professional',
      language: 'English',
      tone: 'informative'
    },
    deliverySettings: {
      schedule: {
        time: '07:00',
        timezone: 'America/New_York',
        weekdays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
      },
      email: {
        recipient: '',
        subject: 'Daily Summary - {date}',
        format: 'html',
        includeActions: true
      },
      backup: {
        enabled: false,
        slackChannel: '',
        webhookUrl: ''
      }
    },
    selectedActionsList: [],
    wantsActions: null
  })

  const stepConfig = {
    intro: { 
      title: 'Daily Calendar + Email Summary', 
      subtitle: 'Get personalized morning briefings with AI',
      icon: Calendar,
      progress: 0
    },
    naming: {
      title: 'Name Your Automation',
      subtitle: 'Give your automation a memorable name',
      icon: Settings,
      progress: 10
    },
    'google-auth': { 
      title: 'Google Authentication', 
      subtitle: 'Connect your Google account',
      icon: SiGoogle,
      progress: 20
    },
    'calendar-config': { 
      title: 'Calendar Configuration', 
      subtitle: 'Choose calendars to include',
      icon: SiGooglecalendar,
      progress: 30
    },
    'email-config': { 
      title: 'Email Configuration', 
      subtitle: 'Set up Gmail filtering',
      icon: SiGmail,
      progress: 40
    },
    'ai-settings': { 
      title: 'AI Settings', 
      subtitle: 'Configure summary generation',
      icon: Brain,
      progress: 50
    },
    'delivery-settings': { 
      title: 'Delivery Settings', 
      subtitle: 'Schedule your daily digest',
      icon: Send,
      progress: 60
    },
    'actions-question': { 
      title: 'Additional Actions', 
      subtitle: 'Add more capabilities to your automation',
      icon: Plus,
      progress: 70
    },
    'actions': { 
      title: 'Configure Actions', 
      subtitle: 'Set up your selected actions',
      icon: Settings,
      progress: 80
    },
    review: { 
      title: 'Review Configuration', 
      subtitle: 'Confirm your automation settings',
      icon: Eye,
      progress: 90
    },
    complete: { 
      title: 'Complete', 
      subtitle: 'Your automation is ready',
      icon: Check,
      progress: 100
    }
  }

  const nextStep = () => {
    const steps: StepType[] = ['intro', 'naming', 'google-auth', 'calendar-config', 'email-config', 'ai-settings', 'delivery-settings', 'actions-question', 'actions', 'review', 'complete']
    const currentIndex = steps.indexOf(currentStep)
    
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1])
    }
  }

  const prevStep = () => {
    const steps: StepType[] = ['intro', 'naming', 'google-auth', 'calendar-config', 'email-config', 'ai-settings', 'delivery-settings', 'actions-question', 'actions', 'review', 'complete']
    const currentIndex = steps.indexOf(currentStep)
    
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1])
    }
  }

  const mockCalendars = ['Primary', 'Work Calendar', 'Personal', 'Team Meetings']
  const mockLabels = ['Important', 'Work', 'Projects', 'Clients', 'Notifications']
  const weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
  const timezones = [
    'America/New_York',
    'America/Los_Angeles', 
    'America/Chicago',
    'Europe/London',
    'Europe/Paris',
    'Asia/Tokyo',
    'Asia/Shanghai',
    'Australia/Sydney'
  ]

  const renderIntro = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto">
        <Calendar className="h-10 w-10 text-white" />
      </div>
      <div>
        <h2 className="text-2xl font-bold mb-2">Daily Calendar + Email Summary Digest</h2>
        <p className="text-muted-foreground max-w-md mx-auto">
          This template creates personalized morning briefings with today's calendar events
          and Gmail highlights, perfect for busy professionals and startup founders who need
          to start their day fully informed and organized.
        </p>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <SiGooglecalendar className="h-8 w-8 mx-auto mb-2 text-blue-600" />
          <p className="text-xs font-medium">Calendar Sync</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <SiGmail className="h-8 w-8 mx-auto mb-2 text-red-600" />
          <p className="text-xs font-medium">Email Analysis</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Brain className="h-8 w-8 mx-auto mb-2 text-purple-600" />
          <p className="text-xs font-medium">AI Summary</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Clock className="h-8 w-8 mx-auto mb-2 text-green-600" />
          <p className="text-xs font-medium">Morning Delivery</p>
        </div>
      </div>

      <Button 
        onClick={nextStep} 
        size="lg"
        className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700"
      >
        Start Setup
        <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
    </motion.div>
  )

  const renderNaming = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Settings className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h2 className="text-xl font-semibold mb-2">Name Your Automation</h2>
        <p className="text-muted-foreground">
          Give your automation a memorable name that describes what it does
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="automation-name">Automation Name *</Label>
          <Input
            id="automation-name"
            placeholder="e.g., Morning Briefing, Daily Executive Summary"
            value={config.name}
            onChange={(e) => setConfig({ ...config, name: e.target.value })}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && config.name) {
                nextStep()
              }
            }}
            className="mt-2"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Choose a descriptive name you'll easily recognize
          </p>
        </div>
      </div>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          disabled={!config.name}
          className="w-full"
        >
          Continue to Google Auth
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderGoogleAuth = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <SiGoogle className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h3 className="text-lg font-semibold mb-2">Google Authentication</h3>
        <p className="text-muted-foreground text-sm">
          Connect your Google account to access Calendar and Gmail
        </p>
      </div>

      <Card>
        <CardContent className="p-6 space-y-4">
          <div>
            <Label className="text-sm font-medium">Authentication Type <span className="text-red-500">*</span></Label>
            <div className="mt-2">
              <div className="flex items-center space-x-2">
                <input type="radio" id="google-signin" checked readOnly className="text-blue-600" />
                <Label htmlFor="google-signin" className="text-sm">Google Sign-In</Label>
              </div>
            </div>
          </div>

          <div className="text-xs text-muted-foreground">
            <strong>Note:</strong> Select existing Google account from below or Signin with a different account.
          </div>

          {!config.googleAuth.isAuthenticated ? (
            <Button
              onClick={() => setConfig({
                ...config,
                googleAuth: {
                  isAuthenticated: true,
                  userEmail: '<EMAIL>',
                  userName: 'John Doe',
                  accountId: 'gauth-12345'
                }
              })}
              className="w-full"
            >
              <SiGoogle className="mr-2 h-4 w-4" />
              Sign in with Google
            </Button>
          ) : (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Badge variant="default" className="bg-green-600">Connected</Badge>
                <span className="text-sm font-medium">Google</span>
              </div>
              
              <div className="space-y-2">
                <Label className="text-sm font-medium">Google account</Label>
                <div className="p-3 border rounded-md bg-muted/50">
                  <div className="text-sm font-medium">{config.googleAuth.userName}</div>
                  <div className="text-sm text-muted-foreground">({config.googleAuth.userEmail})</div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          disabled={!config.googleAuth.isAuthenticated}
          className="w-full"
        >
          Continue to Calendar Setup
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderCalendarConfig = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <SiGooglecalendar className="h-16 w-16 mx-auto mb-4 text-blue-600" />
        <h3 className="text-lg font-semibold mb-2">Calendar Configuration</h3>
        <p className="text-muted-foreground text-sm">
          Choose which calendars and events to include in your digest
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Select Calendars</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            {mockCalendars.map((calendar) => (
              <div key={calendar} className="flex items-center space-x-2">
                <Checkbox
                  id={calendar}
                  checked={config.calendarConfig.calendars.includes(calendar)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setConfig({
                        ...config,
                        calendarConfig: {
                          ...config.calendarConfig,
                          calendars: [...config.calendarConfig.calendars, calendar]
                        }
                      })
                    } else {
                      setConfig({
                        ...config,
                        calendarConfig: {
                          ...config.calendarConfig,
                          calendars: config.calendarConfig.calendars.filter(c => c !== calendar)
                        }
                      })
                    }
                  }}
                />
                <Label htmlFor={calendar} className="text-sm font-normal cursor-pointer">
                  {calendar}
                </Label>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Event Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div>
              <Label>Daily Time Range</Label>
              <div className="grid grid-cols-2 gap-4 mt-2">
                <div>
                  <Label htmlFor="start-time" className="text-xs">Start</Label>
                  <Input
                    id="start-time"
                    type="time"
                    value={config.calendarConfig.timeRange.start}
                    onChange={(e) => setConfig({
                      ...config,
                      calendarConfig: {
                        ...config.calendarConfig,
                        timeRange: {
                          ...config.calendarConfig.timeRange,
                          start: e.target.value
                        }
                      }
                    })}
                  />
                </div>
                <div>
                  <Label htmlFor="end-time" className="text-xs">End</Label>
                  <Input
                    id="end-time"
                    type="time"
                    value={config.calendarConfig.timeRange.end}
                    onChange={(e) => setConfig({
                      ...config,
                      calendarConfig: {
                        ...config.calendarConfig,
                        timeRange: {
                          ...config.calendarConfig.timeRange,
                          end: e.target.value
                        }
                      }
                    })}
                  />
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="all-day"
                  checked={config.calendarConfig.includeAllDay}
                  onCheckedChange={(checked) => setConfig({
                    ...config,
                    calendarConfig: {
                      ...config.calendarConfig,
                      includeAllDay: checked as boolean
                    }
                  })}
                />
                <Label htmlFor="all-day" className="text-sm font-normal cursor-pointer">
                  Include all-day events
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="declined"
                  checked={config.calendarConfig.includeDeclined}
                  onCheckedChange={(checked) => setConfig({
                    ...config,
                    calendarConfig: {
                      ...config.calendarConfig,
                      includeDeclined: checked as boolean
                    }
                  })}
                />
                <Label htmlFor="declined" className="text-sm font-normal cursor-pointer">
                  Include declined events
                </Label>
              </div>
            </div>

            <div>
              <Label className="text-sm mb-2 block">Event Details to Include</Label>
              <div className="space-y-2">
                {Object.entries({
                  title: 'Event Title',
                  time: 'Time & Duration',
                  location: 'Location',
                  attendees: 'Attendees',
                  description: 'Description'
                }).map(([key, label]) => (
                  <div key={key} className="flex items-center space-x-2">
                    <Checkbox
                      id={key}
                      checked={config.calendarConfig.eventDetails[key as keyof typeof config.calendarConfig.eventDetails]}
                      onCheckedChange={(checked) => setConfig({
                        ...config,
                        calendarConfig: {
                          ...config.calendarConfig,
                          eventDetails: {
                            ...config.calendarConfig.eventDetails,
                            [key]: checked as boolean
                          }
                        }
                      })}
                    />
                    <Label htmlFor={key} className="text-sm font-normal cursor-pointer">
                      {label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          disabled={config.calendarConfig.calendars.length === 0}
          className="w-full"
        >
          Continue to Email Setup
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderEmailConfig = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <SiGmail className="h-16 w-16 mx-auto mb-4 text-red-600" />
        <h3 className="text-lg font-semibold mb-2">Email Configuration</h3>
        <p className="text-muted-foreground text-sm">
          Filter and prioritize emails for your daily summary
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Email Filters</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="lookback">Look Back Period (hours)</Label>
            <Select
              value={config.emailConfig.filters.timeRange.toString()}
              onValueChange={(value) => setConfig({
                ...config,
                emailConfig: {
                  ...config.emailConfig,
                  filters: {
                    ...config.emailConfig.filters,
                    timeRange: parseInt(value)
                  }
                }
              })}
            >
              <SelectTrigger id="lookback" className="mt-2">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="12">12 hours</SelectItem>
                <SelectItem value="24">24 hours</SelectItem>
                <SelectItem value="48">48 hours</SelectItem>
                <SelectItem value="72">72 hours</SelectItem>
                <SelectItem value="168">1 week</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="unread-only"
              checked={config.emailConfig.filters.unreadOnly}
              onCheckedChange={(checked) => setConfig({
                ...config,
                emailConfig: {
                  ...config.emailConfig,
                  filters: {
                    ...config.emailConfig.filters,
                    unreadOnly: checked as boolean
                  }
                }
              })}
            />
            <Label htmlFor="unread-only" className="text-sm font-normal cursor-pointer">
              Only include unread emails
            </Label>
          </div>

          <div>
            <Label>Gmail Labels</Label>
            <p className="text-xs text-muted-foreground mb-2">Select labels to include</p>
            <div className="space-y-2">
              {mockLabels.map((label) => (
                <div key={label} className="flex items-center space-x-2">
                  <Checkbox
                    id={label}
                    checked={config.emailConfig.filters.labels.includes(label)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setConfig({
                          ...config,
                          emailConfig: {
                            ...config.emailConfig,
                            filters: {
                              ...config.emailConfig.filters,
                              labels: [...config.emailConfig.filters.labels, label]
                            }
                          }
                        })
                      } else {
                        setConfig({
                          ...config,
                          emailConfig: {
                            ...config.emailConfig,
                            filters: {
                              ...config.emailConfig.filters,
                              labels: config.emailConfig.filters.labels.filter(l => l !== label)
                            }
                          }
                        })
                      }
                    }}
                  />
                  <Label htmlFor={label} className="text-sm font-normal cursor-pointer">
                    {label}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div>
            <Label htmlFor="keywords">Priority Keywords (comma-separated)</Label>
            <Input
              id="keywords"
              placeholder="e.g., urgent, important, action required"
              value={config.emailConfig.filters.keywords.join(', ')}
              onChange={(e) => setConfig({
                ...config,
                emailConfig: {
                  ...config.emailConfig,
                  filters: {
                    ...config.emailConfig.filters,
                    keywords: e.target.value.split(',').map(k => k.trim()).filter(Boolean)
                  }
                }
              })}
              className="mt-2"
            />
            <p className="text-xs text-muted-foreground mt-1">
              Emails with these keywords will be prioritized
            </p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Summary Options</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label>Summary Depth</Label>
            <RadioGroup
              value={config.emailConfig.summaryDepth}
              onValueChange={(value) => setConfig({
                ...config,
                emailConfig: {
                  ...config.emailConfig,
                  summaryDepth: value as any
                }
              })}
              className="mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="brief" id="brief" />
                <Label htmlFor="brief" className="font-normal">Brief (Headlines only)</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="detailed" id="detailed" />
                <Label htmlFor="detailed" className="font-normal">Detailed (Key points)</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="comprehensive" id="comprehensive" />
                <Label htmlFor="comprehensive" className="font-normal">Comprehensive (Full summary)</Label>
              </div>
            </RadioGroup>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="attachments"
              checked={config.emailConfig.includeAttachments}
              onCheckedChange={(checked) => setConfig({
                ...config,
                emailConfig: {
                  ...config.emailConfig,
                  includeAttachments: checked as boolean
                }
              })}
            />
            <Label htmlFor="attachments" className="text-sm font-normal cursor-pointer">
              Include attachment information
            </Label>
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          className="w-full"
        >
          Continue to AI Settings
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderAISettings = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Brain className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h3 className="text-lg font-semibold mb-2">AI Configuration</h3>
        <p className="text-muted-foreground text-sm">
          Customize how AI generates your daily summaries
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">AI Provider</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <RadioGroup
            value={config.aiSettings.provider}
            onValueChange={(value) => setConfig({
              ...config,
              aiSettings: { ...config.aiSettings, provider: value as any }
            })}
          >
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <RadioGroupItem value="openai" id="openai" />
              <Label htmlFor="openai" className="flex items-center gap-2 cursor-pointer">
                <SiOpenai className="h-4 w-4" />
                OpenAI
              </Label>
            </div>
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <RadioGroupItem value="anthropic" id="anthropic" />
              <Label htmlFor="anthropic" className="flex items-center gap-2 cursor-pointer">
                <SiAnthropic className="h-4 w-4" />
                Anthropic
              </Label>
            </div>
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <RadioGroupItem value="google" id="google" />
              <Label htmlFor="google" className="flex items-center gap-2 cursor-pointer">
                <SiGoogle className="h-4 w-4" />
                Google AI
              </Label>
            </div>
          </RadioGroup>

          <div>
            <Label>Model Selection</Label>
            <Select
              value={config.aiSettings.model}
              onValueChange={(value) => setConfig({
                ...config,
                aiSettings: { ...config.aiSettings, model: value }
              })}
            >
              <SelectTrigger className="mt-2">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {config.aiSettings.provider === 'openai' && (
                  <>
                    <SelectItem value="gpt-4o">GPT-4o (Recommended)</SelectItem>
                    <SelectItem value="gpt-4">GPT-4</SelectItem>
                    <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                  </>
                )}
                {config.aiSettings.provider === 'anthropic' && (
                  <>
                    <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
                    <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                    <SelectItem value="claude-3-haiku">Claude 3 Haiku</SelectItem>
                  </>
                )}
                {config.aiSettings.provider === 'google' && (
                  <>
                    <SelectItem value="gemini-1.5-pro">Gemini 1.5 Pro</SelectItem>
                    <SelectItem value="gemini-1.0-pro">Gemini 1.0 Pro</SelectItem>
                  </>
                )}
              </SelectContent>
            </Select>
          </div>

          <Button
            className="w-full"
            onClick={() => setConfig({
              ...config,
              aiSettings: { ...config.aiSettings, isConfigured: true }
            })}
          >
            Verify API Configuration
          </Button>

          {config.aiSettings.isConfigured && (
            <div className="flex items-center gap-2 text-green-600">
              <Check className="h-4 w-4" />
              <span className="text-sm">AI integration configured successfully</span>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Summary Style</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label>Summary Style</Label>
            <Select
              value={config.aiSettings.summaryStyle}
              onValueChange={(value) => setConfig({
                ...config,
                aiSettings: { ...config.aiSettings, summaryStyle: value as any }
              })}
            >
              <SelectTrigger className="mt-2">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="professional">Professional</SelectItem>
                <SelectItem value="casual">Casual</SelectItem>
                <SelectItem value="executive">Executive</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label>Tone</Label>
            <Select
              value={config.aiSettings.tone}
              onValueChange={(value) => setConfig({
                ...config,
                aiSettings: { ...config.aiSettings, tone: value as any }
              })}
            >
              <SelectTrigger className="mt-2">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="informative">Informative</SelectItem>
                <SelectItem value="friendly">Friendly</SelectItem>
                <SelectItem value="formal">Formal</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="language">Language</Label>
            <Input
              id="language"
              value={config.aiSettings.language}
              onChange={(e) => setConfig({
                ...config,
                aiSettings: { ...config.aiSettings, language: e.target.value }
              })}
              className="mt-2"
            />
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          disabled={!config.aiSettings.isConfigured}
          className="w-full"
        >
          Continue to Delivery Settings
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderDeliverySettings = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Send className="h-16 w-16 mx-auto mb-4 text-green-600" />
        <h3 className="text-lg font-semibold mb-2">Delivery Settings</h3>
        <p className="text-muted-foreground text-sm">
          Schedule when and how to receive your daily digest
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Schedule</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="delivery-time">Delivery Time</Label>
              <Input
                id="delivery-time"
                type="time"
                value={config.deliverySettings.schedule.time}
                onChange={(e) => setConfig({
                  ...config,
                  deliverySettings: {
                    ...config.deliverySettings,
                    schedule: {
                      ...config.deliverySettings.schedule,
                      time: e.target.value
                    }
                  }
                })}
                className="mt-2"
              />
            </div>
            <div>
              <Label htmlFor="timezone">Timezone</Label>
              <Select
                value={config.deliverySettings.schedule.timezone}
                onValueChange={(value) => setConfig({
                  ...config,
                  deliverySettings: {
                    ...config.deliverySettings,
                    schedule: {
                      ...config.deliverySettings.schedule,
                      timezone: value
                    }
                  }
                })}
              >
                <SelectTrigger id="timezone" className="mt-2">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {timezones.map((tz) => (
                    <SelectItem key={tz} value={tz}>{tz}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label>Delivery Days</Label>
            <p className="text-xs text-muted-foreground mb-2">Select which days to receive your digest</p>
            <div className="grid grid-cols-2 gap-2">
              {weekdays.map((day) => (
                <div key={day} className="flex items-center space-x-2">
                  <Checkbox
                    id={day}
                    checked={config.deliverySettings.schedule.weekdays.includes(day)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setConfig({
                          ...config,
                          deliverySettings: {
                            ...config.deliverySettings,
                            schedule: {
                              ...config.deliverySettings.schedule,
                              weekdays: [...config.deliverySettings.schedule.weekdays, day]
                            }
                          }
                        })
                      } else {
                        setConfig({
                          ...config,
                          deliverySettings: {
                            ...config.deliverySettings,
                            schedule: {
                              ...config.deliverySettings.schedule,
                              weekdays: config.deliverySettings.schedule.weekdays.filter(d => d !== day)
                            }
                          }
                        })
                      }
                    }}
                  />
                  <Label htmlFor={day} className="text-sm font-normal cursor-pointer">
                    {day}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Email Delivery</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="recipient">Recipient Email *</Label>
            <Input
              id="recipient"
              type="email"
              placeholder="<EMAIL>"
              value={config.deliverySettings.email.recipient}
              onChange={(e) => setConfig({
                ...config,
                deliverySettings: {
                  ...config.deliverySettings,
                  email: {
                    ...config.deliverySettings.email,
                    recipient: e.target.value
                  }
                }
              })}
              className="mt-2"
            />
          </div>

          <div>
            <Label htmlFor="subject">Email Subject</Label>
            <Input
              id="subject"
              placeholder="Daily Summary - {date}"
              value={config.deliverySettings.email.subject}
              onChange={(e) => setConfig({
                ...config,
                deliverySettings: {
                  ...config.deliverySettings,
                  email: {
                    ...config.deliverySettings.email,
                    subject: e.target.value
                  }
                }
              })}
              className="mt-2"
            />
            <p className="text-xs text-muted-foreground mt-1">
              Use {'{date}'} to include the current date
            </p>
          </div>

          <div>
            <Label>Email Format</Label>
            <RadioGroup
              value={config.deliverySettings.email.format}
              onValueChange={(value) => setConfig({
                ...config,
                deliverySettings: {
                  ...config.deliverySettings,
                  email: {
                    ...config.deliverySettings.email,
                    format: value as any
                  }
                }
              })}
              className="mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="html" id="html" />
                <Label htmlFor="html" className="font-normal">HTML (Rich formatting)</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="text" id="text" />
                <Label htmlFor="text" className="font-normal">Plain Text</Label>
              </div>
            </RadioGroup>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-base">Backup Delivery (Optional)</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="backup" className="text-sm font-normal cursor-pointer">
              Enable backup delivery
            </Label>
            <Switch
              id="backup"
              checked={config.deliverySettings.backup.enabled}
              onCheckedChange={(checked) => setConfig({
                ...config,
                deliverySettings: {
                  ...config.deliverySettings,
                  backup: {
                    ...config.deliverySettings.backup,
                    enabled: checked
                  }
                }
              })}
            />
          </div>

          {config.deliverySettings.backup.enabled && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="slack">Slack Channel (optional)</Label>
                <Input
                  id="slack"
                  placeholder="#daily-summary"
                  value={config.deliverySettings.backup.slackChannel}
                  onChange={(e) => setConfig({
                    ...config,
                    deliverySettings: {
                      ...config.deliverySettings,
                      backup: {
                        ...config.deliverySettings.backup,
                        slackChannel: e.target.value
                      }
                    }
                  })}
                  className="mt-2"
                />
              </div>

              <div>
                <Label htmlFor="webhook">Webhook URL (optional)</Label>
                <Input
                  id="webhook"
                  placeholder="https://your-webhook-endpoint.com"
                  value={config.deliverySettings.backup.webhookUrl}
                  onChange={(e) => setConfig({
                    ...config,
                    deliverySettings: {
                      ...config.deliverySettings,
                      backup: {
                        ...config.deliverySettings.backup,
                        webhookUrl: e.target.value
                      }
                    }
                  })}
                  className="mt-2"
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          disabled={!config.deliverySettings.email.recipient || config.deliverySettings.schedule.weekdays.length === 0}
          className="w-full"
        >
          Continue
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderActionsQuestion = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Plus className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h3 className="text-lg font-semibold mb-2">Do you want to add any actions?</h3>
        <p className="text-muted-foreground text-sm">
          Actions let you extend your automation with additional capabilities
        </p>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <Button
          variant={config.wantsActions === true ? "default" : "outline"}
          size="lg"
          onClick={() => {
            setConfig({ ...config, wantsActions: true })
            nextStep()
          }}
          className="h-auto py-6"
        >
          <div className="flex flex-col items-center gap-2">
            <Check className="h-8 w-8" />
            <span>Yes, I want to add actions</span>
          </div>
        </Button>
        <Button
          variant={config.wantsActions === false ? "default" : "outline"}
          size="lg"
          onClick={() => {
            setConfig({ ...config, wantsActions: false })
            setCurrentStep('review')  // Skip directly to review
          }}
          className="h-auto py-6"
        >
          <div className="flex flex-col items-center gap-2">
            <ArrowRight className="h-8 w-8" />
            <span>No, continue without</span>
          </div>
        </Button>
      </div>

      <div>
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
      </div>
    </motion.div>
  )

  const renderActions = () => {
    const selectedActionsList = config.selectedActionsList || []
    const [wantsMoreActions, setWantsMoreActions] = useState(false)
    const [actionSearchQuery, setActionSearchQuery] = useState('')

    const availableActionsList = [
      { id: 'discord-read', name: 'Discord Read', icon: MessageSquare, desc: 'Read messages from any Discord channel' },
      { id: 'discord-send', name: 'Discord Send', icon: MessageSquare, desc: 'Send messages to any Discord channel' },
      { id: 'gmail-read', name: 'Gmail Read', icon: Mail, desc: 'Read emails from your Gmail account' },
      { id: 'gmail-send', name: 'Gmail Send', icon: Mail, desc: 'Send emails via your Gmail account' },
      { id: 'gcalendar-read', name: 'Google Calendar Read', icon: Calendar, desc: 'Read events from any Google Calendar' },
      { id: 'gcalendar-write', name: 'Google Calendar Write', icon: Calendar, desc: 'Create events on any Google Calendar' },
      { id: 'http-request', name: 'HTTP Request', icon: Globe, desc: 'Make GET, POST, PUT, or DELETE HTTP requests' },
      { id: 'slack-read', name: 'Slack Read', icon: Hash, desc: 'Read messages from a specified Slack channel' },
      { id: 'slack-send', name: 'Slack Send', icon: Hash, desc: 'Send a message to a specified Slack channel' }
    ]

    // Filter actions based on search query
    const filteredActions = availableActionsList.filter(action => {
      const query = actionSearchQuery.toLowerCase()
      return (
        action.name.toLowerCase().includes(query) ||
        action.desc.toLowerCase().includes(query)
      )
    })

    const addAction = (action: any) => {
      const newAction = {
        id: Date.now().toString(),
        type: action.id,
        name: action.name,
        description: action.desc,
        config: {}
      }
      const updatedList = [...selectedActionsList, newAction]
      setConfig({ ...config, selectedActionsList: updatedList })
      setShowActionConfig(updatedList.length - 1)
      setWantsMoreActions(false)
    }

    const removeAction = (index: number) => {
      const updatedList = selectedActionsList.filter((_, i) => i !== index)
      setConfig({ ...config, selectedActionsList: updatedList })
    }

    // If no actions yet or wants more actions, show action selection
    if (selectedActionsList.length === 0 || wantsMoreActions) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          <div className="text-center">
            <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
            <h3 className="text-lg font-semibold mb-2">
              {selectedActionsList.length === 0 ? 'What should happen next?' : 'Add another action?'}
            </h3>
            <p className="text-muted-foreground text-sm">
              {selectedActionsList.length === 0 
                ? 'Choose an action to perform when your trigger fires'
                : 'Chain multiple actions to create powerful workflows'}
            </p>
          </div>

          {/* Search */}
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search actions..."
                value={actionSearchQuery}
                onChange={(e) => setActionSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Available Actions */}
          {filteredActions.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No actions found matching "{actionSearchQuery}"</p>
            </div>
          ) : (
            <div className="grid gap-3 max-w-2xl mx-auto">
              {filteredActions.map((action) => (
                <Card
                  key={action.id}
                  className="cursor-pointer transition-all hover:shadow-md"
                  onClick={() => addAction(action)}
                >
                  <CardHeader className="p-4">
                    <div className="flex items-center gap-3">
                      <action.icon className="h-5 w-5 text-primary" />
                      <div className="flex-1">
                        <CardTitle className="text-sm">{action.name}</CardTitle>
                        <CardDescription className="text-xs mt-1">{action.desc}</CardDescription>
                      </div>
                      <Plus className="h-5 w-5 text-muted-foreground" />
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          )}

          <div className="flex gap-3 max-w-2xl mx-auto">
            <Button onClick={prevStep} variant="outline" className="w-full">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Previous
            </Button>
            {selectedActionsList.length > 0 && (
              <Button 
                onClick={() => setWantsMoreActions(false)} 
                variant="outline"
                className="w-full"
              >
                Continue without adding
              </Button>
            )}
          </div>
        </motion.div>
      )
    }

    // Show current actions with option to add more
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <div className="text-center">
          <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
          <h3 className="text-lg font-semibold mb-2">Your Action Chain</h3>
          <p className="text-muted-foreground text-sm">
            {selectedActionsList.length} action{selectedActionsList.length !== 1 ? 's' : ''} configured
          </p>
        </div>

        {/* Action Chain Visualization */}
        <div className="max-w-2xl mx-auto space-y-3">
          {/* Trigger */}
          <div className="flex items-center gap-3 p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg border-2 border-purple-200 dark:border-purple-800">
            <Clock className="h-5 w-5 text-purple-600" />
            <div className="flex-1">
              <p className="font-medium">Trigger: Daily Digest</p>
              <p className="text-xs text-muted-foreground">Every day at {config.deliverySettings.schedule.time}</p>
            </div>
          </div>

          {/* Arrow */}
          <div className="flex justify-center">
            <ArrowRight className="h-4 w-4 text-muted-foreground" />
          </div>

          {/* Actions */}
          {selectedActionsList.map((action, index) => (
            <div key={action.id}>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center gap-3 p-4 bg-muted/30 rounded-lg border"
              >
                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-xs font-medium">
                  {index + 1}
                </div>
                <div className="flex-1">
                  <p className="font-medium">{action.name}</p>
                  <p className="text-xs text-muted-foreground">{action.description}</p>
                </div>
                <div className="flex gap-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setShowActionConfig(index)}
                    className="h-8 px-2"
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => removeAction(index)}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </motion.div>
              {index < selectedActionsList.length - 1 && (
                <div className="flex justify-center my-2">
                  <ArrowRight className="h-4 w-4 text-muted-foreground" />
                </div>
              )}
            </div>
          ))}

          {/* Add Another Action Button */}
          <Button
            variant="outline"
            onClick={() => setWantsMoreActions(true)}
            className="w-full"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Another Action
          </Button>
        </div>

        <div className="flex gap-3 max-w-2xl mx-auto">
          <Button onClick={prevStep} variant="outline" className="w-full">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button 
            onClick={nextStep} 
            className="w-full"
            disabled={selectedActionsList.length === 0}
          >
            Continue <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>

        {/* Action Configuration Modal */}
        {showActionConfig !== null && selectedActionsList[showActionConfig] && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowActionConfig(null)}
          >
            <motion.div
              initial={{ scale: 0.95 }}
              animate={{ scale: 1 }}
              className="bg-card p-6 rounded-lg shadow-lg max-w-md w-full max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">
                Configure {selectedActionsList[showActionConfig].name}
              </h3>
              
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Configuration options will appear here based on the action type.
                </p>
                <div className="flex justify-end gap-2 mt-6">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowActionConfig(null)}
                  >
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => setShowActionConfig(null)}
                  >
                    Save Configuration
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </motion.div>
    )
  }

  const renderReview = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Eye className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h3 className="text-lg font-semibold mb-2">Review Your Configuration</h3>
        <p className="text-muted-foreground text-sm">
          Make sure everything looks correct before creating your automation
        </p>
      </div>

      <div className="space-y-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Automation Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Name</span>
              <span className="text-sm font-medium">{config.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Google Account</span>
              <span className="text-sm font-medium">{config.googleAuth.userEmail}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Calendar Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Calendars</span>
              <span className="text-sm font-medium">{config.calendarConfig.calendars.length} selected</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Time Range</span>
              <span className="text-sm font-medium">{config.calendarConfig.timeRange.start} - {config.calendarConfig.timeRange.end}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Email Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Look Back</span>
              <span className="text-sm font-medium">{config.emailConfig.filters.timeRange} hours</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Summary Depth</span>
              <span className="text-sm font-medium capitalize">{config.emailConfig.summaryDepth}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">AI Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Provider</span>
              <span className="text-sm font-medium capitalize">{config.aiSettings.provider}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Model</span>
              <span className="text-sm font-medium">{config.aiSettings.model}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Style</span>
              <span className="text-sm font-medium capitalize">{config.aiSettings.summaryStyle}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Delivery Schedule</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Time</span>
              <span className="text-sm font-medium">{config.deliverySettings.schedule.time}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Days</span>
              <span className="text-sm font-medium">{config.deliverySettings.schedule.weekdays.length} days/week</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Recipient</span>
              <span className="text-sm font-medium">{config.deliverySettings.email.recipient}</span>
            </div>
          </CardContent>
        </Card>

        {config.selectedActionsList.length > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Actions ({config.selectedActionsList.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {config.selectedActionsList.map((action) => (
                  <div key={action.id} className="text-sm">
                    • {action.name}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <div className="flex gap-3">
        <Button 
          onClick={prevStep}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700"
        >
          Create Automation
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderComplete = () => (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto">
        <Check className="h-10 w-10 text-green-600 dark:text-green-400" />
      </div>
      <div>
        <h2 className="text-3xl font-bold mb-2">Automation Created!</h2>
        <p className="text-muted-foreground text-lg">
          Your "{config.name}" automation is now ready and will start delivering your daily digest at {config.deliverySettings.schedule.time}.
        </p>
      </div>
      
      <div className="flex flex-col sm:flex-row gap-3 justify-center mt-8">
        <Button 
          onClick={() => setLocation('/dashboard/automations')}
          size="lg"
          className="bg-primary hover:bg-primary/90"
        >
          View All Automations
        </Button>
        <Button 
          onClick={() => setLocation('/dashboard/browse-templates')}
          variant="outline"
          size="lg"
        >
          Browse More Templates
        </Button>
      </div>
    </motion.div>
  )

  const renderStepContent = () => {
    switch (currentStep) {
      case 'intro': return renderIntro()
      case 'naming': return renderNaming()
      case 'google-auth': return renderGoogleAuth()
      case 'calendar-config': return renderCalendarConfig()
      case 'email-config': return renderEmailConfig()
      case 'ai-settings': return renderAISettings()
      case 'delivery-settings': return renderDeliverySettings()
      case 'actions-question': return renderActionsQuestion()
      case 'actions': return renderActions()
      case 'review': return renderReview()
      case 'complete': return renderComplete()
      default: return null
    }
  }

  const CurrentIcon = stepConfig[currentStep].icon

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => setLocation('/dashboard/browse-templates')}
          className="mb-4"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back to Templates
        </Button>

        {currentStep !== 'complete' && (
          <>
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <CurrentIcon className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">{stepConfig[currentStep].title}</h1>
                <p className="text-muted-foreground text-sm">{stepConfig[currentStep].subtitle}</p>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex justify-between text-xs text-muted-foreground mb-2">
                <span>Step {Object.keys(stepConfig).indexOf(currentStep) + 1} of 10</span>
                <span>{stepConfig[currentStep].progress}% Complete</span>
              </div>
              <Progress value={stepConfig[currentStep].progress} className="h-2" />
            </div>
          </>
        )}
      </div>

      {/* Content */}
      <Card className="p-6">
        {renderStepContent()}
      </Card>
    </div>
  )
}