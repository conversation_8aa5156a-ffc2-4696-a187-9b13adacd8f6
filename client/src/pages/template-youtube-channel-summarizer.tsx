import { useState, Fragment } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Progress } from '@/components/ui/progress'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Textarea } from '@/components/ui/textarea'
import { ArrowRight, ChevronLeft, Settings, Zap, Check, Youtube, Mail, Plus, X, Key, Search, MessageSquare, Calendar, Globe, Hash } from 'lucide-react'
import { useLocation } from 'wouter'
import { motion } from 'framer-motion'
import { SiOpenai, SiGmail } from 'react-icons/si'
import { ActionsSelector } from '@/components/actions-selector'
import { CustomActionsEditor } from '@/components/custom-actions-editor'
import type { CustomAction } from '@/components/custom-actions-editor'
import { EmojiCelebration } from '@/components/emoji-celebration'

type StepType = 'intro' | 'naming' | 'youtube-config' | 'apify-setup' | 'ai-settings' | 'email-config' | 'actions-question' | 'actions' | 'review' | 'complete'

interface WorkflowConfig {
  name: string
  youtubeConfig: {
    channelId: string
    channelName: string
    videosToFetch: number
    searchQuery: string
    sortBy: 'newest' | 'oldest' | 'popular'
    includeShorts: boolean
  }
  apifySetup: {
    apiKey: string
    isAuthenticated: boolean
    actorId: string
  }
  aiSettings: {
    provider: 'openai' | 'anthropic' | 'google'
    model: string
    summaryLength: 'brief' | 'detailed' | 'comprehensive'
    summaryStyle: 'bullet-points' | 'paragraph' | 'technical' | 'simple'
    includeKeypoints: boolean
    includeTimestamps: boolean
    customInstructions: string
  }
  emailConfig: {
    recipient: string
    ccRecipients: string
    subject: string
    sendFrequency: 'immediate' | 'daily' | 'weekly' | 'monthly'
    sendTime: string
    emailFormat: 'html' | 'plain'
    includeVideoThumbnails: boolean
  }
  selectedActionsList: Array<{
    id: string
    type: string
    name: string
    description: string
    config: Record<string, any>
  }>
  wantsActions: boolean | null
}

export default function YouTubeChannelSummarizerTemplate() {
  const [, setLocation] = useLocation()
  const [currentStep, setCurrentStep] = useState<StepType>('intro')
  const [workflow, setWorkflow] = useState<WorkflowConfig>({
    name: '',
    youtubeConfig: {
      channelId: '',
      channelName: '',
      videosToFetch: 10,
      searchQuery: '',
      sortBy: 'newest',
      includeShorts: false
    },
    apifySetup: {
      apiKey: '',
      isAuthenticated: false,
      actorId: 'youtube-channel-scraper'
    },
    aiSettings: {
      provider: 'openai',
      model: 'gpt-4o',
      summaryLength: 'detailed',
      summaryStyle: 'bullet-points',
      includeKeypoints: true,
      includeTimestamps: true,
      customInstructions: ''
    },
    emailConfig: {
      recipient: '',
      ccRecipients: '',
      subject: 'YouTube Channel Summary - {channel_name}',
      sendFrequency: 'weekly',
      sendTime: '09:00',
      emailFormat: 'html',
      includeVideoThumbnails: true
    },
    selectedActionsList: [],
    wantsActions: null
  })

  const [selectedActions, setSelectedActions] = useState<string[]>([])
  const [wantsMoreActions, setWantsMoreActions] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [actionConfigOpen, setActionConfigOpen] = useState<string | null>(null)
  const [tempActionConfig, setTempActionConfig] = useState<Record<string, any>>({})
  const [showActionConfig, setShowActionConfig] = useState<number | null>(null)
  const [showCelebration, setShowCelebration] = useState(false)
  
  const [customActions, setCustomActions] = useState<CustomAction[]>([])
  const [customActionEditId, setCustomActionEditId] = useState<string | null>(null)

  const steps: { id: StepType; title: string; icon: React.ComponentType<{ className?: string }> }[] = [
    { id: 'intro', title: 'Introduction', icon: Youtube },
    { id: 'naming', title: 'Name Your Automation', icon: Settings },
    { id: 'youtube-config', title: 'YouTube Channel Setup', icon: Youtube },
    { id: 'apify-setup', title: 'Apify Configuration', icon: Key },
    { id: 'ai-settings', title: 'AI Summary Settings', icon: SiOpenai },
    { id: 'email-config', title: 'Email Configuration', icon: Mail },
    { id: 'actions-question', title: 'Additional Actions', icon: Zap },
    { id: 'actions', title: 'Configure Actions', icon: Zap },
    { id: 'review', title: 'Review', icon: Settings },
    { id: 'complete', title: 'Complete', icon: Check }
  ]

  const nextStep = () => {
    const currentIndex = steps.findIndex(s => s.id === currentStep)
    if (currentIndex < steps.length - 1) {
      const nextStepId = steps[currentIndex + 1].id
      
      // Skip actions step if user doesn't want actions
      if (nextStepId === 'actions' && workflow.wantsActions === false) {
        setCurrentStep(steps[currentIndex + 2].id)
      } else if (currentStep === 'review') {
        setShowCelebration(true)
        setCurrentStep(nextStepId)
      } else {
        setCurrentStep(nextStepId)
      }
    }
  }

  const previousStep = () => {
    const currentIndex = steps.findIndex(s => s.id === currentStep)
    if (currentIndex > 0) {
      const prevStepId = steps[currentIndex - 1].id
      
      // Skip actions step if user doesn't want actions
      if (prevStepId === 'actions' && workflow.wantsActions === false) {
        setCurrentStep(steps[currentIndex - 2].id)
      } else {
        setCurrentStep(prevStepId)
      }
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent, action: () => void) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      action()
    }
  }

  const handleApifyAuth = () => {
    // Simulate Apify authentication
    setWorkflow(prev => ({
      ...prev,
      apifySetup: {
        ...prev.apifySetup,
        isAuthenticated: true
      }
    }))
  }

  const handleActionsChange = (actions: string[]) => {
    setSelectedActions(actions)
  }

  const handleActionConfig = (actionId: string) => {
    setActionConfigOpen(actionId)
    const existingAction = workflow.selectedActionsList.find(a => a.id === actionId)
    if (existingAction) {
      setTempActionConfig(existingAction.config)
    } else {
      // Initialize default config based on action type
      const defaultConfig: Record<string, any> = {}
      if (actionId === 'send_email') {
        defaultConfig.recipient = ''
        defaultConfig.subject = 'YouTube Summary Report'
      } else if (actionId === 'webhook') {
        defaultConfig.url = ''
        defaultConfig.method = 'POST'
      } else if (actionId === 'google_sheets') {
        defaultConfig.spreadsheetId = ''
        defaultConfig.range = 'A1'
      } else if (actionId === 'slack') {
        defaultConfig.channel = ''
        defaultConfig.message = ''
      }
      setTempActionConfig(defaultConfig)
    }
  }

  const handleActionConfigSave = (actionId: string) => {
    const actionDetails = {
      id: `${actionId}_${Date.now()}`,
      type: actionId,
      name: getActionName(actionId),
      description: getActionDescription(actionId),
      config: { ...tempActionConfig }
    }

    setWorkflow(prev => ({
      ...prev,
      selectedActionsList: [...prev.selectedActionsList, actionDetails]
    }))

    setActionConfigOpen(null)
    setTempActionConfig({})
    
    // Remove from selected actions
    setSelectedActions(prev => prev.filter(id => id !== actionId))
  }

  const handleRemoveAction = (actionId: string) => {
    setWorkflow(prev => ({
      ...prev,
      selectedActionsList: prev.selectedActionsList.filter(a => a.id !== actionId)
    }))
  }

  const getActionName = (actionId: string): string => {
    const names: Record<string, string> = {
      send_email: 'Send Email',
      webhook: 'Webhook',
      google_sheets: 'Google Sheets',
      slack: 'Slack Notification',
      google_drive: 'Google Drive',
      notion: 'Notion',
      discord: 'Discord',
      generate_pdf: 'Generate PDF',
      sms: 'SMS Notification',
      database: 'Database',
      api_call: 'API Call',
      ai_model: 'AI Model'
    }
    return names[actionId] || actionId
  }

  const getActionDescription = (actionId: string): string => {
    const descriptions: Record<string, string> = {
      send_email: 'Send summary reports via email',
      webhook: 'Send data to external services',
      google_sheets: 'Log summaries to spreadsheet',
      slack: 'Send notifications to Slack',
      google_drive: 'Save summaries to Drive',
      notion: 'Create pages in Notion',
      discord: 'Send updates to Discord',
      generate_pdf: 'Generate PDF reports',
      sms: 'Send SMS alerts',
      database: 'Store data in database',
      api_call: 'Make custom API calls',
      ai_model: 'Process with AI models'
    }
    return descriptions[actionId] || ''
  }

  const handleCustomActionsChange = (actions: CustomAction[]) => {
    setCustomActions(actions)
    setWorkflow(prev => ({
      ...prev,
      selectedActionsList: actions
    }))
  }

  const renderIntroStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center mx-auto">
        <Youtube className="h-10 w-10 text-white" />
      </div>
      
      <div>
        <h2 className="text-2xl font-bold mb-2">YouTube Channel Video Summarizer</h2>
        <p className="text-muted-foreground max-w-md mx-auto">
          Automatically find videos from any YouTube channel, generate AI-powered summaries, 
          and receive organized email reports with key insights and timestamps.
        </p>
      </div>
      
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Youtube className="h-8 w-8 mx-auto mb-2 text-red-600" />
          <p className="text-sm">YouTube</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Key className="h-8 w-8 mx-auto mb-2 text-green-600" />
          <p className="text-sm">Apify</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <SiOpenai className="h-8 w-8 mx-auto mb-2 text-emerald-600" />
          <p className="text-sm">OpenAI</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Mail className="h-8 w-8 mx-auto mb-2 text-blue-600" />
          <p className="text-sm">Email</p>
        </div>
      </div>

      <Button
        onClick={nextStep}
        className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
        size="lg"
      >
        Start Setup
      </Button>
    </motion.div>
  )

  const renderNamingStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Settings className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h2 className="text-xl font-semibold mb-2">Name Your Automation</h2>
        <p className="text-muted-foreground">
          Give your automation a memorable name that describes what it does
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="automation-name">Automation Name *</Label>
          <Input
            id="automation-name"
            placeholder="e.g., Tech Channel Weekly Summary"
            value={workflow.name}
            onChange={(e) => setWorkflow(prev => ({ ...prev, name: e.target.value }))}
            onKeyDown={(e) => handleKeyPress(e, () => workflow.name.trim() && nextStep())}
            className="mt-2"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Choose a descriptive name you'll easily recognize
          </p>
        </div>
      </div>

      <div className="flex gap-3">
        <Button onClick={previousStep} variant="outline" className="w-full min-h-[44px]">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          disabled={!workflow.name.trim()}
          className="w-full min-h-[44px]"
        >
          Continue to YouTube Setup
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderYouTubeConfig = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="space-y-4">
        <div>
          <Label htmlFor="channel-id">YouTube Channel ID *</Label>
          <Input
            id="channel-id"
            placeholder="e.g., UC_x5XG1OV2P6uZZ5FSM9Ttw"
            value={workflow.youtubeConfig.channelId}
            onChange={(e) => setWorkflow(prev => ({
              ...prev,
              youtubeConfig: { ...prev.youtubeConfig, channelId: e.target.value }
            }))}
            className="mt-1"
          />
          <p className="text-sm text-muted-foreground mt-1">
            Find this in the channel URL or about page
          </p>
        </div>

        <div>
          <Label htmlFor="channel-name">Channel Name</Label>
          <Input
            id="channel-name"
            placeholder="e.g., Google Developers"
            value={workflow.youtubeConfig.channelName}
            onChange={(e) => setWorkflow(prev => ({
              ...prev,
              youtubeConfig: { ...prev.youtubeConfig, channelName: e.target.value }
            }))}
            className="mt-1"
          />
        </div>

        <div>
          <Label htmlFor="videos-count">Number of Videos to Fetch</Label>
          <Select
            value={workflow.youtubeConfig.videosToFetch.toString()}
            onValueChange={(value) => setWorkflow(prev => ({
              ...prev,
              youtubeConfig: { ...prev.youtubeConfig, videosToFetch: parseInt(value) }
            }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="5">5 videos</SelectItem>
              <SelectItem value="10">10 videos</SelectItem>
              <SelectItem value="20">20 videos</SelectItem>
              <SelectItem value="50">50 videos</SelectItem>
              <SelectItem value="100">100 videos</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="sort-by">Sort Videos By</Label>
          <Select
            value={workflow.youtubeConfig.sortBy}
            onValueChange={(value: 'newest' | 'oldest' | 'popular') => setWorkflow(prev => ({
              ...prev,
              youtubeConfig: { ...prev.youtubeConfig, sortBy: value }
            }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newest">Newest First</SelectItem>
              <SelectItem value="oldest">Oldest First</SelectItem>
              <SelectItem value="popular">Most Popular</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="search-query">Search Query (Optional)</Label>
          <Input
            id="search-query"
            placeholder="e.g., tutorial, review"
            value={workflow.youtubeConfig.searchQuery}
            onChange={(e) => setWorkflow(prev => ({
              ...prev,
              youtubeConfig: { ...prev.youtubeConfig, searchQuery: e.target.value }
            }))}
            className="mt-1"
          />
          <p className="text-sm text-muted-foreground mt-1">
            Filter videos by keywords in title or description
          </p>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="include-shorts"
            checked={workflow.youtubeConfig.includeShorts}
            onCheckedChange={(checked) => setWorkflow(prev => ({
              ...prev,
              youtubeConfig: { ...prev.youtubeConfig, includeShorts: checked as boolean }
            }))}
          />
          <Label htmlFor="include-shorts">Include YouTube Shorts</Label>
        </div>
      </div>

      <div className="flex gap-3">
        <Button onClick={previousStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          disabled={!workflow.youtubeConfig.channelId}
          className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
        >
          Continue <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderApifySetup = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="space-y-4">
        <div>
          <Label htmlFor="apify-key">Apify API Key *</Label>
          <Input
            id="apify-key"
            type="password"
            placeholder="Enter your Apify API key"
            value={workflow.apifySetup.apiKey}
            onChange={(e) => setWorkflow(prev => ({
              ...prev,
              apifySetup: { ...prev.apifySetup, apiKey: e.target.value }
            }))}
            className="mt-1"
          />
          <p className="text-sm text-muted-foreground mt-1">
            Get your API key from <a href="https://console.apify.com/account/integrations" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">Apify Console</a>
          </p>
        </div>

        {workflow.apifySetup.apiKey && (
          <div className="space-y-4">
            {!workflow.apifySetup.isAuthenticated ? (
              <Button onClick={handleApifyAuth} className="w-full" variant="outline">
                <Key className="mr-2 h-4 w-4" />
                Authenticate with Apify
              </Button>
            ) : (
              <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                    <Key className="h-5 w-5" />
                  </div>
                  <div>
                    <p className="font-medium">Apify Connected</p>
                    <p className="text-sm text-muted-foreground">YouTube Channel Scraper Actor Ready</p>
                  </div>
                </div>
                <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Active</span>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="flex gap-3">
        <Button onClick={previousStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          disabled={!workflow.apifySetup.apiKey || !workflow.apifySetup.isAuthenticated}
          className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
        >
          Continue <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderAISettings = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="space-y-4">
        <div>
          <Label>AI Provider</Label>
          <Select
            value={workflow.aiSettings.provider}
            onValueChange={(value: 'openai' | 'anthropic' | 'google') => setWorkflow(prev => ({
              ...prev,
              aiSettings: { ...prev.aiSettings, provider: value }
            }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="openai">OpenAI</SelectItem>
              <SelectItem value="anthropic">Anthropic</SelectItem>
              <SelectItem value="google">Google AI</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label>AI Model</Label>
          <Select
            value={workflow.aiSettings.model}
            onValueChange={(value) => setWorkflow(prev => ({
              ...prev,
              aiSettings: { ...prev.aiSettings, model: value }
            }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {workflow.aiSettings.provider === 'openai' && (
                <>
                  <SelectItem value="gpt-4o">GPT-4o (Latest)</SelectItem>
                  <SelectItem value="gpt-4">GPT-4</SelectItem>
                  <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                </>
              )}
              {workflow.aiSettings.provider === 'anthropic' && (
                <>
                  <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
                  <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                  <SelectItem value="claude-3-haiku">Claude 3 Haiku</SelectItem>
                </>
              )}
              {workflow.aiSettings.provider === 'google' && (
                <>
                  <SelectItem value="gemini-1.5-pro">Gemini 1.5 Pro</SelectItem>
                  <SelectItem value="gemini-1.0-pro">Gemini 1.0 Pro</SelectItem>
                </>
              )}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label>Summary Length</Label>
          <Select
            value={workflow.aiSettings.summaryLength}
            onValueChange={(value: 'brief' | 'detailed' | 'comprehensive') => setWorkflow(prev => ({
              ...prev,
              aiSettings: { ...prev.aiSettings, summaryLength: value }
            }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="brief">Brief (2-3 sentences)</SelectItem>
              <SelectItem value="detailed">Detailed (1-2 paragraphs)</SelectItem>
              <SelectItem value="comprehensive">Comprehensive (Full analysis)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label>Summary Style</Label>
          <Select
            value={workflow.aiSettings.summaryStyle}
            onValueChange={(value: 'bullet-points' | 'paragraph' | 'technical' | 'simple') => setWorkflow(prev => ({
              ...prev,
              aiSettings: { ...prev.aiSettings, summaryStyle: value }
            }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="bullet-points">Bullet Points</SelectItem>
              <SelectItem value="paragraph">Paragraph Format</SelectItem>
              <SelectItem value="technical">Technical Analysis</SelectItem>
              <SelectItem value="simple">Simple Language</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="include-keypoints"
              checked={workflow.aiSettings.includeKeypoints}
              onCheckedChange={(checked) => setWorkflow(prev => ({
                ...prev,
                aiSettings: { ...prev.aiSettings, includeKeypoints: checked as boolean }
              }))}
            />
            <Label htmlFor="include-keypoints">Include Key Points & Takeaways</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="include-timestamps"
              checked={workflow.aiSettings.includeTimestamps}
              onCheckedChange={(checked) => setWorkflow(prev => ({
                ...prev,
                aiSettings: { ...prev.aiSettings, includeTimestamps: checked as boolean }
              }))}
            />
            <Label htmlFor="include-timestamps">Include Important Timestamps</Label>
          </div>
        </div>

        <div>
          <Label htmlFor="custom-instructions">Custom Instructions (Optional)</Label>
          <Textarea
            id="custom-instructions"
            placeholder="e.g., Focus on technical details, extract code examples, highlight best practices..."
            value={workflow.aiSettings.customInstructions}
            onChange={(e) => setWorkflow(prev => ({
              ...prev,
              aiSettings: { ...prev.aiSettings, customInstructions: e.target.value }
            }))}
            className="mt-1"
            rows={3}
          />
        </div>
      </div>

      <div className="flex gap-3">
        <Button onClick={previousStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep}
          className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
        >
          Continue <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderEmailConfig = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="space-y-4">
        <div>
          <Label htmlFor="recipient">Email Recipient *</Label>
          <Input
            id="recipient"
            type="email"
            placeholder="<EMAIL>"
            value={workflow.emailConfig.recipient}
            onChange={(e) => setWorkflow(prev => ({
              ...prev,
              emailConfig: { ...prev.emailConfig, recipient: e.target.value }
            }))}
            className="mt-1"
          />
        </div>

        <div>
          <Label htmlFor="cc-recipients">CC Recipients (Optional)</Label>
          <Input
            id="cc-recipients"
            placeholder="<EMAIL>, <EMAIL>"
            value={workflow.emailConfig.ccRecipients}
            onChange={(e) => setWorkflow(prev => ({
              ...prev,
              emailConfig: { ...prev.emailConfig, ccRecipients: e.target.value }
            }))}
            className="mt-1"
          />
          <p className="text-sm text-muted-foreground mt-1">
            Separate multiple emails with commas
          </p>
        </div>

        <div>
          <Label htmlFor="subject">Email Subject</Label>
          <Input
            id="subject"
            placeholder="YouTube Summary - {channel_name}"
            value={workflow.emailConfig.subject}
            onChange={(e) => setWorkflow(prev => ({
              ...prev,
              emailConfig: { ...prev.emailConfig, subject: e.target.value }
            }))}
            className="mt-1"
          />
          <p className="text-sm text-muted-foreground mt-1">
            Use {'{channel_name}'} or {'{date}'} as placeholders
          </p>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label>Send Frequency</Label>
            <Select
              value={workflow.emailConfig.sendFrequency}
              onValueChange={(value: 'immediate' | 'daily' | 'weekly' | 'monthly') => setWorkflow(prev => ({
                ...prev,
                emailConfig: { ...prev.emailConfig, sendFrequency: value }
              }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="immediate">Immediate</SelectItem>
                <SelectItem value="daily">Daily</SelectItem>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="send-time">Send Time</Label>
            <Input
              id="send-time"
              type="time"
              value={workflow.emailConfig.sendTime}
              onChange={(e) => setWorkflow(prev => ({
                ...prev,
                emailConfig: { ...prev.emailConfig, sendTime: e.target.value }
              }))}
              className="mt-1"
            />
          </div>
        </div>

        <div>
          <Label>Email Format</Label>
          <Select
            value={workflow.emailConfig.emailFormat}
            onValueChange={(value: 'html' | 'plain') => setWorkflow(prev => ({
              ...prev,
              emailConfig: { ...prev.emailConfig, emailFormat: value }
            }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="html">HTML (Rich formatting)</SelectItem>
              <SelectItem value="plain">Plain Text</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="include-thumbnails"
            checked={workflow.emailConfig.includeVideoThumbnails}
            onCheckedChange={(checked) => setWorkflow(prev => ({
              ...prev,
              emailConfig: { ...prev.emailConfig, includeVideoThumbnails: checked as boolean }
            }))}
          />
          <Label htmlFor="include-thumbnails">Include video thumbnails in email</Label>
        </div>
      </div>

      <div className="flex gap-3">
        <Button onClick={previousStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          disabled={!workflow.emailConfig.recipient}
          className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
        >
          Continue <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderActionsQuestion = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Zap className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h3 className="text-lg font-semibold mb-2">Do you want to add any tools or actions?</h3>
        <p className="text-muted-foreground text-sm">
          Actions let you send data, trigger notifications, and integrate with other services
        </p>
      </div>
      
      <div className="flex gap-3 max-w-sm mx-auto">
        <Button 
          onClick={() => {
            setWorkflow(prev => ({ ...prev, wantsActions: true }))
            nextStep()
          }}
          className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
        >
          Yes, add actions
        </Button>
        <Button 
          variant="outline"
          onClick={() => {
            setWorkflow(prev => ({ ...prev, wantsActions: false }))
            nextStep()
          }}
          className="w-full"
        >
          No, continue
        </Button>
      </div>

      <div className="flex gap-3">
        <Button onClick={previousStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
      </div>
    </motion.div>
  )

  const renderActionsStep = () => {
    const selectedActionsList = workflow.selectedActionsList || []

    const availableActionsList = [
      { id: 'discord-read', name: 'Discord Read', icon: MessageSquare, desc: 'Read messages from any Discord channel' },
      { id: 'discord-send', name: 'Discord Send', icon: MessageSquare, desc: 'Send messages to any Discord channel' },
      { id: 'gmail-read', name: 'Gmail Read', icon: Mail, desc: 'Read emails from your Gmail account' },
      { id: 'gmail-send', name: 'Gmail Send', icon: Mail, desc: 'Send emails via your Gmail account' },
      { id: 'gcalendar-read', name: 'Google Calendar Read', icon: Calendar, desc: 'Read events from any Google Calendar' },
      { id: 'gcalendar-write', name: 'Google Calendar Write', icon: Calendar, desc: 'Create events on any Google Calendar' },
      { id: 'http-request', name: 'HTTP Request', icon: Globe, desc: 'Make GET, POST, PUT, or DELETE HTTP requests' },
      { id: 'slack-read', name: 'Slack Read', icon: Hash, desc: 'Read messages from a specified Slack channel' },
      { id: 'slack-send', name: 'Slack Send', icon: Hash, desc: 'Send a message to a specified Slack channel' }
    ]

    // Filter actions based on search query
    const filteredActions = availableActionsList.filter(action => {
      const query = searchQuery.toLowerCase()
      return (
        action.name.toLowerCase().includes(query) ||
        action.desc.toLowerCase().includes(query)
      )
    })

    const addAction = (action: any) => {
      const newAction = {
        id: Date.now().toString(),
        type: action.id,
        name: action.name,
        description: action.desc,
        config: {}
      }
      const updatedList = [...selectedActionsList, newAction]
      setWorkflow({ ...workflow, selectedActionsList: updatedList })
      setShowActionConfig(updatedList.length - 1)
      setWantsMoreActions(false)
    }

    const removeAction = (index: number) => {
      const updatedList = selectedActionsList.filter((_, i) => i !== index)
      setWorkflow({ ...workflow, selectedActionsList: updatedList })
    }

    // If no actions yet or wants more actions, show action selection
    if (selectedActionsList.length === 0 || wantsMoreActions) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          <div className="text-center">
            <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
            <h3 className="text-lg font-semibold mb-2">
              {selectedActionsList.length === 0 ? 'What should happen next?' : 'Add another action?'}
            </h3>
            <p className="text-muted-foreground text-sm">
              {selectedActionsList.length === 0 
                ? 'Choose actions to process your YouTube summaries'
                : 'Continue building your action chain'}
            </p>
          </div>

          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search actions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          <div className="grid gap-3">
            {filteredActions.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No actions found matching "{searchQuery}"
              </div>
            ) : (
              filteredActions.map((action) => (
                <Card
                  key={action.id}
                  className="cursor-pointer transition-all hover:ring-2 hover:ring-primary"
                  onClick={() => addAction(action)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <action.icon className="h-5 w-5 text-primary" />
                      <div className="flex-1">
                        <p className="font-medium">{action.name}</p>
                        <p className="text-sm text-muted-foreground">{action.desc}</p>
                      </div>
                      <ChevronLeft className="h-4 w-4 text-muted-foreground rotate-180" />
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>

          <div className="flex gap-3">
            <Button onClick={previousStep} variant="outline" className="w-full">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Previous
            </Button>
            {selectedActionsList.length > 0 && (
              <Button
                onClick={() => {
                  setWantsMoreActions(false)
                  nextStep()
                }}
                className="w-full"
              >
                Skip & Continue
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            )}
          </div>
        </motion.div>
      )
    }

    // Show current actions with option to add more
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <div className="text-center">
          <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
          <h3 className="text-lg font-semibold mb-2">Your Action Chain</h3>
          <p className="text-muted-foreground text-sm">
            {selectedActionsList.length} action{selectedActionsList.length !== 1 ? 's' : ''} configured
          </p>
        </div>

        {/* Action Chain Visualization */}
        <div className="max-w-2xl mx-auto space-y-3">
          {/* Trigger */}
          <div className="flex items-center gap-3 p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg border-2 border-purple-200 dark:border-purple-800">
            <Youtube className="h-5 w-5 text-purple-600" />
            <div className="flex-1">
              <p className="font-medium">Trigger: YouTube Channel Monitoring</p>
              <p className="text-xs text-muted-foreground">When new videos are found in the channel</p>
            </div>
          </div>

          {/* Arrow */}
          <div className="flex justify-center">
            <ArrowRight className="h-4 w-4 text-muted-foreground" />
          </div>

          {/* Actions */}
          {selectedActionsList.map((action, index) => (
            <div key={action.id}>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center gap-3 p-4 bg-muted/30 rounded-lg border"
              >
                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-xs font-medium">
                  {index + 1}
                </div>
                <div className="flex-1">
                  <p className="font-medium">{action.name}</p>
                  <p className="text-xs text-muted-foreground">{action.description}</p>
                </div>
                <div className="flex gap-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setShowActionConfig(index)}
                    className="h-8 px-2"
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => removeAction(index)}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </motion.div>
              {index < selectedActionsList.length - 1 && (
                <div className="flex justify-center my-2">
                  <ArrowRight className="h-4 w-4 text-muted-foreground" />
                </div>
              )}
            </div>
          ))}

          {/* Add Another Action Button */}
          <Button
            variant="outline"
            onClick={() => setWantsMoreActions(true)}
            className="w-full"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Another Action
          </Button>
        </div>

        <div className="flex gap-3 max-w-2xl mx-auto">
          <Button onClick={previousStep} variant="outline" className="w-full">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button 
            onClick={nextStep} 
            className="w-full"
            disabled={selectedActionsList.length === 0}
          >
            Continue <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>

        {/* Action Configuration Modal */}
        {showActionConfig !== null && selectedActionsList[showActionConfig] && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowActionConfig(null)}
          >
            <motion.div
              initial={{ scale: 0.95 }}
              animate={{ scale: 1 }}
              className="bg-card p-6 rounded-lg shadow-lg max-w-md w-full max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">
                Configure {selectedActionsList[showActionConfig].name}
              </h3>
              
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Configuration options will appear here based on the action type.
                </p>
                <div className="flex justify-end gap-2 mt-6">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowActionConfig(null)}
                  >
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => setShowActionConfig(null)}
                  >
                    Save
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </motion.div>
    )
  }

  const renderReviewStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="space-y-6">
        <h3 className="text-lg font-semibold">Automation Summary</h3>
        <div className="space-y-4">
          <div className="flex justify-between">
            <span className="font-medium">Name:</span>
            <span>{workflow.name}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium">YouTube Channel:</span>
            <span>{workflow.youtubeConfig.channelName || workflow.youtubeConfig.channelId}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium">Videos to Fetch:</span>
            <span>{workflow.youtubeConfig.videosToFetch}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium">AI Model:</span>
            <span>{workflow.aiSettings.model}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium">Summary Style:</span>
            <span className="capitalize">{workflow.aiSettings.summaryStyle.replace('-', ' ')}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium">Email Recipient:</span>
            <span>{workflow.emailConfig.recipient}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium">Send Frequency:</span>
            <span className="capitalize">{workflow.emailConfig.sendFrequency}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium">Additional Actions:</span>
            <span>{workflow.selectedActionsList.length} actions</span>
          </div>
        </div>
      </div>

      <div className="flex justify-between items-center">
        <Button variant="outline" onClick={previousStep} className="gap-2">
          <ChevronLeft className="w-4 h-4" />
          Previous
        </Button>
        <Button onClick={nextStep} className="gap-2 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700">
          Create Automation
          <Check className="w-4 h-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderCompleteStep = () => (
    <>
      {showCelebration && (
        <EmojiCelebration
          onComplete={() => setShowCelebration(false)}
          duration={3000}
        />
      )}
      
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="text-center space-y-6"
      >
        <div className="w-20 h-20 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto">
          <Check className="h-10 w-10 text-green-600 dark:text-green-400" />
        </div>
        
        <div>
          <h2 className="text-2xl font-bold mb-2">Automation Created!</h2>
          <p className="text-muted-foreground">
            Your YouTube channel summarizer is now active
          </p>
        </div>

        <div className="space-y-3 max-w-sm mx-auto">
          <Button 
            onClick={() => setLocation('/dashboard/automations')}
            className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
            size="lg"
          >
            View My Automations
          </Button>
          <Button 
            onClick={() => setLocation('/dashboard/browse-templates')}
            variant="outline"
            className="w-full"
            size="lg"
          >
            Browse More Templates
          </Button>
        </div>
      </motion.div>
    </>
  )

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'intro':
        return renderIntroStep()
      case 'naming':
        return renderNamingStep()
      case 'youtube-config':
        return renderYouTubeConfig()
      case 'apify-setup':
        return renderApifySetup()
      case 'ai-settings':
        return renderAISettings()
      case 'email-config':
        return renderEmailConfig()
      case 'actions-question':
        return renderActionsQuestion()
      case 'actions':
        return renderActionsStep()
      case 'review':
        return renderReviewStep()
      case 'complete':
        return renderCompleteStep()
      default:
        return null
    }
  }

  const CurrentIcon = steps.find(s => s.id === currentStep)?.icon || Settings
  
  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => setLocation('/dashboard/browse-templates')}
          className="mb-4"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back to Templates
        </Button>

        {currentStep !== 'complete' && currentStep !== 'intro' && (
          <>
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <CurrentIcon className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">{steps.find(s => s.id === currentStep)?.title}</h1>
                <p className="text-muted-foreground text-sm">
                  {currentStep === 'naming' && 'Give your automation a memorable name'}
                  {currentStep === 'youtube-config' && 'Configure which YouTube channel to monitor'}
                  {currentStep === 'apify-setup' && 'Connect Apify to scrape YouTube video data'}
                  {currentStep === 'ai-settings' && 'Configure how AI will summarize the videos'}
                  {currentStep === 'email-config' && 'Set up email delivery preferences'}
                  {currentStep === 'actions-question' && 'Extend your automation with additional actions'}
                  {currentStep === 'actions' && 'Configure additional actions for your workflow'}
                  {currentStep === 'review' && 'Review your automation configuration'}
                </p>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mb-6">
              {(() => {
                const workflowSteps = steps.filter(s => s.id !== 'intro' && s.id !== 'complete')
                const currentIndex = workflowSteps.findIndex(s => s.id === currentStep)
                const progress = Math.round(((currentIndex + 1) / workflowSteps.length) * 100)
                return (
                  <>
                    <div className="flex justify-between text-xs text-muted-foreground mb-2">
                      <span>Step {currentIndex + 1} of {workflowSteps.length}</span>
                      <span>{progress}% Complete</span>
                    </div>
                    <Progress value={progress} className="h-2" />
                  </>
                )
              })()}
            </div>
          </>
        )}
      </div>

      {/* Content */}
      {currentStep !== 'intro' && currentStep !== 'complete' ? (
        <Card className="p-6">
          {renderCurrentStep()}
        </Card>
      ) : (
        renderCurrentStep()
      )}

      {/* Action Configuration Dialogs */}
      <Dialog open={actionConfigOpen === 'send_email'} onOpenChange={(open) => !open && setActionConfigOpen(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Configure Email Action</DialogTitle>
            <DialogDescription>Set up email delivery for your YouTube summaries</DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="email-recipient">Recipient Email</Label>
              <Input
                id="email-recipient"
                type="email"
                placeholder="<EMAIL>"
                value={tempActionConfig.recipient || ''}
                onChange={(e) => setTempActionConfig({ ...tempActionConfig, recipient: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email-subject">Email Subject</Label>
              <Input
                id="email-subject"
                placeholder="YouTube Channel Summary Report"
                value={tempActionConfig.subject || ''}
                onChange={(e) => setTempActionConfig({ ...tempActionConfig, subject: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setActionConfigOpen(null)}>Cancel</Button>
            <Button onClick={() => handleActionConfigSave('send_email')}>Save Configuration</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={actionConfigOpen === 'webhook'} onOpenChange={(open) => !open && setActionConfigOpen(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Configure Webhook</DialogTitle>
            <DialogDescription>Send YouTube summary data to an external service</DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="webhook-url">Webhook URL</Label>
              <Input
                id="webhook-url"
                type="url"
                placeholder="https://api.example.com/webhook"
                value={tempActionConfig.url || ''}
                onChange={(e) => setTempActionConfig({ ...tempActionConfig, url: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="webhook-method">HTTP Method</Label>
              <Select
                value={tempActionConfig.method || 'POST'}
                onValueChange={(value) => setTempActionConfig({ ...tempActionConfig, method: value })}
              >
                <SelectTrigger id="webhook-method">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="POST">POST</SelectItem>
                  <SelectItem value="PUT">PUT</SelectItem>
                  <SelectItem value="PATCH">PATCH</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setActionConfigOpen(null)}>Cancel</Button>
            <Button onClick={() => handleActionConfigSave('webhook')}>Save Configuration</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={actionConfigOpen === 'google_sheets'} onOpenChange={(open) => !open && setActionConfigOpen(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Configure Google Sheets</DialogTitle>
            <DialogDescription>Log YouTube summaries to a Google Sheets spreadsheet</DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="sheets-id">Spreadsheet ID</Label>
              <Input
                id="sheets-id"
                placeholder="1234567890abcdef..."
                value={tempActionConfig.spreadsheetId || ''}
                onChange={(e) => setTempActionConfig({ ...tempActionConfig, spreadsheetId: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="sheets-range">Sheet Range</Label>
              <Input
                id="sheets-range"
                placeholder="Sheet1!A1"
                value={tempActionConfig.range || ''}
                onChange={(e) => setTempActionConfig({ ...tempActionConfig, range: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setActionConfigOpen(null)}>Cancel</Button>
            <Button onClick={() => handleActionConfigSave('google_sheets')}>Save Configuration</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={actionConfigOpen === 'slack'} onOpenChange={(open) => !open && setActionConfigOpen(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Configure Slack Notification</DialogTitle>
            <DialogDescription>Send YouTube summary notifications to Slack</DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="slack-channel">Channel</Label>
              <Input
                id="slack-channel"
                placeholder="#youtube-summaries"
                value={tempActionConfig.channel || ''}
                onChange={(e) => setTempActionConfig({ ...tempActionConfig, channel: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="slack-message">Message Template</Label>
              <Textarea
                id="slack-message"
                placeholder="New YouTube videos summarized: {count} videos from {channel_name}"
                value={tempActionConfig.message || ''}
                onChange={(e) => setTempActionConfig({ ...tempActionConfig, message: e.target.value })}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setActionConfigOpen(null)}>Cancel</Button>
            <Button onClick={() => handleActionConfigSave('slack')}>Save Configuration</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}