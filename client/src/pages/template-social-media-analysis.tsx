import { useState } from 'react'
import { useLocation } from 'wouter'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ActionsSelector } from '@/components/actions-selector'
import { Progress } from '@/components/ui/progress'
import { 
  ChevronLeft, 
  ChevronRight, 
  Users, 
  Twitter, 
  Linkedin, 
  Brain, 
  Mail, 
  CheckCircle, 
  Calendar,
  Clock,
  FileSpreadsheet,
  Zap,
  Key,
  Globe,
  MessageSquare,
  Bot,
  Target,
  Settings,
  Eye,
  Shield,
  FileText,
  Sparkles
} from 'lucide-react'

type StepType = 'intro' | 'naming' | 'google-auth' | 'sheets-config' | 'api-config' | 'ai-settings' | 'email-config' | 'actions-question' | 'actions' | 'review' | 'complete'

interface SocialMediaConfig {
  name: string
  googleAuth: {
    isAuthenticated: boolean
    userEmail: string
    userName: string
    accountId: string
  }
  sheetsConfig: {
    spreadsheetId: string
    sheetName: string
    columns: {
      linkedinUrl: string
      name: string
      twitterHandle: string
      email: string
      done: string
    }
  }
  apiConfig: {
    rapidApi: {
      apiKey: string
      twitterPlan: string
      linkedinPlan: string
    }
    endpoints: {
      twitter: string
      linkedin: string
    }
  }
  aiSettings: {
    provider: 'openai' | 'anthropic' | 'google'
    model: string
    isConfigured: boolean
    analysisPrompt: string
    emailTone: 'professional' | 'friendly' | 'casual' | 'persuasive'
    language: string
    creativity: 'conservative' | 'balanced' | 'creative'
  }
  emailConfig: {
    service: 'gmail' | 'smtp' | 'sendgrid'
    smtpSettings: {
      host: string
      port: number
      secure: boolean
      username: string
      password: string
    }
    template: {
      subjectTemplate: string
      bodyTemplate: string
      includeSignature: boolean
      signature: string
    }
    tracking: {
      sendCopyToSelf: boolean
      updateSheetStatus: boolean
      logActivity: boolean
    }
  }
  selectedActions: Array<{
    id: string
    type: string
    name: string
    description: string
    config: Record<string, any>
  }>
  wantsActions: boolean | null
}

const defaultConfig: SocialMediaConfig = {
  name: '',
  googleAuth: {
    isAuthenticated: false,
    userEmail: '',
    userName: '',
    accountId: ''
  },
  sheetsConfig: {
    spreadsheetId: '',
    sheetName: 'Leads',
    columns: {
      linkedinUrl: 'A',
      name: 'B', 
      twitterHandle: 'C',
      email: 'D',
      done: 'E'
    }
  },
  apiConfig: {
    rapidApi: {
      apiKey: '',
      twitterPlan: 'basic',
      linkedinPlan: 'basic'
    },
    endpoints: {
      twitter: 'twitter-api45.p.rapidapi.com',
      linkedin: 'linkedin-api8.p.rapidapi.com'
    }
  },
  aiSettings: {
    provider: 'openai',
    model: 'gpt-4o',
    isConfigured: false,
    analysisPrompt: 'Analyze the social media profile data and generate a personalized email that references specific interests, recent activities, or professional background.',
    emailTone: 'professional',
    language: 'English',
    creativity: 'balanced'
  },
  emailConfig: {
    service: 'gmail',
    smtpSettings: {
      host: '',
      port: 587,
      secure: false,
      username: '',
      password: ''
    },
    template: {
      subjectTemplate: 'Connection opportunity - {name}',
      bodyTemplate: 'Hi {name},\n\n{ai_generated_content}\n\nBest regards,\n{sender_name}',
      includeSignature: true,
      signature: ''
    },
    tracking: {
      sendCopyToSelf: true,
      updateSheetStatus: true,
      logActivity: true
    }
  },
  selectedActions: [],
  wantsActions: null
}

const steps: { id: StepType; title: string; icon: React.ComponentType<{ className?: string }> }[] = [
  { id: 'intro', title: 'Introduction', icon: Users },
  { id: 'naming', title: 'Name Automation', icon: FileSpreadsheet },
  { id: 'google-auth', title: 'Google Authentication', icon: Key },
  { id: 'sheets-config', title: 'Google Sheets Setup', icon: FileSpreadsheet },
  { id: 'api-config', title: 'Social Media APIs', icon: Globe },
  { id: 'ai-settings', title: 'AI Configuration', icon: Brain },
  { id: 'email-config', title: 'Email Setup', icon: Mail },
  { id: 'actions-question', title: 'Additional Actions', icon: Zap },
  { id: 'actions', title: 'Configure Actions', icon: Settings },
  { id: 'review', title: 'Review', icon: Eye },
  { id: 'complete', title: 'Complete', icon: CheckCircle }
]

const stepConfig: Record<StepType, { title: string; subtitle: string; icon: React.ComponentType<{ className?: string }>; progress: number }> = {
  intro: { title: 'Social Media Analysis & Email Generation', subtitle: 'Automate lead research and personalized outreach', icon: Users, progress: 0 },
  naming: { title: 'Name Your Automation', subtitle: 'Give your automation a memorable name', icon: FileText, progress: 10 },
  'google-auth': { title: 'Connect Google Account', subtitle: 'Authenticate to access Google Sheets', icon: Shield, progress: 20 },
  'sheets-config': { title: 'Configure Google Sheets', subtitle: 'Set up your lead data source', icon: FileSpreadsheet, progress: 30 },
  'api-config': { title: 'API Configuration', subtitle: 'Connect to social media APIs', icon: Globe, progress: 40 },
  'ai-settings': { title: 'AI Settings', subtitle: 'Configure analysis and email generation', icon: Brain, progress: 50 },
  'email-config': { title: 'Email Configuration', subtitle: 'Set up email delivery settings', icon: Mail, progress: 60 },
  'actions-question': { title: 'Additional Actions', subtitle: 'Extend your automation with more actions', icon: Zap, progress: 70 },
  actions: { title: 'Configure Actions', subtitle: 'Set up your selected actions', icon: Settings, progress: 80 },
  review: { title: 'Review Configuration', subtitle: 'Confirm your automation settings', icon: Eye, progress: 90 },
  complete: { title: 'Automation Created!', subtitle: 'Your automation is ready', icon: CheckCircle, progress: 100 }
}

export default function SocialMediaAnalysisTemplate() {
  const [, setLocation] = useLocation()
  const [currentStep, setCurrentStep] = useState<StepType>('intro')
  const [config, setConfig] = useState<SocialMediaConfig>(defaultConfig)
  const [searchQuery, setSearchQuery] = useState('')

  const currentStepIndex = steps.findIndex(step => step.id === currentStep)
  const progress = ((currentStepIndex + 1) / steps.length) * 100

  const handleNext = () => {
    const currentIndex = steps.findIndex(step => step.id === currentStep)
    if (currentIndex < steps.length - 1) {
      const nextStep = steps[currentIndex + 1]
      
      // Skip actions step if user doesn't want actions
      if (nextStep.id === 'actions' && config.wantsActions === false) {
        setCurrentStep('review')
      } else {
        setCurrentStep(nextStep.id)
      }
    }
  }

  const handlePrevious = () => {
    const currentIndex = steps.findIndex(step => step.id === currentStep)
    if (currentIndex > 0) {
      const prevStep = steps[currentIndex - 1]
      
      // Skip actions step if coming back and user doesn't want actions
      if (prevStep.id === 'actions' && config.wantsActions === false) {
        setCurrentStep('actions-question')
      } else {
        setCurrentStep(prevStep.id)
      }
    }
  }

  const handleActionsChange = (actions: string[]) => {
    const selectedActions = actions.map(actionId => ({
      id: actionId,
      type: actionId,
      name: actionId.charAt(0).toUpperCase() + actionId.slice(1),
      description: `${actionId.charAt(0).toUpperCase() + actionId.slice(1)} configuration`,
      config: {}
    }))
    
    setConfig(prev => ({
      ...prev,
      selectedActions
    }))
  }

  const handleGoogleSignIn = () => {
    // Simulate Google authentication
    setConfig(prev => ({
      ...prev,
      googleAuth: {
        isAuthenticated: true,
        userEmail: '<EMAIL>',
        userName: 'John Doe',
        accountId: 'google_12345'
      }
    }))
  }

  const renderIntroStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center mx-auto">
        <Users className="h-10 w-10 text-white" />
      </div>
      <div>
        <h2 className="text-2xl font-bold mb-2">Social Media Analysis & Email Generation</h2>
        <p className="text-muted-foreground max-w-md mx-auto">
          This template creates an automated workflow that analyzes social media profiles from Google Sheets, 
          extracts professional insights using AI, and generates personalized outreach emails automatically.
        </p>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <FileSpreadsheet className="h-8 w-8 mx-auto mb-2 text-green-600" />
          <p className="text-xs font-medium">Google Sheets</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Brain className="h-8 w-8 mx-auto mb-2 text-purple-600" />
          <p className="text-xs font-medium">AI Analysis</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Users className="h-8 w-8 mx-auto mb-2 text-blue-600" />
          <p className="text-xs font-medium">Social Media</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Mail className="h-8 w-8 mx-auto mb-2 text-red-600" />
          <p className="text-xs font-medium">Email Generation</p>
        </div>
      </div>

      <Button 
        onClick={handleNext} 
        size="lg"
        className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
      >
        Start Setup
        <ChevronRight className="ml-2 h-4 w-4" />
      </Button>
    </motion.div>
  )

  const renderNamingStep = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Name Your Automation</h2>
        <p className="text-muted-foreground">Choose a descriptive name for your social media analysis workflow</p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="automation-name">Automation Name *</Label>
          <Input
            id="automation-name"
            value={config.name}
            onChange={(e) => setConfig(prev => ({ ...prev, name: e.target.value }))}
            placeholder="e.g., LinkedIn Lead Outreach, Social Media Prospect Analysis"
            className="mt-1"
          />
        </div>
        
        <div className="p-4 bg-muted/50 rounded-lg">
          <h4 className="font-medium mb-2">Suggested Names:</h4>
          <div className="flex flex-wrap gap-2">
            {[
              'LinkedIn Lead Outreach',
              'Social Media Prospect Analysis', 
              'Automated Sales Outreach',
              'Personalized Email Campaign',
              'Social Intelligence Outreach'
            ].map((name) => (
              <Badge 
                key={name}
                variant="secondary" 
                className="cursor-pointer hover:bg-primary hover:text-primary-foreground"
                onClick={() => setConfig(prev => ({ ...prev, name }))}
              >
                {name}
              </Badge>
            ))}
          </div>
        </div>
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={handlePrevious}>
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={handleNext} disabled={!config.name.trim()}>
          Continue
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  )

  const renderGoogleAuthStep = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Google Authentication</h2>
        <p className="text-muted-foreground">Connect your Google account to access Google Sheets</p>
      </div>

      <div className="space-y-4">
        <div>
          <Label className="text-base font-medium">Authentication Type *</Label>
          <div className="mt-3 space-y-3">
            <div className="flex items-center space-x-3">
              <div className="w-4 h-4 rounded-full border-2 border-primary bg-primary"></div>
              <span className="font-medium">Google OAuth</span>
            </div>
          </div>
        </div>

        {!config.googleAuth.isAuthenticated ? (
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Note: Select existing Google account from below or Sign in with a different account
            </p>
            
            <Button 
              onClick={handleGoogleSignIn}
              className="w-full flex items-center justify-center space-x-2"
            >
              <svg className="w-5 h-5" viewBox="0 0 24 24">
                <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              Sign in with Google
            </Button>
          </div>
        ) : (
          <div className="p-4 border rounded-lg bg-green-50 dark:bg-green-900/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-green-800 dark:text-green-200">
                  Google account: {config.googleAuth.userName} ({config.googleAuth.userEmail})
                </p>
                <Badge variant="secondary" className="mt-1 bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                  Connected
                </Badge>
              </div>
              <CheckCircle className="h-5 w-5 text-green-600" />
            </div>
          </div>
        )}
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={handlePrevious}>
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={handleNext} disabled={!config.googleAuth.isAuthenticated}>
          Continue
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  )

  const renderSheetsConfigStep = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Google Sheets Configuration</h2>
        <p className="text-muted-foreground">Set up your lead data spreadsheet with required columns</p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="spreadsheet-id">Spreadsheet ID *</Label>
          <Input
            id="spreadsheet-id"
            value={config.sheetsConfig.spreadsheetId}
            onChange={(e) => setConfig(prev => ({
              ...prev,
              sheetsConfig: { ...prev.sheetsConfig, spreadsheetId: e.target.value }
            }))}
            placeholder="1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
            className="mt-1"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Found in the Google Sheets URL after /d/
          </p>
        </div>

        <div>
          <Label htmlFor="sheet-name">Sheet Name</Label>
          <Input
            id="sheet-name"
            value={config.sheetsConfig.sheetName}
            onChange={(e) => setConfig(prev => ({
              ...prev,
              sheetsConfig: { ...prev.sheetsConfig, sheetName: e.target.value }
            }))}
            placeholder="Leads"
            className="mt-1"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="linkedin-column">LinkedIn URL Column</Label>
            <Select 
              value={config.sheetsConfig.columns.linkedinUrl}
              onValueChange={(value) => setConfig(prev => ({
                ...prev,
                sheetsConfig: { 
                  ...prev.sheetsConfig, 
                  columns: { ...prev.sheetsConfig.columns, linkedinUrl: value }
                }
              }))}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select column" />
              </SelectTrigger>
              <SelectContent>
                {['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'].map(col => (
                  <SelectItem key={col} value={col}>Column {col}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="name-column">Name Column</Label>
            <Select 
              value={config.sheetsConfig.columns.name}
              onValueChange={(value) => setConfig(prev => ({
                ...prev,
                sheetsConfig: { 
                  ...prev.sheetsConfig, 
                  columns: { ...prev.sheetsConfig.columns, name: value }
                }
              }))}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select column" />
              </SelectTrigger>
              <SelectContent>
                {['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'].map(col => (
                  <SelectItem key={col} value={col}>Column {col}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="twitter-column">Twitter Handle Column</Label>
            <Select 
              value={config.sheetsConfig.columns.twitterHandle}
              onValueChange={(value) => setConfig(prev => ({
                ...prev,
                sheetsConfig: { 
                  ...prev.sheetsConfig, 
                  columns: { ...prev.sheetsConfig.columns, twitterHandle: value }
                }
              }))}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select column" />
              </SelectTrigger>
              <SelectContent>
                {['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'].map(col => (
                  <SelectItem key={col} value={col}>Column {col}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="email-column">Email Column</Label>
            <Select 
              value={config.sheetsConfig.columns.email}
              onValueChange={(value) => setConfig(prev => ({
                ...prev,
                sheetsConfig: { 
                  ...prev.sheetsConfig, 
                  columns: { ...prev.sheetsConfig.columns, email: value }
                }
              }))}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select column" />
              </SelectTrigger>
              <SelectContent>
                {['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'].map(col => (
                  <SelectItem key={col} value={col}>Column {col}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="p-4 bg-muted/50 rounded-lg">
          <h4 className="font-medium mb-2">File</h4>
          <p className="text-sm text-muted-foreground">There are no files, please refresh</p>
        </div>
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={handlePrevious}>
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={handleNext} disabled={!config.sheetsConfig.spreadsheetId}>
          Continue
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  )

  const renderApiConfigStep = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Social Media API Configuration</h2>
        <p className="text-muted-foreground">Configure RapidAPI access for Twitter and LinkedIn data extraction</p>
      </div>

      <div className="space-y-6">
        <div>
          <Label htmlFor="rapidapi-key">RapidAPI Key *</Label>
          <Input
            id="rapidapi-key"
            type="password"
            value={config.apiConfig.rapidApi.apiKey}
            onChange={(e) => setConfig(prev => ({
              ...prev,
              apiConfig: {
                ...prev.apiConfig,
                rapidApi: { ...prev.apiConfig.rapidApi, apiKey: e.target.value }
              }
            }))}
            placeholder="Enter your RapidAPI key"
            className="mt-1"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Get your API key from <a href="https://rapidapi.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">RapidAPI Dashboard</a>
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="p-4 border rounded-lg">
            <div className="flex items-center space-x-2 mb-3">
              <Twitter className="h-5 w-5 text-blue-400" />
              <h3 className="font-semibold">Twitter API</h3>
            </div>
            
            <div className="space-y-3">
              <div>
                <Label htmlFor="twitter-plan">Subscription Plan</Label>
                <Select 
                  value={config.apiConfig.rapidApi.twitterPlan}
                  onValueChange={(value) => setConfig(prev => ({
                    ...prev,
                    apiConfig: {
                      ...prev.apiConfig,
                      rapidApi: { ...prev.apiConfig.rapidApi, twitterPlan: value }
                    }
                  }))}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="basic">Basic (Free)</SelectItem>
                    <SelectItem value="pro">Pro ($9.99/month)</SelectItem>
                    <SelectItem value="ultra">Ultra ($49.99/month)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="twitter-endpoint">API Endpoint</Label>
                <Input
                  id="twitter-endpoint"
                  value={config.apiConfig.endpoints.twitter}
                  onChange={(e) => setConfig(prev => ({
                    ...prev,
                    apiConfig: {
                      ...prev.apiConfig,
                      endpoints: { ...prev.apiConfig.endpoints, twitter: e.target.value }
                    }
                  }))}
                  className="mt-1"
                />
              </div>
            </div>
          </div>

          <div className="p-4 border rounded-lg">
            <div className="flex items-center space-x-2 mb-3">
              <Linkedin className="h-5 w-5 text-blue-600" />
              <h3 className="font-semibold">LinkedIn API</h3>
            </div>
            
            <div className="space-y-3">
              <div>
                <Label htmlFor="linkedin-plan">Subscription Plan</Label>
                <Select 
                  value={config.apiConfig.rapidApi.linkedinPlan}
                  onValueChange={(value) => setConfig(prev => ({
                    ...prev,
                    apiConfig: {
                      ...prev.apiConfig,
                      rapidApi: { ...prev.apiConfig.rapidApi, linkedinPlan: value }
                    }
                  }))}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="basic">Basic (Free)</SelectItem>
                    <SelectItem value="pro">Pro ($19.99/month)</SelectItem>
                    <SelectItem value="ultra">Ultra ($99.99/month)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="linkedin-endpoint">API Endpoint</Label>
                <Input
                  id="linkedin-endpoint"
                  value={config.apiConfig.endpoints.linkedin}
                  onChange={(e) => setConfig(prev => ({
                    ...prev,
                    apiConfig: {
                      ...prev.apiConfig,
                      endpoints: { ...prev.apiConfig.endpoints, linkedin: e.target.value }
                    }
                  }))}
                  className="mt-1"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={handlePrevious}>
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={handleNext} disabled={!config.apiConfig.rapidApi.apiKey}>
          Continue
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  )

  const renderAiSettingsStep = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">AI Configuration</h2>
        <p className="text-muted-foreground">Configure AI settings for analyzing social media data and generating personalized emails</p>
      </div>

      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="ai-provider">AI Provider</Label>
            <Select 
              value={config.aiSettings.provider}
              onValueChange={(value: 'openai' | 'anthropic' | 'google') => setConfig(prev => ({
                ...prev,
                aiSettings: { ...prev.aiSettings, provider: value }
              }))}
            >
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="openai">OpenAI</SelectItem>
                <SelectItem value="anthropic">Anthropic (Claude)</SelectItem>
                <SelectItem value="google">Google (Gemini)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="ai-model">Model</Label>
            <Select 
              value={config.aiSettings.model}
              onValueChange={(value) => setConfig(prev => ({
                ...prev,
                aiSettings: { ...prev.aiSettings, model: value }
              }))}
            >
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {config.aiSettings.provider === 'openai' && (
                  <>
                    <SelectItem value="gpt-4o">GPT-4o</SelectItem>
                    <SelectItem value="gpt-4">GPT-4</SelectItem>
                    <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                  </>
                )}
                {config.aiSettings.provider === 'anthropic' && (
                  <>
                    <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
                    <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                    <SelectItem value="claude-3-haiku">Claude 3 Haiku</SelectItem>
                  </>
                )}
                {config.aiSettings.provider === 'google' && (
                  <>
                    <SelectItem value="gemini-1.5-pro">Gemini 1.5 Pro</SelectItem>
                    <SelectItem value="gemini-1.0-pro">Gemini 1.0 Pro</SelectItem>
                  </>
                )}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div>
          <Label htmlFor="analysis-prompt">Analysis Instructions</Label>
          <Textarea
            id="analysis-prompt"
            value={config.aiSettings.analysisPrompt}
            onChange={(e) => setConfig(prev => ({
              ...prev,
              aiSettings: { ...prev.aiSettings, analysisPrompt: e.target.value }
            }))}
            rows={4}
            className="mt-1"
            placeholder="Provide instructions for how the AI should analyze social media profiles..."
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="email-tone">Email Tone</Label>
            <Select 
              value={config.aiSettings.emailTone}
              onValueChange={(value: 'professional' | 'friendly' | 'casual' | 'persuasive') => setConfig(prev => ({
                ...prev,
                aiSettings: { ...prev.aiSettings, emailTone: value }
              }))}
            >
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="professional">Professional</SelectItem>
                <SelectItem value="friendly">Friendly</SelectItem>
                <SelectItem value="casual">Casual</SelectItem>
                <SelectItem value="persuasive">Persuasive</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="language">Language</Label>
            <Select 
              value={config.aiSettings.language}
              onValueChange={(value) => setConfig(prev => ({
                ...prev,
                aiSettings: { ...prev.aiSettings, language: value }
              }))}
            >
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="English">English</SelectItem>
                <SelectItem value="Spanish">Spanish</SelectItem>
                <SelectItem value="French">French</SelectItem>
                <SelectItem value="German">German</SelectItem>
                <SelectItem value="Italian">Italian</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="creativity">Creativity Level</Label>
            <Select 
              value={config.aiSettings.creativity}
              onValueChange={(value: 'conservative' | 'balanced' | 'creative') => setConfig(prev => ({
                ...prev,
                aiSettings: { ...prev.aiSettings, creativity: value }
              }))}
            >
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="conservative">Conservative</SelectItem>
                <SelectItem value="balanced">Balanced</SelectItem>
                <SelectItem value="creative">Creative</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={handlePrevious}>
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={handleNext}>
          Continue
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  )

  const renderEmailConfigStep = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Email Configuration</h2>
        <p className="text-muted-foreground">Set up email delivery and template settings</p>
      </div>

      <div className="space-y-6">
        <div>
          <Label htmlFor="email-service">Email Service</Label>
          <Select 
            value={config.emailConfig.service}
            onValueChange={(value: 'gmail' | 'smtp' | 'sendgrid') => setConfig(prev => ({
              ...prev,
              emailConfig: { ...prev.emailConfig, service: value }
            }))}
          >
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="gmail">Gmail</SelectItem>
              <SelectItem value="smtp">SMTP</SelectItem>
              <SelectItem value="sendgrid">SendGrid</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {config.emailConfig.service === 'smtp' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border rounded-lg">
            <div>
              <Label htmlFor="smtp-host">SMTP Host</Label>
              <Input
                id="smtp-host"
                value={config.emailConfig.smtpSettings.host}
                onChange={(e) => setConfig(prev => ({
                  ...prev,
                  emailConfig: {
                    ...prev.emailConfig,
                    smtpSettings: { ...prev.emailConfig.smtpSettings, host: e.target.value }
                  }
                }))}
                placeholder="smtp.gmail.com"
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="smtp-port">Port</Label>
              <Input
                id="smtp-port"
                type="number"
                value={config.emailConfig.smtpSettings.port}
                onChange={(e) => setConfig(prev => ({
                  ...prev,
                  emailConfig: {
                    ...prev.emailConfig,
                    smtpSettings: { ...prev.emailConfig.smtpSettings, port: parseInt(e.target.value) }
                  }
                }))}
                placeholder="587"
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="smtp-username">Username</Label>
              <Input
                id="smtp-username"
                value={config.emailConfig.smtpSettings.username}
                onChange={(e) => setConfig(prev => ({
                  ...prev,
                  emailConfig: {
                    ...prev.emailConfig,
                    smtpSettings: { ...prev.emailConfig.smtpSettings, username: e.target.value }
                  }
                }))}
                placeholder="<EMAIL>"
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="smtp-password">Password</Label>
              <Input
                id="smtp-password"
                type="password"
                value={config.emailConfig.smtpSettings.password}
                onChange={(e) => setConfig(prev => ({
                  ...prev,
                  emailConfig: {
                    ...prev.emailConfig,
                    smtpSettings: { ...prev.emailConfig.smtpSettings, password: e.target.value }
                  }
                }))}
                placeholder="App password"
                className="mt-1"
              />
            </div>
          </div>
        )}

        <div className="space-y-4">
          <h3 className="font-semibold">Email Template</h3>
          
          <div>
            <Label htmlFor="subject-template">Subject Template</Label>
            <Input
              id="subject-template"
              value={config.emailConfig.template.subjectTemplate}
              onChange={(e) => setConfig(prev => ({
                ...prev,
                emailConfig: {
                  ...prev.emailConfig,
                  template: { ...prev.emailConfig.template, subjectTemplate: e.target.value }
                }
              }))}
              placeholder="Connection opportunity - {name}"
              className="mt-1"
            />
            <p className="text-xs text-muted-foreground mt-1">
              Use {'{name}'} for recipient name, {'{company}'} for company
            </p>
          </div>

          <div>
            <Label htmlFor="body-template">Email Body Template</Label>
            <Textarea
              id="body-template"
              value={config.emailConfig.template.bodyTemplate}
              onChange={(e) => setConfig(prev => ({
                ...prev,
                emailConfig: {
                  ...prev.emailConfig,
                  template: { ...prev.emailConfig.template, bodyTemplate: e.target.value }
                }
              }))}
              rows={6}
              className="mt-1"
              placeholder="Hi {name},&#10;&#10;{ai_generated_content}&#10;&#10;Best regards,&#10;{sender_name}"
            />
            <p className="text-xs text-muted-foreground mt-1">
              Use {'{ai_generated_content}'} for AI-generated personalized content
            </p>
          </div>
        </div>

        <div className="space-y-3">
          <h3 className="font-semibold">Tracking Options</h3>
          
          <div className="flex items-center space-x-2">
            <Checkbox
              id="send-copy"
              checked={config.emailConfig.tracking.sendCopyToSelf}
              onCheckedChange={(checked) => setConfig(prev => ({
                ...prev,
                emailConfig: {
                  ...prev.emailConfig,
                  tracking: { ...prev.emailConfig.tracking, sendCopyToSelf: !!checked }
                }
              }))}
            />
            <Label htmlFor="send-copy">Send copy to myself</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="update-sheet"
              checked={config.emailConfig.tracking.updateSheetStatus}
              onCheckedChange={(checked) => setConfig(prev => ({
                ...prev,
                emailConfig: {
                  ...prev.emailConfig,
                  tracking: { ...prev.emailConfig.tracking, updateSheetStatus: !!checked }
                }
              }))}
            />
            <Label htmlFor="update-sheet">Update Google Sheets status</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="log-activity"
              checked={config.emailConfig.tracking.logActivity}
              onCheckedChange={(checked) => setConfig(prev => ({
                ...prev,
                emailConfig: {
                  ...prev.emailConfig,
                  tracking: { ...prev.emailConfig.tracking, logActivity: !!checked }
                }
              }))}
            />
            <Label htmlFor="log-activity">Log email activity</Label>
          </div>
        </div>
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={handlePrevious}>
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={handleNext}>
          Continue
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  )

  const renderActionsQuestionStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Zap className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h3 className="text-lg font-semibold mb-2">Do you want to add any actions?</h3>
        <p className="text-muted-foreground text-sm mb-6">
          Extend your automation with additional actions (optional)
        </p>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-6">
        <Button
          variant={config.wantsActions === true ? "default" : "outline"}
          size="lg"
          onClick={() => {
            setConfig({ ...config, wantsActions: true })
            handleNext()
          }}
          className="h-auto py-6"
        >
          <div className="flex flex-col items-center gap-2">
            <CheckCircle className="h-8 w-8" />
            <span>Yes, I want to add actions</span>
          </div>
        </Button>
        <Button
          variant={config.wantsActions === false ? "default" : "outline"}
          size="lg"
          onClick={() => {
            setConfig({ ...config, wantsActions: false })
            setCurrentStep('review')  // Skip directly to review
          }}
          className="h-auto py-6"
        >
          <div className="flex flex-col items-center gap-2">
            <ChevronRight className="h-8 w-8" />
            <span>No, continue without</span>
          </div>
        </Button>
      </div>

      <div>
        <Button onClick={handlePrevious} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
      </div>
    </motion.div>
  )

  const renderActionsStep = () => {
    const selectedActionsList = config.selectedActions || []

    const availableActionsList = [
      { id: 'discord-read', name: 'Discord Read', icon: MessageSquare, desc: 'Read messages from any Discord channel' },
      { id: 'discord-send', name: 'Discord Send', icon: MessageSquare, desc: 'Send messages to any Discord channel' },
      { id: 'gmail-read', name: 'Gmail Read', icon: Mail, desc: 'Read emails from your Gmail account' },
      { id: 'gmail-send', name: 'Gmail Send', icon: Mail, desc: 'Send emails via your Gmail account' },
      { id: 'gcalendar-read', name: 'Google Calendar Read', icon: Calendar, desc: 'Read events from any Google Calendar' },
      { id: 'gcalendar-write', name: 'Google Calendar Write', icon: Calendar, desc: 'Create events on any Google Calendar' },
      { id: 'http-request', name: 'HTTP Request', icon: Globe, desc: 'Make GET, POST, PUT, or DELETE HTTP requests' },
      { id: 'slack-read', name: 'Slack Read', icon: MessageSquare, desc: 'Read messages from a specified Slack channel' },
      { id: 'slack-send', name: 'Slack Send', icon: MessageSquare, desc: 'Send a message to a specified Slack channel' }
    ]

    // Filter actions based on search query
    const filteredActions = availableActionsList.filter(action => {
      const query = searchQuery.toLowerCase()
      return (
        action.name.toLowerCase().includes(query) ||
        action.desc.toLowerCase().includes(query)
      )
    })

    const addAction = (action: any) => {
      const newAction = {
        id: Date.now().toString(),
        type: action.id,
        name: action.name,
        description: action.desc,
        config: {}
      }
      const updatedList = [...selectedActionsList, newAction]
      setConfig({ ...config, selectedActions: updatedList })
    }

    const removeAction = (index: number) => {
      const updatedList = selectedActionsList.filter((_, i) => i !== index)
      setConfig({ ...config, selectedActions: updatedList })
    }

    // If no actions yet or wants more actions, show action selection
    if (selectedActionsList.length === 0) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          <div className="text-center">
            <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
            <h3 className="text-lg font-semibold mb-2">What should happen next?</h3>
            <p className="text-muted-foreground text-sm">
              Choose an action to perform when your trigger fires
            </p>
          </div>

          {/* Search */}
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Target className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search actions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Available Actions */}
          {filteredActions.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No actions found matching "{searchQuery}"</p>
            </div>
          ) : (
            <div className="grid gap-3 max-w-2xl mx-auto">
              {filteredActions.map((action) => (
                <Card
                  key={action.id}
                  className="cursor-pointer transition-all hover:shadow-md border-2 hover:border-primary/20"
                  onClick={() => addAction(action)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <action.icon className="h-6 w-6 text-primary" />
                      <div className="flex-1">
                        <h4 className="font-medium">{action.name}</h4>
                        <p className="text-sm text-muted-foreground">{action.desc}</p>
                      </div>
                      <Target className="h-5 w-5 text-muted-foreground" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          <div className="flex gap-3 max-w-2xl mx-auto">
            <Button onClick={handlePrevious} variant="outline" className="w-full">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Previous
            </Button>
          </div>
        </motion.div>
      )
    }

    // Show selected actions with option to add more
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <div className="text-center">
          <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
          <h3 className="text-lg font-semibold mb-2">Your Action Chain</h3>
          <p className="text-muted-foreground text-sm">
            Review and configure your selected actions
          </p>
        </div>

        {/* Selected Actions */}
        <div className="space-y-3 max-w-2xl mx-auto">
          {selectedActionsList.map((action, index) => (
            <Card key={action.id} className="border-2">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-primary">{index + 1}</span>
                    </div>
                    <div>
                      <h4 className="font-medium">{action.name}</h4>
                      <p className="text-sm text-muted-foreground">{action.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {}}
                    >
                      <Settings className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => removeAction(index)}
                    >
                      <Target className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="flex gap-3 max-w-2xl mx-auto">
          <Button onClick={handlePrevious} variant="outline" className="w-full">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button onClick={handleNext} className="w-full">
            Continue to Review
            <ChevronRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </motion.div>
    )
  }

  const renderReviewStep = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Review Configuration</h2>
        <p className="text-muted-foreground">Review your automation settings before creating</p>
      </div>

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileSpreadsheet className="h-5 w-5" />
              <span>Basic Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><span className="font-medium">Name:</span> {config.name}</p>
              <p><span className="font-medium">Google Account:</span> {config.googleAuth.userEmail}</p>
              <p><span className="font-medium">Spreadsheet ID:</span> {config.sheetsConfig.spreadsheetId}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Globe className="h-5 w-5" />
              <span>API Configuration</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><span className="font-medium">RapidAPI Key:</span> •••••••••</p>
              <p><span className="font-medium">Twitter Plan:</span> {config.apiConfig.rapidApi.twitterPlan}</p>
              <p><span className="font-medium">LinkedIn Plan:</span> {config.apiConfig.rapidApi.linkedinPlan}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Brain className="h-5 w-5" />
              <span>AI Settings</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><span className="font-medium">Provider:</span> {config.aiSettings.provider}</p>
              <p><span className="font-medium">Model:</span> {config.aiSettings.model}</p>
              <p><span className="font-medium">Email Tone:</span> {config.aiSettings.emailTone}</p>
              <p><span className="font-medium">Language:</span> {config.aiSettings.language}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Mail className="h-5 w-5" />
              <span>Email Configuration</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><span className="font-medium">Service:</span> {config.emailConfig.service}</p>
              <p><span className="font-medium">Subject Template:</span> {config.emailConfig.template.subjectTemplate}</p>
              <p><span className="font-medium">Send Copy to Self:</span> {config.emailConfig.tracking.sendCopyToSelf ? 'Yes' : 'No'}</p>
              <p><span className="font-medium">Update Sheet Status:</span> {config.emailConfig.tracking.updateSheetStatus ? 'Yes' : 'No'}</p>
            </div>
          </CardContent>
        </Card>

        {config.selectedActions.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Zap className="h-5 w-5" />
                <span>Additional Actions</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {config.selectedActions.map((action, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <Badge variant="secondary">{action.name}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={handlePrevious}>
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={handleNext}>
          Create Automation
          <CheckCircle className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  )

  const renderCompleteStep = () => (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto">
        <CheckCircle className="h-10 w-10 text-green-600 dark:text-green-400" />
      </div>
      <div>
        <h2 className="text-3xl font-bold mb-2">Automation Created!</h2>
        <p className="text-muted-foreground text-lg">
          Your "{config.name}" automation is now ready and will start analyzing social media profiles automatically.
        </p>
      </div>
      
      <div className="flex flex-col sm:flex-row gap-3 justify-center mt-8">
        <Button 
          onClick={() => setLocation('/dashboard/automations')}
          size="lg"
          className="bg-primary hover:bg-primary/90"
        >
          View All Automations
        </Button>
        <Button 
          onClick={() => setLocation('/dashboard/browse-templates')}
          variant="outline"
          size="lg"
        >
          Browse More Templates
        </Button>
      </div>
    </motion.div>
  )

  const getCurrentStepContent = () => {
    switch (currentStep) {
      case 'intro': return renderIntroStep()
      case 'naming': return renderNamingStep()
      case 'google-auth': return renderGoogleAuthStep()
      case 'sheets-config': return renderSheetsConfigStep()
      case 'api-config': return renderApiConfigStep()
      case 'ai-settings': return renderAiSettingsStep()
      case 'email-config': return renderEmailConfigStep()
      case 'actions-question': return renderActionsQuestionStep()
      case 'actions': return renderActionsStep()
      case 'review': return renderReviewStep()
      case 'complete': return renderCompleteStep()
      default: return renderIntroStep()
    }
  }

  const CurrentIcon = stepConfig[currentStep].icon

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => setLocation('/dashboard/browse-templates')}
          className="mb-4"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back to Templates
        </Button>

        {currentStep !== 'complete' && (
          <>
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <CurrentIcon className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">{stepConfig[currentStep].title}</h1>
                <p className="text-muted-foreground text-sm">{stepConfig[currentStep].subtitle}</p>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex justify-between text-xs text-muted-foreground mb-2">
                <span>Step {Object.keys(stepConfig).indexOf(currentStep) + 1} of 8</span>
                <span>{stepConfig[currentStep].progress}% Complete</span>
              </div>
              <Progress value={stepConfig[currentStep].progress} className="h-2" />
            </div>
          </>
        )}
      </div>

      {/* Content */}
      <div className="bg-card rounded-lg shadow-sm">
        <div className="p-6">
          {getCurrentStepContent()}
        </div>
      </div>
    </div>
  )
}