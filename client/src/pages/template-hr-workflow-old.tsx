import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { useLocation } from 'wouter'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  Users, 
  FileText, 
  FolderOpen, 
  Brain, 
  Star,
  Settings,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  Zap,
  AlertCircle,
  Eye,
  EyeOff,
  Shield,
  Database,
  UserCheck
} from 'lucide-react'
import { <PERSON><PERSON><PERSON>gledrive, SiGooglesheets } from 'react-icons/si'
import { ActionsSelector } from '@/components/actions-selector'

type StepType = 'intro' | 'naming' | 'drive-monitoring' | 'ai-scoring' | 'evaluation-settings' | 'sheets-integration' | 'actions-question' | 'actions' | 'review' | 'complete'

interface HRWorkflowConfig {
  name: string
  googleAuth: {
    isAuthenticated: boolean
    userEmail: string
    userName: string
    accountId: string
  }
  driveMonitoring: {
    folderId: string
    folderPath: string
    fileTypes: string[]
    monitoringFrequency: number
    timeUnit: 'minutes' | 'hours' | 'days'
    filesList: string[]
  }
  aiScoring: {
    provider: 'openai' | 'anthropic' | 'google'
    model: string
    isConfigured: boolean
    extractionFields: {
      personalInfo: boolean
      experience: boolean
      education: boolean
      skills: boolean
      certifications: boolean
      languages: boolean
    }
  }
  evaluationSettings: {
    scoringCriteria: {
      experience: { weight: number, required: boolean }
      education: { weight: number, required: boolean }
      skills: { weight: number, required: boolean }
      cultural: { weight: number, required: boolean }
    }
    passingScore: number
    flagForReview: {
      enabled: boolean
      threshold: number
      reviewerEmail: string
    }
    customPrompt: string
  }
  sheetsIntegration: {
    spreadsheetId: string
    sheetName: string
    columns: {
      candidateName: string
      email: string
      phone: string
      totalScore: string
      experienceScore: string
      educationScore: string
      skillsScore: string
      status: string
      reviewFlag: string
      resumeLink: string
    }
    filesList: string[]
  }
}

const defaultConfig: HRWorkflowConfig = {
  name: '',
  googleAuth: {
    isAuthenticated: false,
    userEmail: '',
    userName: '',
    accountId: ''
  },
  driveMonitoring: {
    folderId: '',
    folderPath: '',
    fileTypes: ['pdf', 'doc', 'docx'],
    monitoringFrequency: 30,
    timeUnit: 'minutes',
    filesList: []
  },
  aiScoring: {
    provider: 'openai',
    model: 'gpt-4o',
    isConfigured: false,
    extractionFields: {
      personalInfo: true,
      experience: true,
      education: true,
      skills: true,
      certifications: false,
      languages: false
    }
  },
  evaluationSettings: {
    scoringCriteria: {
      experience: { weight: 40, required: true },
      education: { weight: 25, required: true },
      skills: { weight: 30, required: true },
      cultural: { weight: 5, required: false }
    },
    passingScore: 70,
    flagForReview: {
      enabled: true,
      threshold: 85,
      reviewerEmail: ''
    },
    customPrompt: 'Evaluate this candidate based on job requirements and industry standards.'
  },
  sheetsIntegration: {
    spreadsheetId: '',
    sheetName: 'HR_Candidates',
    columns: {
      candidateName: 'A',
      email: 'B',
      phone: 'C',
      totalScore: 'D',
      experienceScore: 'E',
      educationScore: 'F',
      skillsScore: 'G',
      status: 'H',
      reviewFlag: 'I',
      resumeLink: 'J'
    },
    filesList: []
  }
}

const steps: { id: StepType; title: string; icon: React.ComponentType<{ className?: string }> }[] = [
  { id: 'intro', title: 'Introduction', icon: Users },
  { id: 'naming', title: 'Name Automation', icon: FileText },
  { id: 'drive-monitoring', title: 'Google Drive Monitoring', icon: FolderOpen },
  { id: 'ai-scoring', title: 'CV Parsing & AI Scoring', icon: Brain },
  { id: 'evaluation-settings', title: 'Candidate Evaluation', icon: Star },
  { id: 'sheets-integration', title: 'Google Sheets Integration', icon: Database },
  { id: 'review', title: 'Review', icon: Eye },
  { id: 'complete', title: 'Complete', icon: Zap }
]

const aiModels = {
  openai: ['gpt-4o', 'gpt-4', 'gpt-3.5-turbo'],
  anthropic: ['claude-sonnet-4-20250514', 'claude-3-5-sonnet-20241022', 'claude-3-haiku-20240307'],
  google: ['gemini-2.5-pro', 'gemini-2.5-flash', 'gemini-1.5-pro']
}

const fileTypes = [
  { id: 'pdf', label: 'PDF Documents', extension: 'pdf' },
  { id: 'doc', label: 'Word Documents (.doc)', extension: 'doc' },
  { id: 'docx', label: 'Word Documents (.docx)', extension: 'docx' },
  { id: 'txt', label: 'Text Files', extension: 'txt' }
]

export default function HRWorkflowTemplate() {
  const [currentStep, setCurrentStep] = useState<StepType>('intro')
  const [config, setConfig] = useState<HRWorkflowConfig>(defaultConfig)
  const [, setLocation] = useLocation()

  const currentStepIndex = steps.findIndex(step => step.id === currentStep)
  const progress = ((currentStepIndex + 1) / steps.length) * 100

  const handleNext = () => {
    const nextIndex = currentStepIndex + 1
    if (nextIndex < steps.length) {
      setCurrentStep(steps[nextIndex].id)
    }
  }

  const handlePrevious = () => {
    const prevIndex = currentStepIndex - 1
    if (prevIndex >= 0) {
      setCurrentStep(steps[prevIndex].id)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && currentStep !== 'complete') {
      handleNext()
    }
  }

  const updateConfig = (section: keyof HRWorkflowConfig | '', updates: any) => {
    if (section === '') {
      setConfig(prev => ({ ...prev, ...updates }))
    } else {
      setConfig(prev => ({
        ...prev,
        [section]: { ...prev[section], ...updates }
      }))
    }
  }

  const renderIntroStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="flex justify-center">
        <div className="p-4 bg-blue-100 dark:bg-blue-900 rounded-full">
          <Users className="h-12 w-12 text-blue-600" />
        </div>
      </div>
      
      <div>
        <h2 className="text-2xl font-bold mb-2">AI Automated HR Workflow</h2>
        <p className="text-muted-foreground mb-6">
          Automatically monitor Google Drive for resumes, extract candidate information using AI, score applicants, and log results to Google Sheets
        </p>
      </div>

      <div className="grid md:grid-cols-4 gap-4 text-left">
        <Card>
          <CardHeader className="pb-3">
            <FolderOpen className="h-6 w-6 text-blue-500 mb-2" />
            <CardTitle className="text-sm">Drive Monitoring</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Automatically detect new resume uploads in designated Google Drive folders
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <Brain className="h-6 w-6 text-purple-500 mb-2" />
            <CardTitle className="text-sm">AI CV Parsing</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Extract candidate information and score resumes using advanced AI models
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <Star className="h-6 w-6 text-yellow-500 mb-2" />
            <CardTitle className="text-sm">Smart Evaluation</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Configurable scoring criteria with automatic flagging for human review
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <Database className="h-6 w-6 text-green-500 mb-2" />
            <CardTitle className="text-sm">Sheets Integration</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Structured logging of all candidate data and scores in Google Sheets
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-center">
        <Button onClick={handleNext} className="w-full max-w-md">
          Get Started
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderNamingStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <FileText className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h2 className="text-xl font-semibold mb-2">Name Your Automation</h2>
        <p className="text-muted-foreground">
          Give your HR workflow automation a memorable and descriptive name
        </p>
      </div>

      <div className="space-y-4 max-w-md mx-auto">
        <div>
          <Label htmlFor="automation-name">Automation Name *</Label>
          <Input
            id="automation-name"
            placeholder="e.g., Software Engineer Resume Screening, Marketing Team Hiring"
            value={config.name}
            onChange={(e) => updateConfig('', { name: e.target.value })}
            onKeyDown={handleKeyPress}
            className="mt-2"
            autoFocus
          />
          <p className="text-xs text-muted-foreground mt-1">
            Choose a name that clearly describes the role or department this workflow serves
          </p>
        </div>
      </div>

      <div className="flex gap-3 justify-center">
        <Button variant="outline" onClick={handlePrevious}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <Button onClick={handleNext} disabled={!config.name}>
          Continue to Drive Setup
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderDriveMonitoringStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <div className="flex justify-center items-center gap-2 mb-4">
          <SiGoogledrive className="h-8 w-8 text-green-600" />
          <h3 className="text-xl font-semibold">Google Drive Monitoring</h3>
        </div>
        <p className="text-muted-foreground">
          Configure which Google Drive folder to monitor for new resume uploads
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-green-600" />
            Authentication Type *
          </CardTitle>
          <CardDescription>
            Connect your Google account to access Google Drive folders
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <RadioGroup value="google" className="space-y-2">
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="google" id="google-auth" />
              <Label htmlFor="google-auth">Google Account Authentication</Label>
            </div>
          </RadioGroup>
          
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <p className="text-sm text-muted-foreground mb-3">
              Note: Select existing Google account from below or Sign in with a different account
            </p>
            
            {config.googleAuth.isAuthenticated ? (
              <div className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded border">
                <span className="text-sm">Google account: {config.googleAuth.userName} ({config.googleAuth.userEmail})</span>
                <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">Connected</Badge>
              </div>
            ) : (
              <Button 
                onClick={() => updateConfig('googleAuth', { 
                  isAuthenticated: true, 
                  userEmail: '<EMAIL>', 
                  userName: 'John Doe',
                  accountId: 'google_123'
                })}
                className="w-full"
              >
                <SiGoogledrive className="mr-2 h-4 w-4" />
                Connect Google Account
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Folder Configuration</CardTitle>
          <CardDescription>
            Specify which Google Drive folder to monitor for resume uploads
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="folder-path">Folder Path</Label>
            <Input
              id="folder-path"
              placeholder="/HR/Resumes or /Hiring/Applications"
              value={config.driveMonitoring.folderPath}
              onChange={(e) => updateConfig('driveMonitoring', { folderPath: e.target.value })}
            />
            <p className="text-xs text-muted-foreground mt-1">
              The folder path where resumes will be uploaded
            </p>
          </div>

          <div>
            <Label>Supported File Types</Label>
            <div className="grid grid-cols-2 gap-3 mt-2">
              {fileTypes.map((type) => (
                <div key={type.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={type.id}
                    checked={config.driveMonitoring.fileTypes.includes(type.extension)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        updateConfig('driveMonitoring', {
                          fileTypes: [...config.driveMonitoring.fileTypes, type.extension]
                        })
                      } else {
                        updateConfig('driveMonitoring', {
                          fileTypes: config.driveMonitoring.fileTypes.filter(t => t !== type.extension)
                        })
                      }
                    }}
                  />
                  <Label htmlFor={type.id} className="text-sm">{type.label}</Label>
                </div>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="frequency">Check Frequency</Label>
              <Input
                id="frequency"
                type="number"
                min="1"
                value={config.driveMonitoring.monitoringFrequency}
                onChange={(e) => updateConfig('driveMonitoring', { monitoringFrequency: parseInt(e.target.value) })}
              />
            </div>
            <div>
              <Label htmlFor="time-unit">Time Unit</Label>
              <Select
                value={config.driveMonitoring.timeUnit}
                onValueChange={(value: 'minutes' | 'hours' | 'days') => 
                  updateConfig('driveMonitoring', { timeUnit: value })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="minutes">Minutes</SelectItem>
                  <SelectItem value="hours">Hours</SelectItem>
                  <SelectItem value="days">Days</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded">
            <h4 className="font-medium text-sm mb-2">Files</h4>
            <p className="text-sm text-muted-foreground">
              There are no files, please refresh
            </p>
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button variant="outline" onClick={handlePrevious}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={handleNext} disabled={!config.googleAuth.isAuthenticated || !config.driveMonitoring.folderPath}>
          Continue to AI Setup
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderAIScoringStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <div className="flex justify-center items-center gap-2 mb-4">
          <Brain className="h-8 w-8 text-purple-600" />
          <h3 className="text-xl font-semibold">CV Parsing & AI Scoring</h3>
        </div>
        <p className="text-muted-foreground">
          Configure AI models to extract candidate information and score resumes
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>AI Model Configuration</CardTitle>
          <CardDescription>
            Choose the AI provider and model for CV parsing and scoring
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="ai-provider">AI Provider</Label>
            <Select
              value={config.aiScoring.provider}
              onValueChange={(value: 'openai' | 'anthropic' | 'google') => 
                updateConfig('aiScoring', { provider: value, model: aiModels[value][0] })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="openai">OpenAI</SelectItem>
                <SelectItem value="anthropic">Anthropic</SelectItem>
                <SelectItem value="google">Google AI</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="ai-model">AI Model</Label>
            <Select
              value={config.aiScoring.model}
              onValueChange={(value) => updateConfig('aiScoring', { model: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {aiModels[config.aiScoring.provider].map((model) => (
                  <SelectItem key={model} value={model}>{model}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="ai-configured"
              checked={config.aiScoring.isConfigured}
              onCheckedChange={(checked) => updateConfig('aiScoring', { isConfigured: !!checked })}
            />
            <Label htmlFor="ai-configured" className="text-sm">
              I have configured my API key for this AI provider
            </Label>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Information Extraction</CardTitle>
          <CardDescription>
            Select what information to extract from candidate resumes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            {Object.entries(config.aiScoring.extractionFields).map(([field, enabled]) => (
              <div key={field} className="flex items-center space-x-2">
                <Checkbox
                  id={field}
                  checked={enabled}
                  onCheckedChange={(checked) => 
                    updateConfig('aiScoring', {
                      extractionFields: {
                        ...config.aiScoring.extractionFields,
                        [field]: !!checked
                      }
                    })
                  }
                />
                <Label htmlFor={field} className="text-sm capitalize">
                  {field.replace(/([A-Z])/g, ' $1').trim()}
                </Label>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button variant="outline" onClick={handlePrevious}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={handleNext} disabled={!config.aiScoring.isConfigured}>
          Continue to Evaluation
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderEvaluationSettingsStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <div className="flex justify-center items-center gap-2 mb-4">
          <Star className="h-8 w-8 text-yellow-600" />
          <h3 className="text-xl font-semibold">Candidate Evaluation Settings</h3>
        </div>
        <p className="text-muted-foreground">
          Configure scoring criteria and thresholds for candidate evaluation
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Scoring Criteria</CardTitle>
          <CardDescription>
            Set weights for different evaluation criteria (total should be 100%)
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {Object.entries(config.evaluationSettings.scoringCriteria).map(([criteria, settings]) => (
            <div key={criteria} className="grid grid-cols-3 gap-4 items-center">
              <Label className="capitalize">{criteria} Score</Label>
              <Input
                type="number"
                min="0"
                max="100"
                value={settings.weight}
                onChange={(e) => 
                  updateConfig('evaluationSettings', {
                    scoringCriteria: {
                      ...config.evaluationSettings.scoringCriteria,
                      [criteria]: {
                        ...settings,
                        weight: parseInt(e.target.value) || 0
                      }
                    }
                  })
                }
                className="w-20"
              />
              <div className="flex items-center space-x-2">
                <Checkbox
                  id={`${criteria}-required`}
                  checked={settings.required}
                  onCheckedChange={(checked) => 
                    updateConfig('evaluationSettings', {
                      scoringCriteria: {
                        ...config.evaluationSettings.scoringCriteria,
                        [criteria]: {
                          ...settings,
                          required: !!checked
                        }
                      }
                    })
                  }
                />
                <Label htmlFor={`${criteria}-required`} className="text-sm">Required</Label>
              </div>
            </div>
          ))}
          <div className="text-sm text-muted-foreground">
            Current total: {Object.values(config.evaluationSettings.scoringCriteria).reduce((sum, c) => sum + c.weight, 0)}%
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Review Thresholds</CardTitle>
          <CardDescription>
            Set automatic thresholds for candidate progression and human review
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="passing-score">Minimum Passing Score (%)</Label>
            <Input
              id="passing-score"
              type="number"
              min="0"
              max="100"
              value={config.evaluationSettings.passingScore}
              onChange={(e) => updateConfig('evaluationSettings', { passingScore: parseInt(e.target.value) || 0 })}
            />
            <p className="text-xs text-muted-foreground mt-1">
              Candidates below this score will be automatically rejected
            </p>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="flag-review"
              checked={config.evaluationSettings.flagForReview.enabled}
              onCheckedChange={(checked) => 
                updateConfig('evaluationSettings', {
                  flagForReview: {
                    ...config.evaluationSettings.flagForReview,
                    enabled: !!checked
                  }
                })
              }
            />
            <Label htmlFor="flag-review">Flag high-scoring candidates for human review</Label>
          </div>

          {config.evaluationSettings.flagForReview.enabled && (
            <div className="pl-6 space-y-3">
              <div>
                <Label htmlFor="review-threshold">Review Threshold (%)</Label>
                <Input
                  id="review-threshold"
                  type="number"
                  min="0"
                  max="100"
                  value={config.evaluationSettings.flagForReview.threshold}
                  onChange={(e) => 
                    updateConfig('evaluationSettings', {
                      flagForReview: {
                        ...config.evaluationSettings.flagForReview,
                        threshold: parseInt(e.target.value) || 0
                      }
                    })
                  }
                />
              </div>
              <div>
                <Label htmlFor="reviewer-email">Reviewer Email</Label>
                <Input
                  id="reviewer-email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={config.evaluationSettings.flagForReview.reviewerEmail}
                  onChange={(e) => 
                    updateConfig('evaluationSettings', {
                      flagForReview: {
                        ...config.evaluationSettings.flagForReview,
                        reviewerEmail: e.target.value
                      }
                    })
                  }
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Custom Evaluation Prompt</CardTitle>
          <CardDescription>
            Customize the AI prompt for candidate evaluation (optional)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder="Evaluate this candidate based on our job requirements and company culture..."
            value={config.evaluationSettings.customPrompt}
            onChange={(e) => updateConfig('evaluationSettings', { customPrompt: e.target.value })}
            rows={3}
          />
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button variant="outline" onClick={handlePrevious}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={handleNext}>
          Continue to Sheets Integration
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderSheetsIntegrationStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <div className="flex justify-center items-center gap-2 mb-4">
          <SiGooglesheets className="h-8 w-8 text-green-600" />
          <h3 className="text-xl font-semibold">Google Sheets Integration</h3>
        </div>
        <p className="text-muted-foreground">
          Configure where candidate data and scores will be logged
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Spreadsheet Configuration</CardTitle>
          <CardDescription>
            Select or create a Google Sheets spreadsheet for candidate tracking
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="spreadsheet-id">Spreadsheet ID</Label>
            <Input
              id="spreadsheet-id"
              placeholder="1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
              value={config.sheetsIntegration.spreadsheetId}
              onChange={(e) => updateConfig('sheetsIntegration', { spreadsheetId: e.target.value })}
            />
            <p className="text-xs text-muted-foreground mt-1">
              Found in the Google Sheets URL between /d/ and /edit
            </p>
          </div>

          <div>
            <Label htmlFor="sheet-name">Sheet Name</Label>
            <Input
              id="sheet-name"
              value={config.sheetsIntegration.sheetName}
              onChange={(e) => updateConfig('sheetsIntegration', { sheetName: e.target.value })}
            />
          </div>

          <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded">
            <h4 className="font-medium text-sm mb-2">Files</h4>
            <p className="text-sm text-muted-foreground">
              There are no files, please refresh
            </p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Column Mapping</CardTitle>
          <CardDescription>
            Map data fields to specific columns in your spreadsheet
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            {Object.entries(config.sheetsIntegration.columns).map(([field, column]) => (
              <div key={field}>
                <Label htmlFor={field} className="capitalize text-sm">
                  {field.replace(/([A-Z])/g, ' $1').trim()}
                </Label>
                <Input
                  id={field}
                  value={column}
                  onChange={(e) => 
                    updateConfig('sheetsIntegration', {
                      columns: {
                        ...config.sheetsIntegration.columns,
                        [field]: e.target.value
                      }
                    })
                  }
                  placeholder="A, B, C..."
                  className="w-16"
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button variant="outline" onClick={handlePrevious}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={handleNext} disabled={!config.sheetsIntegration.spreadsheetId}>
          Review Configuration
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderReviewStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Eye className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h2 className="text-xl font-semibold mb-2">Review Your Configuration</h2>
        <p className="text-muted-foreground">
          Please review all settings before creating your HR automation workflow
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Drive Monitoring</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div><strong>Folder:</strong> {config.driveMonitoring.folderPath || 'Not specified'}</div>
            <div><strong>File Types:</strong> {config.driveMonitoring.fileTypes.join(', ')}</div>
            <div><strong>Check Frequency:</strong> Every {config.driveMonitoring.monitoringFrequency} {config.driveMonitoring.timeUnit}</div>
            <div><strong>Google Account:</strong> {config.googleAuth.isAuthenticated ? '✓ Connected' : '✗ Not connected'}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">AI Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div><strong>Provider:</strong> {config.aiScoring.provider}</div>
            <div><strong>Model:</strong> {config.aiScoring.model}</div>
            <div><strong>Status:</strong> {config.aiScoring.isConfigured ? '✓ Configured' : '✗ Not configured'}</div>
            <div><strong>Extract Fields:</strong> {Object.entries(config.aiScoring.extractionFields).filter(([_, enabled]) => enabled).map(([field]) => field).join(', ')}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Evaluation Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div><strong>Passing Score:</strong> {config.evaluationSettings.passingScore}%</div>
            <div><strong>Experience Weight:</strong> {config.evaluationSettings.scoringCriteria.experience.weight}%</div>
            <div><strong>Education Weight:</strong> {config.evaluationSettings.scoringCriteria.education.weight}%</div>
            <div><strong>Skills Weight:</strong> {config.evaluationSettings.scoringCriteria.skills.weight}%</div>
            <div><strong>Human Review:</strong> {config.evaluationSettings.flagForReview.enabled ? `Above ${config.evaluationSettings.flagForReview.threshold}%` : 'Disabled'}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Sheets Integration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div><strong>Spreadsheet ID:</strong> {config.sheetsIntegration.spreadsheetId || 'Not specified'}</div>
            <div><strong>Sheet Name:</strong> {config.sheetsIntegration.sheetName}</div>
            <div><strong>Columns Configured:</strong> {Object.keys(config.sheetsIntegration.columns).length}</div>
          </CardContent>
        </Card>
      </div>

      <div className="flex gap-3">
        <Button variant="outline" onClick={handlePrevious}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={handleNext} className="flex-1">
          Create HR Automation
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderCompleteStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="flex justify-center">
        <div className="p-4 bg-green-100 dark:bg-green-900 rounded-full">
          <CheckCircle className="h-12 w-12 text-green-600" />
        </div>
      </div>
      
      <div>
        <h2 className="text-2xl font-bold mb-2">Automation Created!</h2>
        <p className="text-muted-foreground mb-6">
          Your HR automation workflow is now active and monitoring for new resumes
        </p>
      </div>

      <Card className="text-left max-w-md mx-auto">
        <CardHeader>
          <CardTitle>What happens next?</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-start gap-3">
            <div className="p-1 bg-blue-100 dark:bg-blue-900 rounded">
              <FolderOpen className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <p className="font-medium">Drive Monitoring</p>
              <p className="text-sm text-muted-foreground">
                System will monitor your Google Drive folder for new resume uploads
              </p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <div className="p-1 bg-purple-100 dark:bg-purple-900 rounded">
              <Brain className="h-4 w-4 text-purple-600" />
            </div>
            <div>
              <p className="font-medium">AI Processing</p>
              <p className="text-sm text-muted-foreground">
                Each resume will be parsed and scored using your configured AI model
              </p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <div className="p-1 bg-green-100 dark:bg-green-900 rounded">
              <Database className="h-4 w-4 text-green-600" />
            </div>
            <div>
              <p className="font-medium">Data Logging</p>
              <p className="text-sm text-muted-foreground">
                Results will be automatically logged to your Google Sheets
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3 justify-center">
        <Button variant="outline" onClick={() => setLocation('/dashboard/automations')}>
          View All Automations
        </Button>
        <Button onClick={() => setLocation('/dashboard/templates')}>
          Browse More Templates
        </Button>
      </div>
    </motion.div>
  )

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'intro':
        return renderIntroStep()
      case 'naming':
        return renderNamingStep()
      case 'drive-monitoring':
        return renderDriveMonitoringStep()
      case 'ai-scoring':
        return renderAIScoringStep()
      case 'evaluation-settings':
        return renderEvaluationSettingsStep()
      case 'sheets-integration':
        return renderSheetsIntegrationStep()
      case 'review':
        return renderReviewStep()
      case 'complete':
        return renderCompleteStep()
      default:
        return renderIntroStep()
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Users className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold">AI Automated HR Workflow</h1>
          </div>
          <p className="text-muted-foreground">
            Automatically monitor, analyze, and score candidate resumes using AI
          </p>
        </div>

        {/* Progress */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <span className="text-sm font-medium">
              Step {currentStepIndex + 1} of {steps.length}
            </span>
            <span className="text-sm text-muted-foreground">
              {Math.round(progress)}% complete
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Steps Navigation */}
        <div className="mb-8">
          <div className="flex justify-between items-center overflow-x-auto pb-2">
            {steps.map((step, index) => {
              const Icon = step.icon
              const isActive = index === currentStepIndex
              const isCompleted = index < currentStepIndex
              
              return (
                <div key={step.id} className="flex flex-col items-center min-w-0 flex-1">
                  <div className={`
                    w-10 h-10 rounded-full flex items-center justify-center mb-2
                    ${isActive ? 'bg-blue-600 text-white' : 
                      isCompleted ? 'bg-green-600 text-white' : 
                      'bg-gray-200 dark:bg-gray-700 text-gray-400'}
                  `}>
                    {isCompleted ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <Icon className="h-5 w-5" />
                    )}
                  </div>
                  <span className={`text-xs text-center max-w-20 ${
                    isActive ? 'font-medium' : 'text-muted-foreground'
                  }`}>
                    {step.title}
                  </span>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto">
          {renderCurrentStep()}
        </div>
      </div>
    </div>
  )
}