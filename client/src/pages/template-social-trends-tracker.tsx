import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Progress } from '@/components/ui/progress'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { useLocation } from 'wouter'
import { motion } from 'framer-motion'
import { 
  ArrowRight, ArrowLeft, Check, ChevronLeft, Zap, Mail, 
  TrendingUp, Globe, Calendar, Bot, Bell, Filter,
  Twitter, Hash, BarChart3, MessageCircle, Settings, Plus, X, Table, Key
} from 'lucide-react'


type StepType = 'intro' | 'naming' | 'api-setup' | 'trend-config' | 'ai-summary' | 'email-delivery' | 'actions-question' | 'actions' | 'review' | 'complete'

interface TrendTrackerConfig {
  name: string
  rapidApiConfig: {
    apiKey: string
    endpoint: 'twitter-trends' | 'google-trends' | 'reddit-trends' | 'multi-platform'
    region: string
    language: string
  }
  trendSettings: {
    platforms: string[]
    numberOfTrends: number
    includeHashtags: boolean
    includeKeywords: boolean
    filterCategories: string[]
    excludeTerms: string[]
    minEngagement: number
  }
  aiSummary: {
    provider: 'openai' | 'anthropic' | 'google'
    model: string
    summaryStyle: 'executive' | 'detailed' | 'bullet-points' | 'narrative'
    includeAnalysis: boolean
    includeRecommendations: boolean
    tone: string
    length: string
  }
  emailDelivery: {
    schedule: 'daily' | 'weekly' | 'custom'
    time: string
    dayOfWeek?: string
    recipients: string[]
    subject: string
    includeCharts: boolean
    format: 'html' | 'plain' | 'markdown'
  }
  selectedActionsList: Array<{
    id: string
    type: string
    name: string
    description: string
    config: Record<string, any>
  }>
  wantsActions: boolean | null
}

const defaultConfig: TrendTrackerConfig = {
  name: '',
  rapidApiConfig: {
    apiKey: '',
    endpoint: 'twitter-trends',
    region: 'US',
    language: 'en'
  },
  trendSettings: {
    platforms: ['twitter'],
    numberOfTrends: 10,
    includeHashtags: true,
    includeKeywords: true,
    filterCategories: [],
    excludeTerms: [],
    minEngagement: 1000
  },
  aiSummary: {
    provider: 'openai',
    model: 'gpt-4o',
    summaryStyle: 'executive',
    includeAnalysis: true,
    includeRecommendations: true,
    tone: 'professional',
    length: 'medium'
  },
  emailDelivery: {
    schedule: 'daily',
    time: '09:00',
    recipients: [],
    subject: 'Daily Social Media Trends Report',
    includeCharts: true,
    format: 'html'
  },
  selectedActionsList: [],
  wantsActions: null
}

const steps: { id: StepType; title: string; icon: React.ComponentType<{ className?: string }> }[] = [
  { id: 'intro', title: 'Introduction', icon: TrendingUp },
  { id: 'naming', title: 'Name Your Automation', icon: TrendingUp },
  { id: 'api-setup', title: 'RapidAPI Setup', icon: Key },
  { id: 'trend-config', title: 'Trend Configuration', icon: Filter },
  { id: 'ai-summary', title: 'AI Summary Settings', icon: Bot },
  { id: 'email-delivery', title: 'Email Delivery', icon: Mail },
  { id: 'actions-question', title: 'Additional Actions', icon: Zap },
  { id: 'actions', title: 'Configure Actions', icon: Settings },
  { id: 'review', title: 'Review & Create', icon: Check },
  { id: 'complete', title: 'Complete', icon: Check }
]

const stepConfig: Record<StepType, { title: string; subtitle: string; progress: number; icon: React.ComponentType<{ className?: string }> }> = {
  intro: { title: 'Social Media Trends Tracker', subtitle: 'Stay ahead with AI-powered trend analysis', progress: 0, icon: TrendingUp },
  naming: { title: 'Name Your Automation', subtitle: 'Give your trend tracker a memorable name', progress: 10, icon: TrendingUp },
  'api-setup': { title: 'Connect to RapidAPI', subtitle: 'Set up your trend data source', progress: 25, icon: Key },
  'trend-config': { title: 'Configure Trend Settings', subtitle: 'Customize what trends to track', progress: 40, icon: Filter },
  'ai-summary': { title: 'AI Summary Settings', subtitle: 'Configure how trends are analyzed', progress: 55, icon: Bot },
  'email-delivery': { title: 'Email Delivery Setup', subtitle: 'Schedule your trend reports', progress: 70, icon: Mail },
  'actions-question': { title: 'Additional Actions', subtitle: 'Extend your automation', progress: 80, icon: Zap },
  actions: { title: 'Configure Actions', subtitle: 'Set up your selected actions', progress: 85, icon: Settings },
  review: { title: 'Review Configuration', subtitle: 'Confirm your settings', progress: 95, icon: Check },
  complete: { title: 'Automation Created!', subtitle: 'Your trend tracker is ready', progress: 100, icon: Check }
}

export default function SocialTrendsTrackerTemplate() {
  const [currentStep, setCurrentStep] = useState<StepType>('intro')
  const [config, setConfig] = useState<TrendTrackerConfig>(defaultConfig)
  const [, setLocation] = useLocation()
  const [showActionConfig, setShowActionConfig] = useState<number | null>(null)
  const [wantsMoreActions, setWantsMoreActions] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  const currentStepIndex = steps.findIndex(s => s.id === currentStep)
  const progress = stepConfig[currentStep].progress

  const nextStep = () => {
    if (currentStep === 'actions-question') {
      if (config.wantsActions === false) {
        setCurrentStep('review')
        return
      }
    }
    
    if (currentStep === 'actions' && !wantsMoreActions && config.selectedActionsList.length > 0) {
      setCurrentStep('review')
      return
    }

    const currentIndex = steps.findIndex(s => s.id === currentStep)
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1].id)
    }
  }

  const prevStep = () => {
    if (currentStep === 'review' && config.wantsActions === false) {
      setCurrentStep('actions-question')
      return
    }
    
    const currentIndex = steps.findIndex(s => s.id === currentStep)
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1].id)
    }
  }

  const renderIntro = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto">
        <TrendingUp className="h-10 w-10 text-white" />
      </div>
      <div>
        <h2 className="text-2xl font-bold mb-2">Social Media Trends Tracker</h2>
        <p className="text-muted-foreground max-w-md mx-auto">
          This template creates an automated workflow that monitors social media trends across platforms, 
          analyzes them with AI, and delivers comprehensive reports via email at your preferred schedule.
        </p>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Key className="h-8 w-8 mx-auto mb-2 text-blue-600" />
          <p className="text-xs font-medium">RapidAPI</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <TrendingUp className="h-8 w-8 mx-auto mb-2 text-purple-600" />
          <p className="text-xs font-medium">Trend Analysis</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Bot className="h-8 w-8 mx-auto mb-2 text-green-600" />
          <p className="text-xs font-medium">AI Summary</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Mail className="h-8 w-8 mx-auto mb-2 text-red-600" />
          <p className="text-xs font-medium">Email Reports</p>
        </div>
      </div>

      <Button 
        onClick={nextStep} 
        size="lg"
        className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
      >
        Start Setup
        <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
    </motion.div>
  )

  const renderNaming = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">Let's name your trend tracker</h3>
        <p className="text-muted-foreground text-sm">
          Choose a name that describes what trends you're monitoring
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="automation-name">Automation Name</Label>
          <Input
            id="automation-name"
            placeholder="e.g., Daily Tech Trends Monitor"
            value={config.name}
            onChange={(e) => setConfig({ ...config, name: e.target.value })}
            className="mt-1"
          />
          <p className="text-xs text-muted-foreground mt-1">
            This name will help you identify this automation later
          </p>
        </div>
      </div>

      <div className="flex gap-3">
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          className="w-full"
          disabled={!config.name.trim()}
        >
          Continue
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderApiSetup = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Key className="h-12 w-12 mx-auto mb-3 text-blue-600" />
        <h3 className="text-lg font-semibold mb-2">Connect to RapidAPI</h3>
        <p className="text-muted-foreground text-sm">
          Set up your connection to access social media trend data
        </p>
      </div>

      <Card>
        <CardContent className="pt-6 space-y-4">
          <div>
            <Label htmlFor="api-key">RapidAPI Key</Label>
            <Input
              id="api-key"
              type="password"
              placeholder="Enter your RapidAPI key"
              value={config.rapidApiConfig.apiKey}
              onChange={(e) => setConfig({
                ...config,
                rapidApiConfig: { ...config.rapidApiConfig, apiKey: e.target.value }
              })}
              className="mt-1"
            />
            <p className="text-xs text-muted-foreground mt-1">
              Get your API key from rapidapi.com/hub
            </p>
          </div>

          <div>
            <Label htmlFor="endpoint">Trend Data Source</Label>
            <Select
              value={config.rapidApiConfig.endpoint}
              onValueChange={(value: any) => setConfig({
                ...config,
                rapidApiConfig: { ...config.rapidApiConfig, endpoint: value }
              })}
            >
              <SelectTrigger id="endpoint" className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="twitter-trends">Twitter Trends API</SelectItem>
                <SelectItem value="google-trends">Google Trends API</SelectItem>
                <SelectItem value="reddit-trends">Reddit Trends API</SelectItem>
                <SelectItem value="multi-platform">Multi-Platform Aggregator</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="region">Region</Label>
              <Select
                value={config.rapidApiConfig.region}
                onValueChange={(value) => setConfig({
                  ...config,
                  rapidApiConfig: { ...config.rapidApiConfig, region: value }
                })}
              >
                <SelectTrigger id="region" className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="US">United States</SelectItem>
                  <SelectItem value="UK">United Kingdom</SelectItem>
                  <SelectItem value="CA">Canada</SelectItem>
                  <SelectItem value="AU">Australia</SelectItem>
                  <SelectItem value="Global">Global</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="language">Language</Label>
              <Select
                value={config.rapidApiConfig.language}
                onValueChange={(value) => setConfig({
                  ...config,
                  rapidApiConfig: { ...config.rapidApiConfig, language: value }
                })}
              >
                <SelectTrigger id="language" className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="en">English</SelectItem>
                  <SelectItem value="es">Spanish</SelectItem>
                  <SelectItem value="fr">French</SelectItem>
                  <SelectItem value="de">German</SelectItem>
                  <SelectItem value="ja">Japanese</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          className="w-full"
          disabled={!config.rapidApiConfig.apiKey}
        >
          Continue
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderTrendConfig = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Filter className="h-12 w-12 mx-auto mb-3 text-purple-600" />
        <h3 className="text-lg font-semibold mb-2">Configure Trend Tracking</h3>
        <p className="text-muted-foreground text-sm">
          Customize what trends you want to monitor
        </p>
      </div>

      <Card>
        <CardContent className="pt-6 space-y-6">
          <div>
            <Label className="mb-3 block">Select Platforms</Label>
            <div className="space-y-2">
              {['twitter', 'reddit', 'google', 'tiktok'].map(platform => (
                <div key={platform} className="flex items-center space-x-2">
                  <Checkbox
                    id={platform}
                    checked={config.trendSettings.platforms.includes(platform)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setConfig({
                          ...config,
                          trendSettings: {
                            ...config.trendSettings,
                            platforms: [...config.trendSettings.platforms, platform]
                          }
                        })
                      } else {
                        setConfig({
                          ...config,
                          trendSettings: {
                            ...config.trendSettings,
                            platforms: config.trendSettings.platforms.filter(p => p !== platform)
                          }
                        })
                      }
                    }}
                  />
                  <Label htmlFor={platform} className="capitalize cursor-pointer">
                    {platform}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div>
            <Label htmlFor="num-trends">Number of Trends to Track</Label>
            <Select
              value={config.trendSettings.numberOfTrends.toString()}
              onValueChange={(value) => setConfig({
                ...config,
                trendSettings: { ...config.trendSettings, numberOfTrends: parseInt(value) }
              })}
            >
              <SelectTrigger id="num-trends" className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">Top 5</SelectItem>
                <SelectItem value="10">Top 10</SelectItem>
                <SelectItem value="20">Top 20</SelectItem>
                <SelectItem value="50">Top 50</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="hashtags">Include Hashtags</Label>
              <Switch
                id="hashtags"
                checked={config.trendSettings.includeHashtags}
                onCheckedChange={(checked) => setConfig({
                  ...config,
                  trendSettings: { ...config.trendSettings, includeHashtags: checked }
                })}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="keywords">Include Keywords</Label>
              <Switch
                id="keywords"
                checked={config.trendSettings.includeKeywords}
                onCheckedChange={(checked) => setConfig({
                  ...config,
                  trendSettings: { ...config.trendSettings, includeKeywords: checked }
                })}
              />
            </div>
          </div>

          <div>
            <Label htmlFor="exclude">Exclude Terms (comma-separated)</Label>
            <Textarea
              id="exclude"
              placeholder="e.g., politics, controversial, adult"
              value={config.trendSettings.excludeTerms.join(', ')}
              onChange={(e) => setConfig({
                ...config,
                trendSettings: {
                  ...config.trendSettings,
                  excludeTerms: e.target.value.split(',').map(t => t.trim()).filter(Boolean)
                }
              })}
              className="mt-1"
              rows={2}
            />
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          className="w-full"
          disabled={config.trendSettings.platforms.length === 0}
        >
          Continue
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderAiSummary = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Bot className="h-12 w-12 mx-auto mb-3 text-green-600" />
        <h3 className="text-lg font-semibold mb-2">AI Summary Settings</h3>
        <p className="text-muted-foreground text-sm">
          Configure how AI analyzes and summarizes trends
        </p>
      </div>

      <Card>
        <CardContent className="pt-6 space-y-4">
          <div>
            <Label>AI Provider</Label>
            <RadioGroup
              value={config.aiSummary.provider}
              onValueChange={(value: any) => setConfig({
                ...config,
                aiSummary: { ...config.aiSummary, provider: value }
              })}
              className="mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="openai" id="openai" />
                <Label htmlFor="openai">OpenAI</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="anthropic" id="anthropic" />
                <Label htmlFor="anthropic">Anthropic</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="google" id="google" />
                <Label htmlFor="google">Google AI</Label>
              </div>
            </RadioGroup>
          </div>

          <div>
            <Label htmlFor="model">Model</Label>
            <Select
              value={config.aiSummary.model}
              onValueChange={(value) => setConfig({
                ...config,
                aiSummary: { ...config.aiSummary, model: value }
              })}
            >
              <SelectTrigger id="model" className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {config.aiSummary.provider === 'openai' && (
                  <>
                    <SelectItem value="gpt-4o">GPT-4o</SelectItem>
                    <SelectItem value="gpt-4">GPT-4</SelectItem>
                    <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                  </>
                )}
                {config.aiSummary.provider === 'anthropic' && (
                  <>
                    <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
                    <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                    <SelectItem value="claude-3-haiku">Claude 3 Haiku</SelectItem>
                  </>
                )}
                {config.aiSummary.provider === 'google' && (
                  <>
                    <SelectItem value="gemini-1.5-pro">Gemini 1.5 Pro</SelectItem>
                    <SelectItem value="gemini-1.0-pro">Gemini 1.0 Pro</SelectItem>
                  </>
                )}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="style">Summary Style</Label>
            <Select
              value={config.aiSummary.summaryStyle}
              onValueChange={(value: any) => setConfig({
                ...config,
                aiSummary: { ...config.aiSummary, summaryStyle: value }
              })}
            >
              <SelectTrigger id="style" className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="executive">Executive Summary</SelectItem>
                <SelectItem value="detailed">Detailed Analysis</SelectItem>
                <SelectItem value="bullet-points">Bullet Points</SelectItem>
                <SelectItem value="narrative">Narrative Report</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="analysis">Include Trend Analysis</Label>
              <Switch
                id="analysis"
                checked={config.aiSummary.includeAnalysis}
                onCheckedChange={(checked) => setConfig({
                  ...config,
                  aiSummary: { ...config.aiSummary, includeAnalysis: checked }
                })}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="recommendations">Include Recommendations</Label>
              <Switch
                id="recommendations"
                checked={config.aiSummary.includeRecommendations}
                onCheckedChange={(checked) => setConfig({
                  ...config,
                  aiSummary: { ...config.aiSummary, includeRecommendations: checked }
                })}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={nextStep} className="w-full">
          Continue
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderEmailDelivery = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Mail className="h-12 w-12 mx-auto mb-3 text-red-600" />
        <h3 className="text-lg font-semibold mb-2">Email Delivery Setup</h3>
        <p className="text-muted-foreground text-sm">
          Configure when and how to receive trend reports
        </p>
      </div>

      <Card>
        <CardContent className="pt-6 space-y-4">
          <div>
            <Label>Schedule</Label>
            <RadioGroup
              value={config.emailDelivery.schedule}
              onValueChange={(value: any) => setConfig({
                ...config,
                emailDelivery: { ...config.emailDelivery, schedule: value }
              })}
              className="mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="daily" id="daily" />
                <Label htmlFor="daily">Daily</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="weekly" id="weekly" />
                <Label htmlFor="weekly">Weekly</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="custom" id="custom" />
                <Label htmlFor="custom">Custom</Label>
              </div>
            </RadioGroup>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="time">Delivery Time</Label>
              <Input
                id="time"
                type="time"
                value={config.emailDelivery.time}
                onChange={(e) => setConfig({
                  ...config,
                  emailDelivery: { ...config.emailDelivery, time: e.target.value }
                })}
                className="mt-1"
              />
            </div>

            {config.emailDelivery.schedule === 'weekly' && (
              <div>
                <Label htmlFor="day">Day of Week</Label>
                <Select
                  value={config.emailDelivery.dayOfWeek || 'monday'}
                  onValueChange={(value) => setConfig({
                    ...config,
                    emailDelivery: { ...config.emailDelivery, dayOfWeek: value }
                  })}
                >
                  <SelectTrigger id="day" className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="monday">Monday</SelectItem>
                    <SelectItem value="tuesday">Tuesday</SelectItem>
                    <SelectItem value="wednesday">Wednesday</SelectItem>
                    <SelectItem value="thursday">Thursday</SelectItem>
                    <SelectItem value="friday">Friday</SelectItem>
                    <SelectItem value="saturday">Saturday</SelectItem>
                    <SelectItem value="sunday">Sunday</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          <div>
            <Label htmlFor="recipients">Email Recipients</Label>
            <Textarea
              id="recipients"
              placeholder="Enter email addresses (one per line)"
              value={config.emailDelivery.recipients.join('\n')}
              onChange={(e) => setConfig({
                ...config,
                emailDelivery: {
                  ...config.emailDelivery,
                  recipients: e.target.value.split('\n').map(e => e.trim()).filter(Boolean)
                }
              })}
              className="mt-1"
              rows={3}
            />
          </div>

          <div>
            <Label htmlFor="subject">Email Subject</Label>
            <Input
              id="subject"
              value={config.emailDelivery.subject}
              onChange={(e) => setConfig({
                ...config,
                emailDelivery: { ...config.emailDelivery, subject: e.target.value }
              })}
              className="mt-1"
            />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="charts">Include Visual Charts</Label>
            <Switch
              id="charts"
              checked={config.emailDelivery.includeCharts}
              onCheckedChange={(checked) => setConfig({
                ...config,
                emailDelivery: { ...config.emailDelivery, includeCharts: checked }
              })}
            />
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={nextStep} 
          className="w-full"
          disabled={config.emailDelivery.recipients.length === 0}
        >
          Continue
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderActionsQuestion = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Zap className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h3 className="text-lg font-semibold mb-2">Do you want to add any actions?</h3>
        <p className="text-muted-foreground text-sm mb-6">
          Extend your automation with additional actions (optional)
        </p>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-6">
        <Button
          variant={config.wantsActions === true ? "default" : "outline"}
          size="lg"
          onClick={() => {
            setConfig({ ...config, wantsActions: true })
            nextStep()
          }}
          className="h-auto py-6"
        >
          <div className="flex flex-col items-center gap-2">
            <Check className="h-8 w-8" />
            <span>Yes, I want to add actions</span>
          </div>
        </Button>
        <Button
          variant={config.wantsActions === false ? "default" : "outline"}
          size="lg"
          onClick={() => {
            setConfig({ ...config, wantsActions: false })
            setCurrentStep('review')  // Skip directly to review
          }}
          className="h-auto py-6"
        >
          <div className="flex flex-col items-center gap-2">
            <ArrowRight className="h-8 w-8" />
            <span>No, continue without</span>
          </div>
        </Button>
      </div>

      <div>
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
      </div>
    </motion.div>
  )

  const renderActions = () => {
    const selectedActionsList = config.selectedActionsList || []

    const availableActionsList = [
      { id: 'discord-read', name: 'Discord Read', icon: MessageCircle, desc: 'Read messages from any Discord channel' },
      { id: 'discord-send', name: 'Discord Send', icon: MessageCircle, desc: 'Send messages to any Discord channel' },
      { id: 'gmail-read', name: 'Gmail Read', icon: Mail, desc: 'Read emails from your Gmail account' },
      { id: 'gmail-send', name: 'Gmail Send', icon: Mail, desc: 'Send emails via your Gmail account' },
      { id: 'gcalendar-read', name: 'Google Calendar Read', icon: Calendar, desc: 'Read events from any Google Calendar' },
      { id: 'gcalendar-write', name: 'Google Calendar Write', icon: Calendar, desc: 'Create events on any Google Calendar' },
      { id: 'http-request', name: 'HTTP Request', icon: Globe, desc: 'Make GET, POST, PUT, or DELETE HTTP requests' },
      { id: 'slack-read', name: 'Slack Read', icon: Hash, desc: 'Read messages from a specified Slack channel' },
      { id: 'slack-send', name: 'Slack Send', icon: Hash, desc: 'Send a message to a specified Slack channel' }
    ]

    // Filter actions based on search query
    const filteredActions = availableActionsList.filter(action => {
      const query = searchQuery.toLowerCase()
      return (
        action.name.toLowerCase().includes(query) ||
        action.desc.toLowerCase().includes(query)
      )
    })

    const addAction = (action: any) => {
      const newAction = {
        id: Date.now().toString(),
        type: action.id,
        name: action.name,
        description: action.desc,
        config: {}
      }
      const updatedList = [...selectedActionsList, newAction]
      setConfig({ ...config, selectedActionsList: updatedList })
      setShowActionConfig(updatedList.length - 1)
      setWantsMoreActions(false)
    }

    const removeAction = (index: number) => {
      const updatedList = selectedActionsList.filter((_, i) => i !== index)
      setConfig({ ...config, selectedActionsList: updatedList })
    }

    // If no actions yet or wants more actions, show action selection
    if (selectedActionsList.length === 0 || wantsMoreActions) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          <div className="text-center">
            <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
            <h3 className="text-lg font-semibold mb-2">
              {selectedActionsList.length === 0 ? 'What should happen next?' : 'Add another action?'}
            </h3>
            <p className="text-muted-foreground text-sm">
              Select actions to enhance your automation
            </p>
          </div>

          {selectedActionsList.length > 0 && (
            <div className="bg-muted/30 rounded-lg p-4">
              <p className="text-sm font-medium mb-2">Current Actions:</p>
              <div className="space-y-2">
                {selectedActionsList.map((action, index) => (
                  <div key={action.id} className="flex items-center justify-between text-sm">
                    <span>{index + 1}. {action.name}</span>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => removeAction(index)}
                      className="h-6 w-6 p-0"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="space-y-4">
            <div className="relative">
              <Input
                placeholder="Search actions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
              <Settings className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-96 overflow-y-auto">
              {filteredActions.map((action) => (
                <Card 
                  key={action.id} 
                  className="cursor-pointer hover:bg-muted/50 transition-colors"
                  onClick={() => addAction(action)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <action.icon className="h-5 w-5 text-primary mt-0.5" />
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-sm">{action.name}</p>
                        <p className="text-xs text-muted-foreground">{action.desc}</p>
                      </div>
                      <Plus className="h-4 w-4 text-muted-foreground" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {filteredActions.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Settings className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>No actions found matching "{searchQuery}"</p>
              </div>
            )}
          </div>

          <div className="flex gap-3">
            <Button onClick={prevStep} variant="outline" className="w-full">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Previous
            </Button>
            {selectedActionsList.length > 0 && (
              <Button 
                onClick={() => setWantsMoreActions(false)} 
                className="w-full"
              >
                Continue
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            )}
          </div>
        </motion.div>
      )
    }

    // Show action chain view
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <div className="text-center">
          <Settings className="h-16 w-16 mx-auto mb-4 text-blue-600" />
          <h3 className="text-lg font-semibold mb-2">Your Action Chain</h3>
          <p className="text-muted-foreground text-sm">
            {selectedActionsList.length} action{selectedActionsList.length !== 1 ? 's' : ''} configured
          </p>
        </div>

        <div className="max-w-2xl mx-auto space-y-3">
          <div className="flex items-center gap-3 p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg border-2 border-purple-200 dark:border-purple-800">
            <Key className="h-5 w-5 text-purple-600" />
            <div className="flex-1">
              <p className="font-medium">Trigger: Trend Data Update</p>
              <p className="text-xs text-muted-foreground">When new trends are detected</p>
            </div>
          </div>

          <div className="flex justify-center">
            <ArrowRight className="h-4 w-4 text-muted-foreground" />
          </div>

          {selectedActionsList.map((action, index) => (
            <div key={action.id}>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center gap-3 p-4 bg-muted/30 rounded-lg border"
              >
                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-xs font-medium">
                  {index + 1}
                </div>
                <div className="flex-1">
                  <p className="font-medium">{action.name}</p>
                  <p className="text-xs text-muted-foreground">{action.description}</p>
                </div>
                <div className="flex gap-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setShowActionConfig(index)}
                    className="h-8 px-2"
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => removeAction(index)}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </motion.div>
              {index < selectedActionsList.length - 1 && (
                <div className="flex justify-center my-2">
                  <ArrowRight className="h-4 w-4 text-muted-foreground" />
                </div>
              )}
            </div>
          ))}

          <Button
            variant="outline"
            onClick={() => setWantsMoreActions(true)}
            className="w-full"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Another Action
          </Button>
        </div>

        <div className="flex gap-3">
          <Button onClick={prevStep} variant="outline" className="w-full">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button onClick={nextStep} className="w-full">
            Continue
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </motion.div>
    )
  }

  const renderReview = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Check className="h-16 w-16 mx-auto mb-4 text-green-600" />
        <h3 className="text-lg font-semibold mb-2">Review Your Configuration</h3>
        <p className="text-muted-foreground text-sm">
          Confirm your trend tracker settings before creating
        </p>
      </div>

      <div className="space-y-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Automation Name</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <p className="text-sm font-medium">{config.name}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Key className="h-4 w-4 text-blue-600" />
              RapidAPI Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0 space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Data Source:</span>
              <span className="font-medium capitalize">
                {config.rapidApiConfig.endpoint.replace('-', ' ')}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Region:</span>
              <span className="font-medium">{config.rapidApiConfig.region}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Filter className="h-4 w-4 text-purple-600" />
              Trend Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0 space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Platforms:</span>
              <span className="font-medium capitalize">
                {config.trendSettings.platforms.join(', ')}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Number of Trends:</span>
              <span className="font-medium">{config.trendSettings.numberOfTrends}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Bot className="h-4 w-4 text-green-600" />
              AI Summary
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0 space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Provider:</span>
              <span className="font-medium capitalize">{config.aiSummary.provider}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Style:</span>
              <span className="font-medium capitalize">
                {config.aiSummary.summaryStyle.replace('-', ' ')}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Mail className="h-4 w-4 text-red-600" />
              Email Delivery
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0 space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Schedule:</span>
              <span className="font-medium capitalize">{config.emailDelivery.schedule}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Recipients:</span>
              <span className="font-medium">{config.emailDelivery.recipients.length} added</span>
            </div>
          </CardContent>
        </Card>

        {config.selectedActionsList.length > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <Zap className="h-4 w-4 text-purple-600" />
                Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <p className="text-sm text-muted-foreground">
                {config.selectedActionsList.length} action{config.selectedActionsList.length !== 1 ? 's' : ''} configured
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      <div className="flex gap-3">
        <Button onClick={prevStep} variant="outline" className="w-full">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={nextStep} className="w-full">
          Create Automation
          <Check className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderComplete = () => (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto">
        <Check className="h-10 w-10 text-green-600" />
      </div>
      
      <div>
        <h1 className="text-2xl font-bold mb-2">Automation Created!</h1>
        <p className="text-muted-foreground">
          Your social media trends tracker has been set up successfully
        </p>
      </div>

      <Card className="max-w-md mx-auto">
        <CardContent className="pt-6">
          <div className="space-y-3 text-sm">
            <div className="flex items-center gap-2">
              <Check className="h-4 w-4 text-green-600" />
              <span>RapidAPI connected for {config.trendSettings.platforms.length} platforms</span>
            </div>
            <div className="flex items-center gap-2">
              <Check className="h-4 w-4 text-green-600" />
              <span>AI summary configured with {config.aiSummary.provider}</span>
            </div>
            <div className="flex items-center gap-2">
              <Check className="h-4 w-4 text-green-600" />
              <span>{config.emailDelivery.schedule} email reports scheduled</span>
            </div>
            {config.selectedActionsList.length > 0 && (
              <div className="flex items-center gap-2">
                <Check className="h-4 w-4 text-green-600" />
                <span>{config.selectedActionsList.length} additional actions configured</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3 max-w-md mx-auto">
        <Button onClick={() => setLocation('/dashboard/automations')} className="w-full">
          View My Automations
        </Button>
        <Button onClick={() => setLocation('/dashboard/browse-templates')} variant="outline" className="w-full">
          Back to Templates
        </Button>
      </div>
    </motion.div>
  )

  const renderStep = () => {
    switch (currentStep) {
      case 'intro':
        return renderIntro()
      case 'naming':
        return renderNaming()
      case 'api-setup':
        return renderApiSetup()
      case 'trend-config':
        return renderTrendConfig()
      case 'ai-summary':
        return renderAiSummary()
      case 'email-delivery':
        return renderEmailDelivery()
      case 'actions-question':
        return renderActionsQuestion()
      case 'actions':
        return renderActions()
      case 'review':
        return renderReview()
      case 'complete':
        return renderComplete()
      default:
        return null
    }
  }

  const getCurrentStepConfig = () => {
    const step = steps.find(s => s.id === currentStep)
    return step || steps[0]
  }

  const currentStepConfig = getCurrentStepConfig()
  const CurrentIcon = currentStepConfig.icon

  // Import available actions
  const availableActions = [
    {
      id: 'slack-notification',
      name: 'Send to Slack',
      description: 'Post trend summaries to a Slack channel',
      icon: MessageCircle,
      color: 'bg-purple-500',
      category: 'write' as const
    },
    {
      id: 'google-sheets',
      name: 'Log to Google Sheets',
      description: 'Save trend data to a spreadsheet',
      icon: Table,
      color: 'bg-green-500',
      category: 'write' as const
    },
    {
      id: 'twitter-post',
      name: 'Post to Twitter',
      description: 'Share trending topics on Twitter',
      icon: Twitter,
      color: 'bg-blue-500',
      category: 'write' as const
    },
    {
      id: 'webhook',
      name: 'Send Webhook',
      description: 'Send trend data to a webhook URL',
      icon: Globe,
      color: 'bg-indigo-500',
      category: 'write' as const
    }
  ]

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => setLocation('/dashboard/browse-templates')}
          className="mb-4"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back to Templates
        </Button>

        {currentStep !== 'complete' && (
          <>
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <CurrentIcon className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">{currentStepConfig.title}</h1>
                <p className="text-muted-foreground text-sm">
                  {stepConfig[currentStep].subtitle}
                </p>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex justify-between text-xs text-muted-foreground mb-2">
                <span>Step {currentStepIndex + 1} of {steps.length - 2}</span>
                <span>{Math.round(progress)}% Complete</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          </>
        )}
      </div>

      {/* Content */}
      {currentStep !== 'intro' && currentStep !== 'complete' && (
        <Card>
          <CardContent className="pt-6">
            {renderStep()}
          </CardContent>
        </Card>
      )}
      
      {(currentStep === 'intro' || currentStep === 'complete') && renderStep()}
    </div>
  )
}