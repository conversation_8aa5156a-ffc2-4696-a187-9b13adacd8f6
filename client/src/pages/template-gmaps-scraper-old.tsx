import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { useLocation } from 'wouter'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  MapPin, 
  Key, 
  Search, 
  Database, 
  Download,
  CheckCircle,
  Check,
  ArrowRight,
  ArrowLeft,
  X,
  ChevronLeft,
  Globe,
  FileSpreadsheet,
  FileText,
  Zap,
  AlertCircle,
  Eye,
  EyeOff,
  Shield,
  Settings,
  Mail,
  Calendar,
  MessageSquare,
  Hash
} from 'lucide-react'
import { <PERSON><PERSON><PERSON>glema<PERSON>, SiGooglesheets } from 'react-icons/si'
import { Checkbox } from "@/components/ui/checkbox"

type StepType = 'intro' | 'naming' | 'serpapi-config' | 'search-config' | 'data-export' | 'actions-question' | 'actions' | 'review' | 'complete'

interface ScraperConfig {
  name: string
  serpApi: {
    apiKey: string
    isConfigured: boolean
  }
  googleAuth: {
    isAuthenticated: boolean
    userEmail: string
    userName: string
    accountId: string
  }
  searchConfig: {
    query: string
    location: string
    radius: number
    resultsLimit: number
    categories: string[]
  }
  dataFields: {
    businessName: boolean
    address: boolean
    phone: boolean
    website: boolean
    rating: boolean
    reviews: boolean
    hours: boolean
    categories: boolean
  }
  exportConfig: {
    format: 'csv' | 'json' | 'googlesheets'
    destination: string
    filename: string
    filesList: string[]
  }
  selectedActionsList: Array<{
    id: string
    type: string
    name: string
    description: string
    config: Record<string, any>
  }>
  wantsActions: boolean | null
}

const defaultConfig: ScraperConfig = {
  name: '',
  serpApi: {
    apiKey: '',
    isConfigured: false
  },
  googleAuth: {
    isAuthenticated: false,
    userEmail: '',
    userName: '',
    accountId: ''
  },
  searchConfig: {
    query: '',
    location: '',
    radius: 5,
    resultsLimit: 100,
    categories: []
  },
  dataFields: {
    businessName: true,
    address: true,
    phone: true,
    website: true,
    rating: true,
    reviews: false,
    hours: false,
    categories: true
  },
  exportConfig: {
    format: 'csv',
    destination: '',
    filename: 'google-maps-leads',
    filesList: []
  },
  selectedActionsList: [],
  wantsActions: null
}

const steps: { id: StepType; title: string; icon: React.ComponentType<{ className?: string }> }[] = [
  { id: 'intro', title: 'Introduction', icon: MapPin },
  { id: 'naming', title: 'Name Automation', icon: FileText },
  { id: 'serpapi-config', title: 'SerpAPI Configuration', icon: Key },
  { id: 'search-config', title: 'Search Configuration', icon: Search },
  { id: 'data-export', title: 'Data Fields & Export', icon: Database },
  { id: 'actions-question', title: 'Actions Question', icon: Zap },
  { id: 'actions', title: 'Actions', icon: Zap },
  { id: 'review', title: 'Review', icon: CheckCircle },
  { id: 'complete', title: 'Complete', icon: CheckCircle }
]

const businessCategories = [
  'Restaurants',
  'Hotels',
  'Shopping',
  'Gas Stations',
  'Hospitals',
  'Banks',
  'Schools',
  'Gyms',
  'Pharmacies',
  'Real Estate',
  'Law Firms',
  'Dentists',
  'Auto Repair',
  'Beauty Salons',
  'Cafes'
]

export default function GoogleMapsScraperTemplate() {
  const [currentStep, setCurrentStep] = useState<StepType>('intro')
  const [config, setConfig] = useState<ScraperConfig>(defaultConfig)
  const [, setLocation] = useLocation()
  const [showApiKey, setShowApiKey] = useState(false)

  const currentStepIndex = steps.findIndex(step => step.id === currentStep)
  const progress = ((currentStepIndex + 1) / steps.length) * 100

  const handleNext = () => {
    const nextIndex = currentStepIndex + 1
    if (nextIndex < steps.length) {
      setCurrentStep(steps[nextIndex].id)
    }
  }

  const handlePrevious = () => {
    const prevIndex = currentStepIndex - 1
    if (prevIndex >= 0) {
      setCurrentStep(steps[prevIndex].id)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && currentStep !== 'complete') {
      handleNext()
    }
  }

  const updateConfig = (section: keyof ScraperConfig | '', updates: any) => {
    if (section === '') {
      // Direct update to config root
      setConfig(prev => ({ ...prev, ...updates }))
    } else {
      setConfig(prev => ({
        ...prev,
        [section]: { ...prev[section], ...updates }
      }))
    }
  }

  const renderIntro = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-green-600 rounded-full flex items-center justify-center mx-auto">
        <MapPin className="h-10 w-10 text-white" />
      </div>
      <div>
        <h2 className="text-2xl font-bold mb-2">Google Maps Data Scraper</h2>
        <p className="text-muted-foreground max-w-md mx-auto">
          This template creates an automated workflow that extracts business data from Google Maps
          using SerpAPI, filters results based on your criteria, and exports to your preferred format
          for lead generation or market research.
        </p>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <SiGooglemaps className="h-8 w-8 mx-auto mb-2 text-blue-600" />
          <p className="text-xs font-medium">Google Maps</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Search className="h-8 w-8 mx-auto mb-2 text-purple-600" />
          <p className="text-xs font-medium">Smart Search</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Database className="h-8 w-8 mx-auto mb-2 text-green-600" />
          <p className="text-xs font-medium">Data Extract</p>
        </div>
        <div className="text-center p-3 bg-muted/30 rounded-lg">
          <Download className="h-8 w-8 mx-auto mb-2 text-orange-600" />
          <p className="text-xs font-medium">Auto Export</p>
        </div>
      </div>

      <Button 
        onClick={handleNext} 
        size="lg"
        className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700"
      >
        Start Setup
        <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
    </motion.div>
  )

  const renderNaming = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Settings className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h2 className="text-xl font-semibold mb-2">Name Your Automation</h2>
        <p className="text-muted-foreground">
          Give your automation a memorable name that describes what it does
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="automation-name">Automation Name *</Label>
          <Input
            id="automation-name"
            placeholder="e.g., Restaurant Leads NYC, Dentist Contact List"
            value={config.name}
            onChange={(e) => updateConfig('', { name: e.target.value })}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && config.name) {
                handleNext()
              }
            }}
            className="mt-2"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Choose a descriptive name you'll easily recognize
          </p>
        </div>
      </div>

      <div className="flex gap-3">
        <Button 
          onClick={handlePrevious}
          variant="outline"
          className="w-full"
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={handleNext} 
          disabled={!config.name}
          className="w-full"
        >
          Continue to SerpAPI Setup
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )
          <div className="space-y-2">
            <Label htmlFor="automationName">Automation Name</Label>
            <Input
              id="automationName"
              placeholder="e.g., Restaurant Leads NYC, Dentist Contact List"
              value={config.name}
              onChange={(e) => updateConfig('', { name: e.target.value })}
              onKeyDown={handleKeyPress}
              autoFocus
            />
          </div>
        </CardContent>
      </Card>

      <div className="grid md:grid-cols-3 gap-4 text-left">
        <Card>
          <CardHeader className="pb-3">
            <Key className="h-6 w-6 text-orange-500 mb-2" />
            <CardTitle className="text-sm">SerpAPI Integration</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Configure your SerpAPI key for reliable Google Maps data extraction
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <Search className="h-6 w-6 text-blue-500 mb-2" />
            <CardTitle className="text-sm">Smart Search</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Set up location, radius, categories, and result limits for targeted scraping
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <Database className="h-6 w-6 text-green-500 mb-2" />
            <CardTitle className="text-sm">Export Options</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Export to CSV, JSON, or directly to Google Sheets for immediate use
            </p>
          </CardContent>
        </Card>
      </div>

      <Button 
        onClick={handleNext} 
        disabled={!config.name.trim()}
        className="w-full sm:w-auto" 
        onKeyDown={handleKeyPress}
      >
        Get Started
        <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
    </motion.div>
  )

  const renderSerpApiStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Key className="h-12 w-12 text-orange-500 mx-auto mb-4" />
        <h2 className="text-2xl font-bold mb-2">SerpAPI Configuration</h2>
        <p className="text-muted-foreground">
          Connect your SerpAPI account to access Google Maps data
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            API Key Setup
          </CardTitle>
          <CardDescription>
            Enter your SerpAPI key to enable Google Maps data extraction
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="apiKey">SerpAPI Key</Label>
            <div className="relative">
              <Input
                id="apiKey"
                type={showApiKey ? "text" : "password"}
                placeholder="Enter your SerpAPI key..."
                value={config.serpApi.apiKey}
                onChange={(e) => updateConfig('serpApi', { 
                  apiKey: e.target.value,
                  isConfigured: e.target.value.length > 0
                })}
                onKeyDown={handleKeyPress}
                className="pr-10"
              />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 h-full px-3"
                onClick={() => setShowApiKey(!showApiKey)}
              >
                {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
            </div>
            {config.serpApi.apiKey && (
              <div className="flex items-center gap-2 text-sm text-green-600">
                <CheckCircle className="h-4 w-4" />
                API key configured
              </div>
            )}
          </div>

          <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
            <div className="flex items-start gap-2">
              <AlertCircle className="h-5 w-5 text-blue-500 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-blue-800 dark:text-blue-200 mb-1">
                  Don't have a SerpAPI account?
                </p>
                <p className="text-blue-700 dark:text-blue-300">
                  Sign up at serpapi.com to get your API key. They offer 100 free searches per month.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button variant="outline" onClick={handlePrevious}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={handleNext} 
          disabled={!config.serpApi.isConfigured}
          className="flex-1"
          onKeyDown={handleKeyPress}
        >
          Next: Search Configuration
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderSearchConfigStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Search className="h-12 w-12 text-blue-500 mx-auto mb-4" />
        <h2 className="text-2xl font-bold mb-2">Search Configuration</h2>
        <p className="text-muted-foreground">
          Define what and where to search for businesses
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Search Parameters</CardTitle>
            <CardDescription>
              Configure your search query and location
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="query">Search Query</Label>
              <Input
                id="query"
                placeholder="e.g., restaurants, coffee shops, dentists"
                value={config.searchConfig.query}
                onChange={(e) => updateConfig('searchConfig', { query: e.target.value })}
                onKeyDown={handleKeyPress}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                placeholder="e.g., New York, NY or 10001"
                value={config.searchConfig.location}
                onChange={(e) => updateConfig('searchConfig', { location: e.target.value })}
                onKeyDown={handleKeyPress}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="radius">Search Radius (miles)</Label>
              <Select value={config.searchConfig.radius.toString()} onValueChange={(value) => updateConfig('searchConfig', { radius: parseInt(value) })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1 mile</SelectItem>
                  <SelectItem value="5">5 miles</SelectItem>
                  <SelectItem value="10">10 miles</SelectItem>
                  <SelectItem value="25">25 miles</SelectItem>
                  <SelectItem value="50">50 miles</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="limit">Results Limit</Label>
              <Select value={config.searchConfig.resultsLimit.toString()} onValueChange={(value) => updateConfig('searchConfig', { resultsLimit: parseInt(value) })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="50">50 results</SelectItem>
                  <SelectItem value="100">100 results</SelectItem>
                  <SelectItem value="200">200 results</SelectItem>
                  <SelectItem value="500">500 results</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Business Categories</CardTitle>
            <CardDescription>
              Filter by specific business types (optional)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-2 max-h-64 overflow-y-auto">
              {businessCategories.map((category) => (
                <div key={category} className="flex items-center space-x-2">
                  <Checkbox
                    id={category}
                    checked={config.searchConfig.categories.includes(category)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        updateConfig('searchConfig', {
                          categories: [...config.searchConfig.categories, category]
                        })
                      } else {
                        updateConfig('searchConfig', {
                          categories: config.searchConfig.categories.filter(c => c !== category)
                        })
                      }
                    }}
                  />
                  <Label htmlFor={category} className="text-sm">
                    {category}
                  </Label>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex gap-3">
        <Button variant="outline" onClick={handlePrevious}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={handleNext} 
          disabled={!config.searchConfig.query || !config.searchConfig.location}
          className="flex-1"
          onKeyDown={handleKeyPress}
        >
          Next: Data Fields & Export
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderDataExportStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Database className="h-12 w-12 text-green-500 mx-auto mb-4" />
        <h2 className="text-2xl font-bold mb-2">Data Fields & Export</h2>
        <p className="text-muted-foreground">
          Choose which data to extract and how to export it
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Data Fields</CardTitle>
            <CardDescription>
              Select which business information to extract
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {Object.entries(config.dataFields).map(([field, enabled]) => (
              <div key={field} className="flex items-center space-x-2">
                <Checkbox
                  id={field}
                  checked={enabled}
                  onCheckedChange={(checked) => updateConfig('dataFields', { [field]: checked })}
                />
                <Label htmlFor={field} className="text-sm capitalize">
                  {field.replace(/([A-Z])/g, ' $1').toLowerCase()}
                </Label>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Export Configuration</CardTitle>
            <CardDescription>
              Choose how to save and access your data
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Export Format</Label>
              <RadioGroup
                value={config.exportConfig.format}
                onValueChange={(value) => updateConfig('exportConfig', { format: value })}
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="csv" id="csv" />
                  <Label htmlFor="csv" className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    CSV File
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="json" id="json" />
                  <Label htmlFor="json" className="flex items-center gap-2">
                    <FileSpreadsheet className="h-4 w-4" />
                    JSON File
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="googlesheets" id="googlesheets" />
                  <Label htmlFor="googlesheets" className="flex items-center gap-2">
                    <SiGooglesheets className="h-4 w-4" />
                    Google Sheets
                  </Label>
                </div>
              </RadioGroup>
            </div>

            <div className="space-y-2">
              <Label htmlFor="filename">File Name</Label>
              <Input
                id="filename"
                placeholder="google-maps-leads"
                value={config.exportConfig.filename}
                onChange={(e) => updateConfig('exportConfig', { filename: e.target.value })}
                onKeyDown={handleKeyPress}
              />
            </div>

            {config.exportConfig.format === 'googlesheets' && (
              <>
                <Card>
                  <CardContent className="p-6 space-y-4">
                    <div>
                      <Label className="text-sm font-medium">Authentication Type <span className="text-red-500">*</span></Label>
                      <div className="mt-2">
                        <div className="flex items-center space-x-2">
                          <input type="radio" id="google-signin" checked readOnly className="text-blue-600" />
                          <Label htmlFor="google-signin" className="text-sm">Google Sign-In</Label>
                        </div>
                      </div>
                    </div>

                    <div className="text-xs text-muted-foreground">
                      <strong>Note:</strong> Select existing Google account from below or Signin with a different account.
                    </div>

                    {!config.googleAuth.isAuthenticated ? (
                      <Button
                        onClick={() => {
                          updateConfig('googleAuth', {
                            isAuthenticated: true,
                            userEmail: '<EMAIL>',
                            userName: 'Kaizar Bharmal',
                            accountId: 'gauth-12345'
                          })
                        }}
                        className="w-full"
                      >
                        <SiGooglesheets className="mr-2 h-4 w-4" />
                        Sign in with Google
                      </Button>
                    ) : (
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <Badge variant="default" className="bg-green-600">Connected</Badge>
                          <span className="text-sm font-medium">Google</span>
                        </div>
                        
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">Google account</Label>
                          <div className="p-3 border rounded-md bg-muted/50">
                            <div className="text-sm font-medium">{config.googleAuth.userName}</div>
                            <div className="text-sm text-muted-foreground">({config.googleAuth.userEmail})</div>
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {config.googleAuth.isAuthenticated && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">File</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {config.exportConfig.filesList.length === 0 ? (
                        <div className="text-center py-4">
                          <p className="text-sm text-muted-foreground">
                            There are no files, please refresh
                          </p>
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="mt-2"
                            onClick={() => updateConfig('exportConfig', { 
                              filesList: ['sheet1', 'sheet2', 'sheet3'] 
                            })}
                          >
                            Refresh Files
                          </Button>
                        </div>
                      ) : (
                        <Select
                          value={config.exportConfig.destination}
                          onValueChange={(value) => updateConfig('exportConfig', { destination: value })}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select a spreadsheet or create new" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="new">Create New Spreadsheet</SelectItem>
                            <SelectItem value="sheet1">Business Leads Database</SelectItem>
                            <SelectItem value="sheet2">Market Research Data</SelectItem>
                            <SelectItem value="sheet3">Lead Generation Q1</SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                    </CardContent>
                  </Card>
                )}
              </>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="flex gap-3">
        <Button variant="outline" onClick={handlePrevious}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button 
          onClick={handleNext} 
          className="flex-1"
          onKeyDown={handleKeyPress}
        >
          Continue to Custom Actions
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderActionsQuestion = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <Zap className="h-16 w-16 mx-auto mb-4 text-purple-600" />
        <h2 className="text-xl font-semibold mb-2">Do you want to add any actions?</h2>
        <p className="text-muted-foreground">
          Extend your automation with additional actions like sending notifications or updating databases
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 max-w-md mx-auto">
        <Button
          size="lg"
          variant="outline"
          onClick={() => {
            updateConfig('', { wantsActions: false })
            // Skip actions step and go directly to review
            const reviewIndex = steps.findIndex(s => s.id === 'review')
            setCurrentStep(steps[reviewIndex].id)
          }}
          className="h-24 flex flex-col items-center justify-center gap-2"
        >
          <X className="h-8 w-8" />
          <span>No, continue</span>
        </Button>
        <Button
          size="lg"
          onClick={() => {
            updateConfig('', { wantsActions: true })
            handleNext()
          }}
          className="h-24 flex flex-col items-center justify-center gap-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
        >
          <CheckCircle className="h-8 w-8" />
          <span>Yes, add actions</span>
        </Button>
      </div>

      <div className="flex justify-center">
        <Button variant="outline" onClick={handlePrevious}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
      </div>
    </motion.div>
  )

  const renderActions = () => {
    const [showActionConfig, setShowActionConfig] = useState<number | null>(null)
    
    const handleActionsChange = (selectedIds: string[]) => {
      const selectedActions = selectedIds.map(id => {
        const existing = config.selectedActionsList.find(a => a.id === id)
        if (existing) return existing
        
        return {
          id,
          type: id,
          name: id.split('-').map(w => w.charAt(0).toUpperCase() + w.slice(1)).join(' '),
          description: `Configure ${id} action`,
          config: {}
        }
      })
      updateConfig('', { selectedActionsList: selectedActions })
    }

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <div className="text-center">
          <Zap className="h-16 w-16 mx-auto mb-4 text-purple-600" />
          <h2 className="text-xl font-semibold mb-2">Select Actions</h2>
          <p className="text-muted-foreground">
            Choose actions to extend your scraper's capabilities
          </p>
        </div>

        <ActionsSelector
          selectedActions={config.selectedActionsList.map(a => a.id)}
          onActionsChange={handleActionsChange}
        />

        <div className="flex gap-3">
          <Button onClick={handlePrevious} variant="outline" className="w-full">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button onClick={handleNext} className="w-full">
            Review Configuration <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>

        {/* Action Configuration Dialog */}
        {showActionConfig !== null && config.selectedActionsList[showActionConfig] && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowActionConfig(null)}
          >
            <motion.div
              initial={{ scale: 0.95 }}
              animate={{ scale: 1 }}
              className="bg-card p-6 rounded-lg shadow-lg max-w-md w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">Configure {config.selectedActionsList[showActionConfig].name}</h3>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Configuration options will appear here based on the action type.
                </p>
                <div className="flex justify-end gap-2 mt-6">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowActionConfig(null)}
                  >
                    Close
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </motion.div>
    )
  }

  const renderReviewStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
        <h2 className="text-2xl font-bold mb-2">Review Configuration</h2>
        <p className="text-muted-foreground">
          Review your scraper settings before creating the automation
        </p>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Automation Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div><strong>Name:</strong> {config.name}</div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>SerpAPI key configured</span>
            </div>
            {config.exportConfig.format === 'googlesheets' && config.googleAuth.isAuthenticated && (
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>Google account connected ({config.googleAuth.userEmail})</span>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Search Parameters</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div><strong>Query:</strong> {config.searchConfig.query}</div>
            <div><strong>Location:</strong> {config.searchConfig.location}</div>
            <div><strong>Radius:</strong> {config.searchConfig.radius} miles</div>
            <div><strong>Results Limit:</strong> {config.searchConfig.resultsLimit}</div>
            {config.searchConfig.categories.length > 0 && (
              <div>
                <strong>Categories:</strong> {config.searchConfig.categories.join(', ')}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Data & Export</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div>
              <strong>Data Fields:</strong>{' '}
              {Object.entries(config.dataFields)
                .filter(([, enabled]) => enabled)
                .map(([field]) => field.replace(/([A-Z])/g, ' $1').toLowerCase())
                .join(', ')}
            </div>
            <div><strong>Export Format:</strong> {config.exportConfig.format.toUpperCase()}</div>
            <div><strong>Filename:</strong> {config.exportConfig.filename}</div>
          </CardContent>
        </Card>

        {config.selectedActionsList.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Selected Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div>{config.selectedActionsList.length} action{config.selectedActionsList.length !== 1 ? 's' : ''} selected</div>
            </CardContent>
          </Card>
        )}
      </div>

      <div className="flex gap-3">
        <Button variant="outline" onClick={handlePrevious}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        <Button onClick={handleNext} className="flex-1" onKeyDown={handleKeyPress}>
          Create Automation
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderCompleteStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-6"
    >
      <div className="flex justify-center">
        <div className="p-4 bg-green-100 dark:bg-green-900 rounded-full">
          <CheckCircle className="h-12 w-12 text-green-600" />
        </div>
      </div>
      
      <div>
        <h2 className="text-2xl font-bold mb-2">Automation Created!</h2>
        <p className="text-muted-foreground mb-6">
          Your Google Maps scraper is ready to extract business data
        </p>
      </div>

      <Card>
        <CardContent className="pt-6">
          <div className="grid md:grid-cols-3 gap-4 text-left">
            <div className="text-center">
              <Search className="h-8 w-8 text-blue-500 mx-auto mb-2" />
              <p className="font-medium">Search Configured</p>
              <p className="text-sm text-muted-foreground">
                Ready to scrape {config.searchConfig.query} in {config.searchConfig.location}
              </p>
            </div>
            <div className="text-center">
              <Database className="h-8 w-8 text-green-500 mx-auto mb-2" />
              <p className="font-medium">Data Fields Selected</p>
              <p className="text-sm text-muted-foreground">
                Extracting {Object.values(config.dataFields).filter(Boolean).length} data fields
              </p>
            </div>
            <div className="text-center">
              <Download className="h-8 w-8 text-purple-500 mx-auto mb-2" />
              <p className="font-medium">Export Ready</p>
              <p className="text-sm text-muted-foreground">
                Results will be saved as {config.exportConfig.format.toUpperCase()}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3 justify-center">
        <Button variant="outline" onClick={() => setLocation('/dashboard/automations')}>
          View All Automations
        </Button>
        <Button onClick={() => setLocation('/dashboard/templates')}>
          Browse More Templates
        </Button>
      </div>
    </motion.div>
  )

  const renderNamingStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center">
        <FileText className="h-16 w-16 mx-auto mb-4 text-primary" />
        <h2 className="text-xl font-semibold mb-2">Name Your Automation</h2>
        <p className="text-muted-foreground">
          Give your scraper automation a memorable and descriptive name
        </p>
      </div>

      <div className="space-y-4 max-w-md mx-auto">
        <div>
          <Label htmlFor="automation-name">Automation Name *</Label>
          <Input
            id="automation-name"
            placeholder="e.g., Restaurant Leads NYC, Dentist Contact List"
            value={config.name}
            onChange={(e) => updateConfig('', { name: e.target.value })}
            onKeyDown={handleKeyPress}
            className="mt-2"
            autoFocus
          />
          <p className="text-xs text-muted-foreground mt-1">
            Choose a name that clearly describes your target business type and location
          </p>
        </div>
      </div>

      <div className="flex gap-3 justify-center">
        <Button variant="outline" onClick={handlePrevious}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <Button onClick={handleNext} disabled={!config.name}>
          Continue to SerpAPI Setup
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  )

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'intro':
        return renderIntroStep()
      case 'naming':
        return renderNamingStep()
      case 'serpapi-config':
        return renderSerpApiStep()
      case 'search-config':
        return renderSearchConfigStep()
      case 'data-export':
        return renderDataExportStep()
      case 'actions-question':
        return renderActionsQuestion()
      case 'actions':
        return renderActions()
      case 'review':
        return renderReviewStep()
      case 'complete':
        return renderCompleteStep()
      default:
        return renderIntroStep()
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-2 mb-4">
            <SiGooglemaps className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold">Google Maps Data Scraper</h1>
          </div>
          <p className="text-muted-foreground">
            Extract business data for lead generation and market research
          </p>
        </div>

        {/* Progress */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <span className="text-sm font-medium">
              Step {currentStepIndex + 1} of {steps.length}
            </span>
            <span className="text-sm text-muted-foreground">
              {Math.round(progress)}% complete
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Steps Navigation */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            {steps.map((step, index) => {
              const Icon = step.icon
              const isActive = index === currentStepIndex
              const isCompleted = index < currentStepIndex
              
              return (
                <div key={step.id} className="flex flex-col items-center">
                  <div className={`
                    w-10 h-10 rounded-full flex items-center justify-center mb-2
                    ${isActive ? 'bg-blue-600 text-white' : 
                      isCompleted ? 'bg-green-600 text-white' : 
                      'bg-gray-200 dark:bg-gray-700 text-gray-400'}
                  `}>
                    {isCompleted ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <Icon className="h-5 w-5" />
                    )}
                  </div>
                  <span className={`text-xs text-center max-w-20 ${
                    isActive ? 'font-medium' : 'text-muted-foreground'
                  }`}>
                    {step.title}
                  </span>
                </div>
              )
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto">
          {renderCurrentStep()}
        </div>
      </div>
    </div>
  )
}