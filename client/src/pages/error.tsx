import { useLocation } from 'wouter'
import { <PERSON>ert<PERSON><PERSON>gle, RefreshCw, Home, Bug, Mail } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useState, useEffect } from 'react'

interface ErrorPageProps {
  error?: Error
  errorInfo?: { componentStack?: string }
  onRetry?: () => void
}

export default function ErrorPage({ error, errorInfo, onRetry }: ErrorPageProps) {
  const [, setLocation] = useLocation()
  const [showDetails, setShowDetails] = useState(false)
  const [storedError, setStoredError] = useState<any>(null)

  useEffect(() => {
    // Check for stored error from navigation
    const errorData = sessionStorage.getItem('app_error')
    if (errorData && !error) {
      try {
        setStoredError(JSON.parse(errorData))
        sessionStorage.removeItem('app_error')
      } catch (e) {
        console.error('Failed to parse stored error:', e)
      }
    }
  }, [error])

  const currentError = error || (storedError ? new Error(storedError.message) : null)

  const handleRefresh = () => {
    window.location.reload()
  }

  const handleReport = () => {
    const errorDetails = {
      message: currentError?.message || 'Unknown error',
      stack: currentError?.stack || storedError?.stack || 'No stack trace available',
      componentStack: errorInfo?.componentStack || 'No component stack available',
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      url: window.location.href
    }
    
    // In a real app, you'd send this to your error reporting service
    console.error('Error Report:', errorDetails)
    
    // For now, we'll copy to clipboard
    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2))
      .then(() => alert('Error details copied to clipboard'))
      .catch(() => alert('Error details logged to console'))
  }

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="max-w-lg w-full space-y-6">
        {/* Error Icon */}
        <div className="flex justify-center">
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-red-600 to-orange-600 rounded-full blur-lg opacity-20"></div>
            <div className="relative w-24 h-24 bg-card border-2 border-red-200 dark:border-red-800 rounded-full flex items-center justify-center">
              <AlertTriangle className="h-12 w-12 text-red-500" />
            </div>
          </div>
        </div>

        {/* Error Content */}
        <div className="text-center space-y-3">
          <h1 className="text-3xl font-bold text-foreground">
            Something went wrong
          </h1>
          <p className="text-muted-foreground">
            We encountered an unexpected error. Don't worry, your data is safe.
          </p>
        </div>

        {/* Error Alert */}
        {currentError && (
          <Alert className="border-red-200 dark:border-red-800">
            <AlertTriangle className="h-4 w-4 text-red-500" />
            <AlertDescription className="text-sm">
              {currentError.message || 'An unexpected error occurred'}
            </AlertDescription>
          </Alert>
        )}

        {/* Action Buttons */}
        <Card className="bg-card/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-lg text-center">What would you like to do?</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {onRetry && (
              <Button 
                onClick={onRetry}
                className="w-full bg-[#155DB8] hover:bg-[#155DB8]/90"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            )}
            
            <Button 
              onClick={handleRefresh}
              variant="outline" 
              className="w-full"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Page
            </Button>

            <Button 
              onClick={() => setLocation('/dashboard')}
              variant="outline" 
              className="w-full"
            >
              <Home className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
          </CardContent>
        </Card>

        {/* Error Details */}
        <Card className="bg-card/30 backdrop-blur-sm">
          <CardContent className="pt-6 space-y-3">
            <Button 
              onClick={() => setShowDetails(!showDetails)}
              variant="ghost" 
              className="w-full text-xs"
            >
              <Bug className="h-3 w-3 mr-2" />
              {showDetails ? 'Hide' : 'Show'} Technical Details
            </Button>

            {showDetails && currentError && (
              <div className="space-y-2">
                <div className="bg-muted/50 rounded p-3 text-xs font-mono">
                  <p className="text-red-600 dark:text-red-400 font-semibold">Error:</p>
                  <p className="text-foreground break-words">{currentError.message}</p>
                  
                  {(currentError.stack || storedError?.stack) && (
                    <>
                      <p className="text-red-600 dark:text-red-400 font-semibold mt-2">Stack Trace:</p>
                      <pre className="text-foreground text-xs overflow-auto max-h-32 whitespace-pre-wrap">
                        {currentError.stack || storedError?.stack}
                      </pre>
                    </>
                  )}
                </div>
                
                <Button 
                  onClick={handleReport}
                  variant="outline" 
                  size="sm"
                  className="w-full text-xs"
                >
                  <Mail className="h-3 w-3 mr-2" />
                  Copy Error Report
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Help Text */}
        <p className="text-xs text-muted-foreground text-center">
          If this problem persists, please contact support with the error details above.
        </p>
      </div>
    </div>
  )
}