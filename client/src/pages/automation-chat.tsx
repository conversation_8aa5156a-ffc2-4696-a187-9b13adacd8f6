import { useState, useRef, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useLocation } from 'wouter'
import { 
  Send,
  ChevronLeft,
  Bot,
  User,
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { useAuth } from '@/contexts/AuthContext'
import { cn } from '@/lib/utils'
import { ErrorHandler } from '@/components/error-handler'

interface Message {
  id: string
  type: 'user' | 'bot' | 'system'
  content: string
  timestamp: Date
  status?: 'success' | 'error' | 'running'
}

interface AutomationInfo {
  id: string
  name: string
  status: 'active' | 'inactive' | 'error'
  lastRun: Date | null
  nextRun: Date | null
}

// Mock automation data
const mockAutomation: AutomationInfo = {
  id: '1',
  name: 'Daily Report Email',
  status: 'active',
  lastRun: new Date(Date.now() - 3600000),
  nextRun: new Date(Date.now() + 3600000 * 20),
}

// Mock initial messages
const initialMessages: Message[] = [
  {
    id: '1',
    type: 'system',
    content: 'Welcome to the automation chat interface! You can trigger automations, check status, and get updates here.',
    timestamp: new Date(Date.now() - 86400000),
  },
  {
    id: '2',
    type: 'bot',
    content: 'Automation "Daily Report Email" is active and scheduled to run in 20 hours.',
    timestamp: new Date(Date.now() - 3600000),
    status: 'success'
  }
]

export default function AutomationChat() {
  const params = useParams()
  const [location, setLocation] = useLocation()
  const { user } = useAuth()
  const scrollRef = useRef<HTMLDivElement>(null)
  
  const [messages, setMessages] = useState<Message[]>(initialMessages)
  const [inputValue, setInputValue] = useState('')
  const [isTyping, setIsTyping] = useState(false)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight
    }
  }, [messages])

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    }

    setMessages([...messages, userMessage])
    setInputValue('')
    setIsTyping(true)

    // Simulate bot response
    setTimeout(() => {
      const botResponse = generateBotResponse(inputValue)
      setMessages(prev => [...prev, botResponse])
      setIsTyping(false)
    }, 1000)
  }

  const generateBotResponse = (userInput: string): Message => {
    const input = userInput.toLowerCase()
    let content = ''
    let status: 'success' | 'error' | 'running' | undefined

    if (input.includes('run') || input.includes('trigger') || input.includes('start')) {
      content = 'Starting automation "Daily Report Email"...'
      status = 'running'
      
      // Simulate completion after 2 seconds
      setTimeout(() => {
        const completionMessage: Message = {
          id: (Date.now() + 1000).toString(),
          type: 'bot',
          content: 'Automation completed successfully! Report has been sent to 3 recipients.',
          timestamp: new Date(),
          status: 'success'
        }
        setMessages(prev => [...prev, completionMessage])
      }, 2000)
    } else if (input.includes('status')) {
      content = `Automation is currently ${mockAutomation.status}. Last run: ${mockAutomation.lastRun?.toLocaleTimeString()}. Next scheduled run: ${mockAutomation.nextRun?.toLocaleTimeString()}.`
      status = 'success'
    } else if (input.includes('stop') || input.includes('pause')) {
      content = 'Automation has been paused. It will not run until you activate it again.'
      status = 'error'
    } else if (input.includes('help')) {
      content = 'Available commands:\n• "run" or "trigger" - Start the automation\n• "status" - Check current status\n• "stop" or "pause" - Pause the automation\n• "logs" - View recent execution logs'
    } else if (input.includes('logs')) {
      content = 'Recent executions:\n• 1 hour ago - Success (2.3s)\n• 25 hours ago - Success (2.1s)\n• 2 days ago - Failed (API timeout)'
    } else {
      content = "I can help you manage this automation. Try commands like 'run', 'status', or 'help'."
    }

    return {
      id: Date.now().toString(),
      type: 'bot',
      content,
      timestamp: new Date(),
      status
    }
  }

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'running':
        return <Clock className="h-4 w-4 text-blue-500 animate-spin" />
      default:
        return null
    }
  }

  const formatTime = (date: Date) => {
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    
    if (days > 0) {
      return `${days}d ago`
    } else {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    }
  }

  return (
    <div className="max-w-4xl mx-auto h-[calc(100vh-8rem)] flex flex-col">
      {/* Header */}
      <div className="mb-6">
        <Button
          variant="ghost"
          onClick={() => setLocation('/dashboard/automations')}
          className="mb-4"
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Back to Automations
        </Button>
        
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">{mockAutomation.name}</h1>
            <div className="flex items-center gap-4">
              <Badge
                variant={mockAutomation.status === 'active' ? 'default' : 'secondary'}
                className={cn(
                  mockAutomation.status === 'active' && 'bg-green-100 text-green-800'
                )}
              >
                {mockAutomation.status}
              </Badge>
              <span className="text-sm text-muted-foreground">
                Last run: {mockAutomation.lastRun?.toLocaleTimeString() || 'Never'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Error Handler for this specific automation */}
      <div className="mb-6">
        <ErrorHandler automationId={params.id} compact={true} />
      </div>

      {/* Chat Container */}
      <Card className="flex-1 flex flex-col overflow-hidden">
        {/* Messages */}
        <ScrollArea className="flex-1 p-4" ref={scrollRef}>
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={cn(
                  "flex gap-3",
                  message.type === 'user' && "justify-end"
                )}
              >
                {message.type !== 'user' && (
                  <Avatar className="h-8 w-8">
                    <AvatarFallback>
                      {message.type === 'bot' ? <Bot className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
                    </AvatarFallback>
                  </Avatar>
                )}
                
                <div className={cn(
                  "flex flex-col gap-1 max-w-[70%]",
                  message.type === 'user' && "items-end"
                )}>
                  <div
                    className={cn(
                      "rounded-lg px-4 py-2",
                      message.type === 'user' 
                        ? "bg-[#155DB8] text-white" 
                        : message.type === 'system'
                        ? "bg-muted text-muted-foreground"
                        : "bg-card border"
                    )}
                  >
                    <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                  </div>
                  <div className="flex items-center gap-2 px-1">
                    <span className="text-xs text-muted-foreground">
                      {formatTime(message.timestamp)}
                    </span>
                    {message.status && getStatusIcon(message.status)}
                  </div>
                </div>
                
                {message.type === 'user' && (
                  <Avatar className="h-8 w-8">
                    <AvatarFallback>
                      <User className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                )}
              </div>
            ))}
            
            {isTyping && (
              <div className="flex gap-3">
                <Avatar className="h-8 w-8">
                  <AvatarFallback>
                    <Bot className="h-4 w-4" />
                  </AvatarFallback>
                </Avatar>
                <div className="bg-card border rounded-lg px-4 py-2">
                  <div className="flex gap-1">
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-pulse" />
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-pulse" style={{ animationDelay: '0.2s' }} />
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-pulse" style={{ animationDelay: '0.4s' }} />
                  </div>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Input */}
        <div className="border-t p-4">
          <form
            onSubmit={(e) => {
              e.preventDefault()
              handleSendMessage()
            }}
            className="flex gap-2"
          >
            <Input
              placeholder="Type a message... (try 'run', 'status', or 'help')"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              className="flex-1"
            />
            <Button
              type="submit"
              size="icon"
              disabled={!inputValue.trim()}
              className="bg-[#155DB8] hover:bg-[#155DB8]/90"
            >
              <Send className="h-4 w-4" />
            </Button>
          </form>
        </div>
      </Card>
    </div>
  )
}