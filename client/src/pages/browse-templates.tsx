import { useState } from 'react'
import { useLocation } from 'wouter'
import { 
  Mail, 
  Calendar, 
  FileSpreadsheet, 
  MessageSquare,
  Globe,
  Zap,
  Search,
  Filter,
  Eye,
  Play,
  FileText,
  Brain,
  Users,
  Bell,
  ClipboardList,
  TrendingUp
} from 'lucide-react'
import { SiGooglesheets, SiSlack, SiDiscord, SiNotion, SiWordpress, SiGmail, SiGooglemaps, SiYoutube, SiCanva, SiTelegram } from 'react-icons/si'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog'

interface Template {
  id: string
  name: string
  description: string
  trigger: string
  actions: string[]
  icon: React.ComponentType<{ className?: string }>
  category: string
}

const templates: Template[] = [
  {
    id: 'blog-content-generator',
    name: 'AI Blog Content Generator',
    description: 'Generate high-quality blog content from keywords in Google Sheets, with email approval and auto-publishing',
    trigger: 'Google Sheets',
    actions: ['AI Generation', 'Email Approval', 'WordPress', 'Medium'],
    icon: FileText,
    category: 'Content',
  },
  {
    id: 'gmaps-data-scraper',
    name: 'Google Maps Data Scraper',
    description: 'Extract business data from Google Maps using SerpAPI for lead generation and market research',
    trigger: 'SerpAPI',
    actions: ['Data Extraction', 'CSV Export', 'Google Sheets'],
    icon: SiGooglemaps,
    category: 'Lead Generation',
  },
  {
    id: 'gmail-email-automation',
    name: 'Gmail Email Automation',
    description: 'Intelligently monitor, analyze, and automate incoming Gmail messages using AI for auto-sorting and alerts',
    trigger: 'Gmail',
    actions: ['AI Analysis', 'Auto-Sort', 'Alerts', 'Human Review'],
    icon: SiGmail,
    category: 'Productivity',
  },
  {
    id: 'youtube-apify-transcription',
    name: 'YouTube Video Transcription & Summary',
    description: 'Extract audio from YouTube videos using Apify, generate accurate transcriptions, and create intelligent summaries with AI',
    trigger: 'YouTube',
    actions: ['Video Processing', 'AI Transcription', 'Smart Summaries', 'Content Delivery'],
    icon: SiYoutube,
    category: 'Content',
  },
  {
    id: 'hr-workflow',
    name: 'AI Automated HR Workflow',
    description: 'Monitor Google Drive for resumes, extract candidate information using AI, score applicants, and log results to Google Sheets',
    trigger: 'Google Drive',
    actions: ['CV Parsing', 'AI Scoring', 'Candidate Evaluation', 'Sheets Integration'],
    icon: Users,
    category: 'HR',
  },
  {
    id: 'daily-digest',
    name: 'Daily Calendar + Email Summary Digest',
    description: 'Get personalized morning briefings with today\'s calendar events and Gmail highlights, perfect for busy professionals and founders',
    trigger: 'Schedule',
    actions: ['Calendar Sync', 'Email Analysis', 'AI Summary', 'Morning Delivery'],
    icon: Bell,
    category: 'Productivity',
  },
  {
    id: 'lead-qualification',
    name: 'AI Lead Qualification',
    description: 'Automatically qualify leads from form responses using AI scoring and send qualified prospects to Google Sheets or CRM',
    trigger: 'Form Submission',
    actions: ['AI Scoring', 'Lead Qualification', 'Google Sheets', 'Email Alerts'],
    icon: ClipboardList,
    category: 'Lead Generation',
  },
  {
    id: 'google-sheets-reports',
    name: 'Google Sheets Recurring Reports',
    description: 'Automatically generate professional reports from Google Sheets data using AI formatting and deliver via email on schedule',
    trigger: 'Google Sheets',
    actions: ['Report Generation', 'AI Formatting', 'Email Delivery', 'Scheduled Automation'],
    icon: FileSpreadsheet,
    category: 'Productivity',
  },
  {
    id: 'social-media-analysis',
    name: 'Social Media Analysis & Email Generation',
    description: 'Analyze LinkedIn and Twitter profiles from Google Sheets using AI to generate personalized outreach emails automatically',
    trigger: 'Google Sheets',
    actions: ['Social Media Analysis', 'AI Content Generation', 'Email Automation', 'Progress Tracking'],
    icon: Users,
    category: 'Lead Generation',
  },
  {
    id: 'invoice-organizer',
    name: 'Invoice PDF Organizer',
    description: 'Monitors Gmail or Drive for invoices (PDFs), extracts date and amount data, logs to Google Sheets, and sorts by month',
    trigger: 'Gmail/Drive',
    actions: ['PDF Extraction', 'AI Data Processing', 'Google Sheets', 'Auto-Organization'],
    icon: FileText,
    category: 'Productivity',
  },
  {
    id: 'social-trends-tracker',
    name: 'Social Media Trends Tracker',
    description: 'Monitor social media trends across platforms with RapidAPI, analyze with AI, and deliver comprehensive email reports daily or weekly',
    trigger: 'RapidAPI',
    actions: ['Trend Analysis', 'AI Summary', 'Email Reports', 'Multi-Platform'],
    icon: TrendingUp,
    category: 'Content',
  },
  {
    id: 'website-social-summarizer',
    name: 'Website Content & Social Media Summarizer',
    description: 'Summarize website content and create social media posts with ChatGPT and Browse AI',
    trigger: 'Website URL',
    actions: ['Content Scraping', 'AI Summarization', 'Social Media Generation', 'Multi-Platform Posts'],
    icon: Globe,
    category: 'Content',
  },
  {
    id: 'canva-design-automation',
    name: 'Canva Design Automation',
    description: 'Automate the process of generating personalized Canva designs using data from Google Sheets, exporting them, and saving them to Google Drive',
    trigger: 'Google Sheets',
    actions: ['Design Generation', 'Data Personalization', 'Auto Export', 'Google Drive'],
    icon: SiCanva,
    category: 'Content',
  },
  {
    id: 'youtube-channel-summarizer',
    name: 'YouTube Channel Video Summarizer',
    description: 'Find videos from any YouTube channel, generate AI-powered summaries with ChatGPT, and email organized reports with key insights',
    trigger: 'YouTube',
    actions: ['Apify Scraping', 'AI Summaries', 'Email Reports', 'Timestamps'],
    icon: SiYoutube,
    category: 'Content',
  },
  {
    id: 'telegram-calendar',
    name: 'Telegram to Calendar Events',
    description: 'Automatically create Google Calendar events from Telegram messages using AI parsing',
    trigger: 'Telegram',
    actions: ['Google Calendar', 'AI Parsing', 'Event Creation'],
    icon: SiTelegram,
    category: 'Productivity',
  },
]

const categories = ['All', 'Content', 'Productivity', 'Lead Generation', 'HR']

export default function BrowseTemplates() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All')
  const [previewTemplate, setPreviewTemplate] = useState<Template | null>(null)
  const [location, setLocation] = useLocation()

  const filteredTemplates = templates.filter((template) => {
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === 'All' || template.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const handleUseTemplate = (template: Template) => {
    if (template.id === 'blog-content-generator') {
      setLocation('/dashboard/templates/blog-workflow')
    } else if (template.id === 'gmaps-data-scraper') {
      setLocation('/dashboard/templates/gmaps-scraper')
    } else if (template.id === 'gmail-email-automation') {
      setLocation('/dashboard/templates/gmail-automation')
    } else if (template.id === 'youtube-apify-transcription') {
      setLocation('/dashboard/templates/youtube-apify')
    } else if (template.id === 'hr-workflow') {
      setLocation('/dashboard/templates/hr-workflow')
    } else if (template.id === 'daily-digest') {
      setLocation('/dashboard/templates/daily-digest')
    } else if (template.id === 'lead-qualification') {
      setLocation('/dashboard/templates/lead-qualification')
    } else if (template.id === 'google-sheets-reports') {
      setLocation('/dashboard/templates/google-sheets-reports')
    } else if (template.id === 'social-media-analysis') {
      setLocation('/dashboard/templates/social-media-analysis')
    } else if (template.id === 'invoice-organizer') {
      setLocation('/dashboard/templates/invoice-organizer')
    } else if (template.id === 'social-trends-tracker') {
      setLocation('/dashboard/templates/social-trends-tracker')
    } else if (template.id === 'website-social-summarizer') {
      setLocation('/dashboard/templates/website-social-summarizer')
    } else if (template.id === 'canva-design-automation') {
      setLocation('/dashboard/templates/canva-design-automation')
    } else if (template.id === 'youtube-channel-summarizer') {
      setLocation('/dashboard/templates/youtube-channel-summarizer')
    } else if (template.id === 'telegram-calendar') {
      setLocation('/dashboard/templates/telegram-calendar')
    } else {
      // Navigate to automation creation with template
      setLocation(`/dashboard/automations/new?template=${template.id}`)
    }
  }

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Browse Templates</h1>
        <p className="text-muted-foreground">
          Get started quickly with pre-made automation templates
        </p>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search templates..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="w-full sm:w-auto">
              <Filter className="h-4 w-4 mr-2" />
              {selectedCategory}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuLabel>Category</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {categories.map((category) => (
              <DropdownMenuItem
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={selectedCategory === category ? 'bg-accent' : ''}
              >
                {category}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Templates Grid */}
      <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
        {filteredTemplates.map((template) => {
          const Icon = template.icon
          
          return (
            <Card key={template.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="p-2 bg-[#155DB8]/10 rounded-lg">
                    <Icon className="h-6 w-6 text-[#155DB8]" />
                  </div>
                  <Badge variant="secondary">{template.category}</Badge>
                </div>
                <CardTitle className="mt-4">{template.name}</CardTitle>
                <CardDescription>{template.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span className="font-medium">Trigger:</span> {template.trigger}
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground mt-1">
                  <span className="font-medium">Actions:</span> {template.actions.join(', ')}
                </div>

              </CardContent>
              <CardFooter className="gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-1"
                  onClick={() => setPreviewTemplate(template)}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </Button>
                <Button
                  size="sm"
                  className="flex-1 bg-[#155DB8] hover:bg-[#155DB8]/90"
                  onClick={() => handleUseTemplate(template)}
                >
                  <Play className="h-4 w-4 mr-2" />
                  Use
                </Button>
              </CardFooter>
            </Card>
          )
        })}
      </div>

      {/* Preview Dialog */}
      <Dialog open={!!previewTemplate} onOpenChange={() => setPreviewTemplate(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{previewTemplate?.name}</DialogTitle>
            <DialogDescription>{previewTemplate?.description}</DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div>
              <h4 className="font-medium mb-2">How it works</h4>
              <ol className="list-decimal list-inside space-y-2 text-sm text-muted-foreground">
                <li>Connect your {previewTemplate?.trigger} account</li>
                <li>Set up trigger conditions (e.g., specific labels, keywords)</li>
                <li>Configure {previewTemplate?.actions.join(' and ')} settings</li>
                <li>Test the automation with sample data</li>
                <li>Activate and let it run automatically</li>
              </ol>
            </div>
            <div>
              <h4 className="font-medium mb-2">Required connections</h4>
              <div className="flex flex-wrap gap-2">
                <Badge variant="outline">{previewTemplate?.trigger}</Badge>
                {previewTemplate?.actions.map((action) => (
                  <Badge key={action} variant="outline">{action}</Badge>
                ))}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setPreviewTemplate(null)}>
              Close
            </Button>
            <Button
              className="bg-[#155DB8] hover:bg-[#155DB8]/90"
              onClick={() => {
                if (previewTemplate) {
                  handleUseTemplate(previewTemplate)
                }
              }}
            >
              Use Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}