import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useQuery, useMutation } from '@tanstack/react-query';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { SiGooglesheets, SiGoogledrive } from 'react-icons/si';
import { CheckCircle, AlertCircle, Loader2, RefreshCw, ExternalLink, Plus, TestTube } from 'lucide-react';

interface GoogleCredential {
  id: number;
  service: string;
  name: string;
  type: string;
  isActive: boolean;
  createdAt: string;
  expiresAt: string;
}

interface SpreadsheetInfo {
  id: string;
  name: string;
  url: string;
  sheets: SheetInfo[];
}

interface SheetInfo {
  sheetId: number;
  title: string;
  gridProperties: {
    rowCount: number;
    columnCount: number;
  };
}

export default function GoogleSheetsTest() {
  const { toast } = useToast();
  const [selectedCredential, setSelectedCredential] = React.useState<number | null>(null);
  const [selectedSpreadsheet, setSelectedSpreadsheet] = React.useState<string>('');
  const [selectedSheet, setSelectedSheet] = React.useState<string>('');
  const [testData, setTestData] = React.useState<string>('["Test Name", "Test Email", "Test Phone"]');
  const [connectionStatus, setConnectionStatus] = React.useState<'idle' | 'testing' | 'connected' | 'failed'>('idle');
  const [spreadsheets, setSpreadsheets] = React.useState<SpreadsheetInfo[]>([]);
  const [sheetData, setSheetData] = React.useState<any[]>([]);

  const { data: credentials, isLoading: credentialsLoading, refetch: refetchCredentials } = useQuery<GoogleCredential[]>({
    queryKey: ['/api/credentials'],
    queryFn: () => apiRequest('GET', '/api/credentials'),
  });

  const googleCredentials = Array.isArray(credentials) ? credentials.filter(cred => cred.service === 'google') : [];

  const testConnection = useMutation({
    mutationFn: async (credentialId: number) => {
      setConnectionStatus('testing');
      const response = await apiRequest('POST', '/api/google-sheets/test-connection', {
        credentialId
      });
      return response;
    },
    onSuccess: (data) => {
      if (data.connected) {
        setConnectionStatus('connected');
        toast({
          title: "Connection Successful",
          description: "Google Sheets connection is working properly",
        });
        loadSpreadsheets();
      } else {
        setConnectionStatus('failed');
        toast({
          title: "Connection Failed",
          description: "Unable to connect to Google Sheets",
          variant: "destructive",
        });
      }
    },
    onError: (error) => {
      setConnectionStatus('failed');
      toast({
        title: "Connection Error",
        description: error.message || "Failed to test connection",
        variant: "destructive",
      });
    }
  });

  const loadSpreadsheets = async () => {
    if (!selectedCredential) return;
    
    try {
      const response = await apiRequest('POST', '/api/google-sheets/list-spreadsheets', {
        credentialId: selectedCredential,
        maxResults: 20
      });
      setSpreadsheets(response.spreadsheets || []);
    } catch (error) {
      console.error('Failed to load spreadsheets:', error);
      toast({
        title: "Error",
        description: "Failed to load spreadsheets",
        variant: "destructive",
      });
    }
  };

  const loadSheetData = async () => {
    if (!selectedCredential || !selectedSpreadsheet || !selectedSheet) return;
    
    try {
      const response = await apiRequest('POST', '/api/google-sheets/get-sheet-data', {
        credentialId: selectedCredential,
        spreadsheetId: selectedSpreadsheet,
        range: `${selectedSheet}!A1:Z100`
      });
      setSheetData(response.values || []);
    } catch (error) {
      console.error('Failed to load sheet data:', error);
      toast({
        title: "Error",
        description: "Failed to load sheet data",
        variant: "destructive",
      });
    }
  };

  const appendTestData = async () => {
    if (!selectedCredential || !selectedSpreadsheet || !selectedSheet) return;
    
    try {
      const data = JSON.parse(testData);
      await apiRequest('POST', '/api/google-sheets/append-data', {
        credentialId: selectedCredential,
        spreadsheetId: selectedSpreadsheet,
        range: `${selectedSheet}!A1`,
        values: [data]
      });
      
      toast({
        title: "Data Added",
        description: "Test data has been appended to the sheet",
      });
      
      // Refresh sheet data
      loadSheetData();
    } catch (error) {
      console.error('Failed to append data:', error);
      toast({
        title: "Error",
        description: "Failed to append data to sheet",
        variant: "destructive",
      });
    }
  };

  const createNewCredential = async () => {
    try {
      // Create a temporary automation for OAuth
      const automation = await apiRequest('POST', '/api/automations', {
        name: 'Google Sheets OAuth Setup',
        description: 'Temporary automation for Google Sheets authentication',
        trigger: { type: 'schedule', config: { runType: 'once', runDate: '2025-01-01', runTime: '00:00' } },
        actions: [{ id: 'temp-1', name: 'Temp Action', type: 'google-sheets', config: {} }]
      });

      const response = await apiRequest('GET', `/api/auth/google/start?automationId=${automation.id}`);
      if (response.authUrl) {
        window.open(response.authUrl, 'google-auth', 'width=600,height=700');
        
        // Listen for OAuth completion
        const handleMessage = (event: MessageEvent) => {
          if (event.origin !== window.location.origin) return;
          
          if (event.data.type === 'oauth-success') {
            window.removeEventListener('message', handleMessage);
            refetchCredentials();
            toast({
              title: "Authentication Successful",
              description: "Google account connected successfully",
            });
          }
        };
        
        window.addEventListener('message', handleMessage);
      }
    } catch (error) {
      console.error('Google authentication error:', error);
      toast({
        title: "Authentication Error",
        description: "Failed to start Google authentication",
        variant: "destructive",
      });
    }
  };

  const selectedSpreadsheetInfo = spreadsheets.find(s => s.id === selectedSpreadsheet);

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto space-y-6">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-2">Google Sheets Integration Test</h1>
          <p className="text-muted-foreground">Test and configure Google Sheets integration</p>
        </div>

        {/* Credentials Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <SiGooglesheets className="h-5 w-5 text-green-600" />
              Google Credentials
            </CardTitle>
            <CardDescription>
              Select or create Google credentials with Sheets and Drive access
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {credentialsLoading ? (
              <div className="flex items-center justify-center p-8">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                <span>Loading credentials...</span>
              </div>
            ) : googleCredentials.length > 0 ? (
              <div className="space-y-4">
                {googleCredentials.map((credential) => (
                  <div key={credential.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-2">
                        <SiGooglesheets className="h-4 w-4 text-green-600" />
                        <SiGoogledrive className="h-4 w-4 text-yellow-600" />
                      </div>
                      <div>
                        <p className="font-medium">{credential.name}</p>
                        <p className="text-sm text-muted-foreground">
                          Expires: {new Date(credential.expiresAt).toLocaleString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant={selectedCredential === credential.id ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSelectedCredential(credential.id)}
                      >
                        {selectedCredential === credential.id ? "Selected" : "Select"}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => testConnection.mutate(credential.id)}
                        disabled={testConnection.isPending}
                      >
                        {testConnection.isPending && selectedCredential === credential.id ? (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        ) : (
                          <TestTube className="h-3 w-3" />
                        )}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <SiGooglesheets className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No Google Credentials Found</h3>
                <p className="text-muted-foreground mb-4">
                  Create a new Google credential with Sheets and Drive access
                </p>
                <Button onClick={createNewCredential} className="bg-green-600 hover:bg-green-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Connect Google Account
                </Button>
              </div>
            )}

            {/* Connection Status */}
            {connectionStatus !== 'idle' && (
              <div className={`p-4 rounded-lg border ${
                connectionStatus === 'connected' ? 'bg-green-50 border-green-200' :
                connectionStatus === 'failed' ? 'bg-red-50 border-red-200' :
                'bg-yellow-50 border-yellow-200'
              }`}>
                <div className="flex items-center gap-2">
                  {connectionStatus === 'testing' && <Loader2 className="h-4 w-4 animate-spin" />}
                  {connectionStatus === 'connected' && <CheckCircle className="h-4 w-4 text-green-600" />}
                  {connectionStatus === 'failed' && <AlertCircle className="h-4 w-4 text-red-600" />}
                  <span className="font-medium">
                    {connectionStatus === 'testing' && 'Testing connection...'}
                    {connectionStatus === 'connected' && 'Connected to Google Sheets'}
                    {connectionStatus === 'failed' && 'Connection failed - may need re-authentication'}
                  </span>
                </div>
                {connectionStatus === 'failed' && (
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="mt-2"
                    onClick={createNewCredential}
                  >
                    Re-authenticate with Google
                  </Button>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Spreadsheet Selection */}
        {connectionStatus === 'connected' && (
          <Card>
            <CardHeader>
              <CardTitle>Spreadsheet Selection</CardTitle>
              <CardDescription>
                Choose a spreadsheet and sheet to work with
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Select value={selectedSpreadsheet} onValueChange={setSelectedSpreadsheet}>
                  <SelectTrigger className="flex-1">
                    <SelectValue placeholder="Select a spreadsheet" />
                  </SelectTrigger>
                  <SelectContent>
                    {spreadsheets.map((spreadsheet) => (
                      <SelectItem key={spreadsheet.id} value={spreadsheet.id}>
                        {spreadsheet.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button variant="outline" onClick={loadSpreadsheets} disabled={!selectedCredential}>
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>

              {selectedSpreadsheetInfo && (
                <div className="space-y-4">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Badge variant="outline">{selectedSpreadsheetInfo.sheets.length} sheets</Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => window.open(selectedSpreadsheetInfo.url, '_blank')}
                    >
                      <ExternalLink className="h-3 w-3 mr-1" />
                      View in Google Sheets
                    </Button>
                  </div>

                  <Select value={selectedSheet} onValueChange={setSelectedSheet}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a sheet" />
                    </SelectTrigger>
                    <SelectContent>
                      {selectedSpreadsheetInfo.sheets.map((sheet) => (
                        <SelectItem key={sheet.sheetId} value={sheet.title}>
                          <div className="flex items-center gap-2">
                            <span>{sheet.title}</span>
                            <Badge variant="outline">
                              {sheet.gridProperties.rowCount}×{sheet.gridProperties.columnCount}
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Data Operations */}
        {selectedSheet && (
          <Card>
            <CardHeader>
              <CardTitle>Data Operations</CardTitle>
              <CardDescription>
                Test reading and writing data to Google Sheets
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Button onClick={loadSheetData} variant="outline">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Load Sheet Data
                </Button>
                <Button onClick={appendTestData} className="bg-green-600 hover:bg-green-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Append Test Data
                </Button>
              </div>

              <div className="space-y-2">
                <Label htmlFor="test-data">Test Data (JSON Array)</Label>
                <Input
                  id="test-data"
                  value={testData}
                  onChange={(e) => setTestData(e.target.value)}
                  placeholder='["Column1", "Column2", "Column3"]'
                />
              </div>

              {sheetData.length > 0 && (
                <div className="space-y-2">
                  <Label>Sheet Data Preview</Label>
                  <div className="border rounded-lg p-4 bg-gray-50 max-h-60 overflow-auto">
                    <table className="w-full text-sm">
                      <tbody>
                        {sheetData.slice(0, 10).map((row, index) => (
                          <tr key={index}>
                            {row.map((cell: string, cellIndex: number) => (
                              <td key={cellIndex} className="px-2 py-1 border-r border-gray-200">
                                {cell}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                    {sheetData.length > 10 && (
                      <p className="text-xs text-muted-foreground mt-2">
                        Showing first 10 rows of {sheetData.length} total rows
                      </p>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}