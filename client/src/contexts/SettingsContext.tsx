import React, { createContext, useContext, useEffect, useState } from 'react'
import { useAuth } from './AuthContext'

interface Settings {
  timezone: string
  dateFormat: string
  timeFormat: '12h' | '24h'
  notifications: {
    email: boolean
    browser: boolean
    automationSuccess: boolean
    automationFailure: boolean
  }
}

interface SettingsContextType {
  settings: Settings
  updateSettings: (settings: Partial<Settings>) => Promise<void>
  loading: boolean
}

const defaultSettings: Settings = {
  timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  dateFormat: 'MM/DD/YYYY',
  timeFormat: '12h',
  notifications: {
    email: true,
    browser: true,
    automationSuccess: true,
    automationFailure: true,
  },
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined)

export const useSettings = () => {
  const context = useContext(SettingsContext)
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider')
  }
  return context
}

export const SettingsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [settings, setSettings] = useState<Settings>(defaultSettings)
  const [loading, setLoading] = useState(true)
  const { user } = useAuth()

  useEffect(() => {
    if (user) {
      loadSettings()
    } else {
      setSettings(defaultSettings)
      setLoading(false)
    }
  }, [user])

  const loadSettings = async () => {
    if (!user) return

    try {
      // Load settings from localStorage for now (will be replaced with Supabase later)
      const storedSettings = localStorage.getItem(`settings_${user.id}`)
      
      if (storedSettings) {
        setSettings({
          ...defaultSettings,
          ...JSON.parse(storedSettings),
        })
      }
    } catch (error) {
      console.error('Error loading settings from localStorage:', error)
    } finally {
      setLoading(false)
    }
  }

  const updateSettings = async (newSettings: Partial<Settings>) => {
    if (!user) return

    const updatedSettings = {
      ...settings,
      ...newSettings,
    }

    setSettings(updatedSettings)

    try {
      // Save settings to localStorage for now (will be replaced with Supabase later)
      localStorage.setItem(`settings_${user.id}`, JSON.stringify(updatedSettings))
    } catch (error) {
      console.error('Error saving settings to localStorage:', error)
      // Revert on error
      setSettings(settings)
    }
  }

  return (
    <SettingsContext.Provider value={{ settings, updateSettings, loading }}>
      {children}
    </SettingsContext.Provider>
  )
}