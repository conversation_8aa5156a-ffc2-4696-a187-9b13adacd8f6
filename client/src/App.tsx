import { Switch, Route, Redirect, useLocation } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { AuthProvider, useAuth } from "@/contexts/AuthContext";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { SettingsProvider } from "@/contexts/SettingsContext";
import { ErrorBoundary } from "@/components/error-boundary";
import { FullPageLoader } from "@/components/ui/loading-spinner";
import NotFoundPage from "@/pages/404";
import ErrorPage from "@/pages/error";
import AuthPage from "@/pages/auth";
import Dashboard from "@/pages/dashboard";
import DashboardLayout from "@/pages/dashboard-layout";
import BrowseTemplates from "@/pages/browse-templates";
import MyAutomations from "@/pages/my-automations";
import ActivityLogs from "@/pages/activity-logs";
import Settings from "@/pages/settings";
import Team from "@/pages/team";
import Credentials from "@/pages/credentials";

import AutomationForm from "@/pages/automation-form-new";
import AutomationFormMultiPage from "@/pages/automation-form-multi-page";
import AutomationChat from "@/pages/automation-chat";
import AutomationEdit from "@/pages/automation-edit";
import BlogWorkflowTemplate from "@/pages/template-blog-workflow";
import GoogleMapsScraperTemplate from "@/pages/template-gmaps-scraper";
import GmailAutomationTemplate from "@/pages/template-gmail-automation";
import YouTubeApifyTemplate from "@/pages/template-youtube-apify";
import HRWorkflowTemplate from "@/pages/template-hr-workflow";
import DailyDigestTemplate from "@/pages/template-daily-digest";
import LeadQualificationTemplate from "@/pages/template-lead-qualification";
import GoogleSheetsReportsTemplate from "@/pages/template-google-sheets-reports";
import SocialMediaAnalysisTemplate from "@/pages/template-social-media-analysis";
import InvoiceOrganizerTemplate from "@/pages/template-invoice-organizer";
import SocialTrendsTrackerTemplate from "@/pages/template-social-trends-tracker";
import WebsiteSocialSummarizerTemplate from "@/pages/template-website-social-summarizer";
import CanvaDesignAutomationTemplate from "@/pages/template-canva-design-automation";
import YouTubeChannelSummarizerTemplate from "@/pages/template-youtube-channel-summarizer";
import TelegramCalendarTemplate from "@/pages/template-telegram-calendar";
import OAuthSuccess from "@/pages/oauth-success";
import GoogleSheetsTest from "@/pages/google-sheets-test";




function Router() {
  const { user, loading, isPasswordRecovery } = useAuth();
  const [location] = useLocation();

  if (loading) {
    return <FullPageLoader />;
  }

  // Handle root path redirect immediately
  if (location === "/") {
    return user && !isPasswordRecovery ? <Redirect to="/dashboard" /> : <Redirect to="/auth" />;
  }

  return (
    <Switch>

      <Route path="/auth">
        {user && !isPasswordRecovery ? <Redirect to="/dashboard" /> : <AuthPage />}
      </Route>

      <Route path="/dashboard">
        {user ? <Dashboard /> : <Redirect to="/auth" />}
      </Route>

      {/* Protected Dashboard Routes */}
      <Route path="/dashboard/browse-templates">
        {user ? (
          <DashboardLayout>
            <BrowseTemplates />
          </DashboardLayout>
        ) : (
          <Redirect to="/auth" />
        )}
      </Route>

      <Route path="/dashboard/automations">
        {user ? (
          <DashboardLayout>
            <MyAutomations />
          </DashboardLayout>
        ) : (
          <Redirect to="/auth" />
        )}
      </Route>

      <Route path="/dashboard/logs">
        {user ? (
          <DashboardLayout>
            <ActivityLogs />
          </DashboardLayout>
        ) : (
          <Redirect to="/auth" />
        )}
      </Route>

      <Route path="/dashboard/settings">
        {user ? (
          <DashboardLayout>
            <Settings />
          </DashboardLayout>
        ) : (
          <Redirect to="/auth" />
        )}
      </Route>

      <Route path="/dashboard/team">
        {user ? (
          <DashboardLayout>
            <Team />
          </DashboardLayout>
        ) : (
          <Redirect to="/auth" />
        )}
      </Route>



      <Route path="/dashboard/credentials">
        {user ? (
          <DashboardLayout>
            <Credentials />
          </DashboardLayout>
        ) : (
          <Redirect to="/auth" />
        )}
      </Route>



      <Route path="/dashboard/automations/new">
        {user ? (
          <DashboardLayout>
            <AutomationFormMultiPage />
          </DashboardLayout>
        ) : (
          <Redirect to="/auth" />
        )}
      </Route>

      <Route path="/dashboard/automations/edit/:id">
        {user ? (
          <DashboardLayout>
            <AutomationEdit />
          </DashboardLayout>
        ) : (
          <Redirect to="/auth" />
        )}
      </Route>

      <Route path="/dashboard/automations/:id/chat">
        {user ? (
          <DashboardLayout>
            <AutomationChat />
          </DashboardLayout>
        ) : (
          <Redirect to="/auth" />
        )}
      </Route>

      <Route path="/dashboard/templates/blog-workflow">
        {user ? <BlogWorkflowTemplate /> : <Redirect to="/auth" />}
      </Route>

      <Route path="/dashboard/templates/gmaps-scraper">
        {user ? <GoogleMapsScraperTemplate /> : <Redirect to="/auth" />}
      </Route>

      <Route path="/dashboard/templates/gmail-automation">
        {user ? <GmailAutomationTemplate /> : <Redirect to="/auth" />}
      </Route>

      <Route path="/dashboard/templates/youtube-apify">
        {user ? <YouTubeApifyTemplate /> : <Redirect to="/auth" />}
      </Route>

      <Route path="/dashboard/templates/hr-workflow">
        {user ? <HRWorkflowTemplate /> : <Redirect to="/auth" />}
      </Route>

      <Route path="/dashboard/templates/daily-digest">
        {user ? <DailyDigestTemplate /> : <Redirect to="/auth" />}
      </Route>

      <Route path="/dashboard/templates/lead-qualification">
        {user ? <LeadQualificationTemplate /> : <Redirect to="/auth" />}
      </Route>

      <Route path="/dashboard/templates/google-sheets-reports">
        {user ? <GoogleSheetsReportsTemplate /> : <Redirect to="/auth" />}
      </Route>

      <Route path="/dashboard/templates/social-media-analysis">
        {user ? <SocialMediaAnalysisTemplate /> : <Redirect to="/auth" />}
      </Route>

      <Route path="/dashboard/templates/invoice-organizer">
        {user ? <InvoiceOrganizerTemplate /> : <Redirect to="/auth" />}
      </Route>

      <Route path="/dashboard/templates/social-trends-tracker">
        {user ? <SocialTrendsTrackerTemplate /> : <Redirect to="/auth" />}
      </Route>

      <Route path="/dashboard/templates/website-social-summarizer">
        {user ? <WebsiteSocialSummarizerTemplate /> : <Redirect to="/auth" />}
      </Route>

      <Route path="/dashboard/templates/canva-design-automation">
        {user ? <CanvaDesignAutomationTemplate /> : <Redirect to="/auth" />}
      </Route>

      <Route path="/dashboard/templates/youtube-channel-summarizer">
        {user ? <YouTubeChannelSummarizerTemplate /> : <Redirect to="/auth" />}
      </Route>

      <Route path="/dashboard/templates/telegram-calendar">
        {user ? <TelegramCalendarTemplate /> : <Redirect to="/auth" />}
      </Route>

      {/* Google Sheets Test Page */}
      <Route path="/dashboard/google-sheets-test">
        {user ? (
          <DashboardLayout>
            <GoogleSheetsTest />
          </DashboardLayout>
        ) : (
          <Redirect to="/auth" />
        )}
      </Route>

      {/* OAuth Success Route - accessible without authentication */}
      <Route path="/oauth-success" component={OAuthSuccess} />

      {/* Error routes */}
      <Route path="/error" component={ErrorPage} />
      
      {/* 404 - Must be last */}
      <Route component={NotFoundPage} />
    </Switch>
  );
}

function App() {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <ThemeProvider>
            <AuthProvider>
              <SettingsProvider>
                <Router />
                <Toaster />
              </SettingsProvider>
            </AuthProvider>
          </ThemeProvider>
        </TooltipProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;