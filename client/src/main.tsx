import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";
import { enableSmartRefresh } from "./utils/smart-refresh";

// Register service worker for better performance and caching
if ('serviceWorker' in navigator && import.meta.env.PROD) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('SW registered: ', registration)
      })
      .catch((registrationError) => {
        console.log('SW registration failed: ', registrationError)
      })
  })
}

// Enable smart refresh in development
if (import.meta.env.DEV) {
  enableSmartRefresh()
}

createRoot(document.getElementById("root")!).render(<App />);
