import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { 
  MessageSquare,
  Mail,
  Calendar,
  FileText,
  FileSpreadsheet,
  Globe,
  FilePlus,
  Send,
  Eye,
  Search
} from 'lucide-react'
import { SiDiscord, SiGmail, SiGooglecalendar, SiGoogledocs, SiGooglesheets, SiSlack } from 'react-icons/si'

export interface Action {
  id: string
  name: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  color: string
  category: 'read' | 'write' | 'both'
}

export const availableActions: Action[] = [
  {
    id: 'discord-read',
    name: 'Discord Read',
    description: 'Read messages from any Discord channel',
    icon: SiDiscord,
    color: 'text-indigo-600',
    category: 'read'
  },
  {
    id: 'discord-send',
    name: 'Discord Send',
    description: 'Send messages to any Discord channel',
    icon: SiDiscord,
    color: 'text-indigo-600',
    category: 'write'
  },
  {
    id: 'gmail-read',
    name: 'Gmail Read',
    description: 'Read emails from your Gmail account',
    icon: SiGmail,
    color: 'text-red-600',
    category: 'read'
  },
  {
    id: 'gmail-send',
    name: 'Gmail Send',
    description: 'Send emails via your Gmail account',
    icon: SiGmail,
    color: 'text-red-600',
    category: 'write'
  },
  {
    id: 'google-calendar-read',
    name: 'Google Calendar Read',
    description: 'Read events from any Google Calendar',
    icon: SiGooglecalendar,
    color: 'text-blue-600',
    category: 'read'
  },
  {
    id: 'google-calendar-write',
    name: 'Google Calendar Write',
    description: 'Create events on any Google Calendar',
    icon: SiGooglecalendar,
    color: 'text-blue-600',
    category: 'write'
  },
  {
    id: 'google-docs-read',
    name: 'Google Docs Read',
    description: 'Read content from any Google Doc',
    icon: SiGoogledocs,
    color: 'text-blue-500',
    category: 'read'
  },
  {
    id: 'google-docs-write',
    name: 'Google Docs Write',
    description: 'Write content to any Google Doc',
    icon: SiGoogledocs,
    color: 'text-blue-500',
    category: 'write'
  },
  {
    id: 'google-sheets-read',
    name: 'Google Sheets Read',
    description: 'Read data from any Google Sheet',
    icon: SiGooglesheets,
    color: 'text-green-600',
    category: 'read'
  },
  {
    id: 'google-sheets-write',
    name: 'Google Sheets Write',
    description: 'Write data to any Google Sheet',
    icon: SiGooglesheets,
    color: 'text-green-600',
    category: 'write'
  },
  {
    id: 'http-request',
    name: 'HTTP Request',
    description: 'Make GET, POST, PUT, or DELETE HTTP requests',
    icon: Globe,
    color: 'text-gray-600',
    category: 'both'
  },
  {
    id: 'resume-pdf',
    name: 'Resume PDF Generator',
    description: 'Generate a professional PDF resume from user-provided details',
    icon: FilePlus,
    color: 'text-purple-600',
    category: 'write'
  },
  {
    id: 'slack-read',
    name: 'Slack Read',
    description: 'Read messages from a specified Slack channel',
    icon: SiSlack,
    color: 'text-purple-600',
    category: 'read'
  },
  {
    id: 'slack-send',
    name: 'Slack Send',
    description: 'Send a message to a specified Slack channel',
    icon: SiSlack,
    color: 'text-purple-600',
    category: 'write'
  }
]

interface ActionsSelectorProps {
  selectedActions: string[]
  onActionsChange: (actions: string[]) => void
}

export function ActionsSelector({ selectedActions, onActionsChange }: ActionsSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [showAll, setShowAll] = useState(false)

  const handleActionToggle = (actionId: string) => {
    if (selectedActions.includes(actionId)) {
      onActionsChange(selectedActions.filter(id => id !== actionId))
    } else {
      onActionsChange([...selectedActions, actionId])
    }
  }

  // Filter actions based on search query
  const filteredActions = availableActions.filter(action => {
    const query = searchQuery.toLowerCase()
    return (
      action.name.toLowerCase().includes(query) ||
      action.description.toLowerCase().includes(query) ||
      action.category.toLowerCase().includes(query)
    )
  })

  // Limit to 10 actions initially, unless showAll is true or there's a search query
  const displayedActions = searchQuery || showAll ? filteredActions : filteredActions.slice(0, 10)

  return (
    <div className="space-y-4">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search actions by name, description, or category..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>

      {filteredActions.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-muted-foreground">No actions found matching "{searchQuery}"</p>
        </div>
      ) : (
        <>
          <div className="grid md:grid-cols-2 gap-4">
            {displayedActions.map((action) => {
              const Icon = action.icon
              const isSelected = selectedActions.includes(action.id)
              
              return (
                <Card 
                  key={action.id}
                  className={`cursor-pointer transition-all ${
                    isSelected ? 'ring-2 ring-primary' : ''
                  }`}
                  onClick={() => handleActionToggle(action.id)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Icon className={`h-5 w-5 ${action.color}`} />
                        <div>
                          <CardTitle className="text-base">{action.name}</CardTitle>
                          <Badge variant="outline" className="mt-1">
                            {action.category === 'read' ? 'Read' : action.category === 'write' ? 'Write' : 'Read/Write'}
                          </Badge>
                        </div>
                      </div>
                      <Checkbox checked={isSelected} />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">{action.description}</p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
          
          {!searchQuery && !showAll && filteredActions.length > 10 && (
            <div className="flex justify-center pt-4">
              <Button 
                variant="outline" 
                onClick={() => setShowAll(true)}
                className="w-full max-w-xs"
              >
                Load More Actions ({filteredActions.length - 10} more)
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  )
}