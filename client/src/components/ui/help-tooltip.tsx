import { useState } from 'react'
import { Info, HelpCircle, Sparkles, Lightbulb } from 'lucide-react'
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface HelpTooltipProps {
  content: string
  side?: 'top' | 'right' | 'bottom' | 'left'
  align?: 'start' | 'center' | 'end'
  className?: string
  icon?: 'info' | 'help' | 'sparkles' | 'lightbulb'
  variant?: 'default' | 'ghost' | 'ai'
  children?: React.ReactNode
  example?: string
  learnMoreUrl?: string
}

export function HelpTooltip({
  content,
  side = 'top',
  align = 'center',
  className,
  icon = 'info',
  variant = 'default',
  children,
  example,
  learnMoreUrl
}: HelpTooltipProps) {
  const [open, setOpen] = useState(false)

  const getIcon = () => {
    switch (icon) {
      case 'help':
        return <HelpCircle className="h-3.5 w-3.5" />
      case 'sparkles':
        return <Sparkles className="h-3.5 w-3.5" />
      case 'lightbulb':
        return <Lightbulb className="h-3.5 w-3.5" />
      default:
        return <Info className="h-3.5 w-3.5" />
    }
  }

  const getIconColor = () => {
    if (variant === 'ai') return 'text-purple-600 hover:text-purple-700'
    if (variant === 'ghost') return 'text-muted-foreground hover:text-foreground'
    return 'text-blue-600 hover:text-blue-700'
  }

  return (
    <TooltipProvider>
      <Tooltip open={open} onOpenChange={setOpen}>
        <TooltipTrigger asChild>
          {children || (
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "h-4 w-4 p-0 hover:bg-transparent",
                getIconColor(),
                className
              )}
              onMouseEnter={() => setOpen(true)}
              onMouseLeave={() => setOpen(false)}
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                setOpen(!open)
              }}
            >
              {getIcon()}
            </Button>
          )}
        </TooltipTrigger>
        <TooltipContent 
          side={side} 
          align={align}
          className={cn(
            "max-w-sm z-50",
            variant === 'ai' && "border-purple-200 bg-purple-50 dark:border-purple-800 dark:bg-purple-950"
          )}
        >
          <div className="space-y-2">
            <p className="text-sm">{content}</p>
            {example && (
              <div className="border-t pt-2">
                <p className="text-xs text-muted-foreground mb-1">Example:</p>
                <code className="text-xs bg-muted px-1 py-0.5 rounded">
                  {example}
                </code>
              </div>
            )}
            {learnMoreUrl && (
              <a
                href={learnMoreUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="text-xs text-blue-600 hover:underline block pt-1"
                onClick={(e) => e.stopPropagation()}
              >
                Learn more →
              </a>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}