import { useState } from 'react'
import { Plus, X, GripVertical } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

export interface CustomAction {
  id: string
  name: string
  type: 'webhook' | 'email' | 'database' | 'api' | 'notification' | 'custom'
  description: string
  config: Record<string, any>
}

interface CustomActionsEditorProps {
  actions: CustomAction[]
  onActionsChange: (actions: CustomAction[]) => void
  title?: string
  description?: string
}

export function CustomActionsEditor({ 
  actions, 
  onActionsChange, 
  title = "Custom Actions",
  description = "Add custom actions to extend your automation"
}: CustomActionsEditorProps) {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [editingAction, setEditingAction] = useState<CustomAction | null>(null)
  const [newAction, setNewAction] = useState<Partial<CustomAction>>({
    name: '',
    type: 'webhook',
    description: '',
    config: {}
  })

  const actionTypes = [
    { value: 'webhook', label: 'Webhook', description: 'Send data to external URL' },
    { value: 'email', label: 'Email', description: 'Send custom email notifications' },
    { value: 'database', label: 'Database', description: 'Store data in database' },
    { value: 'api', label: 'API Call', description: 'Call external API' },
    { value: 'notification', label: 'Notification', description: 'Send push notification' },
    { value: 'custom', label: 'Custom Script', description: 'Run custom code' }
  ]

  const handleAddAction = () => {
    if (newAction.name && newAction.type) {
      const action: CustomAction = {
        id: `action_${Date.now()}`,
        name: newAction.name,
        type: newAction.type as CustomAction['type'],
        description: newAction.description || '',
        config: getDefaultConfig(newAction.type as CustomAction['type'])
      }
      onActionsChange([...actions, action])
      setNewAction({ name: '', type: 'webhook', description: '', config: {} })
      setIsAddDialogOpen(false)
    }
  }

  const handleUpdateAction = (actionId: string, updates: Partial<CustomAction>) => {
    onActionsChange(
      actions.map(action => 
        action.id === actionId ? { ...action, ...updates } : action
      )
    )
  }

  const handleDeleteAction = (actionId: string) => {
    onActionsChange(actions.filter(action => action.id !== actionId))
  }

  const getDefaultConfig = (type: CustomAction['type']) => {
    switch (type) {
      case 'webhook':
        return { url: '', method: 'POST', headers: {}, body: '' }
      case 'email':
        return { to: '', subject: '', body: '', from: '' }
      case 'database':
        return { table: '', operation: 'insert', data: {} }
      case 'api':
        return { endpoint: '', method: 'GET', headers: {}, params: {} }
      case 'notification':
        return { title: '', message: '', channel: '' }
      case 'custom':
        return { script: '', language: 'javascript' }
      default:
        return {}
    }
  }

  const renderActionConfig = (action: CustomAction) => {
    switch (action.type) {
      case 'webhook':
        return (
          <div className="space-y-3">
            <div>
              <Label htmlFor={`${action.id}-url`}>Webhook URL</Label>
              <Input
                id={`${action.id}-url`}
                placeholder="https://api.example.com/webhook"
                value={action.config.url || ''}
                onChange={(e) => handleUpdateAction(action.id, {
                  config: { ...action.config, url: e.target.value }
                })}
              />
            </div>
            <div>
              <Label htmlFor={`${action.id}-method`}>Method</Label>
              <Select
                value={action.config.method || 'POST'}
                onValueChange={(value) => handleUpdateAction(action.id, {
                  config: { ...action.config, method: value }
                })}
              >
                <SelectTrigger id={`${action.id}-method`}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="GET">GET</SelectItem>
                  <SelectItem value="POST">POST</SelectItem>
                  <SelectItem value="PUT">PUT</SelectItem>
                  <SelectItem value="DELETE">DELETE</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )
      case 'email':
        return (
          <div className="space-y-3">
            <div>
              <Label htmlFor={`${action.id}-to`}>To</Label>
              <Input
                id={`${action.id}-to`}
                placeholder="<EMAIL>"
                value={action.config.to || ''}
                onChange={(e) => handleUpdateAction(action.id, {
                  config: { ...action.config, to: e.target.value }
                })}
              />
            </div>
            <div>
              <Label htmlFor={`${action.id}-subject`}>Subject</Label>
              <Input
                id={`${action.id}-subject`}
                placeholder="Email subject"
                value={action.config.subject || ''}
                onChange={(e) => handleUpdateAction(action.id, {
                  config: { ...action.config, subject: e.target.value }
                })}
              />
            </div>
          </div>
        )
      case 'database':
        return (
          <div className="space-y-3">
            <div>
              <Label htmlFor={`${action.id}-table`}>Table Name</Label>
              <Input
                id={`${action.id}-table`}
                placeholder="users"
                value={action.config.table || ''}
                onChange={(e) => handleUpdateAction(action.id, {
                  config: { ...action.config, table: e.target.value }
                })}
              />
            </div>
            <div>
              <Label htmlFor={`${action.id}-operation`}>Operation</Label>
              <Select
                value={action.config.operation || 'insert'}
                onValueChange={(value) => handleUpdateAction(action.id, {
                  config: { ...action.config, operation: value }
                })}
              >
                <SelectTrigger id={`${action.id}-operation`}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="insert">Insert</SelectItem>
                  <SelectItem value="update">Update</SelectItem>
                  <SelectItem value="delete">Delete</SelectItem>
                  <SelectItem value="select">Select</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )
      case 'api':
        return (
          <div className="space-y-3">
            <div>
              <Label htmlFor={`${action.id}-endpoint`}>API Endpoint</Label>
              <Input
                id={`${action.id}-endpoint`}
                placeholder="https://api.service.com/endpoint"
                value={action.config.endpoint || ''}
                onChange={(e) => handleUpdateAction(action.id, {
                  config: { ...action.config, endpoint: e.target.value }
                })}
              />
            </div>
            <div>
              <Label htmlFor={`${action.id}-api-method`}>Method</Label>
              <Select
                value={action.config.method || 'GET'}
                onValueChange={(value) => handleUpdateAction(action.id, {
                  config: { ...action.config, method: value }
                })}
              >
                <SelectTrigger id={`${action.id}-api-method`}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="GET">GET</SelectItem>
                  <SelectItem value="POST">POST</SelectItem>
                  <SelectItem value="PUT">PUT</SelectItem>
                  <SelectItem value="DELETE">DELETE</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )
      case 'notification':
        return (
          <div className="space-y-3">
            <div>
              <Label htmlFor={`${action.id}-title`}>Notification Title</Label>
              <Input
                id={`${action.id}-title`}
                placeholder="Notification title"
                value={action.config.title || ''}
                onChange={(e) => handleUpdateAction(action.id, {
                  config: { ...action.config, title: e.target.value }
                })}
              />
            </div>
            <div>
              <Label htmlFor={`${action.id}-channel`}>Channel</Label>
              <Select
                value={action.config.channel || 'push'}
                onValueChange={(value) => handleUpdateAction(action.id, {
                  config: { ...action.config, channel: value }
                })}
              >
                <SelectTrigger id={`${action.id}-channel`}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="push">Push Notification</SelectItem>
                  <SelectItem value="sms">SMS</SelectItem>
                  <SelectItem value="slack">Slack</SelectItem>
                  <SelectItem value="discord">Discord</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )
      case 'custom':
        return (
          <div className="space-y-3">
            <div>
              <Label htmlFor={`${action.id}-script`}>Custom Script</Label>
              <Textarea
                id={`${action.id}-script`}
                placeholder="// Your custom code here"
                value={action.config.script || ''}
                onChange={(e) => handleUpdateAction(action.id, {
                  config: { ...action.config, script: e.target.value }
                })}
                className="font-mono text-sm"
                rows={6}
              />
            </div>
          </div>
        )
      default:
        return null
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">{title}</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">{description}</p>
        </div>
        <Button onClick={() => setIsAddDialogOpen(true)} size="sm">
          <Plus className="h-4 w-4 mr-2" />
          Add Action
        </Button>
      </div>

      <div className="space-y-3">
        {actions.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-8 text-center">
              <p className="text-gray-500 dark:text-gray-400 mb-4">No custom actions added yet</p>
              <Button onClick={() => setIsAddDialogOpen(true)} variant="outline" size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Action
              </Button>
            </CardContent>
          </Card>
        ) : (
          actions.map((action, index) => (
            <Card key={action.id}>
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    <GripVertical className="h-4 w-4 text-gray-400" />
                    <div>
                      <CardTitle className="text-base">{action.name}</CardTitle>
                      <CardDescription className="text-sm">
                        {action.description || actionTypes.find(t => t.value === action.type)?.description}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setEditingAction(action)}
                    >
                      Edit
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteAction(action.id)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {renderActionConfig(action)}
              </CardContent>
            </Card>
          ))
        )}
      </div>

      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Custom Action</DialogTitle>
            <DialogDescription>
              Create a custom action to extend your automation capabilities
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="action-name">Action Name</Label>
              <Input
                id="action-name"
                placeholder="e.g., Send Slack Notification"
                value={newAction.name}
                onChange={(e) => setNewAction({ ...newAction, name: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="action-type">Action Type</Label>
              <Select
                value={newAction.type}
                onValueChange={(value) => setNewAction({ ...newAction, type: value as CustomAction['type'] })}
              >
                <SelectTrigger id="action-type">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {actionTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="action-description">Description (Optional)</Label>
              <Textarea
                id="action-description"
                placeholder="Describe what this action does"
                value={newAction.description}
                onChange={(e) => setNewAction({ ...newAction, description: e.target.value })}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddAction} disabled={!newAction.name}>
              Add Action
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}