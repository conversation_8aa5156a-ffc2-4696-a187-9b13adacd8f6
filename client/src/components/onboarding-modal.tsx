import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent } from '@/components/ui/dialog'
import { useAuth } from '@/contexts/AuthContext'
import { useToast } from '@/hooks/use-toast'
import { 
  ChevronRight, 
  ChevronLeft, 
  Briefcase, 
  Code, 
  TrendingUp, 
  Users, 
  Building, 
  GraduationCap,
  Home,
  Heart,
  Zap,
  Target,
  Clock,
  BarChart,
  Mail,
  Calendar,
  FileText,
  Database,
  MessageSquare,
  ShoppingCart,
  Camera,
  Music,
  Palette,
  Check,
  Sparkles
} from 'lucide-react'

interface OnboardingData {
  role: string
  experience: string
  goals: string[]
  teamSize: string
  industry: string
  automationTypes: string[]
  name: string
  company: string
}

const roles = [
  { id: 'business-owner', label: 'Business Owner', icon: Building, description: 'Running my own business' },
  { id: 'marketing-manager', label: 'Marketing Manager', icon: TrendingUp, description: 'Managing marketing campaigns' },
  { id: 'operations-manager', label: 'Operations Manager', icon: Users, description: 'Streamlining business operations' },
  { id: 'developer', label: 'Developer', icon: Code, description: 'Building and maintaining systems' },
  { id: 'consultant', label: 'Consultant', icon: Briefcase, description: 'Helping clients optimize processes' },
  { id: 'student', label: 'Student', icon: GraduationCap, description: 'Learning about automation' },
  { id: 'freelancer', label: 'Freelancer', icon: Home, description: 'Working independently' },
  { id: 'other', label: 'Other', icon: Heart, description: 'Something else entirely' }
]

const experienceLevels = [
  { id: 'beginner', label: 'Beginner', description: 'New to automation tools' },
  { id: 'intermediate', label: 'Intermediate', description: 'Some experience with automation' },
  { id: 'advanced', label: 'Advanced', description: 'Very experienced with automation' },
  { id: 'expert', label: 'Expert', description: 'I build automation tools' }
]

const goals = [
  { id: 'save-time', label: 'Save Time', icon: Clock, description: 'Automate repetitive tasks' },
  { id: 'increase-productivity', label: 'Increase Productivity', icon: Zap, description: 'Get more done faster' },
  { id: 'reduce-errors', label: 'Reduce Errors', icon: Target, description: 'Minimize manual mistakes' },
  { id: 'scale-business', label: 'Scale Business', icon: TrendingUp, description: 'Handle more work without more people' },
  { id: 'improve-data', label: 'Improve Data Management', icon: BarChart, description: 'Better organize and analyze data' },
  { id: 'enhance-communication', label: 'Enhance Communication', icon: MessageSquare, description: 'Streamline team collaboration' }
]

const automationTypes = [
  { id: 'email', label: 'Email Automation', icon: Mail, description: 'Send automated emails and newsletters' },
  { id: 'scheduling', label: 'Scheduling', icon: Calendar, description: 'Automate calendar and booking systems' },
  { id: 'data-entry', label: 'Data Entry', icon: Database, description: 'Automatically input and organize data' },
  { id: 'reports', label: 'Report Generation', icon: FileText, description: 'Create automated reports and analytics' },
  { id: 'social-media', label: 'Social Media', icon: MessageSquare, description: 'Schedule and manage social posts' },
  { id: 'ecommerce', label: 'E-commerce', icon: ShoppingCart, description: 'Automate online store processes' },
  { id: 'content', label: 'Content Creation', icon: Palette, description: 'Generate and manage content' },
  { id: 'customer-service', label: 'Customer Service', icon: Users, description: 'Automate support and communication' }
]

const teamSizes = [
  { id: 'solo', label: 'Just me', description: 'Working alone' },
  { id: 'small', label: '2-10 people', description: 'Small team' },
  { id: 'medium', label: '11-50 people', description: 'Medium team' },
  { id: 'large', label: '51-200 people', description: 'Large team' },
  { id: 'enterprise', label: '200+ people', description: 'Enterprise' }
]

const industries = [
  'Technology', 'Marketing & Advertising', 'E-commerce', 'Healthcare', 'Education', 
  'Real Estate', 'Finance', 'Consulting', 'Manufacturing', 'Retail', 'Media & Entertainment',
  'Non-profit', 'Government', 'Agriculture', 'Construction', 'Other'
]

export default function OnboardingModal() {
  const [isOpen, setIsOpen] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)
  const { user } = useAuth()
  const { toast } = useToast()
  
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    role: '',
    experience: '',
    goals: [],
    teamSize: '',
    industry: '',
    automationTypes: [],
    name: user?.user_metadata?.full_name || '',
    company: ''
  })

  // Removed "Use Case" from steps - now we have 6 steps instead of 7
  const steps = [
    'Personal Info',
    'Your Role', 
    'Experience Level',
    'Your Goals',
    'Automation Types',
    'Team & Industry',
    'Complete'
  ]

  const progress = ((currentStep + 1) / steps.length) * 100

  useEffect(() => {
    // Check if user needs onboarding - only for new registrations
    if (user) {
      const isNewUser = user.created_at && 
        new Date(user.created_at).getTime() > Date.now() - 60000 // Created within last minute
      const hasCompletedOnboarding = localStorage.getItem(`onboarding_completed_${user.id}`)
      
      if (isNewUser && !hasCompletedOnboarding) {
        setIsOpen(true)
      }
    }
  }, [user])

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleComplete = () => {
    // Save onboarding data and mark as completed for this user
    if (user?.id) {
      localStorage.setItem(`onboarding_data_${user.id}`, JSON.stringify(onboardingData))
      localStorage.setItem(`onboarding_completed_${user.id}`, 'true')
    }
    
    toast({
      title: 'Welcome to Filorina!',
      description: 'Your personalized dashboard is ready. Let\'s start automating!'
    })
    
    setIsOpen(false)
  }

  const handleGoalToggle = (goalId: string) => {
    setOnboardingData(prev => ({
      ...prev,
      goals: prev.goals.includes(goalId) 
        ? prev.goals.filter(g => g !== goalId)
        : [...prev.goals, goalId]
    }))
  }

  const handleAutomationTypeToggle = (typeId: string) => {
    setOnboardingData(prev => ({
      ...prev,
      automationTypes: prev.automationTypes.includes(typeId) 
        ? prev.automationTypes.filter(t => t !== typeId)
        : [...prev.automationTypes, typeId]
    }))
  }

  const renderPersonalInfo = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center space-y-2">
        <h2 className="text-3xl font-bold">Welcome to Filorina!</h2>
        <p className="text-muted-foreground">Let's personalize your experience</p>
      </div>
      
      <div className="space-y-4">
        <div>
          <Label htmlFor="name">Your Name</Label>
          <Input 
            id="name"
            type="text"
            placeholder="John Doe"
            value={onboardingData.name}
            onChange={(e) => setOnboardingData(prev => ({ ...prev, name: e.target.value }))}
            className="mt-1"
          />
        </div>
        
        <div>
          <Label htmlFor="company">Company (Optional)</Label>
          <Input 
            id="company"
            type="text"
            placeholder="Acme Inc."
            value={onboardingData.company}
            onChange={(e) => setOnboardingData(prev => ({ ...prev, company: e.target.value }))}
            className="mt-1"
          />
        </div>
      </div>
    </motion.div>
  )

  const renderRoleSelection = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center space-y-2">
        <h2 className="text-3xl font-bold">What's your role?</h2>
        <p className="text-muted-foreground">This helps us tailor features to your needs</p>
      </div>
      
      <div className="grid grid-cols-2 gap-3 max-h-[300px] overflow-y-auto">
        {roles.map((role) => {
          const Icon = role.icon
          const isSelected = onboardingData.role === role.id
          return (
            <Card 
              key={role.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                isSelected ? 'ring-2 ring-primary bg-primary/5' : ''
              }`}
              onClick={() => setOnboardingData(prev => ({ ...prev, role: role.id }))}
            >
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <div className={`p-2 rounded-lg ${isSelected ? 'bg-primary/20' : 'bg-muted'}`}>
                    <Icon className={`h-5 w-5 ${isSelected ? 'text-primary' : 'text-muted-foreground'}`} />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-sm">{role.label}</h3>
                    <p className="text-xs text-muted-foreground mt-1">{role.description}</p>
                  </div>
                  {isSelected && (
                    <Check className="h-4 w-4 text-primary flex-shrink-0" />
                  )}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
    </motion.div>
  )

  const renderExperienceLevel = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center space-y-2">
        <h2 className="text-3xl font-bold">What's your experience level?</h2>
        <p className="text-muted-foreground">We'll adjust complexity based on your skills</p>
      </div>
      
      <div className="space-y-3">
        {experienceLevels.map((level) => (
          <Card 
            key={level.id}
            className={`cursor-pointer transition-all hover:shadow-md ${
              onboardingData.experience === level.id ? 'ring-2 ring-primary bg-primary/5' : ''
            }`}
            onClick={() => setOnboardingData(prev => ({ ...prev, experience: level.id }))}
          >
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold">{level.label}</h3>
                  <p className="text-sm text-muted-foreground">{level.description}</p>
                </div>
                {onboardingData.experience === level.id && (
                  <Check className="h-5 w-5 text-primary" />
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </motion.div>
  )

  const renderGoals = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center space-y-2">
        <h2 className="text-3xl font-bold">What are your goals?</h2>
        <p className="text-muted-foreground">Select all that apply</p>
      </div>
      
      <div className="grid grid-cols-2 gap-3 max-h-[300px] overflow-y-auto">
        {goals.map((goal) => {
          const Icon = goal.icon
          const isSelected = onboardingData.goals.includes(goal.id)
          return (
            <Card 
              key={goal.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                isSelected ? 'ring-2 ring-primary bg-primary/5' : ''
              }`}
              onClick={() => handleGoalToggle(goal.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <div className={`p-2 rounded-lg ${isSelected ? 'bg-primary/20' : 'bg-muted'}`}>
                    <Icon className={`h-5 w-5 ${isSelected ? 'text-primary' : 'text-muted-foreground'}`} />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-sm">{goal.label}</h3>
                    <p className="text-xs text-muted-foreground mt-1">{goal.description}</p>
                  </div>
                  {isSelected && (
                    <Check className="h-4 w-4 text-primary flex-shrink-0" />
                  )}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
    </motion.div>
  )

  const renderAutomationTypes = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center space-y-2">
        <h2 className="text-3xl font-bold">What would you like to automate?</h2>
        <p className="text-muted-foreground">Select all areas of interest</p>
      </div>
      
      <div className="grid grid-cols-2 gap-3 max-h-[300px] overflow-y-auto">
        {automationTypes.map((type) => {
          const Icon = type.icon
          const isSelected = onboardingData.automationTypes.includes(type.id)
          return (
            <Card 
              key={type.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                isSelected ? 'ring-2 ring-primary bg-primary/5' : ''
              }`}
              onClick={() => handleAutomationTypeToggle(type.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <div className={`p-2 rounded-lg ${isSelected ? 'bg-primary/20' : 'bg-muted'}`}>
                    <Icon className={`h-5 w-5 ${isSelected ? 'text-primary' : 'text-muted-foreground'}`} />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-sm">{type.label}</h3>
                    <p className="text-xs text-muted-foreground mt-1">{type.description}</p>
                  </div>
                  {isSelected && (
                    <Check className="h-4 w-4 text-primary flex-shrink-0" />
                  )}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
    </motion.div>
  )

  const renderTeamAndIndustry = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center space-y-2">
        <h2 className="text-3xl font-bold">Tell us about your team</h2>
        <p className="text-muted-foreground">This helps us recommend the right features</p>
      </div>
      
      <div className="space-y-6">
        <div>
          <Label className="text-base font-semibold mb-3 block">Team Size</Label>
          <div className="space-y-2 max-h-[200px] overflow-y-auto">
            {teamSizes.map((size) => (
              <Card 
                key={size.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  onboardingData.teamSize === size.id ? 'ring-2 ring-primary bg-primary/5' : ''
                }`}
                onClick={() => setOnboardingData(prev => ({ ...prev, teamSize: size.id }))}
              >
                <CardContent className="p-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-semibold">{size.label}</h3>
                      <p className="text-sm text-muted-foreground">{size.description}</p>
                    </div>
                    {onboardingData.teamSize === size.id && (
                      <Check className="h-5 w-5 text-primary" />
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
        
        <div>
          <Label htmlFor="industry" className="text-base font-semibold">Industry</Label>
          <select 
            id="industry"
            className="w-full mt-2 p-3 border border-input rounded-lg bg-background"
            value={onboardingData.industry}
            onChange={(e) => setOnboardingData(prev => ({ ...prev, industry: e.target.value }))}
          >
            <option value="">Select your industry</option>
            {industries.map((industry) => (
              <option key={industry} value={industry}>{industry}</option>
            ))}
          </select>
        </div>
      </div>
    </motion.div>
  )

  const renderComplete = () => (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="text-center space-y-6"
    >
      <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
        <Sparkles className="h-10 w-10 text-primary" />
      </div>
      
      <div>
        <h2 className="text-3xl font-bold mb-2">You're all set, {onboardingData.name}!</h2>
        <p className="text-muted-foreground text-lg">
          We've personalized your Filorina experience based on your preferences.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-md mx-auto">
        <div className="text-center p-4 bg-primary/5 rounded-lg">
          <h3 className="font-semibold text-primary">Recommended Templates</h3>
          <p className="text-sm text-muted-foreground">Custom picks for your role</p>
        </div>
        <div className="text-center p-4 bg-primary/5 rounded-lg">
          <h3 className="font-semibold text-primary">AI Suggestions</h3>
          <p className="text-sm text-muted-foreground">Personalized automation ideas</p>
        </div>
      </div>
      
      <Button onClick={handleComplete} size="lg" className="text-white">
        Enter Dashboard
        <ChevronRight className="ml-2 h-4 w-4" />
      </Button>
    </motion.div>
  )

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0: return renderPersonalInfo()
      case 1: return renderRoleSelection()
      case 2: return renderExperienceLevel()
      case 3: return renderGoals()
      case 4: return renderAutomationTypes()
      case 5: return renderTeamAndIndustry()
      case 6: return renderComplete()
      default: return renderPersonalInfo()
    }
  }

  const canProceed = () => {
    switch (currentStep) {
      case 0: return onboardingData.name.trim() !== ''
      case 1: return onboardingData.role !== ''
      case 2: return onboardingData.experience !== ''
      case 3: return onboardingData.goals.length > 0
      case 4: return onboardingData.automationTypes.length > 0
      case 5: return onboardingData.teamSize !== '' && onboardingData.industry !== ''
      default: return true
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden p-0" hideCloseButton>
        <div className="p-6">
          {/* Progress Bar */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-muted-foreground">
                Step {currentStep + 1} of {steps.length}: {steps[currentStep]}
              </h3>
              <span className="text-sm text-muted-foreground">{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>

          {/* Content */}
          <div className="min-h-[400px]">
            <AnimatePresence mode="wait">
              {renderCurrentStep()}
            </AnimatePresence>
          </div>

          {/* Navigation */}
          <div className="flex justify-between items-center mt-6">
            <Button 
              variant="ghost" 
              onClick={handleBack}
              disabled={currentStep === 0}
              className="gap-2"
            >
              <ChevronLeft className="h-4 w-4" />
              Back
            </Button>
            
            {currentStep < steps.length - 1 ? (
              <Button 
                onClick={handleNext}
                disabled={!canProceed()}
                className="gap-2"
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            ) : null}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}