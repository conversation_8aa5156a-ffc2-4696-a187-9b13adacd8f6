import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  AlertCircle,
  X,
  ChevronDown,
  ChevronUp,
  RefreshCw,
  ExternalLink,
  Info,
  Zap
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { useToast } from '@/hooks/use-toast'
import { ErrorDetailsModal, ErrorDetails } from './error-details-modal'

interface ErrorHandlerProps {
  automationId?: string
  onRetry?: (errorId: string) => void
  compact?: boolean
}

export function ErrorHandler({ automationId, onRetry, compact = false }: ErrorHandlerProps) {
  const { toast } = useToast()
  const [errors, setErrors] = useState<ErrorDetails[]>([])
  const [selectedError, setSelectedError] = useState<ErrorDetails | null>(null)
  const [showErrorModal, setShowErrorModal] = useState(false)
  const [expanded, setExpanded] = useState(!compact)
  const [errorTrend, setErrorTrend] = useState<'increasing' | 'decreasing' | 'stable'>('stable')

  // Mock error data for demonstration
  useEffect(() => {
    const mockErrors: ErrorDetails[] = [
      {
        id: '1',
        timestamp: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
        automation: {
          id: 'auto-1',
          name: 'Daily Sales Report',
          type: 'scheduled'
        },
        error: {
          code: 'API_RATE_LIMIT',
          message: 'Rate limit exceeded for Google Sheets API. Please wait before retrying.',
          type: 'rate_limit',
          severity: 'error',
          details: {
            limit: 100,
            used: 100,
            resetTime: new Date(Date.now() + 1000 * 60 * 60)
          }
        },
        context: {
          step: 'Write to Google Sheets',
          input: { rows: 150, sheet: 'Sales Data' },
          duration: 2340,
          retryCount: 3,
          lastRetry: new Date(Date.now() - 1000 * 60 * 2)
        },
        troubleshooting: {
          possibleCauses: [
            'Too many requests sent to Google Sheets API within a short time',
            'API quota exceeded for the current billing period',
            'Concurrent automations accessing the same spreadsheet'
          ],
          quickFixes: [],
          documentation: [
            'https://developers.google.com/sheets/api/limits',
            'https://filorina.com/docs/rate-limits'
          ],
          relatedErrors: ['API_QUOTA_EXCEEDED', 'SHEETS_PERMISSION_DENIED']
        }
      },
      {
        id: '2',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
        automation: {
          id: 'auto-2',
          name: 'Customer Email Campaign',
          type: 'trigger-based'
        },
        error: {
          code: 'AUTH_TOKEN_EXPIRED',
          message: 'Gmail authentication token has expired. Please re-authenticate.',
          type: 'auth',
          severity: 'critical',
          details: {
            service: 'Gmail',
            expiryTime: new Date(Date.now() - 1000 * 60 * 10)
          }
        },
        context: {
          step: 'Send Email',
          input: { recipients: 45, subject: 'Weekly Newsletter' },
          duration: 1200,
          retryCount: 1
        },
        troubleshooting: {
          possibleCauses: [
            'OAuth token has expired after the standard validity period',
            'Permissions were revoked by the user',
            'Google account security settings were changed'
          ],
          quickFixes: [],
          documentation: [
            'https://developers.google.com/gmail/api/auth/about-auth',
            'https://filorina.com/docs/gmail-authentication'
          ],
          relatedErrors: ['AUTH_REFRESH_FAILED', 'GMAIL_SCOPE_DENIED']
        }
      },
      {
        id: '3',
        timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
        automation: {
          id: 'auto-3',
          name: 'Data Validation Check',
          type: 'scheduled'
        },
        error: {
          code: 'VALIDATION_FAILED',
          message: 'Input data does not match expected format',
          type: 'validation',
          severity: 'warning',
          details: {
            field: 'customer_email',
            expected: 'email format',
            received: 'john.doe@'
          }
        },
        context: {
          step: 'Validate Input Data',
          input: { email: 'john.doe@', name: 'John Doe' },
          duration: 45,
          retryCount: 0
        },
        troubleshooting: {
          possibleCauses: [
            'Incomplete email address in the input data',
            'Data source formatting issue',
            'Missing validation rules for edge cases'
          ],
          quickFixes: [],
          documentation: [
            'https://filorina.com/docs/data-validation',
            'https://filorina.com/docs/email-formats'
          ],
          relatedErrors: ['INVALID_EMAIL_FORMAT', 'MISSING_REQUIRED_FIELD']
        }
      }
    ]

    if (automationId) {
      setErrors(mockErrors.filter(e => e.automation.id === automationId))
    } else {
      setErrors(mockErrors)
    }

    // Calculate error trend
    const recentErrors = mockErrors.filter(e => 
      e.timestamp > new Date(Date.now() - 1000 * 60 * 60 * 24) // Last 24 hours
    )
    if (recentErrors.length > 5) {
      setErrorTrend('increasing')
    } else if (recentErrors.length < 2) {
      setErrorTrend('decreasing')
    }
  }, [automationId])

  const handleRetry = async (errorId: string) => {
    const error = errors.find(e => e.id === errorId)
    if (!error) return

    toast({
      title: "Retrying automation",
      description: `Attempting to retry ${error.automation.name}...`,
    })

    // Simulate retry
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    if (onRetry) {
      onRetry(errorId)
    }

    toast({
      title: "Retry initiated",
      description: "The automation has been queued for retry.",
    })
  }

  const handleApplyFix = async (errorId: string, fixId: string) => {
    toast({
      title: "Fix applied",
      description: "The suggested fix has been applied to your automation.",
    })
    
    // Remove error from list after fix
    setErrors(errors.filter(e => e.id !== errorId))
    setShowErrorModal(false)
  }

  const getErrorSummary = () => {
    const critical = errors.filter(e => e.error.severity === 'critical').length
    const errorCount = errors.filter(e => e.error.severity === 'error').length
    const warnings = errors.filter(e => e.error.severity === 'warning').length
    
    return { critical, error: errorCount, warning: warnings, total: errors.length }
  }

  const summary = getErrorSummary()

  if (compact && errors.length === 0) {
    return null
  }

  if (compact) {
    return (
      <Card className="cursor-pointer" onClick={() => setExpanded(!expanded)}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <CardTitle className="text-base">Recent Errors</CardTitle>
            </div>
            <div className="flex items-center gap-2">
              {summary.critical > 0 && (
                <Badge variant="destructive" className="text-xs">
                  {summary.critical} Critical
                </Badge>
              )}
              {summary.error > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {summary.error} Errors
                </Badge>
              )}
              {expanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </div>
          </div>
        </CardHeader>
        
        <AnimatePresence>
          {expanded && (
            <motion.div
              initial={{ height: 0 }}
              animate={{ height: 'auto' }}
              exit={{ height: 0 }}
              className="overflow-hidden"
            >
              <CardContent className="pt-0 space-y-2">
                {errors.slice(0, 3).map((error) => (
                  <div
                    key={error.id}
                    className="flex items-center justify-between p-2 rounded-lg border hover:bg-accent/50 transition-colors"
                    onClick={(e) => {
                      e.stopPropagation()
                      setSelectedError(error)
                      setShowErrorModal(true)
                    }}
                  >
                    <div className="flex items-center gap-2">
                      <Badge 
                        variant={error.error.severity === 'critical' ? 'destructive' : 'secondary'}
                        className="text-xs"
                      >
                        {error.error.type.replace('_', ' ')}
                      </Badge>
                      <div>
                        <p className="text-sm font-medium line-clamp-1">{error.automation.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(error.timestamp).toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleRetry(error.id)
                      }}
                    >
                      <RefreshCw className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
                
                {errors.length > 3 && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={(e) => {
                      e.stopPropagation()
                      // Navigate to full error logs
                    }}
                  >
                    View All Errors ({errors.length})
                  </Button>
                )}
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-red-600" />
                Error Dashboard
              </CardTitle>
              <CardDescription>
                Monitor and resolve automation errors
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={errorTrend === 'increasing' ? 'destructive' : errorTrend === 'decreasing' ? 'default' : 'secondary'}>
                {errorTrend === 'increasing' ? '↑' : errorTrend === 'decreasing' ? '↓' : '→'} {errorTrend}
              </Badge>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Error Summary */}
          <div className="grid grid-cols-4 gap-3">
            <Card>
              <CardContent className="p-3">
                <p className="text-xs text-muted-foreground">Total Errors</p>
                <p className="text-2xl font-bold">{summary.total}</p>
              </CardContent>
            </Card>
            <Card className={summary.critical > 0 ? 'border-red-200 dark:border-red-800' : ''}>
              <CardContent className="p-3">
                <p className="text-xs text-muted-foreground">Critical</p>
                <p className="text-2xl font-bold text-red-600">{summary.critical}</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-3">
                <p className="text-xs text-muted-foreground">Errors</p>
                <p className="text-2xl font-bold text-orange-600">{summary.error}</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-3">
                <p className="text-xs text-muted-foreground">Warnings</p>
                <p className="text-2xl font-bold text-yellow-600">{summary.warning}</p>
              </CardContent>
            </Card>
          </div>

          {/* AI Insights */}
          <Alert>
            <Zap className="h-4 w-4" />
            <AlertTitle>AI Insight</AlertTitle>
            <AlertDescription>
              {errorTrend === 'increasing' 
                ? 'Error rate is increasing. Most failures are API rate limits. Consider implementing request queuing.'
                : errorTrend === 'decreasing'
                ? 'Great job! Error rate is decreasing. Your recent fixes are working.'
                : 'Error rate is stable. Monitor for patterns in recurring errors.'
              }
            </AlertDescription>
          </Alert>

          {/* Error List */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium">Recent Errors</h3>
            {errors.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <CheckCircle className="h-12 w-12 mx-auto mb-3 text-green-600" />
                <p>No errors detected</p>
                <p className="text-sm">All automations running smoothly</p>
              </div>
            ) : (
              <div className="space-y-2">
                {errors.map((error) => (
                  <Card
                    key={error.id}
                    className="cursor-pointer hover:shadow-md transition-all"
                    onClick={() => {
                      setSelectedError(error)
                      setShowErrorModal(true)
                    }}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="space-y-1 flex-1">
                          <div className="flex items-center gap-2">
                            <Badge 
                              variant={error.error.severity === 'critical' ? 'destructive' : error.error.severity === 'error' ? 'secondary' : 'outline'}
                            >
                              {error.error.code}
                            </Badge>
                            <span className="text-sm font-medium">{error.automation.name}</span>
                            <span className="text-xs text-muted-foreground">
                              • {new Date(error.timestamp).toLocaleString()}
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground line-clamp-2">
                            {error.error.message}
                          </p>
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <span>Step: {error.context.step}</span>
                            <span>Retries: {error.context.retryCount}</span>
                            {error.context.duration && (
                              <span>Duration: {error.context.duration}ms</span>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleRetry(error.id)
                            }}
                          >
                            <RefreshCw className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      {/* Quick Fix Preview */}
                      <div className="mt-3 p-2 bg-muted/50 rounded-lg">
                        <div className="flex items-center gap-2 text-xs">
                          <Info className="h-3 w-3 text-muted-foreground" />
                          <span className="font-medium">Quick Fix Available:</span>
                          <span className="text-muted-foreground">
                            {error.error.type === 'auth' && 'Re-authenticate with service'}
                            {error.error.type === 'rate_limit' && 'Implement request queuing'}
                            {error.error.type === 'validation' && 'Update data validation rules'}
                            {error.error.type === 'network' && 'Check network connectivity'}
                            {error.error.type === 'timeout' && 'Increase timeout duration'}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <ErrorDetailsModal
        error={selectedError}
        open={showErrorModal}
        onClose={() => {
          setShowErrorModal(false)
          setSelectedError(null)
        }}
        onRetry={handleRetry}
        onApplyFix={handleApplyFix}
      />
    </>
  )
}

// Missing import
import { CheckCircle } from 'lucide-react'