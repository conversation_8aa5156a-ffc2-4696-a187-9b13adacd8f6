import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  <PERSON><PERSON><PERSON>, 
  TrendingUp, 
  Clock, 
  Target,
  Lightbulb,
  ChevronRight,
  X,
  Calendar,
  Mail,
  FileText,
  Database,
  Globe,
  MessageSquare,
  BarChart3,
  Users
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { useLocation } from 'wouter'

interface WorkflowSuggestion {
  id: string
  title: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  category: string
  relevanceScore: number
  estimatedTime: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  benefits: string[]
  templateId?: string
  reason: string
}

interface WorkflowSuggestionsProps {
  userProfile?: {
    industry?: string
    teamSize?: string
    goals?: string[]
    currentAutomations?: string[]
  }
  onDismiss?: () => void
  compact?: boolean
}

export function WorkflowSuggestions({ userProfile, onDismiss, compact = false }: WorkflowSuggestionsProps) {
  const [, setLocation] = useLocation()
  const [suggestions, setSuggestions] = useState<WorkflowSuggestion[]>([])
  const [selectedSuggestion, setSelectedSuggestion] = useState<WorkflowSuggestion | null>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(true)

  useEffect(() => {
    // Simulate AI analysis
    const analyzeSuggestions = async () => {
      setIsAnalyzing(true)
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // Generate personalized suggestions based on user profile
      const baseSuggestions: WorkflowSuggestion[] = [
        {
          id: '1',
          title: 'Email Campaign Automation',
          description: 'Automatically segment leads and send personalized email campaigns based on user behavior',
          icon: Mail,
          category: 'Marketing',
          relevanceScore: 95,
          estimatedTime: '2 hours saved/week',
          difficulty: 'beginner',
          benefits: [
            'Increase email open rates by 40%',
            'Automate follow-up sequences',
            'Track engagement metrics'
          ],
          templateId: 'gmail-automation',
          reason: 'Based on your email volume, this could save significant time'
        },
        {
          id: '2',
          title: 'Daily Business Intelligence Dashboard',
          description: 'Aggregate data from multiple sources into a daily email digest with key metrics',
          icon: BarChart3,
          category: 'Analytics',
          relevanceScore: 88,
          estimatedTime: '5 hours saved/week',
          difficulty: 'intermediate',
          benefits: [
            'Real-time business insights',
            'Automated KPI tracking',
            'Data-driven decision making'
          ],
          templateId: 'daily-digest',
          reason: 'Perfect for tracking your key business metrics'
        },
        {
          id: '3',
          title: 'Content Publishing Workflow',
          description: 'Generate and publish blog content across multiple platforms automatically',
          icon: FileText,
          category: 'Content',
          relevanceScore: 82,
          estimatedTime: '8 hours saved/week',
          difficulty: 'beginner',
          benefits: [
            'Consistent content schedule',
            'Multi-platform publishing',
            'SEO optimization'
          ],
          templateId: 'blog-workflow',
          reason: 'Streamline your content creation process'
        },
        {
          id: '4',
          title: 'Lead Qualification System',
          description: 'Automatically score and qualify leads based on engagement and profile data',
          icon: Users,
          category: 'Sales',
          relevanceScore: 78,
          estimatedTime: '6 hours saved/week',
          difficulty: 'intermediate',
          benefits: [
            'Focus on high-quality leads',
            'Automated lead scoring',
            'Improved conversion rates'
          ],
          templateId: 'lead-qualification',
          reason: 'Optimize your sales funnel efficiency'
        },
        {
          id: '5',
          title: 'Social Media Monitoring',
          description: 'Track brand mentions and trending topics across social platforms',
          icon: Globe,
          category: 'Social Media',
          relevanceScore: 72,
          estimatedTime: '4 hours saved/week',
          difficulty: 'beginner',
          benefits: [
            'Real-time brand monitoring',
            'Trend identification',
            'Engagement opportunities'
          ],
          templateId: 'social-trends-tracker',
          reason: 'Stay ahead of social media trends'
        }
      ]
      
      // Sort by relevance score
      const sortedSuggestions = baseSuggestions.sort((a, b) => b.relevanceScore - a.relevanceScore)
      
      setSuggestions(compact ? sortedSuggestions.slice(0, 3) : sortedSuggestions)
      setIsAnalyzing(false)
    }
    
    analyzeSuggestions()
  }, [userProfile, compact])

  const handleUseSuggestion = (suggestion: WorkflowSuggestion) => {
    if (suggestion.templateId) {
      setLocation(`/dashboard/templates/${suggestion.templateId}`)
    } else {
      setLocation('/dashboard/create')
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'intermediate':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'advanced':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default:
        return ''
    }
  }

  if (compact) {
    return (
      <Card className="relative overflow-hidden w-full">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-950/20 dark:to-blue-950/20 opacity-50" />
        
        <CardHeader className="relative pb-4 sm:pb-6">
          <div className="flex items-center justify-between gap-2">
            <div className="flex items-center gap-2 min-w-0">
              <Sparkles className="h-4 w-4 sm:h-5 sm:w-5 text-purple-600 flex-shrink-0" />
              <CardTitle className="text-base sm:text-lg truncate">AI Recommendations</CardTitle>
            </div>
            {onDismiss && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onDismiss}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          <CardDescription className="text-xs sm:text-sm mt-1">
            Personalized automations based on your usage
          </CardDescription>
        </CardHeader>
        
        <CardContent className="relative space-y-2 sm:space-y-3 pt-0">
          {isAnalyzing ? (
            <div className="py-6 sm:py-8 text-center">
              <Sparkles className="h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-2 sm:mb-3 text-purple-600 animate-pulse" />
              <p className="text-xs sm:text-sm text-muted-foreground">Analyzing your workflow patterns...</p>
            </div>
          ) : (
            <>
              <div className="space-y-2">
                {suggestions.map((suggestion, index) => (
                  <motion.div
                    key={suggestion.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="group cursor-pointer"
                    onClick={() => handleUseSuggestion(suggestion)}
                  >
                    <div className="flex items-center gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors">
                      <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                        <suggestion.icon className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-xs sm:text-sm truncate">{suggestion.title}</p>
                        <p className="text-[10px] sm:text-xs text-muted-foreground truncate">{suggestion.estimatedTime}</p>
                      </div>
                      <div className="hidden sm:flex items-center gap-2">
                        <div className="w-10 sm:w-12">
                          <Progress value={suggestion.relevanceScore} className="h-1" />
                        </div>
                        <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground group-hover:text-primary transition-colors flex-shrink-0" />
                      </div>
                      <ChevronRight className="sm:hidden h-3 w-3 text-muted-foreground group-hover:text-primary transition-colors flex-shrink-0" />
                    </div>
                  </motion.div>
                ))}
              </div>
              
              <Button
                variant="outline"
                size="sm"
                className="w-full text-xs sm:text-sm"
                onClick={() => setLocation('/dashboard/ai-suggestions')}
              >
                View All Recommendations
              </Button>
            </>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card className="relative overflow-hidden w-full">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-950/20 dark:to-blue-950/20 opacity-50" />
        
        <CardHeader className="relative px-4 sm:px-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Sparkles className="h-5 w-5 sm:h-6 sm:w-6 text-purple-600" />
                <CardTitle className="text-lg sm:text-xl md:text-2xl">AI-Powered Workflow Suggestions</CardTitle>
              </div>
              <CardDescription className="text-xs sm:text-sm">
                Based on your usage patterns and business needs
              </CardDescription>
            </div>
            {onDismiss && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onDismiss}
                className="h-8 w-8 p-0 self-start sm:self-auto"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </CardHeader>
        
        <CardContent className="relative px-4 sm:px-6">
          {isAnalyzing ? (
            <div className="py-6 sm:py-8 text-center">
              <div className="relative">
                <Sparkles className="h-10 w-10 sm:h-12 sm:w-12 mx-auto mb-3 text-purple-600 animate-pulse" />
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="h-14 w-14 sm:h-16 sm:w-16 rounded-full bg-purple-600/20 animate-ping" />
                </div>
              </div>
              <p className="text-sm sm:text-base font-medium mb-1">Analyzing your workflow patterns</p>
              <p className="text-xs sm:text-sm text-muted-foreground">Creating personalized recommendations...</p>
            </div>
          ) : (
            <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
              {suggestions.map((suggestion, index) => (
                <motion.div
                  key={suggestion.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="group cursor-pointer"
                  onClick={() => setSelectedSuggestion(suggestion)}
                >
                  <Card className="h-full hover:shadow-lg transition-all hover:scale-[1.02] border-muted">
                    <CardHeader className="pb-3 p-3 sm:p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-primary/10 flex items-center justify-center">
                          <suggestion.icon className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />
                        </div>
                        <Badge className={`text-xs ${getDifficultyColor(suggestion.difficulty)}`}>
                          {suggestion.difficulty}
                        </Badge>
                      </div>
                      <CardTitle className="text-base sm:text-lg line-clamp-1">{suggestion.title}</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2 sm:space-y-3 p-3 sm:p-4 pt-0">
                      <p className="text-xs sm:text-sm text-muted-foreground line-clamp-2">
                        {suggestion.description}
                      </p>
                      
                      <div className="flex items-center gap-2 text-xs sm:text-sm">
                        <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
                        <span className="font-medium">{suggestion.estimatedTime}</span>
                      </div>
                      
                      <div className="space-y-1 sm:space-y-2">
                        <div className="flex items-center justify-between text-xs sm:text-sm">
                          <span className="text-muted-foreground">Match Score</span>
                          <span className="font-medium">{suggestion.relevanceScore}%</span>
                        </div>
                        <Progress value={suggestion.relevanceScore} className="h-1.5 sm:h-2" />
                      </div>
                      
                      <div className="pt-1 sm:pt-2 flex items-center text-xs sm:text-sm text-primary">
                        <Lightbulb className="h-3 w-3 sm:h-4 sm:w-4 mr-1 flex-shrink-0" />
                        <span className="line-clamp-1">{suggestion.reason}</span>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Detailed View Modal */}
      <AnimatePresence>
        {selectedSuggestion && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedSuggestion(null)}
          >
            <motion.div
              initial={{ scale: 0.95 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.95 }}
              className="bg-card rounded-lg shadow-xl max-w-2xl w-full max-h-[85vh] sm:max-h-[90vh] overflow-y-auto mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-4 sm:p-6">
                <div className="flex items-start justify-between mb-4 sm:mb-6">
                  <div className="flex items-center gap-2 sm:gap-3">
                    <div className="w-10 h-10 sm:w-14 sm:h-14 rounded-full bg-primary/10 flex items-center justify-center">
                      <selectedSuggestion.icon className="h-5 w-5 sm:h-7 sm:w-7 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-base sm:text-xl font-bold">{selectedSuggestion.title}</h3>
                      <p className="text-xs sm:text-sm text-muted-foreground">{selectedSuggestion.category}</p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedSuggestion(null)}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="space-y-4 sm:space-y-6">
                  <div>
                    <h4 className="font-medium mb-1 sm:mb-2 text-sm sm:text-base">Description</h4>
                    <p className="text-xs sm:text-sm text-muted-foreground">{selectedSuggestion.description}</p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-1 sm:mb-2 text-sm sm:text-base">Why this suggestion?</h4>
                    <div className="p-2 sm:p-3 rounded-lg bg-purple-50 dark:bg-purple-950/20 text-purple-900 dark:text-purple-100">
                      <Lightbulb className="h-3 w-3 sm:h-4 sm:w-4 inline mr-1 sm:mr-2" />
                      <span className="text-xs sm:text-sm">{selectedSuggestion.reason}</span>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2 sm:mb-3 text-sm sm:text-base">Expected Benefits</h4>
                    <div className="space-y-1.5 sm:space-y-2">
                      {selectedSuggestion.benefits.map((benefit, index) => (
                        <div key={index} className="flex items-start gap-2">
                          <Target className="h-3 w-3 sm:h-4 sm:w-4 text-green-600 mt-0.5 flex-shrink-0" />
                          <span className="text-xs sm:text-sm">{benefit}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-3 sm:gap-4 pt-2 sm:pt-4">
                    <div className="text-center p-3 sm:p-4 rounded-lg bg-muted/50">
                      <Clock className="h-5 w-5 sm:h-6 sm:w-6 mx-auto mb-1 sm:mb-2 text-muted-foreground" />
                      <p className="text-xs sm:text-sm text-muted-foreground">Time Saved</p>
                      <p className="font-bold text-sm sm:text-base">{selectedSuggestion.estimatedTime}</p>
                    </div>
                    <div className="text-center p-3 sm:p-4 rounded-lg bg-muted/50">
                      <TrendingUp className="h-5 w-5 sm:h-6 sm:w-6 mx-auto mb-1 sm:mb-2 text-muted-foreground" />
                      <p className="text-xs sm:text-sm text-muted-foreground">Match Score</p>
                      <p className="font-bold text-sm sm:text-base">{selectedSuggestion.relevanceScore}%</p>
                    </div>
                  </div>
                  
                  <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 pt-2 sm:pt-4">
                    <Button
                      onClick={() => handleUseSuggestion(selectedSuggestion)}
                      className="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-xs sm:text-sm"
                    >
                      Use This Template
                      <ChevronRight className="ml-1 sm:ml-2 h-3 w-3 sm:h-4 sm:w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setSelectedSuggestion(null)}
                      className="text-xs sm:text-sm"
                    >
                      Maybe Later
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}