import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  MessageCircle, Send, X, Play, Pause, Square, 
  Clock, CheckCircle, AlertCircle, Bot, User,
  Zap, Settings, ArrowRight
} from 'lucide-react'
import { useToast } from "@/hooks/use-toast"
import { apiRequest } from "@/lib/queryClient"

interface ChatMessage {
  id: string
  type: 'user' | 'system' | 'automation' | 'error' | 'success'
  content: string
  timestamp: Date
  metadata?: {
    triggerData?: any
    actionResult?: any
    executionStep?: string
    automationId?: string
  }
}

interface AutomationChatProps {
  automationId: string
  automationName: string
  isActive: boolean
  onClose: () => void
  triggerData?: any
}

export function AutomationChat({ 
  automationId, 
  automationName, 
  isActive, 
  onClose,
  triggerData 
}: AutomationChatProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [isExecuting, setIsExecuting] = useState(false)
  const [executionStatus, setExecutionStatus] = useState<'idle' | 'running' | 'paused' | 'completed' | 'error'>('idle')
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const { toast } = useToast()

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Initialize chat when automation becomes active
  useEffect(() => {
    if (isActive && triggerData) {
      const welcomeMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'system',
        content: `🚀 Automation "${automationName}" has been triggered! I'm here to help you monitor and interact with the execution.`,
        timestamp: new Date(),
        metadata: { triggerData, automationId }
      }

      const triggerMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'automation',
        content: `Trigger detected: ${getTriggerDescription(triggerData)}`,
        timestamp: new Date(),
        metadata: { triggerData, executionStep: 'trigger' }
      }

      setMessages([welcomeMessage, triggerMessage])
      setExecutionStatus('running')
      
      // Start execution simulation
      simulateExecution()
    }
  }, [isActive, triggerData, automationName, automationId])

  const getTriggerDescription = (data: any) => {
    if (!data) return 'Unknown trigger'
    
    switch (data.type) {
      case 'schedule':
        return `Scheduled execution at ${new Date().toLocaleTimeString()}`
      case 'webhook':
        return `Webhook called from ${data.source || 'external source'}`
      case 'gmail':
        return `New email received from ${data.sender || 'unknown sender'}`
      case 'slack':
        return `Slack message in #${data.channel || 'unknown-channel'}: "${data.message?.substring(0, 50) || 'message'}..."`
      case 'google-sheets':
        return `Google Sheets update detected in "${data.spreadsheet || 'spreadsheet'}"`
      case 'google-drive':
        return `File ${data.action || 'modified'} in Google Drive: "${data.filename || 'unknown file'}"`
      case 'google-calendar':
        return `Calendar event "${data.eventTitle || 'event'}" ${data.action || 'occurred'}`
      default:
        return `${data.type} trigger activated`
    }
  }

  const simulateExecution = async () => {
    setIsExecuting(true)
    
    // Simulate execution steps
    const steps = [
      'Processing trigger data...',
      'Executing actions...',
      'Running AI analysis...',
      'Sending notifications...',
      'Updating databases...',
      'Execution completed successfully!'
    ]

    for (let i = 0; i < steps.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 1000))
      
      const stepMessage: ChatMessage = {
        id: `step-${Date.now()}-${i}`,
        type: i === steps.length - 1 ? 'success' : 'automation',
        content: steps[i],
        timestamp: new Date(),
        metadata: { 
          executionStep: `step-${i + 1}`,
          automationId 
        }
      }
      
      setMessages(prev => [...prev, stepMessage])
    }
    
    setIsExecuting(false)
    setExecutionStatus('completed')
  }

  const handleSendMessage = async () => {
    if (!newMessage.trim()) return

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: newMessage,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setNewMessage('')

    // Simulate AI response
    setTimeout(() => {
      const responses = [
        "I understand you want to know more about the execution. Let me check the current status...",
        "The automation is running smoothly. All triggers are working as expected.",
        "I can help you modify the execution parameters if needed. What would you like to adjust?",
        "The current step is processing your data. This typically takes 30-60 seconds.",
        "Would you like me to pause the execution or continue monitoring?",
        "I can provide more details about any specific step. Which part interests you most?"
      ]

      const aiResponse: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'system',
        content: responses[Math.floor(Math.random() * responses.length)],
        timestamp: new Date()
      }

      setMessages(prev => [...prev, aiResponse])
    }, 1000 + Math.random() * 2000)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const pauseExecution = () => {
    setExecutionStatus('paused')
    const pauseMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'system',
      content: '⏸️ Execution paused. You can resume anytime or make adjustments.',
      timestamp: new Date()
    }
    setMessages(prev => [...prev, pauseMessage])
  }

  const resumeExecution = () => {
    setExecutionStatus('running')
    const resumeMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'system',
      content: '▶️ Execution resumed. Continuing from where we left off...',
      timestamp: new Date()
    }
    setMessages(prev => [...prev, resumeMessage])
  }

  const stopExecution = () => {
    setExecutionStatus('idle')
    setIsExecuting(false)
    const stopMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'error',
      content: '🛑 Execution stopped by user. All processes have been terminated.',
      timestamp: new Date()
    }
    setMessages(prev => [...prev, stopMessage])
  }

  const getStatusColor = () => {
    switch (executionStatus) {
      case 'running': return 'text-green-600'
      case 'paused': return 'text-yellow-600'
      case 'completed': return 'text-blue-600'
      case 'error': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getStatusIcon = () => {
    switch (executionStatus) {
      case 'running': return <Play className="h-4 w-4" />
      case 'paused': return <Pause className="h-4 w-4" />
      case 'completed': return <CheckCircle className="h-4 w-4" />
      case 'error': return <AlertCircle className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  if (!isActive) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          onClick={(e) => e.stopPropagation()}
          className="fixed inset-4 md:inset-8 lg:inset-16 bg-background rounded-lg shadow-2xl border flex flex-col max-h-[calc(100vh-2rem)] md:max-h-[calc(100vh-4rem)] lg:max-h-[calc(100vh-8rem)]"
        >
          {/* Header */}
          <div className="flex-shrink-0 p-4 md:p-6 border-b">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <MessageCircle className="h-6 w-6 md:h-7 md:w-7 text-blue-600" />
                <div>
                  <h2 className="text-xl md:text-2xl font-bold">Automation Chat</h2>
                  <p className="text-sm text-muted-foreground">Interactive execution monitoring</p>
                </div>
              </div>
              <Button variant="ghost" size="sm" onClick={onClose} className="h-8 w-8 md:h-10 md:w-10">
                <X className="h-4 w-4 md:h-5 md:w-5" />
              </Button>
            </div>
            
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 mt-4">
              <div className="flex items-center gap-3">
                <Badge variant="outline" className="text-sm">
                  {automationName}
                </Badge>
                <div className={`flex items-center gap-2 text-sm ${getStatusColor()}`}>
                  {getStatusIcon()}
                  <span className="capitalize">{executionStatus}</span>
                </div>
              </div>
              
              <div className="flex gap-2">
                {executionStatus === 'running' && (
                  <Button variant="outline" size="sm" onClick={pauseExecution}>
                    <Pause className="h-4 w-4 mr-2" />
                    Pause
                  </Button>
                )}
                {executionStatus === 'paused' && (
                  <Button variant="outline" size="sm" onClick={resumeExecution}>
                    <Play className="h-4 w-4 mr-2" />
                    Resume
                  </Button>
                )}
                {(executionStatus === 'running' || executionStatus === 'paused') && (
                  <Button variant="destructive" size="sm" onClick={stopExecution}>
                    <Square className="h-4 w-4 mr-2" />
                    Stop
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Messages Area */}
          <div className="flex-1 flex flex-col min-h-0">
            <ScrollArea className="flex-1 px-4 md:px-6">
              <div className="space-y-4 py-4">
                {messages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div className={`flex gap-3 max-w-[90%] md:max-w-[80%] lg:max-w-[70%] ${message.type === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                      <div className="flex-shrink-0 mt-1">
                        {message.type === 'user' ? (
                          <User className="h-6 w-6 md:h-7 md:w-7 text-blue-600" />
                        ) : message.type === 'system' ? (
                          <Bot className="h-6 w-6 md:h-7 md:w-7 text-green-600" />
                        ) : message.type === 'automation' ? (
                          <Zap className="h-6 w-6 md:h-7 md:w-7 text-purple-600" />
                        ) : message.type === 'success' ? (
                          <CheckCircle className="h-6 w-6 md:h-7 md:w-7 text-green-600" />
                        ) : (
                          <AlertCircle className="h-6 w-6 md:h-7 md:w-7 text-red-600" />
                        )}
                      </div>
                      
                      <div className={`rounded-lg px-4 py-3 text-sm md:text-base ${
                        message.type === 'user' 
                          ? 'bg-blue-600 text-white' 
                          : message.type === 'error'
                          ? 'bg-red-50 text-red-800 border border-red-200'
                          : message.type === 'success'
                          ? 'bg-green-50 text-green-800 border border-green-200'
                          : 'bg-muted'
                      }`}>
                        <p className="leading-relaxed">{message.content}</p>
                        <p className={`text-xs md:text-sm mt-2 opacity-70 ${
                          message.type === 'user' ? 'text-blue-100' : 'text-muted-foreground'
                        }`}>
                          {message.timestamp.toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ))}
                
                {isExecuting && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="flex justify-start"
                  >
                    <div className="flex gap-3">
                      <Settings className="h-6 w-6 md:h-7 md:w-7 text-purple-600 animate-spin" />
                      <div className="bg-muted rounded-lg px-4 py-3 text-sm md:text-base">
                        <div className="flex items-center gap-3">
                          <div className="flex gap-1">
                            <div className="w-2 h-2 bg-purple-600 rounded-full animate-pulse"></div>
                            <div className="w-2 h-2 bg-purple-600 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                            <div className="w-2 h-2 bg-purple-600 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                          </div>
                          <span>Executing...</span>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}
              </div>
              <div ref={messagesEndRef} />
            </ScrollArea>
          </div>

          {/* Input Area */}
          <div className="flex-shrink-0 border-t p-4 md:p-6">
            <div className="flex gap-3">
              <Input
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask about execution status, pause, or give instructions..."
                className="flex-1 text-sm md:text-base"
              />
              <Button 
                onClick={handleSendMessage}
                disabled={!newMessage.trim()}
                size="sm"
                className="px-4 py-2 md:px-6 md:py-3"
              >
                <Send className="h-4 w-4 md:h-5 md:w-5" />
                <span className="hidden sm:inline ml-2">Send</span>
              </Button>
            </div>
            
            <div className="flex flex-wrap gap-2 mt-3">
              <Button variant="outline" size="sm" className="text-xs md:text-sm">
                Show logs
              </Button>
              <Button variant="outline" size="sm" className="text-xs md:text-sm">
                View data
              </Button>
              <Button variant="outline" size="sm" className="text-xs md:text-sm">
                Settings
              </Button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}