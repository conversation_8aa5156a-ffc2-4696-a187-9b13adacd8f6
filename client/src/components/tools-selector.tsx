import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  Discord, 
  Globe, 
  FileSpreadsheet, 
  FileText, 
  MessageSquare, 
  Database,
  Search
} from 'lucide-react'
import { SiAirtable, SiDiscord, SiGooglesheets, SiNotion, SiSlack, SiSupabase } from 'react-icons/si'

export interface Tool {
  id: string
  name: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  color: string
}

export const availableTools: Tool[] = [
  {
    id: 'airtable',
    name: 'Airtable',
    description: 'Enable your agents to manage Airtable data',
    icon: SiAirtable,
    color: 'text-blue-600'
  },
  {
    id: 'discord',
    name: 'Discord',
    description: 'Enable your agents to interact with Discord channels',
    icon: SiDiscord,
    color: 'text-indigo-600'
  },
  {
    id: 'firecrawl',
    name: 'Firecrawl Web Scraper',
    description: 'Enable your agents to crawl and collect website data',
    icon: Globe,
    color: 'text-orange-600'
  },
  {
    id: 'googlesheets',
    name: 'Google Sheets',
    description: 'Enable your agents to read from and write to Google Sheets',
    icon: SiGooglesheets,
    color: 'text-green-600'
  },
  {
    id: 'notion',
    name: 'Notion',
    description: 'Enable your agents to manage Notion pages and databases',
    icon: SiNotion,
    color: 'text-gray-700'
  },
  {
    id: 'slack',
    name: 'Slack',
    description: 'Enable your agents to interact with Slack channels',
    icon: SiSlack,
    color: 'text-purple-600'
  },
  {
    id: 'supabase',
    name: 'Supabase',
    description: 'Enable your agents to interact with Supabase data',
    icon: SiSupabase,
    color: 'text-emerald-600'
  },
  {
    id: 'serpapi',
    name: 'SerpAPI',
    description: 'Enable your agents to perform web searches',
    icon: Search,
    color: 'text-blue-500'
  }
]

interface ToolsSelectorProps {
  selectedTools: string[]
  onToolsChange: (tools: string[]) => void
}

export function ToolsSelector({ selectedTools, onToolsChange }: ToolsSelectorProps) {
  const handleToolToggle = (toolId: string) => {
    if (selectedTools.includes(toolId)) {
      onToolsChange(selectedTools.filter(id => id !== toolId))
    } else {
      onToolsChange([...selectedTools, toolId])
    }
  }

  return (
    <div className="grid md:grid-cols-2 gap-4">
      {availableTools.map((tool) => {
        const Icon = tool.icon
        const isSelected = selectedTools.includes(tool.id)
        
        return (
          <Card 
            key={tool.id}
            className={`cursor-pointer transition-all ${
              isSelected ? 'ring-2 ring-primary' : ''
            }`}
            onClick={() => handleToolToggle(tool.id)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Icon className={`h-5 w-5 ${tool.color}`} />
                  <CardTitle className="text-base">{tool.name}</CardTitle>
                </div>
                <Checkbox checked={isSelected} />
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">{tool.description}</p>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}