import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Sparkles, X, ChevronRight, Lightbulb } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'

interface AISuggestion {
  id: string
  text: string
  type: 'tip' | 'warning' | 'recommendation'
  action?: {
    label: string
    onClick: () => void
  }
}

interface AIHelpSuggestionsProps {
  context: string
  fieldName?: string
  currentValue?: any
  suggestions?: AISuggestion[]
  className?: string
  compact?: boolean
  onDismiss?: () => void
}

export function AIHelpSuggestions({
  context,
  fieldName,
  currentValue,
  suggestions: providedSuggestions,
  className,
  compact = false,
  onDismiss
}: AIHelpSuggestionsProps) {
  const [suggestions, setSuggestions] = useState<AISuggestion[]>(providedSuggestions || [])
  const [isVisible, setIsVisible] = useState(true)
  const [isGenerating, setIsGenerating] = useState(false)

  useEffect(() => {
    if (!providedSuggestions && context) {
      generateSuggestions()
    }
  }, [context, fieldName, currentValue])

  const generateSuggestions = async () => {
    setIsGenerating(true)
    
    // Simulate AI generation
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const generatedSuggestions: AISuggestion[] = []
    
    // Context-aware suggestions
    if (context === 'automation-name') {
      generatedSuggestions.push({
        id: '1',
        text: 'Use descriptive names that clearly indicate what the automation does',
        type: 'tip'
      })
      if (!currentValue || currentValue.length < 5) {
        generatedSuggestions.push({
          id: '2',
          text: 'Names should be at least 5 characters long for clarity',
          type: 'warning'
        })
      }
    } else if (context === 'trigger-schedule') {
      generatedSuggestions.push({
        id: '1',
        text: 'Consider running heavy automations during off-peak hours',
        type: 'recommendation'
      })
      generatedSuggestions.push({
        id: '2',
        text: 'Daily automations work best for reports and summaries',
        type: 'tip'
      })
    } else if (context === 'api-key') {
      generatedSuggestions.push({
        id: '1',
        text: 'Keep your API keys secure and never share them publicly',
        type: 'warning'
      })
      generatedSuggestions.push({
        id: '2',
        text: 'You can find your API key in the service\'s dashboard under Settings',
        type: 'tip'
      })
    } else if (context === 'email-template') {
      generatedSuggestions.push({
        id: '1',
        text: 'Use personalization tokens like {{name}} to make emails more engaging',
        type: 'recommendation'
      })
    } else if (context === 'data-mapping') {
      generatedSuggestions.push({
        id: '1',
        text: 'Map required fields first before optional ones',
        type: 'tip'
      })
    }
    
    setSuggestions(generatedSuggestions)
    setIsGenerating(false)
  }

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'warning':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20'
      case 'recommendation':
        return 'text-purple-600 bg-purple-100 dark:bg-purple-900/20'
      default:
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20'
    }
  }

  if (!isVisible || suggestions.length === 0) return null

  if (compact) {
    return (
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className={cn("inline-flex items-center gap-2", className)}
        >
          <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
            <Sparkles className="h-3 w-3 text-purple-600" />
            <span>{suggestions[0].text}</span>
          </div>
        </motion.div>
      </AnimatePresence>
    )
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        className={cn("space-y-2", className)}
      >
        <Card className="border-purple-200 bg-purple-50/50 dark:border-purple-800 dark:bg-purple-950/20">
          <CardContent className="p-3">
            <div className="flex items-start justify-between mb-2">
              <div className="flex items-center gap-2">
                <Sparkles className="h-4 w-4 text-purple-600" />
                <span className="text-sm font-medium">AI Assistant</span>
                {isGenerating && (
                  <Badge variant="secondary" className="text-xs">
                    Thinking...
                  </Badge>
                )}
              </div>
              {onDismiss && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-5 w-5"
                  onClick={() => {
                    setIsVisible(false)
                    onDismiss()
                  }}
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
            
            <div className="space-y-2">
              {suggestions.map((suggestion) => (
                <motion.div
                  key={suggestion.id}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="flex items-start gap-2"
                >
                  <div className={cn(
                    "mt-0.5 p-1 rounded-full",
                    getSuggestionIcon(suggestion.type)
                  )}>
                    <Lightbulb className="h-3 w-3" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-muted-foreground">
                      {suggestion.text}
                    </p>
                    {suggestion.action && (
                      <Button
                        variant="link"
                        size="sm"
                        className="h-auto p-0 text-xs"
                        onClick={suggestion.action.onClick}
                      >
                        {suggestion.action.label}
                        <ChevronRight className="ml-1 h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </AnimatePresence>
  )
}