import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { SiGooglesheets, SiGoogledrive } from 'react-icons/si';
import { CheckCircle, AlertCircle, Loader2 } from 'lucide-react';

interface GoogleSheetsSetupProps {
  automationId?: string;
  onComplete?: () => void;
}

interface GoogleCredential {
  id: number;
  service: string;
  name: string;
  hasGoogleSheetsAccess?: boolean;
  hasGoogleDriveAccess?: boolean;
}

export function GoogleSheetsSetup({ automationId, onComplete }: GoogleSheetsSetupProps) {
  const { data: credentials, isLoading, refetch } = useQuery<GoogleCredential[]>({
    queryKey: ['/api/credentials'],
    queryFn: () => apiRequest('GET', '/api/credentials'),
  });

  const [testingConnection, setTestingConnection] = React.useState(false);
  const [connectionStatus, setConnectionStatus] = React.useState<'idle' | 'connected' | 'failed'>('idle');

  const googleCredentials = credentials?.filter(cred => cred.service === 'google') || [];

  const handleGoogleAuth = async () => {
    try {
      if (!automationId) {
        console.error('No automation ID provided');
        return;
      }

      const response = await apiRequest('GET', `/api/auth/google/start?automationId=${automationId}`);
      if (response.authUrl) {
        window.open(response.authUrl, 'google-auth', 'width=600,height=700');
        
        // Listen for OAuth completion
        const handleMessage = (event: MessageEvent) => {
          if (event.origin !== window.location.origin) return;
          
          if (event.data.type === 'oauth-success') {
            window.removeEventListener('message', handleMessage);
            refetch();
            onComplete?.();
          }
        };
        
        window.addEventListener('message', handleMessage);
      }
    } catch (error) {
      console.error('Google authentication error:', error);
    }
  };

  const testGoogleSheetsConnection = async (credentialId: number) => {
    setTestingConnection(true);
    setConnectionStatus('idle');
    
    try {
      const response = await apiRequest('POST', '/api/google-sheets/test-connection', {
        credentialId
      });
      
      if (response.connected) {
        setConnectionStatus('connected');
      } else {
        setConnectionStatus('failed');
      }
    } catch (error) {
      console.error('Connection test failed:', error);
      setConnectionStatus('failed');
    } finally {
      setTestingConnection(false);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading Google credentials...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <SiGooglesheets className="h-5 w-5 text-green-600" />
            Google Sheets Setup
          </CardTitle>
          <CardDescription>
            Connect your Google account to access Google Sheets and Drive
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {googleCredentials.length > 0 ? (
            <div className="space-y-3">
              <h4 className="font-medium">Connected Google Accounts</h4>
              {googleCredentials.map((credential) => (
                <div key={credential.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <SiGooglesheets className="h-4 w-4 text-green-600" />
                      <SiGoogledrive className="h-4 w-4 text-yellow-600" />
                    </div>
                    <div>
                      <p className="font-medium">{credential.name}</p>
                      <p className="text-sm text-muted-foreground">Google Account</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {connectionStatus === 'connected' ? (
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Connected
                      </Badge>
                    ) : connectionStatus === 'failed' ? (
                      <Badge variant="destructive">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        Failed
                      </Badge>
                    ) : (
                      <Badge variant="outline">Ready</Badge>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => testGoogleSheetsConnection(credential.id)}
                      disabled={testingConnection}
                    >
                      {testingConnection ? (
                        <>
                          <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                          Testing
                        </>
                      ) : (
                        'Test Connection'
                      )}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <SiGooglesheets className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Connect Google Account</h3>
              <p className="text-muted-foreground mb-4">
                To use Google Sheets, you need to connect your Google account with appropriate permissions.
              </p>
              <Button onClick={handleGoogleAuth} className="bg-green-600 hover:bg-green-700">
                <SiGooglesheets className="h-4 w-4 mr-2" />
                Connect Google Account
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {connectionStatus === 'failed' && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="h-4 w-4" />
              <span className="font-medium">Connection Failed</span>
            </div>
            <p className="text-sm text-red-700 mt-2">
              Your Google account may not have the required permissions for Google Sheets and Drive. 
              Please re-authenticate to grant the necessary access.
            </p>
            <Button 
              variant="outline" 
              size="sm" 
              className="mt-3 border-red-300 text-red-700 hover:bg-red-100"
              onClick={handleGoogleAuth}
            >
              Re-authenticate with Google
            </Button>
          </CardContent>
        </Card>
      )}

      {connectionStatus === 'connected' && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-green-800">
              <CheckCircle className="h-4 w-4" />
              <span className="font-medium">Google Sheets Connected</span>
            </div>
            <p className="text-sm text-green-700 mt-2">
              Your Google account is successfully connected with access to Google Sheets and Drive.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}