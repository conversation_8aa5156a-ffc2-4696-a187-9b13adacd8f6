import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { ArrowLeft, Loader2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useAuth } from '@/contexts/AuthContext'
import { useToast } from '@/hooks/use-toast'

const resetSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
})

type ResetFormData = z.infer<typeof resetSchema>

interface PasswordResetFormProps {
  onBack: () => void
}

export const PasswordResetForm: React.FC<PasswordResetFormProps> = ({ onBack }) => {
  const [isLoading, setIsLoading] = useState(false)
  const { resetPassword } = useAuth()
  const { toast } = useToast()

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ResetFormData>({
    resolver: zodResolver(resetSchema),
    defaultValues: {
      email: '',
    },
  })

  const onSubmit = async (data: ResetFormData) => {
    setIsLoading(true)
    
    try {
      const { error } = await resetPassword(data.email)
      
      if (error) {
        toast({
          title: 'Password reset failed',
          description: error.message,
          variant: 'destructive',
        })
      } else {
        toast({
          title: 'Password reset link sent!',
          description: 'Check your email for instructions to reset your password.',
        })
        onBack()
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="text-center mb-6">
        <h3 className="text-xl font-semibold text-slate-900 mb-2">Reset Password</h3>
        <p className="text-sm text-slate-600">
          Enter your email address and we'll send you a link to reset your password.
        </p>
      </div>

      <div className="space-y-4">
        {/* Email Field */}
        <div className="relative">
          <Input
            {...register('email')}
            type="email"
            placeholder="Email address"
            className="peer w-full px-4 py-3 border border-slate-200 rounded-lg focus:ring-2 focus:ring-brand-blue focus:border-transparent outline-none transition-all duration-200 placeholder-transparent bg-white/50 backdrop-blur-sm"
            disabled={isLoading}
          />
          <Label
            htmlFor="email"
            className="absolute left-4 -top-2.5 bg-white px-2 text-sm font-medium text-slate-600 transition-all duration-200 peer-placeholder-shown:text-base peer-placeholder-shown:text-slate-400 peer-placeholder-shown:top-3 peer-focus:-top-2.5 peer-focus:text-sm peer-focus:text-brand-blue"
          >
            Email address
          </Label>
          {errors.email && (
            <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
          )}
        </div>
      </div>

      {/* Reset Button */}
      <Button
        type="submit"
        disabled={isLoading}
        className="w-full text-white py-3 px-4 rounded-lg font-semibold shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 flex items-center justify-center space-x-2"
        style={{ 
          background: 'linear-gradient(to right, #155DB8, hsl(217, 91%, 60%))' 
        }}
      >
        {isLoading ? (
          <>
            <Loader2 className="animate-spin w-4 h-4" />
            <span>Sending reset link...</span>
          </>
        ) : (
          <span>Send Reset Link</span>
        )}
      </Button>

      {/* Back to Login */}
      <div className="text-center">
        <button
          type="button"
          onClick={onBack}
          className="text-brand-blue hover:text-brand-blue-light transition-colors duration-200 font-medium text-sm flex items-center justify-center space-x-1"
          disabled={isLoading}
        >
          <ArrowLeft size={16} />
          <span>Back to Sign In</span>
        </button>
      </div>
    </form>
  )
}
