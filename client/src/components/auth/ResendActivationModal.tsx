import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Mail, X, Loader2, CheckCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { useAuth } from '@/contexts/AuthContext'
import { useToast } from '@/hooks/use-toast'

const emailSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
})

type EmailFormData = z.infer<typeof emailSchema>

interface ResendActivationModalProps {
  isOpen: boolean
  onClose: () => void
  defaultEmail?: string
}

export const ResendActivationModal: React.FC<ResendActivationModalProps> = ({
  isOpen,
  onClose,
  defaultEmail = '',
}) => {
  const [isLoading, setIsLoading] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const { resendActivation } = useAuth()
  const { toast } = useToast()

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<EmailFormData>({
    resolver: zodResolver(emailSchema),
    defaultValues: {
      email: defaultEmail,
    },
  })

  const onSubmit = async (data: EmailFormData) => {
    setIsLoading(true)
    
    try {
      const { error } = await resendActivation(data.email)
      
      if (error) {
        toast({
          title: 'Failed to resend activation',
          description: error.message,
          variant: 'destructive',
        })
      } else {
        setIsSuccess(true)
        toast({
          title: 'Activation email sent!',
          description: 'Please check your email inbox and spam folder.',
        })
        setTimeout(() => {
          handleClose()
        }, 2000)
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    setIsSuccess(false)
    setIsLoading(false)
    reset()
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Mail className="text-brand-blue" size={24} />
            <span>Resend Activation Email</span>
          </DialogTitle>
        </DialogHeader>

        {isSuccess ? (
          <div className="text-center py-8">
            <CheckCircle className="text-green-500 mx-auto mb-4" size={48} />
            <h3 className="text-lg font-semibold text-slate-900 mb-2">
              Email Sent Successfully!
            </h3>
            <p className="text-slate-600 mb-4">
              We've sent a new activation email to your inbox. Please check your email and spam folder.
            </p>
            <Button onClick={handleClose} style={{ backgroundColor: '#155DB8' }} className="hover:opacity-90">
              Close
            </Button>
          </div>
        ) : (
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-2">
              <p className="text-sm text-slate-600">
                Enter your email address and we'll send you a new activation link.
              </p>
            </div>

            <div className="space-y-4">
              <div className="relative">
                <Input
                  {...register('email')}
                  type="email"
                  placeholder="Email address"
                  className="peer w-full px-4 py-3 border border-slate-200 rounded-lg focus:ring-2 focus:ring-brand-blue focus:border-transparent outline-none transition-all duration-200 placeholder-transparent bg-white/50 backdrop-blur-sm"
                  disabled={isLoading}
                />
                <Label
                  htmlFor="email"
                  className="absolute left-4 -top-2.5 bg-white px-2 text-sm font-medium text-slate-600 transition-all duration-200 peer-placeholder-shown:text-base peer-placeholder-shown:text-slate-400 peer-placeholder-shown:top-3 peer-focus:-top-2.5 peer-focus:text-sm peer-focus:text-brand-blue"
                >
                  Email address
                </Label>
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
                )}
              </div>
            </div>

            <div className="flex space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isLoading}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="flex-1 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                style={{ 
                  background: 'linear-gradient(to right, #155DB8, hsl(217, 91%, 60%))' 
                }}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="animate-spin w-4 h-4 mr-2" />
                    Sending...
                  </>
                ) : (
                  'Send Activation Email'
                )}
              </Button>
            </div>
          </form>
        )}
      </DialogContent>
    </Dialog>
  )
}