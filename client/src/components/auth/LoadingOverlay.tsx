import React from 'react'
import { Loader2 } from 'lucide-react'

interface LoadingOverlayProps {
  isVisible: boolean
  message?: string
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({ 
  isVisible, 
  message = 'Processing...' 
}) => {
  if (!isVisible) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl p-8 shadow-2xl max-w-sm mx-4">
        <div className="text-center">
          <Loader2 className="animate-spin inline-block w-8 h-8 text-brand-blue mb-4" />
          <p className="text-slate-600 font-medium">{message}</p>
        </div>
      </div>
    </div>
  )
}
