import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Eye, EyeOff, Loader2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { useAuth } from '@/contexts/AuthContext'
import { useToast } from '@/hooks/use-toast'
import { HelpTooltip } from '@/components/ui/help-tooltip'

const loginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  remember: z.boolean().default(false),
})

type LoginFormData = z.infer<typeof loginSchema>

interface LoginFormProps {
  onForgotPassword: () => void
  onResendActivation: (email?: string) => void
}

export const LoginForm: React.FC<LoginFormProps> = ({ onForgotPassword, onResendActivation }) => {
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const { signIn } = useAuth()
  const { toast } = useToast()

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
      remember: false,
    },
  })

  const watchedEmail = watch('email')

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true)
    
    try {
      const { error } = await signIn(data.email, data.password)
      
      if (error) {
        if (error.message.includes('Email not confirmed')) {
          toast({
            title: 'Email not verified',
            description: (
              <div className="space-y-2">
                <p>Please check your email and click the verification link.</p>
                <button
                  onClick={() => onResendActivation(data.email)}
                  className="text-blue-600 hover:text-blue-800 underline text-sm"
                >
                  Resend activation email
                </button>
              </div>
            ),
            variant: 'destructive',
          })
        } else if (error.message.includes('Invalid login credentials')) {
          toast({
            title: 'Invalid credentials',
            description: 'Please check your email and password.',
            variant: 'destructive',
          })
        } else {
          toast({
            title: 'Sign in failed',
            description: error.message,
            variant: 'destructive',
          })
        }
      } else {
        toast({
          title: 'Welcome back!',
          description: 'You have been signed in successfully.',
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="space-y-4">
        {/* Email Field */}
        <div className="relative">
          <Input
            {...register('email')}
            type="email"
            placeholder="Email address"
            className="peer w-full px-4 py-3 border border-slate-200 rounded-lg focus:ring-2 focus:ring-brand-blue focus:border-transparent outline-none transition-all duration-200 placeholder-transparent bg-white/50 backdrop-blur-sm"
            disabled={isLoading}
          />
          <Label
            htmlFor="email"
            className="absolute left-4 -top-2.5 bg-white px-2 text-sm font-medium text-slate-600 transition-all duration-200 peer-placeholder-shown:text-base peer-placeholder-shown:text-slate-400 peer-placeholder-shown:top-3 peer-focus:-top-2.5 peer-focus:text-sm peer-focus:text-brand-blue"
          >
            Email address
          </Label>
          <HelpTooltip
            content="Enter the email address associated with your account"
            className="absolute right-3 top-3"
            icon="info"
          />
          {errors.email && (
            <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
          )}
        </div>

        {/* Password Field */}
        <div className="relative">
          <Input
            {...register('password')}
            type={showPassword ? 'text' : 'password'}
            placeholder="Password"
            className="peer w-full px-4 py-3 border border-slate-200 rounded-lg focus:ring-2 focus:ring-brand-blue focus:border-transparent outline-none transition-all duration-200 placeholder-transparent bg-white/50 backdrop-blur-sm pr-12"
            disabled={isLoading}
          />
          <Label
            htmlFor="password"
            className="absolute left-4 -top-2.5 bg-white px-2 text-sm font-medium text-slate-600 transition-all duration-200 peer-placeholder-shown:text-base peer-placeholder-shown:text-slate-400 peer-placeholder-shown:top-3 peer-focus:-top-2.5 peer-focus:text-sm peer-focus:text-brand-blue"
          >
            Password
          </Label>
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-3 text-slate-400 hover:text-slate-600 transition-colors duration-200"
            disabled={isLoading}
          >
            {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
          </button>
          {errors.password && (
            <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
          )}
        </div>
      </div>

      {/* Remember Me and Forgot Password */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Checkbox
            {...register('remember')}
            id="remember"
            disabled={isLoading}
          />
          <Label htmlFor="remember" className="text-sm text-slate-600">
            Remember me
          </Label>
        </div>
        <button
          type="button"
          onClick={onForgotPassword}
          className="text-sm text-brand-blue hover:text-brand-blue-light transition-colors duration-200 font-medium"
          disabled={isLoading}
        >
          Forgot password?
        </button>
      </div>

      {/* Login Button */}
      <Button
        type="submit"
        disabled={isLoading}
        className="w-full text-white py-3 px-4 rounded-lg font-semibold shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200 flex items-center justify-center space-x-2"
        style={{ 
          background: 'linear-gradient(to right, #155DB8, hsl(217, 91%, 60%))' 
        }}
      >
        {isLoading ? (
          <>
            <Loader2 className="animate-spin w-4 h-4" />
            <span>Signing in...</span>
          </>
        ) : (
          <span>Sign In</span>
        )}
      </Button>

      {/* Resend Activation */}
      <div className="text-center">
        <p className="text-sm text-slate-600 mb-2">Haven't received activation email?</p>
        <button
          type="button"
          onClick={() => onResendActivation(watchedEmail)}
          className="text-brand-blue hover:text-brand-blue-light transition-colors duration-200 font-medium text-sm underline"
          disabled={isLoading}
        >
          Resend activation email
        </button>
      </div>
    </form>
  )
}
