import React from 'react'
import { cn } from '@/lib/utils'

interface AuthTabsProps {
  activeTab: 'login' | 'register'
  onTabChange: (tab: 'login' | 'register') => void
}

export const AuthTabs: React.FC<AuthTabsProps> = ({ activeTab, onTabChange }) => {
  return (
    <div className="mb-8">
      <div className="flex space-x-1 bg-slate-100/50 p-1 rounded-lg">
        <button
          onClick={() => onTabChange('login')}
          className={cn(
            'flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200',
            activeTab === 'login'
              ? 'bg-white shadow-sm text-brand-blue border border-brand-blue/20'
              : 'text-slate-600 hover:text-slate-900'
          )}
        >
          Sign In
        </button>
        <button
          onClick={() => onTabChange('register')}
          className={cn(
            'flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200',
            activeTab === 'register'
              ? 'bg-white shadow-sm text-brand-blue border border-brand-blue/20'
              : 'text-slate-600 hover:text-slate-900'
          )}
        >
          Sign Up
        </button>
      </div>
    </div>
  )
}
