import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface EmojiCelebrationProps {
  onComplete?: () => void
  duration?: number
}

const celebrationEmojis = [
  '🎉', '🎊', '🥳', '🎈', '🎁', '🌟', '✨', '🔥', '💫', '🚀',
  '🎯', '⚡', '🌈', '🎪', '🎭', '🏆', '🥇', '🎪', '🎢', '🎡'
]

const congratulationsMessages = [
  'Woohoo! 🎉',
  'Amazing! ✨',
  'Fantastic! 🚀',
  'Incredible! 🌟',
  'Perfect! 💫',
  'Outstanding! 🏆',
  'Brilliant! 🔥',
  'Awesome! 🎊',
  'Spectacular! 🎈',
  'Excellent! 🥳'
]

export function EmojiCelebration({ onComplete, duration = 3000 }: EmojiCelebrationProps) {
  const [showEmojis, setShowEmojis] = useState(true)
  const [currentMessage, setCurrentMessage] = useState('')

  useEffect(() => {
    // Set random congratulations message
    const randomMessage = congratulationsMessages[Math.floor(Math.random() * congratulationsMessages.length)]
    setCurrentMessage(randomMessage)

    const timer = setTimeout(() => {
      setShowEmojis(false)
      onComplete?.()
    }, duration)

    return () => clearTimeout(timer)
  }, [duration, onComplete])

  const generateRandomEmojis = () => {
    return Array.from({ length: 12 }, (_, i) => {
      const emoji = celebrationEmojis[Math.floor(Math.random() * celebrationEmojis.length)]
      const delay = Math.random() * 2
      const duration = 2 + Math.random() * 2
      const x = Math.random() * 100
      const rotation = Math.random() * 360
      
      return (
        <motion.div
          key={i}
          initial={{ 
            opacity: 0, 
            scale: 0, 
            x: `${x}vw`, 
            y: '100vh',
            rotate: 0
          }}
          animate={{ 
            opacity: [0, 1, 1, 0], 
            scale: [0, 1.2, 1, 0.8], 
            y: '-20vh',
            rotate: rotation
          }}
          transition={{ 
            duration: duration,
            delay: delay,
            ease: "easeOut"
          }}
          className="fixed text-4xl pointer-events-none z-50"
          style={{ left: 0, top: 0 }}
        >
          {emoji}
        </motion.div>
      )
    })
  }

  return (
    <AnimatePresence>
      {showEmojis && (
        <>
          {/* Floating emojis */}
          {generateRandomEmojis()}
          
          {/* Center celebration message */}
          <motion.div
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="fixed inset-0 flex items-center justify-center pointer-events-none z-40"
          >
            <motion.div
              animate={{ 
                scale: [1, 1.1, 1],
                rotate: [0, -2, 2, 0]
              }}
              transition={{ 
                duration: 0.6,
                repeat: Infinity,
                repeatType: "reverse"
              }}
              className="text-6xl font-bold text-center bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent"
            >
              {currentMessage}
            </motion.div>
          </motion.div>
          
          {/* Confetti burst effect */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 pointer-events-none z-30"
          >
            {Array.from({ length: 50 }, (_, i) => (
              <motion.div
                key={i}
                initial={{
                  opacity: 0,
                  scale: 0,
                  x: '50vw',
                  y: '50vh',
                }}
                animate={{
                  opacity: [0, 1, 0],
                  scale: [0, 1, 0],
                  x: `${Math.random() * 100}vw`,
                  y: `${Math.random() * 100}vh`,
                }}
                transition={{
                  duration: 3,
                  delay: Math.random() * 1,
                  ease: "easeOut"
                }}
                className="absolute w-2 h-2 rounded-full"
                style={{
                  backgroundColor: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#f9ca24', '#f0932b', '#eb4d4b', '#6c5ce7'][Math.floor(Math.random() * 7)]
                }}
              />
            ))}
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}