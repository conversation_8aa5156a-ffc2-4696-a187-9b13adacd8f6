import { useState } from 'react'
import { useQuery, useMutation } from '@tanstack/react-query'
import { Plus, Eye, EyeOff, Trash2, Key, Shield, CheckCircle, XCircle } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Textarea } from '@/components/ui/textarea'
import { apiRequest, queryClient } from '@/lib/queryClient'

interface Credential {
  id: number
  service: string
  name: string
  type: string
  isActive: boolean
  createdAt: string
  expiresAt?: string
}

const serviceOptions = [
  { value: 'gemini', label: 'Google Gemini', icon: '🤖' },
  { value: 'openai', label: 'OpenAI', icon: '🧠' },
  { value: 'anthropic', label: 'Anthropic Claude', icon: '💭' },
  { value: 'gmail', label: 'Gmail', icon: '📧' },
  { value: 'google-sheets', label: 'Google Sheets', icon: '📊' },
  { value: 'slack', label: 'Slack', icon: '💬' },
  { value: 'discord', label: 'Discord', icon: '🎮' },
  { value: 'webhook', label: 'Custom Webhook', icon: '🔗' },
]

const typeOptions = [
  { value: 'api_key', label: 'API Key' },
  { value: 'oauth', label: 'OAuth Token' },
  { value: 'webhook', label: 'Webhook' },
]

export function CredentialsManager() {
  const [showForm, setShowForm] = useState(false)
  const [showApiKey, setShowApiKey] = useState(false)
  const [formData, setFormData] = useState({
    service: '',
    name: '',
    type: 'api_key',
    apiKey: '',
    webhookUrl: '',
    webhookSecret: ''
  })
  const { toast } = useToast()

  // Fetch credentials
  const { data: credentials = [], isLoading } = useQuery({
    queryKey: ['/api/credentials'],
    queryFn: () => apiRequest('GET', '/api/credentials').then(res => res.json())
  })

  // Create credential mutation
  const createCredentialMutation = useMutation({
    mutationFn: async (credentialData: any) => {
      return apiRequest('POST', '/api/credentials', credentialData).then(res => res.json())
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/credentials'] })
      setShowForm(false)
      setFormData({
        service: '',
        name: '',
        type: 'api_key',
        apiKey: '',
        webhookUrl: '',
        webhookSecret: ''
      })
      toast({
        title: 'Credential added successfully',
        description: 'Your credential has been securely stored and encrypted.',
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to add credential',
        description: error.message || 'Something went wrong. Please try again.',
        variant: 'destructive'
      })
    }
  })

  // Delete credential mutation
  const deleteCredentialMutation = useMutation({
    mutationFn: async (id: number) => {
      return apiRequest('DELETE', `/api/credentials/${id}`)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/credentials'] })
      toast({
        title: 'Credential deleted',
        description: 'The credential has been permanently removed.',
      })
    }
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    let data: any
    
    switch (formData.type) {
      case 'api_key':
        data = { apiKey: formData.apiKey }
        break
      case 'webhook':
        data = { 
          url: formData.webhookUrl,
          secret: formData.webhookSecret || undefined
        }
        break
      default:
        data = {}
    }
    
    createCredentialMutation.mutate({
      service: formData.service,
      name: formData.name,
      type: formData.type,
      data
    })
  }

  const getServiceIcon = (service: string) => {
    const serviceOption = serviceOptions.find(s => s.value === service)
    return serviceOption?.icon || '🔑'
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'api_key': return 'bg-blue-100 text-blue-800'
      case 'oauth': return 'bg-green-100 text-green-800'
      case 'webhook': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const isExpired = (expiresAt?: string) => {
    if (!expiresAt) return false
    return new Date(expiresAt) < new Date()
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Credentials</h2>
          <p className="text-muted-foreground">
            Securely manage API keys and authentication tokens for your automations
          </p>
        </div>
        <Dialog open={showForm} onOpenChange={setShowForm}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Credential
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Add New Credential</DialogTitle>
              <DialogDescription>
                Add a new API key or authentication token. All credentials are encrypted before storage.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="service">Service</Label>
                  <Select 
                    value={formData.service} 
                    onValueChange={(value) => setFormData({...formData, service: value})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select service" />
                    </SelectTrigger>
                    <SelectContent>
                      {serviceOptions.map(service => (
                        <SelectItem key={service.value} value={service.value}>
                          {service.icon} {service.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="type">Type</Label>
                  <Select 
                    value={formData.type} 
                    onValueChange={(value) => setFormData({...formData, type: value})}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {typeOptions.map(type => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div>
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  placeholder="e.g., My Gemini API Key"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  required
                />
              </div>

              {formData.type === 'api_key' && (
                <div>
                  <Label htmlFor="apiKey">API Key</Label>
                  <div className="relative">
                    <Input
                      id="apiKey"
                      type={showApiKey ? "text" : "password"}
                      placeholder="Enter your API key"
                      value={formData.apiKey}
                      onChange={(e) => setFormData({...formData, apiKey: e.target.value})}
                      required
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3"
                      onClick={() => setShowApiKey(!showApiKey)}
                    >
                      {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              )}

              {formData.type === 'webhook' && (
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="webhookUrl">Webhook URL</Label>
                    <Input
                      id="webhookUrl"
                      type="url"
                      placeholder="https://your-webhook-endpoint.com"
                      value={formData.webhookUrl}
                      onChange={(e) => setFormData({...formData, webhookUrl: e.target.value})}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="webhookSecret">Secret (Optional)</Label>
                    <Input
                      id="webhookSecret"
                      type="password"
                      placeholder="Webhook secret for verification"
                      value={formData.webhookSecret}
                      onChange={(e) => setFormData({...formData, webhookSecret: e.target.value})}
                    />
                  </div>
                </div>
              )}

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setShowForm(false)}>
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={createCredentialMutation.isPending}
                >
                  {createCredentialMutation.isPending ? 'Adding...' : 'Add Credential'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {isLoading ? (
        <div className="grid gap-4">
          {[1, 2, 3].map(i => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : credentials.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <Shield className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No credentials added yet</h3>
            <p className="text-muted-foreground mb-4">
              Add your first API key or authentication token to start using AI services and external integrations.
            </p>
            <Button onClick={() => setShowForm(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Your First Credential
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {credentials.map((credential: Credential) => (
            <Card key={credential.id}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <span className="text-2xl">{getServiceIcon(credential.service)}</span>
                    <div>
                      <CardTitle className="text-lg">{credential.name}</CardTitle>
                      <CardDescription className="capitalize">
                        {credential.service} • {credential.type.replace('_', ' ')}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {credential.isActive && !isExpired(credential.expiresAt) ? (
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        <CheckCircle className="mr-1 h-3 w-3" />
                        Active
                      </Badge>
                    ) : (
                      <Badge variant="secondary" className="bg-red-100 text-red-800">
                        <XCircle className="mr-1 h-3 w-3" />
                        {isExpired(credential.expiresAt) ? 'Expired' : 'Inactive'}
                      </Badge>
                    )}
                    <Badge className={getTypeColor(credential.type)}>
                      {credential.type.replace('_', ' ')}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <span>Created {new Date(credential.createdAt).toLocaleDateString()}</span>
                  <div className="flex items-center gap-2">
                    {credential.expiresAt && (
                      <span>
                        Expires {new Date(credential.expiresAt).toLocaleDateString()}
                      </span>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => deleteCredentialMutation.mutate(credential.id)}
                      disabled={deleteCredentialMutation.isPending}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}