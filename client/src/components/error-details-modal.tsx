import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  AlertCircle,
  X,
  Copy,
  CheckCircle,
  ChevronRight,
  RefreshCw,
  Settings,
  Key,
  Globe,
  Database,
  Code,
  FileText,
  Lightbulb,
  ExternalLink,
  AlertTriangle,
  Info
} from 'lucide-react'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useToast } from '@/hooks/use-toast'

export interface ErrorDetails {
  id: string
  timestamp: Date
  automation: {
    id: string
    name: string
    type: string
  }
  error: {
    code: string
    message: string
    type: 'api' | 'auth' | 'network' | 'validation' | 'timeout' | 'rate_limit' | 'configuration' | 'unknown'
    severity: 'critical' | 'error' | 'warning'
    details?: any
    stackTrace?: string
  }
  context: {
    step: string
    input?: any
    output?: any
    duration?: number
    retryCount: number
    lastRetry?: Date
  }
  troubleshooting: {
    possibleCauses: string[]
    quickFixes: QuickFix[]
    documentation: string[]
    relatedErrors: string[]
  }
}

interface QuickFix {
  id: string
  title: string
  description: string
  action: 'retry' | 'configure' | 'authenticate' | 'update' | 'contact'
  icon: React.ComponentType<{ className?: string }>
  automated: boolean
}

interface ErrorDetailsModalProps {
  error: ErrorDetails | null
  open: boolean
  onClose: () => void
  onRetry?: (errorId: string) => void
  onApplyFix?: (errorId: string, fixId: string) => void
}

export function ErrorDetailsModal({ error, open, onClose, onRetry, onApplyFix }: ErrorDetailsModalProps) {
  const { toast } = useToast()
  const [copiedSection, setCopiedSection] = useState<string | null>(null)
  const [applyingFix, setApplyingFix] = useState<string | null>(null)

  if (!error) return null

  const copyToClipboard = async (text: string, section: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedSection(section)
      setTimeout(() => setCopiedSection(null), 2000)
      toast({
        title: "Copied to clipboard",
        description: `${section} has been copied to your clipboard.`
      })
    } catch (err) {
      toast({
        title: "Failed to copy",
        description: "Please try selecting and copying manually.",
        variant: "destructive"
      })
    }
  }

  const getErrorIcon = () => {
    switch (error.error.type) {
      case 'api':
        return <Globe className="h-5 w-5" />
      case 'auth':
        return <Key className="h-5 w-5" />
      case 'network':
        return <Globe className="h-5 w-5" />
      case 'validation':
        return <FileText className="h-5 w-5" />
      case 'timeout':
        return <RefreshCw className="h-5 w-5" />
      case 'rate_limit':
        return <AlertTriangle className="h-5 w-5" />
      case 'configuration':
        return <Settings className="h-5 w-5" />
      default:
        return <AlertCircle className="h-5 w-5" />
    }
  }

  const getErrorColor = () => {
    switch (error.error.severity) {
      case 'critical':
        return 'text-red-600 bg-red-100 dark:bg-red-900/20'
      case 'error':
        return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20'
      case 'warning':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20'
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20'
    }
  }

  const handleApplyFix = async (fix: QuickFix) => {
    if (!fix.automated) {
      toast({
        title: "Manual action required",
        description: fix.description,
      })
      return
    }

    setApplyingFix(fix.id)
    
    // Simulate applying fix
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    if (onApplyFix) {
      onApplyFix(error.id, fix.id)
    }
    
    setApplyingFix(null)
    toast({
      title: "Fix applied",
      description: `${fix.title} has been applied successfully.`,
    })
  }

  const generateQuickFixes = (): QuickFix[] => {
    const fixes: QuickFix[] = []
    
    switch (error.error.type) {
      case 'auth':
        fixes.push({
          id: 'refresh-auth',
          title: 'Refresh Authentication',
          description: 'Re-authenticate with the service to restore access',
          action: 'authenticate',
          icon: Key,
          automated: true
        })
        fixes.push({
          id: 'check-credentials',
          title: 'Check API Credentials',
          description: 'Verify your API keys and tokens are valid',
          action: 'configure',
          icon: Settings,
          automated: false
        })
        break
        
      case 'rate_limit':
        fixes.push({
          id: 'retry-later',
          title: 'Retry After Rate Limit',
          description: 'Wait for rate limit to reset and retry',
          action: 'retry',
          icon: RefreshCw,
          automated: true
        })
        fixes.push({
          id: 'upgrade-plan',
          title: 'Upgrade API Plan',
          description: 'Consider upgrading to a higher tier for more requests',
          action: 'update',
          icon: TrendingUp,
          automated: false
        })
        break
        
      case 'network':
      case 'timeout':
        fixes.push({
          id: 'retry-request',
          title: 'Retry Request',
          description: 'Attempt to reconnect and retry the operation',
          action: 'retry',
          icon: RefreshCw,
          automated: true
        })
        fixes.push({
          id: 'check-connection',
          title: 'Check Network Settings',
          description: 'Verify firewall and proxy settings',
          action: 'configure',
          icon: Globe,
          automated: false
        })
        break
        
      case 'configuration':
        fixes.push({
          id: 'review-config',
          title: 'Review Configuration',
          description: 'Check automation settings and parameters',
          action: 'configure',
          icon: Settings,
          automated: false
        })
        break
        
      default:
        fixes.push({
          id: 'retry-operation',
          title: 'Retry Operation',
          description: 'Try running the automation again',
          action: 'retry',
          icon: RefreshCw,
          automated: true
        })
    }
    
    fixes.push({
      id: 'contact-support',
      title: 'Contact Support',
      description: 'Get help from our support team',
      action: 'contact',
      icon: HelpCircle,
      automated: false
    })
    
    return fixes
  }

  // Use generated fixes if not provided
  const quickFixes = error.troubleshooting.quickFixes.length > 0 
    ? error.troubleshooting.quickFixes 
    : generateQuickFixes()

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader className="pb-4">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-lg ${getErrorColor()}`}>
                {getErrorIcon()}
              </div>
              <div>
                <DialogTitle className="text-xl">Error Details</DialogTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  {error.automation.name} • {error.timestamp.toLocaleString()}
                </p>
              </div>
            </div>
            <Badge variant={error.error.severity === 'critical' ? 'destructive' : 'secondary'}>
              {error.error.severity}
            </Badge>
          </div>
        </DialogHeader>

        <Tabs defaultValue="overview" className="flex-1">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="troubleshoot">Troubleshoot</TabsTrigger>
            <TabsTrigger value="details">Technical Details</TabsTrigger>
            <TabsTrigger value="history">Error History</TabsTrigger>
          </TabsList>

          <ScrollArea className="h-[calc(90vh-200px)] mt-4">
            <TabsContent value="overview" className="space-y-4">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error: {error.error.code}</AlertTitle>
                <AlertDescription className="mt-2">
                  {error.error.message}
                </AlertDescription>
              </Alert>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Context Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Failed Step:</span>
                      <p className="font-medium">{error.context.step}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Error Type:</span>
                      <p className="font-medium capitalize">{error.error.type.replace('_', ' ')}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Retry Attempts:</span>
                      <p className="font-medium">{error.context.retryCount}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Duration:</span>
                      <p className="font-medium">{error.context.duration ? `${error.context.duration}ms` : 'N/A'}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base flex items-center gap-2">
                    <Lightbulb className="h-4 w-4 text-yellow-600" />
                    Quick Actions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {quickFixes.slice(0, 3).map((fix) => (
                    <Button
                      key={fix.id}
                      variant="outline"
                      size="sm"
                      className="w-full justify-start"
                      onClick={() => handleApplyFix(fix)}
                      disabled={applyingFix === fix.id}
                    >
                      <fix.icon className="mr-2 h-4 w-4" />
                      {applyingFix === fix.id ? 'Applying...' : fix.title}
                      {fix.automated && (
                        <Badge variant="secondary" className="ml-auto">
                          Automated
                        </Badge>
                      )}
                    </Button>
                  ))}
                  {onRetry && (
                    <Button
                      variant="default"
                      size="sm"
                      className="w-full"
                      onClick={() => onRetry(error.id)}
                    >
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Retry Automation
                    </Button>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="troubleshoot" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Possible Causes</CardTitle>
                  <CardDescription>
                    Common reasons why this error might occur
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {error.troubleshooting.possibleCauses.map((cause, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <ChevronRight className="h-4 w-4 text-muted-foreground mt-0.5" />
                        <span className="text-sm">{cause}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Recommended Solutions</CardTitle>
                  <CardDescription>
                    Step-by-step fixes for this issue
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  {quickFixes.map((fix, index) => (
                    <div key={fix.id} className="space-y-2">
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center text-xs font-medium">
                          {index + 1}
                        </div>
                        <h4 className="font-medium text-sm">{fix.title}</h4>
                      </div>
                      <p className="text-sm text-muted-foreground ml-8">
                        {fix.description}
                      </p>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ml-8"
                        onClick={() => handleApplyFix(fix)}
                        disabled={applyingFix === fix.id}
                      >
                        {applyingFix === fix.id ? (
                          <>
                            <RefreshCw className="mr-2 h-3 w-3 animate-spin" />
                            Applying...
                          </>
                        ) : (
                          <>
                            <fix.icon className="mr-2 h-3 w-3" />
                            {fix.automated ? 'Apply Fix' : 'Learn More'}
                          </>
                        )}
                      </Button>
                    </div>
                  ))}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Related Documentation</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {error.troubleshooting.documentation.map((doc, index) => (
                    <Button
                      key={index}
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start"
                      onClick={() => window.open(doc, '_blank')}
                    >
                      <FileText className="mr-2 h-4 w-4" />
                      {doc.split('/').pop()}
                      <ExternalLink className="ml-auto h-3 w-3" />
                    </Button>
                  ))}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="details" className="space-y-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle className="text-base">Error Details</CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(JSON.stringify(error.error, null, 2), 'Error details')}
                  >
                    {copiedSection === 'Error details' ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </CardHeader>
                <CardContent>
                  <pre className="text-xs bg-muted p-3 rounded-lg overflow-x-auto">
                    <code>{JSON.stringify(error.error, null, 2)}</code>
                  </pre>
                </CardContent>
              </Card>

              {error.context.input && (
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle className="text-base">Input Data</CardTitle>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(JSON.stringify(error.context.input, null, 2), 'Input data')}
                    >
                      {copiedSection === 'Input data' ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  </CardHeader>
                  <CardContent>
                    <pre className="text-xs bg-muted p-3 rounded-lg overflow-x-auto">
                      <code>{JSON.stringify(error.context.input, null, 2)}</code>
                    </pre>
                  </CardContent>
                </Card>
              )}

              {error.error.stackTrace && (
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle className="text-base">Stack Trace</CardTitle>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(error.error.stackTrace!, 'Stack trace')}
                    >
                      {copiedSection === 'Stack trace' ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-48">
                      <pre className="text-xs bg-muted p-3 rounded-lg">
                        <code>{error.error.stackTrace}</code>
                      </pre>
                    </ScrollArea>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="history" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Error Pattern Analysis</CardTitle>
                  <CardDescription>
                    This error has occurred multiple times
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertTitle>Pattern Detected</AlertTitle>
                      <AlertDescription>
                        This error tends to occur during high API usage periods. Consider implementing rate limiting or request queuing.
                      </AlertDescription>
                    </Alert>
                    
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">Related Errors</h4>
                      {error.troubleshooting.relatedErrors.map((relatedError, index) => (
                        <div key={index} className="flex items-center justify-between p-2 rounded-lg border">
                          <span className="text-sm">{relatedError}</span>
                          <Button variant="ghost" size="sm">
                            View <ChevronRight className="ml-1 h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </ScrollArea>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}

// Missing imports
import { HelpCircle, TrendingUp } from 'lucide-react'