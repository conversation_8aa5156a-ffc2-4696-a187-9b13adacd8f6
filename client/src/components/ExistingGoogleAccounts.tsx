import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { AlertCircle, RefreshCw, ExternalLink, ChevronDown } from 'lucide-react';
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";

interface SpreadsheetInfo {
  id: string;
  name: string;
  url: string;
  sheets: Array<{
    title: string;
    rowCount: number;
    columnCount: number;
  }>;
}

interface DriveFileInfo {
  id: string;
  name: string;
  mimeType: string;
  size?: string;
  modifiedTime: string;
  webViewLink: string;
}

interface DriveFolderInfo {
  id: string;
  name: string;
  modifiedTime: string;
}

interface ExistingGoogleAccountsProps {
  onAccountSelect: (account: any) => void;
  onSpreadsheetSelect?: (spreadsheet: SpreadsheetInfo) => void;
  onSheetSelect?: (sheetName: string) => void;
  onFolderSelect?: (folder: DriveFolderInfo) => void;
  onFileSelect?: (file: DriveFileInfo) => void;
  onCalendarSelect?: (calendar: any) => void;
  selectedAccount?: any;
  selectedAccountId?: string;
  selectedSpreadsheet?: SpreadsheetInfo;
  selectedSheet?: string;
  selectedFolder?: DriveFolderInfo;
  trigger?: 'gmail' | 'google-sheets' | 'google-drive' | 'google-calendar';
}

export function ExistingGoogleAccounts({ 
  onAccountSelect, 
  onSpreadsheetSelect,
  onSheetSelect,
  onFolderSelect,
  onFileSelect,
  onCalendarSelect,
  selectedAccount,
  selectedAccountId,
  selectedSpreadsheet,
  selectedSheet,
  selectedFolder,
  trigger = 'gmail'
}: ExistingGoogleAccountsProps) {
  console.log('ExistingGoogleAccounts component received props:', { 
    selectedAccount, 
    selectedAccountId,
    trigger, 
    hasSelectedAccount: !!selectedAccount,
    hasSelectedAccountId: !!selectedAccountId 
  });
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Google Drive specific state
  const [drivePermissions, setDrivePermissions] = useState<any>(null);
  const [driveFolders, setDriveFolders] = useState<any>(null);
  const [driveFiles, setDriveFiles] = useState<any>(null);
  const [checkingDrivePermissions, setCheckingDrivePermissions] = useState(false);
  const [loadingDriveFolders, setLoadingDriveFolders] = useState(false);
  const [loadingDriveFiles, setLoadingDriveFiles] = useState(false);
  const [drivePermissionsError, setDrivePermissionsError] = useState<any>(null);
  const [driveFoldersError, setDriveFoldersError] = useState<any>(null);
  const [driveFilesError, setDriveFilesError] = useState<any>(null);

  // Google Calendar specific state
  const [calendarPermissions, setCalendarPermissions] = useState<any>(null);
  const [calendars, setCalendars] = useState<any>(null);
  const [checkingCalendarPermissions, setCheckingCalendarPermissions] = useState(false);
  const [loadingCalendars, setLoadingCalendars] = useState(false);
  const [calendarPermissionsError, setCalendarPermissionsError] = useState<any>(null);
  const [calendarsError, setCalendarsError] = useState<any>(null);

  const { data: allCredentials = [] } = useQuery({
    queryKey: ['/api/credentials'],
    queryFn: () => apiRequest('GET', '/api/credentials').then(res => res.json())
  });

  // Filter credentials by service type - simplified approach
  const credentials = allCredentials.filter((cred: any) => {
    return cred.service === 'google' && cred.type === 'oauth' && cred.isActive;
  }).map((cred: any) => ({
    ...cred,
    // Extract email from credential name and create clean display name
    name: (() => {
      // Extract email from various credential name formats
      const emailMatch = cred.name.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
      if (emailMatch) {
        const email = emailMatch[1];
        // Extract name part before @ for display
        const namePart = email.split('@')[0];
        return `${namePart} (${email})`;
      }
      return cred.name || 'Unknown Google Account';
    })()
  }));
  
  // Derive selected account from selectedAccountId if not provided
  const effectiveSelectedAccount = React.useMemo(() => {
    if (selectedAccount) {
      console.log('Using selectedAccount from prop:', selectedAccount);
      return selectedAccount;
    }
    if (selectedAccountId && credentials.length > 0) {
      const found = credentials.find((cred: any) => cred.id.toString() === selectedAccountId.toString());
      console.log('Looking for account by ID:', selectedAccountId, 'Found:', found);
      return found || null;
    }
    return null;
  }, [selectedAccount, selectedAccountId, credentials]);
  
  console.log('effectiveSelectedAccount derived:', {
    effectiveSelectedAccount,
    fromProp: !!selectedAccount,
    fromId: !!selectedAccountId && !selectedAccount,
    credentialsCount: credentials.length
  });

  // State management for different Google services
  const shouldShowSpreadsheetSection = trigger === 'google-sheets' && !!effectiveSelectedAccount;
  const shouldShowDriveSection = trigger === 'google-drive' && !!effectiveSelectedAccount;
  const shouldShowCalendarSection = trigger === 'google-calendar' && !!effectiveSelectedAccount;

  // No auto-selection - user must explicitly select an account

  // Auto-refresh credentials periodically to detect new authentications
  React.useEffect(() => {
    const interval = setInterval(() => {
      queryClient.invalidateQueries({ queryKey: ['/api/credentials'] });
    }, 5000); // Check every 5 seconds

    return () => clearInterval(interval);
  }, [queryClient]);

  // Direct API calls for permissions and spreadsheets
  const [permissions, setPermissions] = React.useState<any>(null);
  const [checkingPermissions, setCheckingPermissions] = React.useState(false);
  const [permissionsError, setPermissionsError] = React.useState<any>(null);



  // Re-authentication mutation
  const reauthMutation = useMutation({
    mutationFn: (automationId: string) => 
      apiRequest('GET', `/api/auth/google/start?automationId=${automationId}&reauth=true`).then(res => res.json()),
    onSuccess: (data) => {
      // Open OAuth popup
      const popup = window.open(data.authUrl, 'google-oauth', 'width=500,height=600');
      
      // Listen for OAuth completion
      const messageHandler = (event: MessageEvent) => {
        if (event.origin !== window.location.origin) return;
        
        if (event.data.type === 'oauth-success') {
          popup?.close();
          window.removeEventListener('message', messageHandler);
          
          // Refresh credentials and permissions (unified approach)
          queryClient.invalidateQueries({ queryKey: ['/api/credentials'] });
          queryClient.invalidateQueries({ queryKey: ['/api/google/check-permissions'] });
          queryClient.invalidateQueries({ queryKey: ['/api/google/list-spreadsheets'] });
          
          toast({
            title: "Authentication Updated",
            description: "Google Sheets access has been granted. You can now select spreadsheets.",
          });
        }
      };
      
      window.addEventListener('message', messageHandler);
    },
    onError: (error: any) => {
      toast({
        title: "Authentication Failed",
        description: error.message || "Failed to update Google authentication",
        variant: "destructive"
      });
    }
  });

  const googleCredentials = credentials;

  // Determine which account to use for spreadsheets - only if explicitly selected
  const accountForSpreadsheets = selectedAccount;

  const [spreadsheets, setSpreadsheets] = React.useState<any>(null);
  const [loadingSpreadsheets, setLoadingSpreadsheets] = React.useState(false);
  const [spreadsheetsError, setSpreadsheetsError] = React.useState<any>(null);

  // Enhanced API calls with better debugging
  const checkPermissionsAndListSpreadsheets = async (accountId: number) => {
    if (!accountId || (trigger !== 'google-sheets' && trigger !== 'google-drive')) {
      console.log('Skipping API calls - accountId:', accountId, 'trigger:', trigger);
      return;
    }
    
    console.log('=== Starting API calls for credential ID:', accountId, '===');
    
    // Reset all states first
    setPermissions(null);
    setSpreadsheets(null);
    setPermissionsError(null);
    setSpreadsheetsError(null);
    
    // Check permissions
    setCheckingPermissions(true);
    try {
      console.log('Making permissions API call...');
      const permissionsResponse = await apiRequest('POST', '/api/google/check-permissions', {
        credentialId: accountId
      });
      const permissionsResult = await permissionsResponse.json();
      console.log('Permissions result:', permissionsResult);
      setPermissions(permissionsResult);
      
      // If permissions are good, get spreadsheets
      if (permissionsResult.hasSheetsPermissions) {
        setLoadingSpreadsheets(true);
        try {
          console.log('Making spreadsheets API call...');
          const spreadsheetsResponse = await apiRequest('POST', '/api/google/list-spreadsheets', {
            credentialId: accountId
          });
          const spreadsheetsResult = await spreadsheetsResponse.json();
          console.log('Spreadsheets result:', spreadsheetsResult);
          setSpreadsheets(spreadsheetsResult);
        } catch (error: any) {
          console.error('Spreadsheets API error:', error);
          setSpreadsheetsError(error);
        } finally {
          setLoadingSpreadsheets(false);
        }
      } else {
        console.log('No sheets permissions found');
      }
    } catch (error: any) {
      console.error('Permissions API error:', error);
      setPermissionsError(error);
    } finally {
      setCheckingPermissions(false);
    }
  };

  // Google Drive functions
  const checkDrivePermissionsAndListItems = async (accountId: number) => {
    console.log('=== Starting Google Drive API calls for credential ID:', accountId, '===');
    setCheckingDrivePermissions(true);
    setDrivePermissionsError(null);
    setDriveFoldersError(null);
    setDriveFilesError(null);

    try {
      console.log('Making Drive permissions API call...');
      // Check permissions first
      const permissionsResponse = await apiRequest('POST', '/api/google-drive/check-permissions', {
        credentialId: accountId
      });
      const permissionsResult = await permissionsResponse.json();
      console.log('Drive permissions result:', permissionsResult);
      setDrivePermissions(permissionsResult);

      if (permissionsResult.hasDrivePermissions) {
        console.log('Drive permissions found, fetching folders and files...');
        setLoadingDriveFolders(true);
        setLoadingDriveFiles(true);

        try {
          // Fetch root folders
          const foldersResponse = await apiRequest('POST', '/api/google-drive/list-folders', {
            credentialId: accountId
          });
          const foldersResult = await foldersResponse.json();
          console.log('Drive folders result:', foldersResult);
          setDriveFolders(foldersResult);
        } catch (error: any) {
          console.error('Drive folders API error:', error);
          setDriveFoldersError(error);
        } finally {
          setLoadingDriveFolders(false);
        }

        try {
          // Fetch recent files
          const filesResponse = await apiRequest('POST', '/api/google-drive/list-files', {
            credentialId: accountId,
            pageSize: 20
          });
          const filesResult = await filesResponse.json();
          console.log('Drive files result:', filesResult);
          setDriveFiles(filesResult);
        } catch (error: any) {
          console.error('Drive files API error:', error);
          setDriveFilesError(error);
        } finally {
          setLoadingDriveFiles(false);
        }
      } else {
        console.log('No Drive permissions found');
      }
    } catch (error: any) {
      console.error('Drive permissions API error:', error);
      setDrivePermissionsError(error);
    } finally {
      setCheckingDrivePermissions(false);
    }
  };

  // Google Calendar functions
  const checkCalendarPermissionsAndListCalendars = async (accountId: number) => {
    console.log('=== Starting Google Calendar API calls for credential ID:', accountId, '===');
    setCheckingCalendarPermissions(true);
    setCalendarPermissionsError(null);
    setCalendarsError(null);

    try {
      console.log('Making Calendar permissions API call...');
      // Check permissions first
      const permissionsResponse = await apiRequest('POST', '/api/google-calendar/check-permissions', {
        credentialId: accountId
      });
      const permissionsResult = await permissionsResponse.json();
      console.log('Calendar permissions result:', permissionsResult);
      setCalendarPermissions(permissionsResult);

      if (permissionsResult.hasCalendarPermissions) {
        console.log('Calendar permissions found, fetching calendars...');
        setLoadingCalendars(true);

        try {
          // Fetch calendars
          const calendarsResponse = await apiRequest('POST', '/api/google-calendar/list-calendars', {
            credentialId: accountId
          });
          const calendarsResult = await calendarsResponse.json();
          console.log('Calendar list result:', calendarsResult);
          setCalendars(calendarsResult);
        } catch (error: any) {
          console.error('Calendar list API error:', error);
          setCalendarsError(error);
        } finally {
          setLoadingCalendars(false);
        }
      } else {
        console.log('No Calendar permissions found');
      }
    } catch (error: any) {
      console.error('Calendar permissions API error:', error);
      setCalendarPermissionsError(error);
    } finally {
      setCheckingCalendarPermissions(false);
    }
  };

  // Simplified account selection logic
  useEffect(() => {
    console.log('=== ExistingGoogleAccounts useEffect triggered ===');
    console.log('effectiveSelectedAccount:', effectiveSelectedAccount);
    console.log('trigger:', trigger);
    console.log('effectiveSelectedAccount?.id:', effectiveSelectedAccount?.id);
    
    if (effectiveSelectedAccount?.id && (trigger === 'google-sheets' || trigger === 'google-drive' || trigger === 'google-calendar')) {
      console.log('Conditions met - Triggering API calls for selected account:', effectiveSelectedAccount.id);
      if (trigger === 'google-sheets') {
        console.log('Calling checkPermissionsAndListSpreadsheets...');
        checkPermissionsAndListSpreadsheets(effectiveSelectedAccount.id);
      } else if (trigger === 'google-drive') {
        console.log('Calling checkDrivePermissionsAndListItems...');
        checkDrivePermissionsAndListItems(effectiveSelectedAccount.id);
      } else if (trigger === 'google-calendar') {
        console.log('Calling checkCalendarPermissionsAndListCalendars...');
        checkCalendarPermissionsAndListCalendars(effectiveSelectedAccount.id);
      }
    } else {
      console.log('Resetting state - no account selected or wrong trigger');
      // Reset all state when no account selected
      setPermissions(null);
      setSpreadsheets(null);
      setPermissionsError(null);
      setSpreadsheetsError(null);
      setDrivePermissions(null);
      setDriveFolders(null);
      setDriveFiles(null);
      setDrivePermissionsError(null);
      setDriveFoldersError(null);
      setDriveFilesError(null);
    }
  }, [effectiveSelectedAccount?.id, trigger]);

  // Simplified account selection handler
  const handleAccountSelect = (account: any) => {
    console.log('Account selected:', account);
    onAccountSelect(account);
  };
  
  console.log('Component render - selectedAccount:', !!selectedAccount, 'trigger:', trigger);

  // Debug Google Sheets integration state
  useEffect(() => {
    if (trigger === 'google-sheets') {
      console.log('Google Sheets state:', {
        hasAccount: !!effectiveSelectedAccount,
        accountId: effectiveSelectedAccount?.id,
        hasPermissions: permissions?.hasSheetsPermissions,
        spreadsheetCount: spreadsheets?.spreadsheets?.length || 0,
        loading: checkingPermissions || loadingSpreadsheets,
        errors: !!permissionsError || !!spreadsheetsError
      });
    }
  }, [trigger, effectiveSelectedAccount, permissions, spreadsheets, checkingPermissions, loadingSpreadsheets, permissionsError, spreadsheetsError]);

  // Debug Google Drive integration state
  useEffect(() => {
    if (trigger === 'google-drive') {
      console.log('Google Drive state:', {
        hasAccount: !!effectiveSelectedAccount,
        accountId: effectiveSelectedAccount?.id,
        hasPermissions: drivePermissions?.hasDrivePermissions,
        folderCount: driveFolders?.folders?.length || 0,
        fileCount: driveFiles?.files?.length || 0,
        loading: checkingDrivePermissions || loadingDriveFolders || loadingDriveFiles,
        errors: !!drivePermissionsError || !!driveFoldersError || !!driveFilesError
      });
    }
  }, [trigger, effectiveSelectedAccount, drivePermissions, driveFolders, driveFiles, checkingDrivePermissions, loadingDriveFolders, loadingDriveFiles, drivePermissionsError, driveFoldersError, driveFilesError]);



  const handleReauth = () => {
    // Use a placeholder automation ID - in production this would come from context
    const automationId = 'temp-' + Date.now();
    reauthMutation.mutate(automationId);
  };

  const refetchSpreadsheets = () => {
    if (effectiveSelectedAccount?.id) {
      checkPermissionsAndListSpreadsheets(effectiveSelectedAccount.id);
    }
  };

  const handleSpreadsheetChange = (spreadsheetId: string) => {
    const spreadsheet = spreadsheets?.spreadsheets?.find((s: any) => s.id === spreadsheetId);
    if (spreadsheet && onSpreadsheetSelect) {
      onSpreadsheetSelect(spreadsheet);
    }
  };

  const handleSheetChange = (sheetName: string) => {
    if (onSheetSelect) {
      onSheetSelect(sheetName);
    }
  };

  if (googleCredentials.length === 0) {
    return (
      <div className="p-3 bg-muted/50 rounded-lg border-2 border-dashed">
        <p className="text-sm text-muted-foreground text-center">
          No connected Google accounts found. Please sign in with Google below.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Sign in with Google button - moved to top */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">Authentication Type *</Label>
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-2">
            <input
              type="radio"
              id="google-signin"
              name="auth-type"
              value="google"
              checked={true}
              readOnly
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
            />
            <label htmlFor="google-signin" className="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">
              Google Sign-In
            </label>
          </div>
        </div>
        <p className="text-sm text-muted-foreground">
          Note: Select existing Google account from below or Sign in with a different account
        </p>
      </div>

      <div className="flex items-center gap-2">
        <Button
          onClick={handleReauth}
          disabled={reauthMutation.isPending}
          className="flex items-center gap-2"
          variant="outline"
        >
          <svg className="w-4 h-4" viewBox="0 0 24 24">
            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
          </svg>
          {reauthMutation.isPending ? 'Signing in...' : 'Sign in with Google'}
        </Button>
      </div>

      <div className="space-y-2">
        <Label className="text-sm font-medium">Connected Google Accounts:</Label>
        {googleCredentials.map((account: any) => (
          <div
            key={account.id}
            className={`flex items-center justify-between p-3 border rounded-lg transition-all duration-200 ${
              selectedAccount?.id === account.id
                ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 shadow-sm'
                : 'bg-muted/50 border-muted'
            }`}
          >
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
                <svg className="w-4 h-4" viewBox="0 0 24 24">
                  <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
              </div>
              <div>
                <p className="text-sm font-medium">{account.name}</p>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                    Connected
                  </Badge>
                  {effectiveSelectedAccount?.id === account.id && (
                    <Badge variant="outline" className="bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100 border-blue-200 dark:border-blue-700">
                      Selected
                    </Badge>
                  )}
                </div>
              </div>
            </div>
            <Button
              variant={effectiveSelectedAccount?.id === account.id ? "default" : "ghost"}
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                handleAccountSelect(account);
              }}
              className={effectiveSelectedAccount?.id === account.id ? "bg-blue-600 hover:bg-blue-700" : ""}
            >
              {effectiveSelectedAccount?.id === account.id ? "Selected" : "Select"}
            </Button>
          </div>
        ))}
      </div>

      {/* Google Sheets Spreadsheet Selection - Show when account is selected */}
      {console.log('RENDER CHECK: shouldShowSpreadsheetSection =', shouldShowSpreadsheetSection)}
      {shouldShowSpreadsheetSection && (
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-3">
            <Label className="text-base font-medium">Select Spreadsheet</Label>
          </div>
          {/* Permission Status */}
          {permissions && !permissions.hasSheetsPermissions && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                This Google account needs additional permissions for Google Sheets access.
                Please use the "Sign in with Google" button above to grant access.
              </AlertDescription>
            </Alert>
          )}

          {/* Spreadsheet Selection - Always show */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Select Spreadsheet:</Label>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  if (selectedAccount) {
                    refetchSpreadsheets();
                  }
                }}
                disabled={loadingSpreadsheets}
                className="h-8 px-2"
              >
                <RefreshCw className={`w-4 h-4 ${loadingSpreadsheets ? 'animate-spin' : ''}`} />
              </Button>
            </div>
            {checkingPermissions ? (
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  Checking permissions...
                </div>
                <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              </div>
            ) : !permissions?.hasSheetsPermissions ? (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  This Google account needs Google Sheets permissions. Please use the "Sign in with Google" button above to grant access.
                </AlertDescription>
              </Alert>
            ) : loadingSpreadsheets ? (
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  Loading spreadsheets...
                </div>
                <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              </div>
            ) : spreadsheetsError ? (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Unable to load spreadsheets. This may be due to missing Google Drive API permissions.
                  Please use the "Sign in with Google" button above to reconnect your account.
                </AlertDescription>
              </Alert>
            ) : spreadsheets?.spreadsheets?.length === 0 ? (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  No spreadsheets found in your Google Drive. Please create a spreadsheet first.
                </AlertDescription>
              </Alert>
            ) : (
              <div className="space-y-2">
                <Select value={selectedSpreadsheet?.id} onValueChange={handleSpreadsheetChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a spreadsheet" />
                  </SelectTrigger>
                  <SelectContent 
                    className="w-full min-w-[320px] max-w-[600px] overflow-y-auto"
                    position="popper"
                    sideOffset={5}
                    alignOffset={0}
                    align="start"
                    avoidCollisions={true}
                    collisionPadding={15}
                    sticky="always"
                  >
                    {spreadsheets?.spreadsheets?.map((sheet: any) => (
                      <SelectItem key={sheet.id} value={sheet.id}>
                        <div className="flex items-center gap-2 max-w-full">
                          <span className="truncate flex-1">{sheet.name}</span>
                          <ExternalLink className="w-3 h-3 text-muted-foreground flex-shrink-0" />
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          {/* Sheet Selection */}
          {selectedSpreadsheet?.sheets && selectedSpreadsheet.sheets.length > 0 && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">Select Sheet:</Label>
              <Select value={selectedSheet} onValueChange={handleSheetChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a sheet" />
                </SelectTrigger>
                <SelectContent 
                  className="w-full min-w-[320px] max-w-[600px] overflow-y-auto"
                  position="popper"
                  sideOffset={5}
                  alignOffset={0}
                  align="start"
                  avoidCollisions={true}
                  collisionPadding={15}
                  sticky="always"
                >
                  {selectedSpreadsheet.sheets.map((sheet: any) => (
                    <SelectItem key={sheet.title} value={sheet.title}>
                      <div className="flex items-center justify-between w-full">
                        <span className="truncate flex-1">{sheet.title}</span>
                        <span className="text-xs text-muted-foreground ml-2 flex-shrink-0">
                          {sheet.rowCount} rows × {sheet.columnCount} columns
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>
      )}

      {/* Google Drive selection section */}
      {shouldShowDriveSection && effectiveSelectedAccount && (
        <div className="border-2 border-blue-200 bg-blue-50 dark:bg-blue-950/20 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <Label className="font-medium">Google Drive Files & Folders</Label>
            <Button
              onClick={() => effectiveSelectedAccount && checkDrivePermissionsAndListItems(effectiveSelectedAccount.id)}
              size="sm"
              variant="outline"
              disabled={checkingDrivePermissions || loadingDriveFolders || loadingDriveFiles}
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${(checkingDrivePermissions || loadingDriveFolders || loadingDriveFiles) ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>

          {/* Permission Status */}
          {drivePermissions && !drivePermissions.hasDrivePermissions && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                This Google account needs additional permissions for Google Drive access.
                Please use the "Sign in with Google" button above to grant access.
              </AlertDescription>
            </Alert>
          )}

          {/* Folders Section */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium">Folders:</Label>
              {checkingDrivePermissions ? (
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <RefreshCw className="w-4 h-4 animate-spin" />
                    Checking permissions...
                  </div>
                  <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                </div>
              ) : !drivePermissions?.hasDrivePermissions ? (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    This Google account needs Google Drive permissions. Please use the "Sign in with Google" button above to grant access.
                  </AlertDescription>
                </Alert>
              ) : loadingDriveFolders ? (
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <RefreshCw className="w-4 h-4 animate-spin" />
                    Loading folders...
                  </div>
                  <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                </div>
              ) : driveFoldersError ? (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Unable to load folders. Please check your Google Drive permissions.
                  </AlertDescription>
                </Alert>
              ) : driveFolders?.folders?.length === 0 ? (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    No folders found in your Google Drive root directory.
                  </AlertDescription>
                </Alert>
              ) : (
                <Select value={selectedFolder?.id} onValueChange={(folderId) => {
                  const folder = driveFolders?.folders?.find((f: any) => f.id === folderId);
                  if (folder && onFolderSelect) {
                    onFolderSelect(folder);
                  }
                }}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a folder (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    {driveFolders?.folders?.map((folder: any) => (
                      <SelectItem key={folder.id} value={folder.id}>
                        <div className="flex items-center gap-2">
                          <span className="truncate">{folder.name}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>

            {/* Files Section */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Recent Files (for preview):</Label>
              {loadingDriveFiles ? (
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <RefreshCw className="w-4 h-4 animate-spin" />
                    Loading files...
                  </div>
                  <div className="space-y-1">
                    <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                    <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                    <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                  </div>
                </div>
              ) : driveFilesError ? (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Unable to load files. Please check your Google Drive permissions.
                  </AlertDescription>
                </Alert>
              ) : driveFiles?.files?.length === 0 ? (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    No files found in your Google Drive.
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="max-h-40 overflow-y-auto space-y-1 border rounded-md p-2">
                  {driveFiles?.files?.slice(0, 10).map((file: any) => (
                    <div 
                      key={file.id} 
                      className="flex items-center gap-2 p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded text-xs cursor-pointer"
                      onClick={() => onFileSelect && onFileSelect(file)}
                    >
                      <span className="truncate flex-1">{file.name}</span>
                      <span className="text-muted-foreground text-xs flex-shrink-0">
                        {file.mimeType?.includes('folder') ? '📁' : 
                         file.mimeType?.includes('pdf') ? '📄' :
                         file.mimeType?.includes('image') ? '🖼️' :
                         file.mimeType?.includes('document') ? '📝' :
                         file.mimeType?.includes('spreadsheet') ? '📊' : '📄'}
                      </span>
                    </div>
                  ))}
                  {driveFiles?.files?.length > 10 && (
                    <div className="text-xs text-muted-foreground text-center py-1">
                      ... and {driveFiles.files.length - 10} more files
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Google Calendar selection section */}
      {shouldShowCalendarSection && effectiveSelectedAccount && (
        <div className="border-2 border-purple-200 bg-purple-50 dark:bg-purple-950/20 dark:border-purple-800 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <Label className="font-medium">Google Calendars</Label>
            <Button
              onClick={() => effectiveSelectedAccount && checkCalendarPermissionsAndListCalendars(effectiveSelectedAccount.id)}
              size="sm"
              variant="outline"
              disabled={checkingCalendarPermissions || loadingCalendars}
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${(checkingCalendarPermissions || loadingCalendars) ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>

          {/* Permission Status */}
          {calendarPermissions && !calendarPermissions.hasCalendarPermissions && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                This Google account needs additional permissions for Google Calendar access.
                Please use the "Sign in with Google" button above to grant access.
              </AlertDescription>
            </Alert>
          )}

          {/* Calendars Section */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium">Available Calendars:</Label>
              {checkingCalendarPermissions ? (
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <RefreshCw className="w-4 h-4 animate-spin" />
                    Checking permissions...
                  </div>
                  <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                </div>
              ) : !calendarPermissions?.hasCalendarPermissions ? (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    This Google account needs Google Calendar permissions. Please use the "Sign in with Google" button above to grant access.
                  </AlertDescription>
                </Alert>
              ) : loadingCalendars ? (
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <RefreshCw className="w-4 h-4 animate-spin" />
                    Loading calendars...
                  </div>
                  <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                </div>
              ) : calendarsError ? (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Unable to load calendars. Please check your Google Calendar permissions.
                  </AlertDescription>
                </Alert>
              ) : calendars?.calendars?.length === 0 ? (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    No calendars found in your Google account.
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="max-h-40 overflow-y-auto space-y-1 border rounded-md p-2">
                  {calendars?.calendars?.map((calendar: any) => (
                    <div 
                      key={calendar.id} 
                      className="flex items-center gap-2 p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded text-xs cursor-pointer"
                      onClick={() => onCalendarSelect && onCalendarSelect(calendar)}
                    >
                      <div className="flex items-center gap-2 flex-1">
                        <div 
                          className="w-3 h-3 rounded-full border" 
                          style={{ backgroundColor: calendar.backgroundColor || '#1976d2' }}
                        ></div>
                        <span className="truncate flex-1">{calendar.summary || calendar.name}</span>
                      </div>
                      <span className="text-muted-foreground text-xs flex-shrink-0">
                        {calendar.primary ? '🏠' : calendar.accessRole === 'owner' ? '👑' : '📅'}
                      </span>
                    </div>
                  ))}
                  {calendars?.calendars?.length > 10 && (
                    <div className="text-xs text-muted-foreground text-center py-1">
                      ... and {calendars.calendars.length - 10} more calendars
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}