import { useState, useEffect } from 'react'
import { RefreshCw, Wifi, WifiOff } from 'lucide-react'

// Development tools component for better development experience
export function DevTools() {
  const [isOnline, setIsOnline] = useState(navigator.onLine)
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date())
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(true)

  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Update timestamp every 30 seconds
    const interval = setInterval(() => {
      setLastUpdate(new Date())
    }, 30000)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
      clearInterval(interval)
    }
  }, [])

  // Only show in development mode
  if (!import.meta.env.DEV) return null

  const handleManualRefresh = () => {
    console.log('[DevTools] Manual refresh triggered')
    window.location.reload()
  }

  const toggleAutoRefresh = () => {
    setAutoRefreshEnabled(!autoRefreshEnabled)
    console.log('[DevTools] Auto-refresh', !autoRefreshEnabled ? 'enabled' : 'disabled')
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-background/90 backdrop-blur-sm border rounded-lg p-3 shadow-lg">
      <div className="flex items-center gap-3 text-sm">
        <div className="flex items-center gap-2">
          {isOnline ? (
            <Wifi className="h-4 w-4 text-green-500" />
          ) : (
            <WifiOff className="h-4 w-4 text-red-500" />
          )}
          <span className="text-muted-foreground">
            {isOnline ? 'Online' : 'Offline'}
          </span>
        </div>
        
        <div className="h-4 w-px bg-border" />
        
        <button
          onClick={handleManualRefresh}
          className="flex items-center gap-1 px-2 py-1 rounded hover:bg-accent text-xs"
          title="Manual refresh (Ctrl+Shift+R)"
        >
          <RefreshCw className="h-3 w-3" />
          Refresh
        </button>
        
        <div className="h-4 w-px bg-border" />
        
        <button
          onClick={toggleAutoRefresh}
          className={`px-2 py-1 rounded text-xs ${
            autoRefreshEnabled 
              ? 'bg-green-500/20 text-green-700 dark:text-green-400' 
              : 'bg-gray-500/20 text-gray-700 dark:text-gray-400'
          }`}
        >
          Auto: {autoRefreshEnabled ? 'ON' : 'OFF'}
        </button>
        
        <div className="text-xs text-muted-foreground">
          {lastUpdate.toLocaleTimeString()}
        </div>
      </div>
    </div>
  )
}