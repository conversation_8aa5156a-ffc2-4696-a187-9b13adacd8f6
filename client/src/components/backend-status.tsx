import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, CheckCircle, Database, Server } from 'lucide-react';
import { healthAPI } from '@/lib/api';

export default function BackendStatus() {
  const { data: health, isLoading, error } = useQuery({
    queryKey: ['/api/health'],
    queryFn: async () => {
      const response = await fetch('/api/health');
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return await response.json();
    },
    refetchInterval: 10000, // Check every 10 seconds
    retry: 1,
  });



  const isHealthy = health?.status === 'healthy';
  const isConnected = health?.database === 'connected';

  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2">
          <Server className="h-5 w-5" />
          Backend Status
        </CardTitle>
        <CardDescription>
          Real-time status of your Filorina backend services
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* API Status */}
          <div className="flex items-center justify-between p-3 border rounded-lg">
            <div className="flex items-center gap-2">
              <Server className="h-4 w-4" />
              <span className="font-medium">API Server</span>
            </div>
            {isLoading ? (
              <Badge variant="secondary">Checking...</Badge>
            ) : error ? (
              <Badge variant="destructive" className="flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                Offline
              </Badge>
            ) : (
              <Badge variant={isHealthy ? "default" : "destructive"} className="flex items-center gap-1">
                {isHealthy ? <CheckCircle className="h-3 w-3" /> : <AlertCircle className="h-3 w-3" />}
                {isHealthy ? 'Online' : 'Error'}
              </Badge>
            )}
          </div>

          {/* Database Status */}
          <div className="flex items-center justify-between p-3 border rounded-lg">
            <div className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              <span className="font-medium">Supabase DB</span>
            </div>
            {isLoading ? (
              <Badge variant="secondary">Checking...</Badge>
            ) : error ? (
              <Badge variant="destructive" className="flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                Unknown
              </Badge>
            ) : (
              <Badge variant={isConnected ? "default" : "destructive"} className="flex items-center gap-1">
                {isConnected ? <CheckCircle className="h-3 w-3" /> : <AlertCircle className="h-3 w-3" />}
                {isConnected ? 'Connected' : 'Disconnected'}
              </Badge>
            )}
          </div>
        </div>

        {/* Status Details */}
        {health && (
          <div className="text-sm text-muted-foreground">
            <div className="flex justify-between">
              <span>Last checked:</span>
              <span>{new Date(health.timestamp).toLocaleTimeString()}</span>
            </div>
            {health.error && (
              <div className="mt-2 p-2 bg-destructive/10 border border-destructive/20 rounded text-destructive text-xs">
                <strong>Error:</strong> {health.error}
              </div>
            )}
          </div>
        )}

        {/* Migration Status */}
        <div className="p-3 bg-muted/50 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Database className="h-4 w-4" />
            <span className="font-medium text-sm">Database Schema</span>
          </div>
          <div className="text-xs text-muted-foreground space-y-1">
            <div>✓ Profiles table ready</div>
            <div>✓ Automations table ready</div>
            <div>✓ Credentials table ready</div>
            <div>✓ Team members table ready</div>
            <div>✓ Activity logs table ready</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}