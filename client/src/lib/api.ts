import { apiRequest } from './queryClient';

// Profile API
export const profileAPI = {
  get: (userId: string) => 
    apiRequest('GET', '/api/profile', null, { 'Authorization': `Bearer ${userId}` }),
  
  create: (profileData: any) => 
    apiRequest('POST', '/api/profile', profileData),
  
  update: (id: string, updates: any) => 
    apiRequest('PATCH', `/api/profile/${id}`, updates),
};

// Automation API
export const automationAPI = {
  list: (userId: string) => 
    apiRequest('GET', '/api/automations', null, { 'Authorization': `Bearer ${userId}` }),
  
  get: (id: number) => 
    apiRequest('GET', `/api/automations/${id}`),
  
  create: (automationData: any) => 
    apiRequest('POST', '/api/automations', automationData),
  
  update: (id: number, updates: any) => 
    apiRequest('PATCH', `/api/automations/${id}`, updates),
  
  delete: (id: number, userId: string) => 
    apiRequest('DELETE', `/api/automations/${id}`, null, { 'Authorization': `Bearer ${userId}` }),
};

// Credentials API
export const credentialsAPI = {
  list: (userId: string) => 
    apiRequest('GET', '/api/credentials', null, { 'Authorization': `Bearer ${userId}` }),
  
  create: (credentialData: any) => 
    apiRequest('POST', '/api/credentials', credentialData),
  
  delete: (id: number, userId: string) => 
    apiRequest('DELETE', `/api/credentials/${id}`, null, { 'Authorization': `Bearer ${userId}` }),
};

// Team API
export const teamAPI = {
  getMembers: (teamId: string) => 
    apiRequest('GET', `/api/team/${teamId}/members`),
  
  addMember: (memberData: any) => 
    apiRequest('POST', '/api/team/members', memberData),
  
  updateMember: (id: number, updates: any) => 
    apiRequest('PATCH', `/api/team/members/${id}`, updates),
  
  removeMember: (id: number, userId: string) => 
    apiRequest('DELETE', `/api/team/members/${id}`, null, { 'Authorization': `Bearer ${userId}` }),
};

// Activity Logs API
export const activityLogsAPI = {
  getUserLogs: (userId: string, limit?: number) => 
    apiRequest('GET', `/api/activity-logs${limit ? `?limit=${limit}` : ''}`, null, { 'Authorization': `Bearer ${userId}` }),
};

// Health Check API
export const healthAPI = {
  check: async () => {
    const response = await fetch('/api/health');
    if (!response.ok) {
      throw new Error(`Health check failed: ${response.status}`);
    }
    return await response.json();
  },
  checkSimple: () => apiRequest('GET', '/health'),
};