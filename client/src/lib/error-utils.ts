import { useLocation } from 'wouter'

// Error types for better categorization
export type ErrorType = 
  | 'network'
  | 'authentication' 
  | 'authorization'
  | 'validation'
  | 'server'
  | 'client'
  | 'unknown'

export interface AppError extends Error {
  type?: ErrorType
  statusCode?: number
  details?: any
}

// Create a custom error class
export class AppError extends Error {
  type: ErrorType
  statusCode?: number
  details?: any

  constructor(
    message: string, 
    type: ErrorType = 'unknown', 
    statusCode?: number, 
    details?: any
  ) {
    super(message)
    this.name = 'AppError'
    this.type = type
    this.statusCode = statusCode
    this.details = details
  }
}

// Error handling utilities
export const errorUtils = {
  // Navigate to error page with error details
  navigateToError: (setLocation: (path: string) => void, error?: Error) => {
    if (error) {
      // Store error in sessionStorage for the error page to access
      sessionStorage.setItem('app_error', JSON.stringify({
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      }))
    }
    setLocation('/error')
  },

  // Navigate to 404 page
  navigateTo404: (setLocation: (path: string) => void) => {
    setLocation('/404')
  },

  // Handle async errors gracefully
  handleAsyncError: async <T>(
    operation: () => Promise<T>,
    onError?: (error: Error) => void
  ): Promise<T | null> => {
    try {
      return await operation()
    } catch (error) {
      const appError = error instanceof Error ? error : new Error(String(error))
      
      if (onError) {
        onError(appError)
      } else {
        console.error('Async operation failed:', appError)
      }
      
      return null
    }
  },

  // Handle API errors
  handleApiError: (error: any): AppError => {
    if (error?.response?.status) {
      const statusCode = error.response.status
      let type: ErrorType = 'server'
      let message = 'An error occurred'

      switch (statusCode) {
        case 400:
          type = 'validation'
          message = 'Invalid request'
          break
        case 401:
          type = 'authentication'
          message = 'Authentication required'
          break
        case 403:
          type = 'authorization'
          message = 'Access denied'
          break
        case 404:
          type = 'client'
          message = 'Resource not found'
          break
        case 500:
        case 502:
        case 503:
          type = 'server'
          message = 'Server error'
          break
        default:
          type = 'unknown'
          message = error.response.data?.message || 'Unknown error'
      }

      return new AppError(
        message,
        type,
        statusCode,
        error.response.data
      )
    }

    if (error?.code === 'NETWORK_ERROR') {
      return new AppError(
        'Network connection failed',
        'network',
        undefined,
        error
      )
    }

    return new AppError(
      error?.message || 'An unexpected error occurred',
      'unknown',
      undefined,
      error
    )
  }
}

// Hook for error handling in components
export function useErrorHandling() {
  const [, setLocation] = useLocation()

  const handleError = (error: Error) => {
    errorUtils.navigateToError(setLocation, error)
  }

  const handle404 = () => {
    errorUtils.navigateTo404(setLocation)
  }

  const handleAsyncError = <T>(operation: () => Promise<T>) => {
    return errorUtils.handleAsyncError(operation, handleError)
  }

  return {
    handleError,
    handle404,
    handleAsyncError,
    navigateToError: (error?: Error) => errorUtils.navigateToError(setLocation, error),
    navigateTo404: () => errorUtils.navigateTo404(setLocation)
  }
}