// Development-only file change detection and auto-refresh system
let lastFileChecksum = ''
let refreshInterval: NodeJS.Timeout | null = null
let isRefreshing = false

interface RefreshConfig {
  enabled: boolean
  interval: number
  endpoint: string
}

const config: RefreshConfig = {
  enabled: false, // Temporarily disabled to stop excessive requests
  interval: 5000, // Check every 5 seconds (reduced frequency)
  endpoint: '/api/dev/file-changes'
}

// Check for file changes by monitoring the health endpoint
async function checkForFileChanges(): Promise<boolean> {
  if (!config.enabled || isRefreshing) return false
  
  try {
    const response = await fetch('/api/health', { 
      method: 'GET',
      cache: 'no-cache',
      headers: {
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    })
    
    if (!response.ok) return false
    
    const data = await response.text()
    const currentChecksum = btoa(data).slice(0, 10) // Simple checksum
    
    if (lastFileChecksum && lastFileChecksum !== currentChecksum) {
      console.log('[Dev Auto-Refresh] File changes detected, refreshing...')
      return true
    }
    
    lastFileChecksum = currentChecksum
    return false
  } catch (error) {
    console.warn('[Dev Auto-Refresh] Error checking for changes:', error)
    return false
  }
}

function performRefresh() {
  if (isRefreshing) return
  
  isRefreshing = true
  console.log('[Dev Auto-Refresh] Reloading page with latest changes...')
  
  // Add a slight delay to ensure all network requests complete
  setTimeout(() => {
    window.location.reload()
  }, 100)
}

export function startAutoRefresh() {
  if (!config.enabled || refreshInterval) return

  console.log('[Dev Auto-Refresh] Starting file change detection system')
  
  // Initialize checksum
  checkForFileChanges()
  
  refreshInterval = setInterval(async () => {
    if (await checkForFileChanges()) {
      performRefresh()
    }
  }, config.interval)

  // Listen for tab visibility changes to force check
  document.addEventListener('visibilitychange', () => {
    if (!document.hidden && config.enabled) {
      setTimeout(checkForFileChanges, 500)
    }
  })

  // Listen for window focus to force check
  window.addEventListener('focus', () => {
    if (config.enabled) {
      setTimeout(checkForFileChanges, 300)
    }
  })

  // Force refresh on certain key combinations
  window.addEventListener('keydown', (e) => {
    if (config.enabled && ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'R')) {
      e.preventDefault()
      performRefresh()
    }
  })
}

export function stopAutoRefresh() {
  if (refreshInterval) {
    clearInterval(refreshInterval)
    refreshInterval = null
    console.log('[Dev Auto-Refresh] Stopped file change detection')
  }
}

// Enhanced error boundary for development
if (config.enabled) {
  window.addEventListener('error', (e) => {
    if (e.message.includes('Loading chunk') || e.message.includes('Loading CSS chunk')) {
      console.log('[Dev Auto-Refresh] Chunk loading error detected, refreshing...')
      performRefresh()
    }
  })
}