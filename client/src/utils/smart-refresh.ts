// Smart refresh system that only triggers on actual file changes
let isEnabled = false
let lastModified = ''

interface SmartRefreshConfig {
  checkInterval: number
  debounceDelay: number
}

const config: SmartRefreshConfig = {
  checkInterval: 12000, // Check every 12 seconds (less frequent)
  debounceDelay: 1500   // Wait 1.5 seconds before refreshing
}

// Check if files have been modified by monitoring server timestamp
async function checkServerTimestamp(): Promise<boolean> {
  try {
    const response = await fetch('/api/health', {
      cache: 'no-cache',
      headers: { 'Cache-Control': 'no-cache' }
    })
    
    if (!response.ok) return false
    
    const serverTime = response.headers.get('Date') || ''
    
    if (lastModified && serverTime !== lastModified) {
      console.log('[Smart Refresh] Server restart detected')
      return true
    }
    
    lastModified = serverTime
    return false
  } catch {
    return false
  }
}

let refreshTimeout: NodeJS.Timeout | null = null

function scheduleRefresh() {
  if (refreshTimeout) clearTimeout(refreshTimeout)
  
  refreshTimeout = setTimeout(() => {
    console.log('[Smart Refresh] Refreshing for latest changes...')
    window.location.reload()
  }, config.debounceDelay)
}

export function enableSmartRefresh() {
  if (isEnabled || !import.meta.env.DEV) return
  
  isEnabled = true
  console.log('[Smart Refresh] Enabled smart refresh system')
  
  // Check for changes periodically
  const interval = setInterval(async () => {
    if (await checkServerTimestamp()) {
      scheduleRefresh()
    }
  }, config.checkInterval)
  
  // Manual refresh shortcut (Ctrl/Cmd + R)
  const handleKeydown = (e: KeyboardEvent) => {
    if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
      e.preventDefault()
      window.location.reload()
    }
  }
  
  document.addEventListener('keydown', handleKeydown)
  
  // Cleanup function
  return () => {
    isEnabled = false
    clearInterval(interval)
    document.removeEventListener('keydown', handleKeydown)
    if (refreshTimeout) clearTimeout(refreshTimeout)
  }
}

export function disableSmartRefresh() {
  isEnabled = false
  if (refreshTimeout) {
    clearTimeout(refreshTimeout)
    refreshTimeout = null
  }
  console.log('[Smart Refresh] Disabled smart refresh system')
}