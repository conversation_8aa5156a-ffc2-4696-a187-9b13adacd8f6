# 🔧 Google OAuth Setup for FlowCraft

## ❌ Current Issue
You're getting `Error 400: invalid_request - Missing required parameter: redirect_uri` because:

1. **Missing Google OAuth credentials** in your `.env` file
2. **Redirect URI not configured** in Google Cloud Console
3. **OAuth application not properly set up**

## 🚀 Step-by-Step Fix

### Step 1: Create Google Cloud Project & OAuth App

1. **Go to [Google Cloud Console](https://console.cloud.google.com/)**
2. **Create a new project** or select existing one
3. **Enable APIs**:
   - Go to "APIs & Services" → "Library"
   - Enable these APIs:
     - Gmail API
     - Google Sheets API
     - Google Drive API
     - Google Calendar API

### Step 2: Create OAuth 2.0 Credentials

1. **Go to "APIs & Services" → "Credentials"**
2. **Click "Create Credentials" → "OAuth 2.0 Client IDs"**
3. **Configure OAuth consent screen** (if not done):
   - Choose "External" user type
   - Fill in app name: "<PERSON><PERSON><PERSON>"
   - Add your email as developer contact
   - Add scopes:
     - `https://www.googleapis.com/auth/gmail.readonly`
     - `https://www.googleapis.com/auth/drive`
     - `https://www.googleapis.com/auth/spreadsheets`
     - `https://www.googleapis.com/auth/userinfo.email`
     - `https://www.googleapis.com/auth/userinfo.profile`

4. **Create OAuth Client ID**:
   - Application type: "Web application"
   - Name: "FlowCraft OAuth Client"
   - **Authorized redirect URIs** (CRITICAL):
     ```
     http://localhost:5000/api/auth/google/callback
     http://127.0.0.1:5000/api/auth/google/callback
     ```

### Step 3: Update Environment Variables

1. **Copy your Client ID and Client Secret** from Google Cloud Console
2. **Update your `.env` file**:

```bash
# Replace these with your actual Google OAuth credentials
GOOGLE_CLIENT_ID=your_actual_google_client_id_here
GOOGLE_CLIENT_SECRET=your_actual_google_client_secret_here
```

**Example of what they should look like:**
```bash
GOOGLE_CLIENT_ID=*********-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-AbCdEfGhIjKlMnOpQrStUvWxYz
```

### Step 4: Restart Your Application

```bash
# Stop the current server (Ctrl+C)
# Then restart:
npm run dev
```

### Step 5: Test OAuth Flow

1. **Go to your FlowCraft application**
2. **Try to create a Gmail or Google Sheets automation**
3. **Click "Connect Google Account"**
4. **You should be redirected to Google's OAuth consent screen**

## 🔍 Troubleshooting

### If you still get "redirect_uri_mismatch":
- Double-check the redirect URI in Google Cloud Console exactly matches:
  `http://localhost:5000/api/auth/google/callback`
- Make sure there are no extra spaces or characters
- Try adding both `localhost` and `127.0.0.1` versions

### If you get "access_denied":
- Make sure your OAuth consent screen is properly configured
- Check that all required scopes are added
- Ensure the app is not in "Testing" mode with restricted users

### If you get "invalid_client":
- Verify your `GOOGLE_CLIENT_ID` and `GOOGLE_CLIENT_SECRET` are correct
- Make sure there are no extra quotes or spaces in the `.env` file

## 📋 Required Redirect URIs for Google Cloud Console

Add these exact URLs to your OAuth client's "Authorized redirect URIs":

```
http://localhost:5000/api/auth/google/callback
http://127.0.0.1:5000/api/auth/google/callback
```

## 🔐 Security Notes

- **Never commit real credentials** to version control
- **Use environment variables** for all sensitive data
- **Restrict OAuth scopes** to only what your app needs
- **Consider using HTTPS** in production

## ✅ Verification Checklist

- [ ] Google Cloud project created
- [ ] Required APIs enabled (Gmail, Sheets, Drive, Calendar)
- [ ] OAuth consent screen configured
- [ ] OAuth 2.0 Client ID created
- [ ] Redirect URIs added to Google Cloud Console
- [ ] `GOOGLE_CLIENT_ID` updated in `.env`
- [ ] `GOOGLE_CLIENT_SECRET` updated in `.env`
- [ ] Application restarted
- [ ] OAuth flow tested

## 🆘 Still Having Issues?

If you're still experiencing problems:

1. **Check the browser console** for detailed error messages
2. **Check your server logs** for OAuth-related errors
3. **Verify your Google Cloud Console settings** match exactly
4. **Try using an incognito/private browser window**
5. **Clear your browser cookies** for localhost

## 📞 Quick Test

Once configured, you can test the OAuth endpoint directly:

```bash
curl "http://localhost:5000/api/auth/google/start?automationId=test"
```

This should return a JSON response with an `authUrl` that you can visit to test the OAuth flow.
