#!/usr/bin/env node

import postgres from 'postgres';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const DATABASE_URL = process.env.DATABASE_URL;

if (!DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is not set');
  process.exit(1);
}

console.log('🧹 FlowCraft Database Maintenance');
console.log('=' .repeat(50));

async function performMaintenance() {
  let client;
  try {
    client = postgres(DATABASE_URL, {
      ssl: 'require',
      max: 1,
      idle_timeout: 60,
      connect_timeout: 30,
      statement_timeout: 60000, // 60 seconds
    });

    console.log('🔍 Starting database maintenance...\n');

    // 1. Clean up dead tuples with VACUUM
    console.log('1️⃣  Cleaning up dead tuples...');
    
    const tables = ['automations', 'credentials', 'profiles', 'team_members', 'activity_logs', 'automation_logs'];
    
    for (const table of tables) {
      try {
        console.log(`   Vacuuming ${table}...`);
        await client.unsafe(`VACUUM ANALYZE ${table}`);
        console.log(`   ✅ ${table} cleaned and analyzed`);
      } catch (error) {
        console.log(`   ⚠️  ${table}: ${error.message}`);
      }
    }

    // 2. Update table statistics
    console.log('\n2️⃣  Updating table statistics...');
    
    try {
      await client`ANALYZE`;
      console.log('   ✅ Database statistics updated');
    } catch (error) {
      console.log(`   ⚠️  Statistics update: ${error.message}`);
    }

    // 3. Check for improvements
    console.log('\n3️⃣  Checking improvements...');
    
    const afterStats = await client`
      SELECT 
        schemaname,
        relname as table_name,
        n_live_tup as live_tuples,
        n_dead_tup as dead_tuples,
        round((n_dead_tup::float / GREATEST(n_live_tup, 1)) * 100, 1) as dead_ratio
      FROM pg_stat_user_tables 
      WHERE schemaname = 'public'
      ORDER BY n_live_tup DESC
    `;
    
    console.log('   Table health after maintenance:');
    afterStats.forEach(stat => {
      const healthIcon = stat.dead_ratio > 20 ? '🔴' : stat.dead_ratio > 10 ? '🟡' : '🟢';
      console.log(`   ${healthIcon} ${stat.table_name}: ${stat.live_tuples} live, ${stat.dead_tuples} dead (${stat.dead_ratio}%)`);
    });

    // 4. Test connection performance
    console.log('\n4️⃣  Testing connection performance...');
    
    const performanceTests = [];
    for (let i = 0; i < 3; i++) {
      const start = Date.now();
      await client`SELECT 1`;
      const end = Date.now();
      performanceTests.push(end - start);
      console.log(`   Test ${i + 1}: ${end - start}ms`);
    }
    
    const avgTime = performanceTests.reduce((a, b) => a + b, 0) / performanceTests.length;
    console.log(`   Average response time: ${avgTime.toFixed(2)}ms`);

    console.log('\n🎉 Database maintenance completed successfully!');
    
  } catch (error) {
    console.error('❌ Database maintenance failed:', error.message);
    return false;
  } finally {
    if (client) {
      await client.end();
    }
  }
  
  return true;
}

// Run maintenance
performMaintenance()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
