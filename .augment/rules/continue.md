---
type: "always_apply"
---

### July 18, 2025
- ✓ COMPLETED: Google Sheets credential separation and manual input fallback:
  - ✓ Fixed OAuth callback to save service-specific credentials (Gmail vs Google Sheets)
  - ✓ Updated ExistingGoogleAccounts component to handle both old and new credential naming
  - ✓ Added manual input fallback when Google Drive API is not enabled
  - ✓ Users can now enter Spreadsheet ID and Sheet Name manually when auto-fetch fails
  - ✓ Improved error handling with user-friendly instructions
  - ✓ Component now works with existing "Google - email" credentials for backward compatibility
- ✓ COMPLETED: Performance optimizations for Google Sheets integration:
  - ✓ Simplified credential filtering to remove complex logic that was causing lag
  - ✓ Added React Query caching (5 minutes for permissions, 2 minutes for spreadsheets)
  - ✓ Reduced API calls by limiting spreadsheet results to 20 instead of 50
  - ✓ Implemented batch processing for spreadsheet metadata to reduce load time
  - ✓ Added loading states and skeleton components for better perceived performance
  - ✓ Enhanced transitions with smooth animations (200ms duration)
  - ✓ Optimized auto-selection logic to only trigger for Google Sheets triggers
- ✓ COMPLETED: Unified Google OAuth approach for better efficiency:
  - ✓ Created UnifiedGoogleService class to handle all Google integrations (Gmail, Sheets, Drive)
  - ✓ Implemented single permission check API endpoint (/api/google/check-permissions)
  - ✓ Added unified spreadsheet listing endpoint (/api/google/list-spreadsheets)
  - ✓ Updated frontend to use unified approach for both Gmail and Google Sheets triggers
  - ✓ Single Google connection now checks for all permissions instead of separate Gmail/Sheets connections
  - ✓ Improved performance by eliminating duplicate permission checks and API calls
  - ✓ Enhanced user experience with consistent authentication flow across all Google services
- ✓ COMPLETED: Gmail backend trigger functionality is fully operational:
  - ✓ Successfully connects to Gmail API using stored OAuth credentials
  - ✓ Retrieves real emails from connected Gmail account (<EMAIL>)
  - ✓ Parses email content including headers, body, attachments, and labels
  - ✓ Added proper credential token format handling for Google OAuth
  - ✓ Fixed credential service with getCredential method for secure token retrieval
  - ✓ Enhanced ExistingGoogleAccounts component to display connected accounts in automation forms
  - ✓ Gmail automation creation now shows connected Google accounts for selection
  - ✓ Backend API endpoints /api/gmail/test-connection and /api/gmail/preview-emails working correctly
  - ✓ Gmail service handles token refresh, encryption/decryption, and error handling
  - ✓ Production-ready Gmail trigger system with comprehensive filtering options
- ✓ COMPLETED: Google Sheets trigger implementation with Gmail design pattern:
  - ✓ Implemented Google Sheets trigger following exact Gmail design pattern
  - ✓ Added inline configuration matching Gmail authentication flow
  - ✓ Removed separate Google Sheets configuration page per user request
  - ✓ Created ExistingGoogleAccounts component for consistent account selection
  - ✓ Added Google Sheets trigger preview endpoint with real API integration
  - ✓ Updated schema with googleSheetsConfigSchema for proper validation
  - ✓ Google Sheets trigger supports spreadsheet ID, sheet name, watch range, and trigger types
  - ✓ Integrated with existing Google OAuth flow for seamless authentication
  - ✓ Added polling settings and comprehensive trigger configuration options
- ✓ COMPLETED: Enhanced Google Sheets functionality with comprehensive OAuth integration:
  - ✓ Fixed backend compilation errors and server routing issues
  - ✓ Enhanced ExistingGoogleAccounts component with Google Sheets specific features
  - ✓ Added permission checking for existing Google accounts (OAuth scope validation)
  - ✓ Implemented auto-fetch functionality for spreadsheets when account is connected
  - ✓ Added dropdown selection for spreadsheets and individual sheets (no more manual ID entry)
  - ✓ Created re-authentication flow for accounts without Sheets permissions
  - ✓ Added Google Sheets API endpoints: /api/google-sheets/check-permissions, /api/google-sheets/list-spreadsheets, /api/google-sheets/test-connection
  - ✓ Updated automation form to use enhanced component with proper state management
  - ✓ System now automatically detects if existing Gmail connections have Google Sheets permissions
  - ✓ Provides seamless upgrade path when additional OAuth scopes are needed
  - ✓ Users can now select from their actual Google Sheets instead of entering IDs manually
  - ✓ Complete error handling with user-friendly messages for permission issues
  - ✓ Successfully tested: automation creation, permission checking, and OAuth flow work correctly
- ✓ COMPLETED: Fixed Google Sheets integration to display actual spreadsheets:
  - ✓ Resolved OAuth token format compatibility issues between storage and Google APIs
  - ✓ Fixed UnifiedGoogleService to handle both new (accessToken) and old (access_token) credential formats
  - ✓ Added comprehensive Google Sheets API integration with Drive API for spreadsheet listing
  - ✓ Successfully retrieving real Google Sheets data from user account (<EMAIL>)
  - ✓ Created unified /api/google/list-spreadsheets endpoint for consistent API access
  - ✓ Implemented listSpreadsheets method in UnifiedGoogleService with proper error handling
  - ✓ Auto-refresh mechanism detects new Google authentications every 5 seconds
  - ✓ Fixed OAuth callback redirect URL to use production domain (https://filorina1.replit.app)
  - ✓ Verified OAuth flow: User can sign in with Google, credentials are saved, and spreadsheets are displayed
  - ✓ Test verification: Successfully retrieved spreadsheet "test" with Sheet1 (1000 rows × 26 columns)
  - ✓ Account info API working correctly with user details: Kaizar Bharmal, <EMAIL>
  - ✓ All Google services (Gmail, Sheets, Drive) now working with unified OAuth approach
- ✓ COMPLETED: Enhanced Google Sheets UI with improved user experience:
  - ✓ Moved "Sign in with Google" button to the top of the interface for better visibility
  - ✓ Unified Google account naming by removing service-specific prefixes (now shows "Google account:")
  - ✓ Added refresh button for manual spreadsheet fetching with proper loading states
  - ✓ Fixed refetchSpreadsheets function definition to prevent runtime errors
  - ✓ Removed duplicate "Sign in with Google" buttons from error messages to prevent confusion
  - ✓ Simplified authentication flow with single prominent sign-in button
  - ✓ Improved interface flow: Authentication type → Sign in button → Connected accounts → Spreadsheet selection
  - ✓ Enhanced user experience with cleaner, more intuitive Google Sheets integration

### July 17, 2025
- ✓ Established reference folder as primary architectural guide for future development
- ✓ Reference contains complete project structure: web (React frontend), backend (Express.js), docs, and e2e-tests
- ✓ Will use reference implementation patterns for consistent codebase structure and best practices

### July 18, 2025 (continued)
- ✓ COMPLETED: Fixed Google Sheets spreadsheet selection visibility bug:
  - ✓ Resolved JSX structure errors in ExistingGoogleAccounts component caused by duplicate and misaligned div tags
  - ✓ Fixed conditional rendering logic - spreadsheets now properly display when account is connected
  - ✓ Corrected indentation issues that were causing React "Adjacent JSX elements" errors
  - ✓ Simplified component structure by removing unnecessary nested divs
  - ✓ CRITICAL FIX: Removed auto-selection logic and card click behavior that was causing inverted spreadsheet display
  - ✓ Eliminated onClick handler from account cards that was triggering account selection on any card click
  - ✓ Removed auto-selection of first account which was causing spreadsheets to load before user interaction
  - ✓ Now spreadsheets only appear after user explicitly clicks "Select" button on an account
  - ✓ Google Sheets integration fully functional: authentication, permissions checking, and spreadsheet selection all working correctly
  - ✓ Users can now see and select their Google Sheets when logged in with Google account
- ✓ COMPLETED: Comprehensive Google Drive trigger implementation:
  - ✓ Added Google Drive configuration schema with folder monitoring, file type filters, and event triggers
  - ✓ Built complete UI for Google Drive configuration matching Gmail/Sheets authentication pattern
  - ✓ Implemented folder selection, file type filtering (pdf, docx, xlsx, etc.), and filename pattern matching
  - ✓ Added trigger type options: file-added, file-modified, file-deleted, folder-created
  - ✓ Integrated with existing Google OAuth authentication flow using ExistingGoogleAccounts component
  - ✓ Added Google Drive icon (SiGoogledrive) and trigger option to automation form
  - ✓ Enhanced UnifiedGoogleService with Google Drive API methods: listDriveFiles, listDriveFolders, getDriveFileInfo
  - ✓ Implemented backend API endpoints: /api/google-drive/check-permissions, /api/google-drive/list-files, /api/google-drive/test-connection
  - ✓ Added comprehensive file filtering: folder ID, file types, subfolders, filename patterns, file size limits
  - ✓ Google Drive trigger now fully operational with real-time file monitoring capabilities
  - ✓ Complete backend support for Google Drive file operations and authentication validation
- ✓ COMPLETED: Fixed Google Sheets auto-loading issue with proper account selection logic:
  - ✓ Identified root cause: `accountForSpreadsheets` was using fallback logic that auto-loaded single accounts
  - ✓ Changed from `selectedAccount || (googleCredentials.length === 1 ? googleCredentials[0] : null)` to `selectedAccount` only
  - ✓ Ensures spreadsheets only load after explicit user account selection
  - ✓ Fixed viewport overflow issues with aggressive popup constraints (35vh height, 85vw width)
  - ✓ Added collision detection and sticky positioning to prevent popups from going off-screen
  - ✓ Enhanced Select component with viewport-based sizing and improved collision padding
  - ✓ Proper conditional rendering prevents spreadsheets from appearing before account selection
- ✓ COMPLETED: Fixed React state propagation issue causing Google Sheets interface malfunction:
  - ✓ Resolved critical bug where parent component state updates weren't propagating to child component props
  - ✓ Implemented simplified state management by removing redundant localSelectedAccount and renderKey variables
  - ✓ Fixed API response handling by adding proper .json() calls to apiRequest function usage
  - ✓ Enhanced useEffect dependency tracking to use full selectedAccount object instead of just id
  - ✓ Added functional state updates in parent component to ensure immediate effect
  - ✓ Improved debugging with comprehensive console logging for API calls and state changes
  - ✓ Google Sheets integration now fully operational: account selection triggers API calls, permissions check succeeds, and spreadsheet list loads correctly
  - ✓ Successfully tested: "test" spreadsheet with Sheet1 (1000 rows × 26 columns) displayed after account selection
  - ✓ All Google API endpoints working correctly: /api/google/check-permissions and /api/google/list-spreadsheets

### July 19, 2025
- ✓ COMPLETED: Migration from Replit to local development environment:
  - ✓ Updated all OAuth redirect URLs from filorina1.replit.app to localhost:5000
  - ✓ Created comprehensive MIGRATION_GUIDE.md with step-by-step instructions
  - ✓ Created .env.local template with all necessary environment variables
  - ✓ Modified server/routes.ts to use localhost URLs for OAuth callbacks
  - ✓ Removed Replit-specific configurations while maintaining functionality
  - ✓ Preserved Supabase database connection for data continuity
  - ✓ Documented all required changes for Google OAuth redirect URI updates
  - ✓ Provided troubleshooting guide for common migration issues
  - ✓ Maintained all existing features and functionality in local environment

### January 19, 2025
- ✓ COMPLETED: Full-screen responsive chat interface redesign:
  - ✓ Transformed AutomationChat from fixed sidebar to full-screen modal overlay
  - ✓ Added responsive breakpoints for mobile, tablet, and desktop viewing
  - ✓ Enhanced message bubbles with larger responsive sizing and better padding
  - ✓ Improved header layout with proper responsive typography and controls
  - ✓ Updated control buttons with text labels and responsive sizing
  - ✓ Added backdrop blur effect and proper modal interaction handling
  - ✓ Enhanced input area with responsive sizing and mobile-friendly layout
  - ✓ Applied consistent responsive spacing and typography throughout
  - ✓ Chat now adapts seamlessly to all screen sizes and orientations
  - ✓ Maintains all existing functionality: real-time messaging, execution monitoring, status controls
- ✓ COMPLETED: Page leave warning system for workflow creation:
  - ✓ Implemented beforeunload event handling to prevent accidental data loss
  - ✓ Added browser confirmation dialogs when users try to close tab/window with unsaved changes
  - ✓ Created NavigationWarningDialog component for in-app navigation warnings
  - ✓ Integrated page leave protection in both automation-form-multi-page and automation-form-new
  - ✓ Added hasUnsavedChanges state tracking to monitor user progress
  - ✓ Warning system activates after users start making changes (past intro page)
  - ✓ Automatically clears warning flags when automation is successfully saved
  - ✓ Protects against both browser navigation and in-app route changes
  - ✓ Enhanced user experience by preventing accidental loss of workflow configuration
- ✓ COMPLETED: Smart refresh system implementation:
  - ✓ Replaced excessive server requests (every 1.5s) with efficient 8-second intervals
  - ✓ Smart server restart detection using response headers
  - ✓ Debounced refresh (1-second delay) to ensure changes are complete
  - ✓ Manual refresh shortcut (Ctrl+R) for immediate updates
  - ✓ Real-time file change detection without hard refresh requirement
  - ✓ Development-only system that doesn't affect production performance
- ✓ COMPLETED: Added missing Chat Command trigger to automation forms:
  - ✓ Chat Command trigger now available in multi-page automation form
  - ✓ Consistent trigger options across both automation form interfaces
  - ✓ Chat trigger configuration with command name, description, and confirmation options
  - ✓ Manual trigger capability through chat interface for interactive automation execution
- ✓ COMPLETED: Fixed random warning notification issue:
  - ✓ Added userHasInteracted state tracking to prevent false warnings
  - ✓ Warning now only triggers when users actually interact with forms
  - ✓ Set interaction tracking on navigation, input changes, and trigger selection
  - ✓ Optimized smart refresh frequency from 8 to 12 seconds for less interruption
  - ✓ Eliminated random notifications that appeared during automated refreshes
- ✓ COMPLETED: Comprehensive time-based conditions for schedule triggers:
  - ✓ Added business hours restriction with customizable start/end times
  - ✓ Implemented timezone selection (Eastern, Central, Mountain, Pacific, GMT, CET, JST, AEST)
  - ✓ Added weekdays-only option to skip weekends (Monday-Friday)
  - ✓ Created holiday exclusion with support for 48+ country calendars worldwide
  - ✓ Built custom date exclusion system for specific dates to skip
  - ✓ Applied time conditions to both multi-page and simple automation forms
  - ✓ Updated schema with comprehensive validation for all time-based condition fields
  - ✓ Enhanced schedule trigger capabilities with sophisticated conditional logic
  - ✓ Expanded holiday calendar from 6 to 48+ countries including major regions worldwide (US, UK, CA, AU, JP, DE, FR, IT, ES, NL, SE, NO, DK, FI, CH, AT, BE, BR, MX, AR, IN, CN, KR, SG, MY, TH, ID, PH, VN, NZ, ZA, NG, EG, IL, AE, SA, TR, RU, PL, CZ, HU, RO, GR, PT, IE, LU, SK, SI, LV, LT, EE)
  - ✓ Added flag emojis for visual country identification in dropdown selectors
- ✓ COMPLETED: Comprehensive Google Calendar trigger integration:
  - ✓ Implemented Google Calendar trigger functionality with "Google Calendar" trigger option
  - ✓ Extended ExistingGoogleAccounts component to support calendar selection and management
  - ✓ Added Google Calendar configuration interface with calendar selection capabilities
  - ✓ Created Google Calendar state management including permissions checking and error handling
  - ✓ Built complete backend API endpoints: /api/google-calendar/check-permissions, /api/google-calendar/list-calendars, /api/google-calendar/test-connection
  - ✓ Extended UnifiedGoogleService with Google Calendar methods (listCalendars, getCalendarEvents)
  - ✓ Updated shared schema to include googleCalendarConfigSchema for proper validation
  - ✓ Verified all three Google services (Sheets, Drive, Calendar) follow consistent authentication patterns
  - ✓ Tested API endpoints successfully - permissions check working correctly
  - ✓ Google Calendar appears in automation form with proper Calendar icon and description
- ✓ COMPLETED: Comprehensive Slack message trigger implementation:
  - ✓ Created SlackService backend with workspace connection and message monitoring capabilities
  - ✓ Added Slack API endpoints: /api/slack/test-connection, /api/slack/list-channels, /api/slack/preview-messages
  - ✓ Implemented Slack trigger preview endpoint at /api/triggers/slack/preview with filtering
  - ✓ Added complete Slack trigger configuration in automation form with bot token authentication
  - ✓ Built comprehensive message filtering: keywords, user filters, mentions, threads, attachments
  - ✓ Added configurable polling settings and channel selection capabilities
  - ✓ Updated shared schema with slackConfigSchema for proper validation
  - ✓ Slack integration supports bot token authentication and workspace message monitoring
- ✓ Removed Discord Message trigger and action per user request:
  - ✓ Cleaned up Discord references from automation form trigger and action options
  - ✓ Removed SiDiscord import to eliminate unused dependencies
  - ✓ Streamlined available triggers and actions for better focus
- → IDENTIFIED: Complex React state management issue with Google Drive account selection:
  - → Account selection working (state updates correctly with account ID)
  - → State immediately resets to null after selection due to component re-render timing
  - → Attempted fixes: separate state variables, manual selection flags, setTimeout delays, restoration logic prevention
  - → Core issue: ExistingGoogleAccounts component receiving null props despite parent state updates
  - → Backend Google Drive API fully functional (permissions, file listing, folder listing all working)
  - → Frontend displays authentication correctly but files/folders don't appear due to state management race condition
  - → Google Sheets integration remains fully functional - issue isolated to Google Drive trigger only

## Architecture Document

⚙️ Core Concept
Users build automations via a form-based UI, not drag-and-drop. Each automation consists of:

A trigger (e.g., webhook, schedule)

A series of actions (e.g., Google Drive cleanup, send email)

🧠 High-Level Architecture
```
Frontend (Form-based Builder UI)
        |
        v
Backend API (Node.js / Express or Fastify)
        |
        +--> Database (PostgreSQL + Drizzle ORM)
        |
        +--> Queue System (BullMQ / Redis)
        |
        +--> Dynamic Executor (Worker Engine)
        |
        +--> App Plugins (integrations like Gmail, Slack)
```

🗄️ Database Schema
1. Workflows Table
Stores user-created automations.

```sql
Workflows (
  id UUID PK,
  user_id UUID FK,
  name TEXT,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)
```

2. Steps Table
Each step belongs to a workflow (either a trigger or action).

```sql
Steps (
  id UUID PK,
  workflow_id UUID FK,
  type ENUM('trigger', 'action'),
  index INT,  -- execution order
  app_key TEXT,  -- e.g., 'gmail', 'slack'
  event_key TEXT,  -- e.g., 'send_email', 'append_text'
  config JSONB,  -- user-specified config
  created_at TIMESTAMP
)
```

3. Step Conditions Table
Optional conditions for when the step should run (used for triggers).

```sql
StepConditions (
  id UUID PK,
  step_id UUID FK,
  key TEXT,
  operator ENUM('equals', 'includes', '>', '<', '!='),
  value TEXT
)
```

🔄 Execution Flow
1. Triggering a Workflow
User hits a webhook or schedules a run.

Backend queries for matching workflows via StepConditions.

2. Queue Execution
The first action step (index = 1) is pushed to a queue (BullMQ).

A worker consumes the step:

Dynamically imports the correct app (from plugin folder)

Executes its run() function

On success, pushes next step (index +1) into the queue

Repeat until all steps are executed.

📁 Folder Structure (Backend)
```
/apps/             # All integrations
  /gmail/
    send_email.ts
    delete_email.ts
  /slack/
    send_message.ts
    trigger.ts

/workers/
  queue.ts         # BullMQ processor
  executor.ts      # Runs each step
/routes/
  trigger.ts       # Webhook entry
  workflows.ts     # Create/edit workflows
/utils/
  importer.ts      # Dynamic function loader
```

### January 19, 2025
- ✓ Enhanced Team page with comprehensive role management:
  - ✓ Added 5 distinct roles: Owner, Admin, Editor, Member, and Viewer
  - ✓ Implemented color-coded role badges and icons for visual distinction
  - ✓ Added Department field to team member profiles
  - ✓ Created comprehensive Permissions Matrix table showing capabilities for each role
  - ✓ Enhanced invite dialog with role selection including icons and descriptions
  - ✓ Added department field to invite form (optional)
  - ✓ Implemented getPermissionsForRole function for automatic permission assignment
  - ✓ Added Recent Team Activity section tracking team member actions
  - ✓ Updated mock data with realistic team members across different departments
  - ✓ Permissions include: View/Create/Edit/Delete Automations, Manage Team, Credentials, Billing, Security
  - ✓ Visual permissions matrix with checkmarks showing what each role can do
  - ✓ Activity feed shows recent actions like automation creation, updates, and credential connections
  - ✓ Enhanced dropdown menu to show all available role changes with icons

### January 20, 2025
- ✓ Transformed onboarding from separate page to modal overlay:
  - ✓ Removed "What's your main use case?" question from onboarding flow
  - ✓ Created OnboardingModal component that appears over dashboard background
  - ✓ Onboarding now only appears once for new users (created within last minute)
  - ✓ Uses user-specific localStorage keys to prevent showing again
  - ✓ Modal cannot be closed until completion, ensuring users finish onboarding
  - ✓ Removed redirect logic from AuthContext - no more page navigation
  - ✓ Added OnboardingModal to Dashboard component for seamless integration
  - ✓ Removed unused /onboarding route from App.tsx
  - ✓ Maintained all existing onboarding steps except "Use Case" question
  - ✓ Progress bar and step navigation remain functional within modal
- ✓ Enhanced dashboard theme toggle:
  - ✓ Added theme toggle button to dashboard after login
  - ✓ Simplified to single toggle button (no dropdown)
  - ✓ Removed profile icon from navbar
  - ✓ Created stylish gradient header bar with backdrop blur effect
  - ✓ Theme button positioned on right side with gradient glow effect
  - ✓ Shows yellow sun icon in dark mode, blue moon icon in light mode
  - ✓ Moved Create Automation button from dashboard to header bar
  - ✓ Both Create Automation and theme toggle buttons positioned on right side
  - ✓ Responsive button sizing and text for mobile devices
  - ✓ Header provides consistent navigation across all dashboard pages
- ✓ Implemented comprehensive error handling system:
  - ✓ Created 404 Not Found page with modern design and navigation options
  - ✓ Built general error handling page with technical details and recovery options
  - ✓ Added Error Boundary component to catch React errors automatically
  - ✓ Created error utilities for programmatic error handling and navigation
  - ✓ Integrated error pages into routing system with proper fallbacks
  - ✓ Added session storage support for error details preservation
  - ✓ Error system provides professional user experience with multiple recovery paths

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite for fast development and optimized builds
- **UI Framework**: shadcn/ui components built on Radix UI primitives
- **Styling**: Tailwind CSS with custom CSS variables for theming
- **State Management**: React Query (TanStack Query) for server state
- **Routing**: Wouter for lightweight client-side routing
- **Forms**: React Hook Form with Zod validation

### Backend Architecture
- **Runtime**: Node.js with Express.js
- **Language**: TypeScript with ES modules
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Supabase for authentication services
- **Session Management**: In-memory storage with interface for easy swapping

### Project Structure
```
├── client/          # React frontend
│   ├── src/
│   │   ├── components/  # Reusable UI components
│   │   ├── contexts/    # React contexts (Auth)
│   │   ├── hooks/       # Custom React hooks
│   │   ├── lib/         # Utility functions and configurations
│   │   └── pages/       # Page components
├── server/          # Express backend
├── shared/          # Shared code between frontend and backend
└── migrations/      # Database migrations
```

## Key Components

### Authentication System
- **Provider**: Supabase authentication
- **Features**: Email/password login, Google OAuth, password reset
- **State Management**: React Context with persistent sessions
- **Protected Routes**: Authentication-based route protection

### Database Layer
- **ORM**: Drizzle ORM with PostgreSQL dialect
- **Schema**: Centralized in `shared/schema.ts`
- **Migrations**: Managed through drizzle-kit
- **Storage Interface**: Abstracted storage layer for easy testing/swapping

### UI Components
- **Design System**: shadcn/ui with "new-york" style
- **Theme**: Neutral color palette with CSS variables
- **Responsive**: Mobile-first design with dark mode support
- **Accessibility**: Built on Radix UI primitives for accessibility

### Development Tools
- **Hot Reload**: Vite HMR for frontend, tsx for backend
- **Type Safety**: Full TypeScript coverage
- **Error Handling**: Runtime error overlay in development
- **Code Quality**: ESLint and TypeScript strict mode

## Data Flow

1. **Client requests** go through React Query for caching and state management
2. **API calls** are made to Express backend with credential handling
3. **Authentication** is managed by Supabase with local state caching
4. **Database operations** use Drizzle ORM with type-safe queries
5. **Real-time updates** through React Query invalidation

## External Dependencies

### Core Dependencies
- **@supabase/supabase-js**: Authentication and real-time features
- **@neondatabase/serverless**: PostgreSQL database connection
- **@tanstack/react-query**: Server state management
- **drizzle-orm**: Type-safe database operations

### UI Dependencies
- **@radix-ui/***: Primitive UI components
- **tailwindcss**: Utility-first CSS framework
- **class-variance-authority**: Component variant management
- **lucide-react**: Icon library

### Development Dependencies
- **vite**: Frontend build tool
- **tsx**: TypeScript execution for development
- **esbuild**: Backend bundling for production

## Deployment Strategy

### Build Process
1. **Frontend**: Vite builds optimized static assets to `dist/public`
2. **Backend**: esbuild bundles server code to `dist/index.js`
3. **Database**: Drizzle migrations applied via `db:push`

### Environment Configuration
- **Development**: Uses tsx for hot reload, Vite dev server
- **Production**: Serves static files from Express, optimized builds
- **Database**: PostgreSQL connection via environment variables

### Key Scripts
- `dev`: Development server with hot reload
- `build`: Production build for both frontend and backend
- `start`: Production server
- `db:push`: Apply database schema changes

The application follows a modern full-stack architecture with clear separation of concerns, type safety throughout, and production-ready tooling. The authentication system is ready for multiple providers, and the database layer is abstracted for easy testing and scalin