Let me check the API call function and debug this step by step. Looking at the `checkPermissionsAndListSpreadsheets` function, I can see potential issues.

First, let's add better debugging to see exactly what's happening:

````tsx path=client/src/components/ExistingGoogleAccounts.tsx mode=EDIT
// Replace the checkPermissionsAndListSpreadsheets function with better error handling
const checkPermissionsAndListSpreadsheets = async (accountId: number) => {
  if (!accountId || trigger !== 'google-sheets') {
    console.log('Skipping API calls - accountId:', accountId, 'trigger:', trigger);
    return;
  }
  
  console.log('=== Starting API calls for credential ID:', accountId, '===');
  
  // Reset all states first
  setPermissions(null);
  setSpreadsheets(null);
  setPermissionsError(null);
  setSpreadsheetsError(null);
  
  // Check permissions
  setCheckingPermissions(true);
  try {
    console.log('Making permissions API call...');
    const permissionsResponse = await apiRequest('POST', '/api/google/check-permissions', {
      credentialId: accountId
    });
    const permissionsResult = await permissionsResponse.json();
    console.log('Permissions result:', permissionsResult);
    setPermissions(permissionsResult);
    
    // If permissions are good, get spreadsheets
    if (permissionsResult.hasSheetsPermissions) {
      setLoadingSpreadsheets(true);
      try {
        console.log('Making spreadsheets API call...');
        const spreadsheetsResponse = await apiRequest('POST', '/api/google/list-spreadsheets', {
          credentialId: accountId
        });
        const spreadsheetsResult = await spreadsheetsResponse.json();
        console.log('Spreadsheets result:', spreadsheetsResult);
        setSpreadsheets(spreadsheetsResult);
        setShowSpreadsheets(true);
      } catch (error: any) {
        console.error('Spreadsheets API error:', error);
        setSpreadsheetsError(error);
      } finally {
        setLoadingSpreadsheets(false);
      }
    } else {
      console.log('No sheets permissions found');
    }
  } catch (error: any) {
    console.error('Permissions API error:', error);
    setPermissionsError(error);
  } finally {
    setCheckingPermissions(false);
  }
};
````

Now let's also check if the `apiRequest` function is working correctly. Can you:

1. **Open browser dev tools** and check the **Console** tab
2. **Click "Select" on a Google account** 
3. **Share the console output** - especially any error messages or API responses

Also, check the **Network** tab in dev tools to see if the API calls are being made and what responses they're returning.

The issue might be:
- API endpoints returning errors
- `apiRequest` function not handling responses correctly  
- Missing authentication headers
- Wrong credential ID format

Share the console output and I can pinpoint the exact issue.
