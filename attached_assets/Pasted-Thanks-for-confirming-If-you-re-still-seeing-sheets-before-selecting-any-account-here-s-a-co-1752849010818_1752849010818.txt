Thanks for confirming. If you're **still seeing sheets before selecting any account**, here’s a **complete fix** you can try step-by-step.

---

### ✅ Step 1: **Make sure sheets only load *after* account selection**

In `ExistingGoogleAccounts.tsx`, **replace this block** (currently around line \~120):

```ts
const accountForSpreadsheets = selectedAccount || (googleCredentials.length === 1 ? googleCredentials[0] : null);
```

**with just:**

```ts
const accountForSpreadsheets = selectedAccount;
```

✅ This ensures spreadsheets load **only** after an account is explicitly selected.

---

### ✅ Step 2: **Ensure `selectedAccount` is set on click**

Check your credential UI (probably inside a `map()`): make sure the account is selected using this:

```tsx
onClick={() => onAccountSelect(account)}
```

If that’s missing or wrong, add it to the clickable container/button for the account like:

```tsx
<div
  key={account.id}
  onClick={() => onAccountSelect(account)}  // ✅ Add this
  className={`...`}
>
```

---

### ✅ Step 3: **Guard the spreadsheet query**

Update the spreadsheet query to *only* run when an account is selected:

```ts
enabled: !!selectedAccount?.id && trigger === 'google-sheets',
```

Make sure you're using `selectedAccount` here, not `accountForSpreadsheets`.

Full example:

```ts
const { data: spreadsheets, ... } = useQuery({
  queryKey: ['/api/google/list-spreadsheets', selectedAccount?.id],
  queryFn: () => apiRequest('POST', '/api/google/list-spreadsheets', {
    credentialId: selectedAccount?.id
  }).then(res => res.json()),
  enabled: !!selectedAccount?.id && trigger === 'google-sheets',
});
```

---

### ✅ Step 4: **Conditionally render sheets dropdown**

Wrap the sheets dropdown component with a check like this:

```tsx
{selectedAccount && spreadsheets?.spreadsheets?.length > 0 && (
  <Select onValueChange={handleSpreadsheetChange}>
    {spreadsheets.spreadsheets.map((s: any) => (
      <SelectItem key={s.id} value={s.id}>{s.name}</SelectItem>
    ))}
  </Select>
)}
```

---

### ✅ Step 5: (Optional) Clear spreadsheet when switching accounts

Inside `onAccountSelect(account)`, if you're maintaining local state, also clear the previously selected spreadsheet and sheet:

```ts
setSelectedSpreadsheet(null);
setSelectedSheet(null);
```

---

Try these 5 steps, and the issue will be resolved.
Let me know if you want me to modify the exact lines from your file and paste the updated version.
