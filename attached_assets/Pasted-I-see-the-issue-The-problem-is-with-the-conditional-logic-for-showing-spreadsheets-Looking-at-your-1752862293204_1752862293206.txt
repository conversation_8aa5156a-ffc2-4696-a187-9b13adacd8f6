I see the issue! The problem is with the conditional logic for showing spreadsheets. Looking at your code, there are multiple variables being used inconsistently. Let me fix this:

````tsx path=client/src/components/ExistingGoogleAccounts.tsx mode=EDIT
// Remove all the complex state variables and logic
// Delete these lines:
// const [localSelectedAccount, setLocalSelectedAccount] = useState<any>(selectedAccount);
// const [renderKey, setRenderKey] = useState(0);
// const accountToUse = selectedAccount || localSelectedAccount;
// const shouldShowSpreadsheetSection = trigger === 'google-sheets' && !!accountToUse;

// Replace with simple, direct logic:
const shouldShowSpreadsheetSection = trigger === 'google-sheets' && !!selectedAccount;
````

````tsx path=client/src/components/ExistingGoogleAccounts.tsx mode=EDIT
// Simplify the useEffect to only use selectedAccount
useEffect(() => {
  if (selectedAccount?.id && trigger === 'google-sheets') {
    console.log('Triggering API calls for selected account:', selectedAccount.id);
    checkPermissionsAndListSpreadsheets(selectedAccount.id);
  } else {
    // Reset state when no account selected
    console.log('Resetting spreadsheet state - no account selected');
    setPermissions(null);
    setSpreadsheets(null);
    setPermissionsError(null);
    setSpreadsheetsError(null);
    setShowSpreadsheets(false);
  }
}, [selectedAccount?.id, trigger]);
````

````tsx path=client/src/components/ExistingGoogleAccounts.tsx mode=EDIT
// Remove the complex sync useEffect - delete this entire block:
// useEffect(() => {
//   console.log('useEffect triggered: selectedAccount changed to', selectedAccount);
//   if (selectedAccount !== localSelectedAccount) {
//     setLocalSelectedAccount(selectedAccount);
//     setRenderKey(prev => prev + 1);
//   }
// }, [selectedAccount, localSelectedAccount]);
````

````tsx path=client/src/components/ExistingGoogleAccounts.tsx mode=EDIT
// Simplify the Select button handler
<Button
  variant="ghost"
  size="sm"
  onClick={(e) => {
    e.stopPropagation();
    console.log('Account selected:', account);
    onAccountSelect(account);
  }}
>
  Select
</Button>
````

````tsx path=client/src/components/ExistingGoogleAccounts.tsx mode=EDIT
// Fix the conditional rendering - use only selectedAccount
{shouldShowSpreadsheetSection && (
  <div className="space-y-4">
    <div className="flex items-center gap-2 mb-3">
      <Label className="text-base font-medium">Select Spreadsheet</Label>
    </div>
    {/* Rest of the spreadsheet UI */}
  </div>
)}
````

The key issues were:

1. **Multiple account variables** - `selectedAccount`, `localSelectedAccount`, `accountToUse` were causing conflicts
2. **Complex state sync** - The `renderKey` and sync logic was interfering with proper state management
3. **Inconsistent conditions** - Different parts of the code were checking different variables

Now the component will:
- Only show spreadsheets when `selectedAccount` exists AND `trigger === 'google-sheets'`
- Only make API calls when an account is actually selected
- Reset spreadsheet state when no account is selected

This should fix both issues: no premature spreadsheet loading and proper loading after account selection.
