Looking at your `ExistingGoogleAccounts.tsx` component, I can see the issue. The spreadsheet loading logic has several problems that prevent sheets from appearing after account selection.

## Issues Found:

1. **State synchronization problems** - Multiple state variables tracking the same account
2. **API calls not triggering properly** - The `useEffect` dependencies aren't working correctly
3. **Complex state management** - Too many state variables causing race conditions

## Solution:

Replace the complex state management with a simpler, more reliable approach:

````tsx path=client/src/components/ExistingGoogleAccounts.tsx mode=EDIT
// Remove these problematic state variables
// const [localSelectedAccount, setLocalSelectedAccount] = useState<any>(selectedAccount);
// const [renderKey, setRenderKey] = useState(0);

// Replace the complex useEffect logic with this simpler approach:
useEffect(() => {
  if (selectedAccount?.id && trigger === 'google-sheets') {
    console.log('Account selected, loading spreadsheets for:', selectedAccount.id);
    checkPermissionsAndListSpreadsheets(selectedAccount.id);
  } else {
    // Reset state when no account selected
    setPermissions(null);
    setSpreadsheets(null);
    setPermissionsError(null);
    setSpreadsheetsError(null);
    setShowSpreadsheets(false);
  }
}, [selectedAccount?.id, trigger]);

// Simplify the account selection handler
const handleAccountSelect = (account: any) => {
  console.log('Account selected:', account);
  onAccountSelect(account);
};
````

````tsx path=client/src/components/ExistingGoogleAccounts.tsx mode=EDIT
// Update the Select button click handler
<Button
  variant="ghost"
  size="sm"
  onClick={(e) => {
    e.stopPropagation();
    handleAccountSelect(account);
  }}
>
  Select
</Button>
````

````tsx path=client/src/components/ExistingGoogleAccounts.tsx mode=EDIT
// Simplify the spreadsheet section condition
{trigger === 'google-sheets' && selectedAccount && (
  <div className="space-y-4">
    <div className="flex items-center gap-2 mb-3">
      <Label className="text-base font-medium">Select Spreadsheet</Label>
    </div>
    {/* Rest of spreadsheet UI */}
  </div>
)}
````

## Key Changes:

1. **Removed redundant state** - Eliminated `localSelectedAccount` and `renderKey`
2. **Simplified useEffect** - Direct dependency on `selectedAccount?.id`
3. **Cleaner account selection** - Single handler function
4. **Fixed conditional rendering** - Simple check for `selectedAccount`

This should resolve the issue where spreadsheets weren't loading after account selection. The component will now properly trigger API calls when an account is selected and display the spreadsheet list.
