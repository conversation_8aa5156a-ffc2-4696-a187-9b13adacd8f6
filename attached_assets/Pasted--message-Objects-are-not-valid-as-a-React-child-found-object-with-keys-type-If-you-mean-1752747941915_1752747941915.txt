{"message": "Objects are not valid as a React child (found: object with keys {type}). If you meant to render a collection of children, use an array instead.", "stack": "Error: Objects are not valid as a React child (found: object with keys {type}). If you meant to render a collection of children, use an array instead.\n    at throwOnInvalidObjectType (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-276SZO74.js?v=82d4dba4:9934:17)\n    at reconcileChildFibers2 (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-276SZO74.js?v=82d4dba4:10564:15)\n    at reconcileChildren (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-276SZO74.js?v=82d4dba4:14290:37)\n    at updateHostComponent (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-276SZO74.js?v=82d4dba4:14807:11)\n    at beginWork (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-276SZO74.js?v=82d4dba4:15935:22)\n    at beginWork$1 (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-276SZO74.js?v=82d4dba4:19753:22)\n    at performUnitOfWork (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-276SZO74.js?v=82d4dba4:19198:20)\n    at workLoopSync (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-276SZO74.js?v=82d4dba4:19137:13)\n    at renderRootSync (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-276SZO74.js?v=82d4dba4:19116:15)\n    at recoverFromConcurrentError (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-276SZO74.js?v=82d4dba4:18736:28)", "componentStack": "\n    at div\n    at Badge (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/src/components/ui/badge.tsx:35:18)\n    at div\n    at div\n    at _c9 (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/src/components/ui/card.tsx:114:12)\n    at div\n    at _c (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/src/components/ui/card.tsx:20:11)\n    at div\n    at div\n    at MyAutomations (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/src/pages/my-automations.tsx:70:56)\n    at main\n    at div\n    at div\n    at DashboardLayout (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/src/pages/dashboard-layout.tsx:68:43)\n    at Route (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=82d4dba4:323:16)\n    at Switch (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=82d4dba4:379:17)\n    at Router (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/src/App.tsx?v=22-RTmjs_LjFsGqim6Bbx:60:49)\n    at SettingsProvider (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/src/contexts/SettingsContext.tsx:41:36)\n    at AuthProvider (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/src/contexts/AuthContext.tsx:30:32)\n    at ThemeProvider (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/src/contexts/ThemeContext.tsx:29:33)\n    at Provider (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-3RXG37ZK.js?v=82d4dba4:38:15)\n    at TooltipProvider (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/@fs/home/<USER>/workspace/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=82d4dba4:64:5)\n    at QueryClientProvider (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/@fs/home/<USER>/workspace/node_modules/.vite/deps/@tanstack_react-query.js?v=82d4dba4:2805:3)\n    at ErrorBoundary (https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/src/components/error-boundary.tsx:8:5)\n    at App", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "timestamp": "2025-07-17T10:25:39.551Z", "url": "https://f8cffd76-a445-4b42-b982-37cbd932df15-00-roo7wqo9wfl5.picard.repl.co/dashboard/automations"}