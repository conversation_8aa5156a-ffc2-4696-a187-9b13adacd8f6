Build a highly interactive and modern user dashboard for Filorina, a no-code automation SaaS, using React, Tailwind CSS, and Supabase. The dashboard should extend an existing authentication system (built with React, Tailwind CSS, and Supabase) and allow users to create and manage n8n-style workflows without a drag-and-drop editor. Use the following Supabase project details:





Supabase URL: https://xyguqslzenektzuimdxi.supabase.co



Public anon key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.O_CgSedeEUy3R9Gvk2SadN_2uEOokSAKtnNzIModRxY

The dashboard should be responsive, modular, scalable, and incorporate modern UI components with smooth animations and transitions. Use Tailwind CSS best practices and the primary color #155DB8 (consistent with the auth UI). Below are the detailed requirements:

1. Dashboard Layout





Sidebar Navigation:





Fixed, collapsible sidebar (hidden on mobile, toggleable with a hamburger menu).



Links to: "Browse Templates," "My Automations," "Account Settings," and "Logs."



Use icons (e.g., from Heroicons) next to each link for visual appeal.



Highlight the active page with a subtle background color (e.g., #155DB8 with white text).



Top Navbar:





Displays user profile info (e.g., name or email from Supabase auth) on the right.



Search bar in the center to search tasks/automations (functional, filtering results dynamically).



Button to "Create New Automation" (opens the form-based interface).



Responsive: On mobile, collapse the search bar into a magnifying glass icon that expands on click.



Main Content Area:





Renders the selected page ("Browse Templates," "My Automations," etc.).



Use a clean, minimal layout with padding and subtle shadows for depth.

2. Browse Templates Page





Display a grid of cards for popular pre-made automation templates (e.g., Gmail → Notion, Google Sheets → Slack).



Template Card Design:





Include template name, short description, and an icon/image representing the services (e.g., Gmail logo).



Buttons: "Preview" (shows a modal with details) and "Use" (opens the form-based interface to configure it).



Use hover effects (e.g., scale up slightly, shadow increase) for interactivity.



Grid should be responsive: 3 columns on desktop, 2 on tablet, 1 on mobile.



Fetch templates dynamically from Supabase (mock with static data initially if needed).

3. My Automations Page





Show a table or list of the user’s created automations.



Columns:





Name (custom name of the automation).



Status (toggleable Active/Inactive switch).



Last Run Time (timestamp, e.g., "2 hours ago").



Actions: "Edit" (opens form interface), "Delete" (confirmation modal), "Chat" (opens chat interface).



Responsive: Table stacks into cards on mobile.



Fetch user automations from Supabase, tied to the logged-in user’s ID.

4. Form-Based Interface





A step-by-step wizard for creating/editing automations, triggered by "Create New Automation" or "Edit."



Steps:





Trigger Selection:





Options: Scheduler, Discord, Chat, Slack, Telegram, Email (dropdown or card selection).



Each trigger shows relevant input fields (e.g., Scheduler: interval; Discord: channel name).



Action Configuration:





Options: Discord (Read/Send), Gmail (Read/Send), Google Calendar (Read/Write), Google Docs (Read/Write), Google Sheets (Read/Write), HTTP Request, Resume PDF Generator, Slack (Read/Send).



Dynamic form fields based on selection (e.g., Google Sheets Write: spreadsheet link, cell range).



Service Connections:





For services like Google Sheets, show:





Dropdown of existing connected accounts (e.g., "Kaizar Bharmal (<EMAIL>)").



"Sign in with a different account" button (triggers OAuth flow via Supabase).



After connection, dropdown to select a specific file (e.g., a Google Sheet).



Review & Save:





Summary of the automation (trigger, actions, inputs).



"Save" button to store in Supabase.



Use a progress bar or step indicators (e.g., 1/4, 2/4) at the top.



Smooth transitions between steps (e.g., fade or slide).

5. Chat Feature





Each automation has an interactive chat interface (accessible via "Chat" button in "My Automations").



Features:





Users can send messages to trigger the automation manually, check status, or receive notifications.



Mock a simple response system (e.g., "Automation started," "Last run: 10:00 AM").



Design: Minimal chat window with a message input and scrollable history, styled with Tailwind.

6. Supabase Integration





Protect the dashboard: Only logged-in users can access it (use Supabase auth session).



Redirect unauthenticated users to the login page.



Fetch user data (e.g., profile, automations) and templates from Supabase.



Store new automations in a Supabase table (e.g., automations with columns: id, user_id, name, trigger, actions, status).

7. Triggers, Actions, and Tools

Incorporate the following into the form interface:





Triggers: Scheduler, Discord, Chat, Slack, Telegram, Email.



Actions: Discord (Read/Send), Gmail (Read/Send), Google Calendar (Read/Write), Google Docs (Read/Write), Google Sheets (Read/Write), HTTP Request, Resume PDF Generator, Slack (Read/Send).



Tools: Airtable, Discord, Firecrawl Web Scraper, Google Sheets, Notion, Slack, Supabase, SerpAPI (for future LLM integration).



LLM: ChatGPT (mock placeholder for future integration).

8. Design & Interactivity





Use Tailwind CSS with primary color #155DB8 and a clean, modern aesthetic.



Add subtle animations (e.ge Stuart transitions (e.g., fade-ins, hover effects, sidebar slide).



Ensure responsiveness: Desktop, tablet, and mobile layouts.



Make it unique: Use gradients, neumorphism, or glassmorphism sparingly for a standout look (e.g., frosted glass effect on modals).



Keep it intuitive: Clear labels, tooltips, and minimal clicks to complete tasks.

9. Code Structure





Components:





DashboardLayout: Sidebar, navbar, and content area.



Sidebar: Navigation links with icons.



TopNavbar: Profile, search, and create button.



BrowseTemplates: Template grid.



TemplateCard: Individual template card.



MyAutomations: Automation list/table.



AutomationForm: Multi-step form interface.



ChatComponent: Per-automation chat UI.



Use React Router for navigation between pages.



Manage state with React hooks (e.g., useState, useEffect).



Integrate Supabase client with hooks for auth and data fetching.

10. Additional Notes





Include a loading spinner for API calls and page transitions.



Add error handling (e.g., "Failed to load templates" with retry button).



Mock pre-made templates initially (e.g., Gmail → Notion, Google Sheets → Slack) with static data.



Ensure the search bar filters tasks/automations in real-time.

Generate a single-page React app (HTML file with JSX) using CDN-hosted React, React Router, and Tailwind CSS. Include Supabase client setup and mock data where needed. The result should be a fully functional, interactive dashboard that’s easy to use and visually distinct.