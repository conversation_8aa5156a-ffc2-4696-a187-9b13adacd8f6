⚙️ Core Concept
Users build automations via a form-based UI, not drag-and-drop. Each automation consists of:

A trigger (e.g., webhook, schedule)

A series of actions (e.g., Google Drive cleanup, send email)

🧠 High-Level Architecture
java
Copy
Edit
Frontend (Form-based Builder UI)
        |
        v
Backend API (Node.js / Express or Fastify)
        |
        +--> Database (PostgreSQL + Drizzle ORM)
        |
        +--> Queue System (BullMQ / Redis)
        |
        +--> Dynamic Executor (Worker Engine)
        |
        +--> App Plugins (integrations like Gmail, Slack)
🗄️ Database Schema
1. Workflows Table
Stores user-created automations.

sql
Copy
Edit
Workflows (
  id UUID PK,
  user_id UUID FK,
  name TEXT,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)
2. Steps Table
Each step belongs to a workflow (either a trigger or action).

sql
Copy
Edit
Steps (
  id UUID PK,
  workflow_id UUID FK,
  type ENUM('trigger', 'action'),
  index INT,  -- execution order
  app_key TEXT,  -- e.g., 'gmail', 'slack'
  event_key TEXT,  -- e.g., 'send_email', 'append_text'
  config J<PERSON>N<PERSON>,  -- user-specified config
  created_at TIMESTAMP
)
3. Step Conditions Table
Optional conditions for when the step should run (used for triggers).

sql
Copy
Edit
StepConditions (
  id UUID PK,
  step_id UUID FK,
  key TEXT,
  operator ENUM('equals', 'includes', '>', '<', '!='),
  value TEXT
)
🔄 Execution Flow
1. Triggering a Workflow
User hits a webhook or schedules a run.

Backend queries for matching workflows via StepConditions.

2. Queue Execution
The first action step (index = 1) is pushed to a queue (BullMQ).

A worker consumes the step:

Dynamically imports the correct app (from plugin folder)

Executes its run() function

On success, pushes next step (index +1) into the queue

Repeat until all steps are executed.

📁 Folder Structure (Backend)
bash
Copy
Edit
/apps/             # All integrations
  /gmail/
    send_email.ts
    delete_email.ts
  /slack/
    send_message.ts
    trigger.ts

/workers/
  queue.ts         # BullMQ processor
  executor.ts      # Runs each step
/routes/
  trigger.ts       # Webhook entry
  workflows.ts     # Create/edit workflows
/utils/
  importer.ts      # Dynamic function loader
