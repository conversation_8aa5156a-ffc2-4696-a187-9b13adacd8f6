#!/usr/bin/env node

import postgres from 'postgres';
import { performance } from 'perf_hooks';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const DATABASE_URL = process.env.DATABASE_URL;

if (!DATABASE_URL) {
  console.error('❌ DATABASE_URL environment variable is not set');
  process.exit(1);
}

console.log('🔍 FlowCraft Database Connection Diagnostics');
console.log('=' .repeat(50));

// Parse the connection string to show details (without password)
const url = new URL(DATABASE_URL);
console.log(`📍 Host: ${url.hostname}`);
console.log(`🔌 Port: ${url.port}`);
console.log(`🗄️  Database: ${url.pathname.slice(1)}`);
console.log(`👤 Username: ${url.username}`);
console.log(`🔐 Password: ${'*'.repeat(url.password.length)} (${url.password.length} chars)`);
console.log('');

async function testConnection() {
  console.log('🧪 Testing database connection...');
  
  let client;
  try {
    // Test 1: Basic connection
    console.log('1️⃣  Creating connection...');
    const start1 = performance.now();
    
    client = postgres(DATABASE_URL, {
      ssl: 'require',
      max: 1,
      idle_timeout: 20,
      connect_timeout: 10,
      debug: false
    });
    
    const end1 = performance.now();
    console.log(`   ✅ Connection created in ${(end1 - start1).toFixed(2)}ms`);
    
    // Test 2: Simple query
    console.log('2️⃣  Testing simple query...');
    const start2 = performance.now();
    
    const result = await client`SELECT 1 as test, NOW() as current_time, version() as pg_version`;
    
    const end2 = performance.now();
    console.log(`   ✅ Query executed in ${(end2 - start2).toFixed(2)}ms`);
    console.log(`   📊 Result: ${JSON.stringify(result[0])}`);
    
    // Test 3: Check database tables
    console.log('3️⃣  Checking database schema...');
    const start3 = performance.now();
    
    const tables = await client`
      SELECT table_name, table_type 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;
    
    const end3 = performance.now();
    console.log(`   ✅ Schema query executed in ${(end3 - start3).toFixed(2)}ms`);
    console.log(`   📋 Found ${tables.length} tables:`);
    tables.forEach(table => {
      console.log(`      - ${table.table_name} (${table.table_type})`);
    });
    
    // Test 4: Check specific FlowCraft tables
    console.log('4️⃣  Checking FlowCraft tables...');
    const flowCraftTables = ['profiles', 'automations', 'credentials', 'team_members', 'activity_logs'];
    
    for (const tableName of flowCraftTables) {
      try {
        const start4 = performance.now();
        const count = await client`SELECT COUNT(*) as count FROM ${client(tableName)}`;
        const end4 = performance.now();
        console.log(`   ✅ ${tableName}: ${count[0].count} records (${(end4 - start4).toFixed(2)}ms)`);
      } catch (error) {
        console.log(`   ❌ ${tableName}: ${error.message}`);
      }
    }
    
    // Test 5: Connection pool stress test
    console.log('5️⃣  Testing connection performance...');
    const iterations = 5;
    const times = [];
    
    for (let i = 0; i < iterations; i++) {
      const start5 = performance.now();
      await client`SELECT 1`;
      const end5 = performance.now();
      const time = end5 - start5;
      times.push(time);
      console.log(`   Query ${i + 1}: ${time.toFixed(2)}ms`);
    }
    
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    console.log(`   📈 Performance Summary:`);
    console.log(`      Average: ${avgTime.toFixed(2)}ms`);
    console.log(`      Min: ${minTime.toFixed(2)}ms`);
    console.log(`      Max: ${maxTime.toFixed(2)}ms`);
    
    // Test 6: Check connection settings
    console.log('6️⃣  Checking connection settings...');
    const settings = await client`
      SELECT name, setting, unit, short_desc 
      FROM pg_settings 
      WHERE name IN ('max_connections', 'shared_buffers', 'work_mem', 'maintenance_work_mem')
      ORDER BY name
    `;
    
    console.log('   🔧 Database settings:');
    settings.forEach(setting => {
      console.log(`      ${setting.name}: ${setting.setting}${setting.unit || ''} - ${setting.short_desc}`);
    });
    
    console.log('');
    console.log('🎉 All database tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Database connection test failed:');
    console.error(`   Error: ${error.message}`);
    console.error(`   Code: ${error.code || 'N/A'}`);
    console.error(`   Stack: ${error.stack}`);
    return false;
  } finally {
    if (client) {
      try {
        await client.end();
        console.log('🔌 Connection closed');
      } catch (error) {
        console.error('⚠️  Error closing connection:', error.message);
      }
    }
  }
  
  return true;
}

// Run the test
testConnection()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
