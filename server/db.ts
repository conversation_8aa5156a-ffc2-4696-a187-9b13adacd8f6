import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from "@shared/schema";

if (!process.env.DATABASE_URL) {
  throw new Error(
    "DATABASE_URL must be set. Please set your Supabase database URL.",
  );
}

// Create the connection with optimized configuration for Supabase
const client = postgres(process.env.DATABASE_URL, {
  ssl: 'require',
  max: 3, // Increased from 1 to allow for better concurrency
  idle_timeout: 30, // Increased to reduce connection churn
  connect_timeout: 15, // Increased for more reliable connections
  max_lifetime: 60 * 30, // 30 minutes max connection lifetime
  prepare: false, // Disable prepared statements for better compatibility
  transform: {
    undefined: null // Transform undefined to null for better JSON handling
  }
});

export const db = drizzle(client, { schema });
